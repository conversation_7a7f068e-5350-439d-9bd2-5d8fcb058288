package main

import (
	"bufio"
	"bytes"
	"context"
	"errors"
	"flag"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"regexp"
	"strconv"
	"sync"
	"syscall"
	"time"
	"yylbe/pkg/base/lhttp"
	"yylbe/pkg/base/llog"
	"yylbe/pkg/base/lunsafe"
	"yylbe/pkg/buildinfo"
	"yylbe/pkg/config/laddrconfig"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"
)

// 简介
// -----------------
// 按表名分组缓冲对 ClickHouse 的写入请求，然后在超时或超量时批量写入

const serviceName = "chbuf"

// 暴露一个端口给本机，收集指标数据
var cmdportFlag = flag.Int("cmdport", 3320, "")

// 暴露一个端口给 prometheus，prometheus 定时拉取指标数据
var exportportFlag = flag.Int("exportport", 3321, "")

var httpServer *http.Server

func initLog() {
	llog.SetPathFuncByService(serviceName)

	if laddrconfig.Hold().HostConfigV2.IsPro {
		zerolog.SetGlobalLevel(zerolog.InfoLevel)
		//zerolog.SetGlobalLevel(zerolog.DebugLevel)
	} else {
		//zerolog.SetGlobalLevel(zerolog.DebugLevel)
		zerolog.SetGlobalLevel(zerolog.InfoLevel)
	}
}

func main() {
	initLog()

	go listenSignals()
	go ingesterMain()
	go consumerMain()

	gin.SetMode(gin.ReleaseMode)
	router := gin.Default()
	router.GET("/health", healthHandler)
	router.GET("/metrics", metricsHandler)
	router.GET("/version", versionHandler)

	addr := "0.0.0.0:" + strconv.Itoa(*exportportFlag)
	httpServer = &http.Server{Addr: addr, Handler: router}

	httpServer.ListenAndServe()
	llog.Get().Info().Msg("MAIN_EXIT")

}

func listenSignals() {
	c := make(chan os.Signal, 2)
	signal.Notify(c, syscall.SIGTERM, syscall.SIGINT)
	for {
		s := <-c
		switch s {
		case syscall.SIGTERM, syscall.SIGINT:
			llog.Get().Info().Str("s", s.String()).Msg("GOT_SIGNAL")
			flushCHBufMap(true)
			if httpServer != nil {
				httpServer.Shutdown(context.TODO())
			}
			return
		}
	}
}

func ingesterMain() {
	addr := "0.0.0.0:" + strconv.Itoa(*cmdportFlag)
	l, err := net.Listen("tcp4", addr)
	if err != nil {
		fmt.Println("Error listening:", err.Error())
		os.Exit(1)
	}
	defer l.Close()

	llog.Get().Info().Str("addr", addr).Msg("ingesterMain.LISTEN")

	for {
		conn, err := l.Accept()
		if err != nil {
			fmt.Println("Error accepting: ", err.Error())
			os.Exit(1)
		}
		go handleConnection(conn)
	}
}

func handleConnection(conn net.Conn) {
	defer conn.Close()

	llog.Get().Info().Str("RemoteAddr", conn.RemoteAddr().String()).Msg("handleConnection.Accept")

	scanner := bufio.NewScanner(conn)
	for scanner.Scan() {
		s := scanner.Text()
		//fmt.Println(s)
		onTextLine(s)
	}

	llog.Get().Info().Str("RemoteAddr", conn.RemoteAddr().String()).Msg("handleConnection.Close")
}

// https://pkg.go.dev/regexp/syntax
// \w 包括下划线
// https://go.dev/ref/spec#String_literals
var reTextLine = regexp.MustCompile(`^(\w+) (.+?) (.+?)$`)

func onTextLine(s string) {
	// 按正则提取出三个字段
	fields := reTextLine.FindStringSubmatch(s)

	if len(fields) != 4 {
		llog.Get().Error().Str("s", s).Msg("onTextLine.PARSE_ERROR_1")
		return
	}

	cmd := fields[1]
	k := fields[2]
	v := fields[3]

	if cmd == "add" {
		onAdd(k, v)
		return
	}

	llog.Get().Error().Str("cmd", cmd).Str("k", k).Str("v", v).Msg("onTextLine.PARSE_ERROR_3")
}

const MaxBufSize = 256 * 1024

var chBufMap = make(map[string]*bytes.Buffer)
var chBufLastFlushTimeMap = make(map[string]time.Time)
var chBufMapMutex sync.Mutex

func chBufPush(tableName, jsonData string) {
	chBufMapMutex.Lock()
	p, ok := chBufMap[tableName]
	if !ok {
		p = &bytes.Buffer{}
		p.Grow(MaxBufSize)
		chBufMap[tableName] = p
	}
	p.WriteString(jsonData)
	p.WriteByte('\n')
	chBufMapMutex.Unlock()
}

func canFlush(tableName string, p *bytes.Buffer, force bool) bool {
	if p == nil {
		return false
	}
	if p.Len() == 0 {
		return false
	}
	if force {
		return true
	}

	timeout := time.Second * 15
	if !laddrconfig.Hold().HostConfigV2.IsPro {
		timeout = time.Second * 5
	}

	if time.Since(chBufLastFlushTimeMap[tableName]) >= timeout {
		return true
	}
	if p.Len() >= MaxBufSize-1024 {
		return true
	}
	return false
}

func extractFlushingCHBufMap(force bool) map[string]*bytes.Buffer {
	var flushing map[string]*bytes.Buffer
	chBufMapMutex.Lock()
	for tableName, p := range chBufMap {
		if !canFlush(tableName, p, force) {
			continue
		}
		if flushing == nil {
			flushing = make(map[string]*bytes.Buffer)
		}
		flushing[tableName] = p
		chBufLastFlushTimeMap[tableName] = time.Now()
		delete(chBufMap, tableName)
	}
	chBufMapMutex.Unlock()
	return flushing
}

func onAdd(tableName, jsonData string) {
	llog.Get().Debug().Str("tableName", tableName).RawJSON("jsonData", lunsafe.StringToByteSlice(jsonData)).Msg("onAdd")

	// 按表名缓冲 JSONEachRow
	chBufPush(tableName, jsonData)
}

func flushCHBufMap(force bool) {
	flushing := extractFlushingCHBufMap(force)
	if flushing == nil {
		return
	}

	for tableName, p := range flushing {
		flushCHBuf(tableName, p)
	}
}

var retrySampler = &zerolog.BurstSampler{
	Burst:  1,
	Period: time.Minute,
}

var ErrHTTPStatusFail = errors.New("ErrHTTPStatusFail")

func flushCHBuf(tableName string, p *bytes.Buffer) {
	err := internalFlushCHBuf(tableName, p)
	if err != nil && retrySampler.Sample(zerolog.ErrorLevel) {
		llog.Get().Error().Str("tableName", tableName).Int("len", p.Len()).Msg("flushCHBuf.SLEEP_THEN_RETRY")
		time.Sleep(15 * time.Second)
		// 重试一次
		internalFlushCHBuf(tableName, p)
	}
}

func internalFlushCHBuf(tableName string, p *bytes.Buffer) error {
	buf := p.Bytes()

	config := laddrconfig.Hold().ClickhouseConfig
	params := url.Values{}
	params.Add("query", "INSERT INTO default."+tableName+" FORMAT JSONEachRow")
	params.Add("input_format_skip_unknown_fields", "1")
	params.Add("user", config.User)
	params.Add("password", config.Password)
	params.Add("database", config.Database)
	urlStr := fmt.Sprintf("http://%s:%d/?", config.Host, config.HttpPort) + params.Encode()

	// 调用 ClickHouse 的 HTTP 接口批量写入 JSONEachRow 格式的数据
	task := lhttp.Task{}
	task.URL = urlStr
	task.Method = "POST"
	task.In = buf
	err := task.Do(context.TODO())
	if err != nil {
		llog.Get().Error().Err(err).Str("tableName", tableName).Int("len", p.Len()).Msg("flushCHBuf.NET_FAIL")
		return err
	}
	if !task.IsStatusOK() {
		llog.Get().Error().Int("Status", task.Status).Str("tableName", tableName).Int("len", p.Len()).Msg("flushCHBuf.STATUS_FAIL")
		return ErrHTTPStatusFail
	}
	llog.Get().Info().Str("tableName", tableName).Int("len", p.Len()).Msg("flushCHBuf.OK")
	return nil
}

func consumerMain() {
	for {
		flushCHBufMap(false)
		time.Sleep(time.Second * 2)
	}
}

// exportToPrometheus 格式化输出到 prometheus 格式
func exportToPrometheus() []byte {
	return nil
}

func healthHandler(c *gin.Context) {
	c.String(http.StatusOK, "OK\n")
}

// metricsHandler 输出 prometheus 格式的指标
func metricsHandler(c *gin.Context) {
	data := exportToPrometheus()
	llog.Get().Debug().Bytes("data", data).Str("RemoteAddr", c.Request.RemoteAddr).Msg("metricsHandler")
	c.Data(http.StatusOK, "text/plain; version=0.0.4; charset=utf-8", data)
}

func versionHandler(c *gin.Context) {
	c.String(http.StatusOK, buildinfo.GetBuildInfoString())
}
