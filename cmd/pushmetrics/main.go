package main

import (
	"bufio"
	"bytes"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"
	"regexp"
	"sort"
	"strconv"
	"sync"
	"sync/atomic"
	"time"
	"yylbe/pkg/base/llog"
	"yylbe/pkg/base/lutil"
	"yylbe/pkg/buildinfo"
	"yylbe/pkg/config/laddrconfig"
	"yylbe/pkg/sdk/sdserversdk"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"
)

// 简介
// -----------------
// 以前写 c++ 或者 golang 服务，都是服务自己提供 /metrics 接口导出指标数据
// 现在 php-fpm 程序的生命期仅在于单个请求，workerman 也是多进程合作，都无法在单个进程内部维护 /metrics 所需的状态
// 可以用 redis 来存 /metrics 所需的状态，但有些点的处理不太方便：
// 比如对基数爆炸的控制，清理废弃指标，导出指标时对 redis 有个尖峰的影响
// 所以这里用一个简单的服务来维护 /metrics 所需的状态，php-fpm 和 workerman 通过 tcp 连接该服务，发送指标更新指令
// php-fpm 对这个服务的 tcp 连接是 persistent 的，可以跨越单个请求的生命期
// 向这个服务发送指标更新指令时，不需要等待这个服务的回复，这个服务也不会回复，这比 redis 指令的 round trip 要快很多
// 这个服务一重启，状态就全清了，比较干净

// 指标类型
// -----------------
// 只有两种指标类型，counter 和 gauge
// counter 是计数器，只能增加，不能减少，重启后重置，重置可以被 prometheus 检测到
// gauge 是水平值，操作方法是赋值
// 常见的 counter 类型指标有：请求次数
// 常见的 gauge 类型指标有：在线人数

// 命令格式
// -----------------
// 一行一个命令
// 只有 add 和 set 两种命令
// 参数是 prometheus 规定的指标导出格式
// https://prometheus.io/docs/instrumenting/exposition_formats/
// add http_requests_total{method="post",code="200"} 1027
// set metric_without_timestamp_and_labels 12

// 服务名
const serviceName = "pushmetrics"

// 暴露一个端口给本机，收集指标数据
var cmdportFlag = flag.Int("cmdport", 3300, "")

// 暴露一个端口给 prometheus，prometheus 定时拉取指标数据
var exportportFlag = flag.Int("exportport", 3301, "")

// 假设一行 100 字节，数据量不要超过 1M 吧
const maxKeyCount = 1000000 / 100

// 不支持指标值为小数，只支持指标值为整数
var hashMap map[string]*lutil.AtomicFloat64
var hashMapMutex sync.RWMutex
var needSorted atomic.Bool
var sortedKeys []string
var sortedValues []*lutil.AtomicFloat64
var sortedMutex sync.Mutex

func init() {
	hashMap = make(map[string]*lutil.AtomicFloat64)
}

var httpServer *http.Server

func initLog() {
	llog.SetPathFuncByService(serviceName)

	if laddrconfig.Hold().HostConfigV2.IsPro {
		zerolog.SetGlobalLevel(zerolog.InfoLevel)
	} else {
		zerolog.SetGlobalLevel(zerolog.DebugLevel)
	}
}

func main() {
	initLog()

	go keepRegisterSelf()
	go metricRegistryServerMain()

	gin.SetMode(gin.ReleaseMode)
	router := gin.Default()
	router.GET("/health", healthHandler)
	router.GET("/metrics", metricsHandler)
	router.GET("/version", versionHandler)

	addr := "0.0.0.0:" + strconv.Itoa(*exportportFlag)
	httpServer = &http.Server{Addr: addr, Handler: router}

	httpServer.ListenAndServe()
	llog.Get().Info().Msg("MAIN_EXIT")

}

func keepRegisterSelf() {
	time.Sleep(1 * time.Second)
	for {
		sdserversdk.Register(serviceName, *exportportFlag)
		time.Sleep(60 * time.Second)
	}
}

func metricRegistryServerMain() {
	addr := "0.0.0.0:" + strconv.Itoa(*cmdportFlag)
	l, err := net.Listen("tcp4", addr)
	if err != nil {
		fmt.Println("Error listening:", err.Error())
		os.Exit(1)
	}
	defer l.Close()

	llog.Get().Info().Str("addr", addr).Msg("metricRegistryServerMain.LISTEN")

	for {
		conn, err := l.Accept()
		if err != nil {
			fmt.Println("Error accepting: ", err.Error())
			os.Exit(1)
		}
		go handleConnection(conn)
	}
}

func handleConnection(conn net.Conn) {
	defer conn.Close()

	llog.Get().Info().Str("RemoteAddr", conn.RemoteAddr().String()).Msg("handleConnection.Accept")

	scanner := bufio.NewScanner(conn)
	for scanner.Scan() {
		s := scanner.Text()
		//fmt.Println(s)
		onTextLine(s)
	}

	llog.Get().Info().Str("RemoteAddr", conn.RemoteAddr().String()).Msg("handleConnection.Close")
}

// 设计思路是这里的代码尽量简单，不要搞复杂
// 如果业务方给的输入有格式错误或者基数爆炸，通过修复业务方的 bug 来解决，解决后重启该服务就都清了
// 所以这里只是简单识别这些指标更新指令，不做复杂的校验

// https://pkg.go.dev/regexp/syntax
// \w 包括下划线
// https://go.dev/ref/spec#String_literals
var reTextLine = regexp.MustCompile(`^(\w+) (.+?) (\S+)$`)

func onTextLine(s string) {
	// 按正则提取出三个字段
	fields := reTextLine.FindStringSubmatch(s)

	if len(fields) != 4 {
		llog.Get().Error().Str("s", s).Msg("onTextLine.PARSE_ERROR_1")
		return
	}

	cmd := fields[1]
	k := fields[2]

	v, err := strconv.ParseFloat(fields[3], 64)
	if err != nil {
		llog.Get().Error().Err(err).Str("s", s).Msg("onTextLine.PARSE_ERROR_2")
		return
	}

	if cmd == "add" {
		onAdd(k, v)
		return
	}
	if cmd == "set" {
		onSet(k, v)
		return
	}

	llog.Get().Error().Str("cmd", cmd).Str("k", k).Float64("v", v).Msg("onTextLine.PARSE_ERROR_3")
}

func onAdd(k string, v float64) {
	llog.Get().Debug().Str("k", k).Float64("v", v).Msg("onAdd")

	// 多线程读取 map 时加读锁
	hashMapMutex.RLock()
	if p, ok := hashMap[k]; ok {
		hashMapMutex.RUnlock()
		// 修改值是原子性操作，多线程安全
		p.Add(v)
		return
	}
	hashMapMutex.RUnlock()
	// 键不存在是低概率事件，加写锁处理了
	hashMapMutex.Lock()
	defer hashMapMutex.Unlock()
	// 双重检查
	if p, ok := hashMap[k]; ok {
		p.Add(v)
		return
	}
	// 基数爆炸不要炸坏该服务
	if len(hashMap) >= maxKeyCount {
		return
	}
	// 新建键值
	p := lutil.NewAtomicFloat64(0)
	p.Store(v)
	hashMap[k] = p
	// 标记需要重新排序
	needSorted.Store(true)
}

func onSet(k string, v float64) {
	llog.Get().Debug().Str("k", k).Float64("v", v).Msg("onSet")

	// 多线程读取 map 时加读锁
	hashMapMutex.RLock()
	if p, ok := hashMap[k]; ok {
		hashMapMutex.RUnlock()
		// 修改值是原子性操作，多线程安全
		p.Store(v)
		return
	}
	hashMapMutex.RUnlock()
	// 键不存在是低概率事件，加写锁处理了
	hashMapMutex.Lock()
	defer hashMapMutex.Unlock()
	// 双重检查
	if p, ok := hashMap[k]; ok {
		p.Store(v)
		return
	}
	// 基数爆炸不要炸坏该服务
	if len(hashMap) >= maxKeyCount {
		return
	}
	// 新建键值
	p := lutil.NewAtomicFloat64(0)
	p.Store(v)
	hashMap[k] = p
	// 标记需要重新排序
	needSorted.Store(true)
}

func checkSort() {
	// 检查是否需要排序
	if !needSorted.Load() {
		return
	}
	// 加锁
	sortedMutex.Lock()
	defer sortedMutex.Unlock()
	// 双重检查是否需要排序
	if !needSorted.Load() {
		return
	}
	// 标记不需要排序
	needSorted.Store(false)
	// 抓取键列表
	hashMapMutex.RLock()
	keys := make([]string, 0, len(hashMap))
	for k := range hashMap {
		keys = append(keys, k)
	}
	hashMapMutex.RUnlock()
	// 排序
	sort.Strings(keys)
	// 根据排序的键列表抓取值列表
	hashMapMutex.RLock()
	values := make([]*lutil.AtomicFloat64, 0, len(keys))
	for _, k := range keys {
		v := hashMap[k]
		// 目前不删指标项，不会出现这种情况
		if v == nil {
			v = lutil.NewAtomicFloat64(0)
		}
		values = append(values, v)
	}
	hashMapMutex.RUnlock()
	// 更新排序后的键值列表
	sortedKeys = keys
	sortedValues = values
}

// exportToPrometheus 格式化输出到 prometheus 格式
func exportToPrometheus() []byte {
	// 检查是否需要排序
	checkSort()
	// 加锁
	sortedMutex.Lock()
	defer sortedMutex.Unlock()
	// 估计输出大小
	size := len(sortedKeys) * 100
	// 拼接输出
	var sb bytes.Buffer
	sb.Grow(size)
	for i, k := range sortedKeys {
		sb.WriteString(k)
		sb.WriteString(" ")
		sb.WriteString(strconv.FormatFloat(sortedValues[i].Load(), 'f', -1, 64))
		sb.WriteString("\n")
	}
	return sb.Bytes()
}

func healthHandler(c *gin.Context) {
	c.String(http.StatusOK, "OK\n")
}

// metricsHandler 输出 prometheus 格式的指标
func metricsHandler(c *gin.Context) {
	data := exportToPrometheus()
	llog.Get().Debug().Bytes("data", data).Str("RemoteAddr", c.Request.RemoteAddr).Msg("metricsHandler")
	c.Data(http.StatusOK, "text/plain; version=0.0.4; charset=utf-8", data)
}

func versionHandler(c *gin.Context) {
	c.String(http.StatusOK, buildinfo.GetBuildInfoString())
}
