package main

import (
	"bufio"
	"context"
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"syscall"
	"time"
	"yylbe/pkg/base/ijson"
	"yylbe/pkg/base/llog"
	"yylbe/pkg/buildinfo"
	"yylbe/pkg/config/laddrconfig"
	"yylbe/pkg/config/lconfigchanged"
	"yylbe/pkg/config/lsourceconfig"
	"yylbe/pkg/sdk/adjustsdk"
	"yylbe/pkg/sdk/alertbotsdk"
	"yylbe/pkg/sdk/fbconversionsapi"
	"yylbe/pkg/sdk/googleanalyticssdk"
	"yylbe/pkg/sdk/roibestapi"

	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog"
)

// 简介
// -----------------
// 这个服务是用来调用第三方接口打点的，比如调用 Adjust 的 S2S API
// https://help.adjust.com/zh/article/s2s-api-reference
// 如果用 php Workerman 来实现，维持 100 个并发需要 100 个进程，50*100M 内存
// 如果用 php Swoole 来实现，高并发比较省资源，但不熟悉这个技术栈
// 如果用 php Guzzle 来实现，可以读取一批，发送一批，也行
// 直接在业务代码里面同步打点是不行的，因为第三方接口延迟比较大，会大幅降低业务消息的处理能力

const serviceName = "dotserver"

// 暴露一个 HTTP 端口，用于监控该服务
var exportportFlag = flag.Int("exportport", 3302, "")

var httpServer *http.Server

func initLog() {
	llog.SetPathFuncByService(serviceName)

	if laddrconfig.Hold().HostConfigV2.IsPro {
		zerolog.SetGlobalLevel(zerolog.InfoLevel)
	} else {
		zerolog.SetGlobalLevel(zerolog.DebugLevel)
	}
}

func main() {
	initLog()

	go listenSignals()

	http.HandleFunc("/health", healthHandler)
	http.HandleFunc("/metrics", metricsHandler)
	http.HandleFunc("/version", versionHandler)
	addr := "0.0.0.0:" + strconv.Itoa(*exportportFlag)
	httpServer = &http.Server{Addr: addr, Handler: nil}

	go dotServerMain()

	httpServer.ListenAndServe()
	llog.Get().Info().Msg("MAIN_EXIT")
}

var gotSIGTERM atomic.Bool

func listenSignals() {
	c := make(chan os.Signal, 2)
	signal.Notify(c, syscall.SIGTERM, syscall.SIGINT)
	for {
		s := <-c
		switch s {
		case syscall.SIGTERM, syscall.SIGINT:
			gotSIGTERM.Store(true)
			llog.Get().Info().Str("s", s.String()).Msg("GOT_SIGNAL")
			return
		}
	}
}

const maxConcurrency = 32

var taskQueue chan string
var taskDoneTrack sync.WaitGroup

var rdb *redis.Client

func initRedis() {
	addr := laddrconfig.Hold().RedisConfig.QueueMaster
	llog.Get().Info().Interface("addr", addr).Msg("initRedis")
	rdb = redis.NewClient(&redis.Options{
		Addr:     addr.Addr,
		Username: addr.Username,
		Password: addr.Password,
		DB:       addr.DB,
	})
}

var adjustClient *adjustsdk.Client

func initAdjustClient() {
	adjustClient = adjustsdk.NewClient()
}

func initWatchConfigChanged() {
	lsourceconfig.Reload(rdb)

	lconfigchanged.ListenConfigChanged(rdb, onConfigChanged)
}

func onConfigChanged(configChangedReasons lconfigchanged.ConfigChangedReasons) {
	checkDoAdjustAddEvents(configChangedReasons)

	lsourceconfig.Reload(rdb)
}

var lastDoAdjustAddEvents time.Time

func checkDoAdjustAddEvents(configChangedReasons lconfigchanged.ConfigChangedReasons) {
	if configChangedReasons.Has(lconfigchanged.CONFIG_CHANGED_REASON_SOURCE_MAP) || time.Since(lastDoAdjustAddEvents) > time.Hour {
		lastDoAdjustAddEvents = time.Now()

		/*
			// 只在线上服跑 Adjust 自动化脚本
			if laddrconfig.Hold().HostConfigV2.IsPro {
				err := exec.Command("php", "php/insight/admin/adjust_add_events.php").Run()
				if err != nil {
					llog.Get().Error().Err(err).Msg("adjust_add_events.FAIL")
				} else {
					llog.Get().Info().Msg("adjust_add_events.OK")
				}
			}
		*/
	}
}

func dotServerMain() {
	llog.Get().Info().Int("maxConcurrency", maxConcurrency).Msg("dotServerMain")

	initRedis()
	initWatchConfigChanged()
	initAdjustClient()

	llog.Get().Info().Interface("data", lsourceconfig.Hold()).Msg("lsourceconfig.CONFIG_DATA")

	doDataCompensation()

	taskQueue = make(chan string)
	for i := 0; i < maxConcurrency; i++ {
		go workerMain()
	}

	for {
		// 收到 SIGTERM 信号后，就不再消费，如果没有进行中的任务则退出
		if gotSIGTERM.Load() {
			llog.Get().Info().Msg("DONE_WAIT.BEGIN")
			taskDoneTrack.Wait()
			llog.Get().Info().Msg("DONE_WAIT_END")
			httpServer.Shutdown(context.TODO())
			return
		}

		msg := pullMsg()
		if msg == "" {
			continue
		}

		taskDoneTrack.Add(1)
		taskQueue <- msg
	}
}

func workerMain() {
	for {
		msg := <-taskQueue
		handleMsg(msg)
		taskDoneTrack.Done()
	}
}

func pullMsg() string {
	vals, err := rdb.BRPop(context.TODO(), time.Second, "mq_dot").Result()

	// 超时了，没有拉取到消息
	if err == redis.Nil {
		return ""
	}

	if err != nil {
		llog.Get().Error().Err(err).Msg("pullMsg_FAIL")
		time.Sleep(time.Second)
		return ""
	}

	// 返回值是二元组：队列名，消息内容
	val := vals[1]
	llog.Get().Debug().Str("val", val).Msg("pullMsg_OK")
	return val
}

type Dot struct {
	T string
	V json.RawMessage
}

type DotUserRegister struct {
	CreatedAt     string // RFC3339
	Source        string
	UserID        int
	UserIP        string
	UserAgent     string
	GoogleADID    string
	AdjustADID    string
	AndroidID     string
	AppInstanceID string
	Fbp           string
	FbExtinfo     []string
	RoibestLinkId string
	AdjustSDK     string
}

type DotUserLogin struct {
	CreatedAt     string // RFC3339
	Source        string
	UserID        int
	UserIP        string
	GoogleADID    string
	AdjustADID    string
	AndroidID     string
	AppInstanceID string
	Fbp           string
	FbExtinfo     []string
	RoibestLinkId string
	AdjustSDK     string
}

type DotUserRecharge struct {
	CreatedAt     string // RFC3339
	Source        string
	UserID        int
	UserIP        string
	UserAgent     string
	GoogleADID    string
	AdjustADID    string
	AndroidID     string
	AppInstanceID string
	Fbp           string
	FbExtinfo     []string
	RoibestLinkId string
	AdjustSDK     string

	// 订单唯一标志
	OrderID string
	// 以完整货币单位表示的收入事件值 (149.99 = $ 149.99)。此参数可接受的 最小值是 0.001。
	Amount float64
	// 收入事件的货币代码
	// INR 印度卢比
	Currency string

	IsFirstRecharge      bool
	IsTodayFirstRecharge bool

	IsManualRecharge bool
	RechargeRemark   string
}

type DotUserWithdraw struct {
	CreatedAt     string // RFC3339
	Source        string
	UserID        int
	UserIP        string
	GoogleADID    string
	AdjustADID    string
	AndroidID     string
	AppInstanceID string
	AdjustSDK     string

	// 以完整货币单位表示的收入事件值 (149.99 = $ 149.99)。此参数可接受的 最小值是 0.001。
	Amount float64
	// 收入事件的货币代码
	// INR 印度卢比
	Currency string
}

func handleMsg(msg string) {
	dot := &Dot{}
	err := ijson.API.UnmarshalFromString(msg, dot)
	if err != nil {
		llog.Get().Error().Err(err).Str("msg", msg).Msg("handleMsg.PARSE_FAIL")
		return
	}

	msgType := dot.T
	msgData := dot.V

	err = onMsgTypeData(msgType, msgData)

	if err != nil {
		llog.Get().Error().Err(err).Str("msg", msg).Msg("handleMsg.FAIL")
		return
	}
}

func onMsgTypeData(msgType string, msgData json.RawMessage) error {
	llog.Get().Info().Str("msgType", msgType).RawJSON("msgData", msgData).Msg("onMsgTypeData.BEGIN")

	var err error
	switch msgType {
	case "DotUserRegister":
		err = onDotUserRegister(msgData)
	case "DotUserLogin":
		err = onDotUserLogin(msgData)
	case "DotUserRecharge":
		err = onDotUserRecharge(msgData)
	case "DotUserWithdraw":
		err = onDotUserWithdraw(msgData)
	}
	return err
}

type LogEntry struct {
	Message string          `json:"message"`
	Dot     json.RawMessage `json:"dot"`
}

func doDataCompensation() {
	// 一行一行地读取 tmp/fuse.log 文件，按 JSON 格式解析

	file, err := os.Open("tmp/fusedot.log")
	if err != nil {
		llog.Get().Error().Err(err).Msg("doDataCompensation.NO_FILE")
		return
	}
	defer file.Close()

	count := 0

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		count++

		line := scanner.Text()
		var entry LogEntry
		err := ijson.API.Unmarshal([]byte(line), &entry)
		if err != nil {
			llog.Get().Error().Err(err).Int("count", count).Msg("doDataCompensation.PARSE_FAIL")
			continue
		}

		if entry.Message == "onDotUserRegister.NO_APP_TOKEN" {
			onMsgTypeData("DotUserRegister", entry.Dot)
		} else if entry.Message == "onDotUserLogin.NO_APP_TOKEN" {
			onMsgTypeData("DotUserLogin", entry.Dot)
		} else if entry.Message == "onDotUserRecharge.NO_APP_TOKEN" {
			onMsgTypeData("DotUserRecharge", entry.Dot)
		}
	}

	if err := scanner.Err(); err != nil {
		llog.Get().Error().Err(err).Int("count", count).Msg("doDataCompensation.SCAN_FAIL")
		return
	}

	llog.Get().Info().Int("count", count).Msg("doDataCompensation.OK")
}

func onDotUserRegister(msgData json.RawMessage) error {
	dot := &DotUserRegister{}
	err := ijson.API.Unmarshal(msgData, dot)
	if err != nil {
		llog.Get().Error().Err(err).Bytes("msgData", msgData).Msg("onDotUserRegister.PARSE_FAIL")
		return err
	}

	llog.Get().Debug().Interface("dot", dot).Msg("onDotUserRegister.BEGIN")

	adjustAppConfig := lsourceconfig.Hold().AdjustGetAppConfig(dot.Source)

	{
		task := &adjustsdk.Task{
			TaskName: "Register",
			CommonParams: adjustsdk.CommonParams{
				Source: dot.Source,
				UserID: dot.UserID,

				AppToken:      adjustAppConfig.AppToken,
				CreatedAt:     dot.CreatedAt,
				UserIP:        dot.UserIP,
				UserAgent:     dot.UserAgent,
				GoogleADID:    dot.GoogleADID,
				AdjustADID:    dot.AdjustADID,
				AndroidID:     dot.AndroidID,
				AppInstanceID: dot.AppInstanceID,
				Fbp:           dot.Fbp,
				FbExtinfo:     dot.FbExtinfo,
				RoibestLinkId: dot.RoibestLinkId,
				AdjustSDK:     dot.AdjustSDK,

				EventToken: adjustAppConfig.Register,
			},
		}
		err := doAdjustTask(task)
		if err != nil {
			return err
		}
	}
	return nil
}

func onDotUserLogin(msgData json.RawMessage) error {
	dot := &DotUserLogin{}
	err := ijson.API.Unmarshal(msgData, dot)
	if err != nil {
		llog.Get().Error().Err(err).Bytes("msgData", msgData).Msg("onDotUserLogin.PARSE_FAIL")
		return err
	}

	llog.Get().Debug().Interface("dot", dot).Msg("onDotUserLogin.BEGIN")

	adjustAppConfig := lsourceconfig.Hold().AdjustGetAppConfig(dot.Source)

	{
		task := &adjustsdk.Task{
			TaskName: "Login",
			CommonParams: adjustsdk.CommonParams{
				Source: dot.Source,
				UserID: dot.UserID,

				AppToken:      adjustAppConfig.AppToken,
				CreatedAt:     dot.CreatedAt,
				UserIP:        dot.UserIP,
				GoogleADID:    dot.GoogleADID,
				AdjustADID:    dot.AdjustADID,
				AndroidID:     dot.AndroidID,
				AppInstanceID: dot.AppInstanceID,
				Fbp:           dot.Fbp,
				FbExtinfo:     dot.FbExtinfo,
				RoibestLinkId: dot.RoibestLinkId,
				AdjustSDK:     dot.AdjustSDK,

				EventToken: adjustAppConfig.Login,
			},
		}
		err := doAdjustTask(task)
		if err != nil {
			return err
		}
	}
	return nil
}

func onDotUserRecharge(msgData json.RawMessage) error {
	dot := &DotUserRecharge{}
	err := ijson.API.Unmarshal(msgData, dot)
	if err != nil {
		llog.Get().Error().Err(err).Bytes("msgData", msgData).Msg("onDotUserRecharge.PARSE_FAIL")
		return err
	}

	llog.Get().Debug().Interface("dot", dot).Msg("onDotUserRecharge.BEGIN")

	adjustAppConfig := lsourceconfig.Hold().AdjustGetAppConfig(dot.Source)

	if dot.IsFirstRecharge {
		task := &adjustsdk.Task{
			TaskName: "FirstRecharge",
			CommonParams: adjustsdk.CommonParams{
				Source: dot.Source,
				UserID: dot.UserID,

				AppToken:      adjustAppConfig.AppToken,
				CreatedAt:     dot.CreatedAt,
				UserIP:        dot.UserIP,
				UserAgent:     dot.UserAgent,
				GoogleADID:    dot.GoogleADID,
				AdjustADID:    dot.AdjustADID,
				AndroidID:     dot.AndroidID,
				AppInstanceID: dot.AppInstanceID,
				Fbp:           dot.Fbp,
				FbExtinfo:     dot.FbExtinfo,
				RoibestLinkId: dot.RoibestLinkId,
				AdjustSDK:     dot.AdjustSDK,

				EventToken: adjustAppConfig.FirstRecharge,
			},
			RevenueParams: &adjustsdk.RevenueParams{
				OrderID:          dot.OrderID,
				Amount:           dot.Amount,
				Currency:         dot.Currency,
				IsManualRecharge: dot.IsManualRecharge,
				RechargeRemark:   dot.RechargeRemark,
			},
		}
		err := doAdjustTask(task)
		if err != nil {
			return err
		}
		// 三个事件按优先级只发一个
		return nil
	}

	if dot.IsTodayFirstRecharge {
		task := &adjustsdk.Task{
			TaskName: "TodayFirstRecharge",
			CommonParams: adjustsdk.CommonParams{
				Source: dot.Source,
				UserID: dot.UserID,

				AppToken:      adjustAppConfig.AppToken,
				CreatedAt:     dot.CreatedAt,
				UserIP:        dot.UserIP,
				UserAgent:     dot.UserAgent,
				GoogleADID:    dot.GoogleADID,
				AdjustADID:    dot.AdjustADID,
				AndroidID:     dot.AndroidID,
				AppInstanceID: dot.AppInstanceID,
				Fbp:           dot.Fbp,
				FbExtinfo:     dot.FbExtinfo,
				RoibestLinkId: dot.RoibestLinkId,
				AdjustSDK:     dot.AdjustSDK,

				EventToken: adjustAppConfig.TodyFirstRecharge,
			},
			RevenueParams: &adjustsdk.RevenueParams{
				OrderID:          dot.OrderID,
				Amount:           dot.Amount,
				Currency:         dot.Currency,
				IsManualRecharge: dot.IsManualRecharge,
				RechargeRemark:   dot.RechargeRemark,
			},
		}
		err := doAdjustTask(task)
		if err != nil {
			return err
		}
		// 三个事件按优先级只发一个
		return nil
	}

	{
		task := &adjustsdk.Task{
			TaskName: "Recharge",
			CommonParams: adjustsdk.CommonParams{
				Source: dot.Source,
				UserID: dot.UserID,

				AppToken:      adjustAppConfig.AppToken,
				CreatedAt:     dot.CreatedAt,
				UserIP:        dot.UserIP,
				UserAgent:     dot.UserAgent,
				GoogleADID:    dot.GoogleADID,
				AdjustADID:    dot.AdjustADID,
				AndroidID:     dot.AndroidID,
				AppInstanceID: dot.AppInstanceID,
				Fbp:           dot.Fbp,
				FbExtinfo:     dot.FbExtinfo,
				RoibestLinkId: dot.RoibestLinkId,
				AdjustSDK:     dot.AdjustSDK,

				EventToken: adjustAppConfig.Recharge,
			},
			RevenueParams: &adjustsdk.RevenueParams{
				OrderID:          dot.OrderID,
				Amount:           dot.Amount,
				Currency:         dot.Currency,
				IsManualRecharge: dot.IsManualRecharge,
				RechargeRemark:   dot.RechargeRemark,
			},
		}

		err := doAdjustTask(task)
		if err != nil {
			return err
		}
		// 三个事件按优先级只发一个
		return nil
	}
}

func onDotUserWithdraw(msgData json.RawMessage) error {
	dot := &DotUserWithdraw{}
	err := ijson.API.Unmarshal(msgData, dot)
	if err != nil {
		llog.Get().Error().Err(err).Bytes("msgData", msgData).Msg("onDotUserWithdraw.PARSE_FAIL")
		return err
	}

	llog.Get().Debug().Interface("dot", dot).Msg("onDotUserWithdraw.BEGIN")

	//adjustAppConfig := lsourceconfig.Hold().AdjustGetAppConfig(dot.Source)

	return nil
}

var ErrAdjustHTTPFail = errors.New("ErrAdjustHTTPFail")

func doAdjustTask(task *adjustsdk.Task) error {
	adjustClient.Send(task)

	alertForManualRecharge(task)
	// 旁路处理到 Firebase 的打点
	dotForFirebase(task)
	// 旁路处理到 Facebook 的打点
	dotForFacebook(task)
	// 旁路处理到 Roibest 的打点
	dotForRoibest(task)

	if task.Err != nil {
		llog.Get().Error().Err(task.Err).Int("StatusCode", task.StatusCode).Str("RespBody", task.RespBody).Str("RequestURI", task.RequestURI).Interface("Header", task.Header).Str("Source", task.CommonParams.Source).Int("UserID", task.CommonParams.UserID).Str("TaskName", task.TaskName).Msg("doAdjustTask_NET_FAIL")
		return task.Err
	}

	if task.StatusCode != http.StatusOK {
		llog.Get().Error().Int("StatusCode", task.StatusCode).Str("RespBody", task.RespBody).Str("RequestURI", task.RequestURI).Interface("Header", task.Header).Str("Source", task.CommonParams.Source).Int("UserID", task.CommonParams.UserID).Str("TaskName", task.TaskName).Msg("doAdjustTask_HTTP_FAIL")
		return ErrAdjustHTTPFail
	}

	llog.Get().Info().Str("RespBody", task.RespBody).Str("RequestURI", task.RequestURI).Interface("Header", task.Header).Str("Source", task.CommonParams.Source).Int("UserID", task.CommonParams.UserID).Str("TaskName", task.TaskName).Msg("doAdjustTask_OK")
	return nil
}

/*
https://help.adjust.com/zh/article/s2s-api-reference

400	Bad event state	原因可能有很多。例如，“created at”时间早于“installed_at”时间。

请查看具体的响应以了解更多详情。
400	Invalid app token	应用识别码设置错误
400	Ignoring event, earlier unique event tracked	在控制面板上已设定成唯一事件，重覆触发事件不再作记录。
400	Invalid callback parameters	无法提取回传参数
400	Invalid event token	事件识别码不存在、设置错误或与应用识别码不匹配。
400	Invalid revenue	金额设置错误或值太高（上限 = 100,000,000,000）
401	Failed to authorize request	授权识别码缺失或不匹配
403	App is inactive	应用已在控制面板中设为非活跃状态
403	Tracking disabled	未启用对该平台的跟踪
403	Event token blocklisted	已将事件识别码加入屏蔽名单
404	App token not found	未找到应用识别码
404	Device not found	未找到设备安装数据
413	Request size too large	您的请求超过了我们的 1 MB 上限
451	Device is opted out	设备已退出跟踪
500	Internal error, contact <EMAIL>	内部错误，请联系 <EMAIL> 了解详细信息
*/

func alertForManualRecharge(task *adjustsdk.Task) {
	if task.RevenueParams == nil {
		return
	}
	if !task.RevenueParams.IsManualRecharge {
		return
	}

	// 这种 PWA 渠道不报 Adjust ❌人工充值打点失败
	if task.CommonParams.RoibestLinkId != "" {
		return
	}

	source := task.CommonParams.Source

	// 过滤掉一些包
	if source == "teentest" {
		return
	}

	eventTitle := "✅人工充值打点成功"
	userID := task.CommonParams.UserID
	rechargeRemark := task.RevenueParams.RechargeRemark
	adjustResp := task.RespBody
	rawQuery := getRawQueryFromRequestURI(task.RequestURI)

	if task.Err != nil {
		eventTitle = "❌人工充值打点失败"
		if len(adjustResp) > 0 {
			adjustResp += " " + task.Err.Error()
		} else {
			adjustResp = task.Err.Error()
		}
	} else {
		if task.StatusCode >= 200 && task.StatusCode <= 299 {
			eventTitle = "✅人工充值打点成功"
		} else {
			eventTitle = "❌人工充值打点失败"
		}
	}

	s := fmt.Sprintf("%s %s %d %s\n%s\n%s\n%s", eventTitle, source, userID, rechargeRemark, adjustResp, rawQuery, task.Header)
	alertbotsdk.SendAlert("sourceconf", s)
}

func getRawQueryFromRequestURI(requestURI string) string {
	pos := strings.Index(requestURI, "?")
	if pos == -1 {
		return ""
	}
	return requestURI[pos+1:]
}

func dotForFirebase(task *adjustsdk.Task) {
	// 只需要给充值打点
	if task.RevenueParams == nil {
		return
	}

	sourceInfo := lsourceconfig.Hold().GetSourceInfo(task.CommonParams.Source)
	if sourceInfo == nil {
		return
	}

	gatask := &googleanalyticssdk.Task{}
	gatask.ApiSecret = sourceInfo.FirebaseAPISecret
	gatask.FirebaseAppID = sourceInfo.FirebaseAppID
	gatask.Req.AppInstanceID = task.CommonParams.AppInstanceID

	if gatask.ApiSecret == "" || gatask.FirebaseAppID == "" || gatask.Req.AppInstanceID == "" {
		return
	}

	gatask.Req.UserID = strconv.Itoa(task.CommonParams.UserID)
	e := googleanalyticssdk.Event{}
	e.Name = "purchase"
	e.Params = map[string]any{
		"currency":       task.RevenueParams.Currency,
		"transaction_id": task.RevenueParams.OrderID,
		"value":          task.RevenueParams.Amount,
		"items": []map[string]any{
			{
				"item_name": task.TaskName,
			},
		},
		"engagement_time_msec": "1000",
		"session_id":           strconv.Itoa(int(time.Now().UnixMicro())),
	}
	gatask.Req.Events = append(gatask.Req.Events, e)

	err := gatask.Execute()
	htask := &gatask.HTask

	if err != nil {
		llog.Get().Error().Err(err).Int("Status", htask.Status).Interface("OutData", htask.OutData).Str("URL", htask.URL).Interface("In", htask.In).Str("Source", task.CommonParams.Source).Int("UserID", task.CommonParams.UserID).Str("TaskName", task.TaskName).Msg("dotForFirebase.FAIL")
		return
	}

	llog.Get().Info().Int("Status", htask.Status).Interface("OutData", htask.OutData).Str("URL", htask.URL).Interface("In", htask.In).Str("Source", task.CommonParams.Source).Int("UserID", task.CommonParams.UserID).Str("TaskName", task.TaskName).Msg("dotForFirebase.OK")
}

func dotForFacebook(task *adjustsdk.Task) {
	// 只需要给充值打点
	// 注册也打点
	if task.RevenueParams == nil && task.TaskName != "Register" {
		return
	}

	sourceInfo := lsourceconfig.Hold().GetSourceInfo(task.CommonParams.Source)
	if sourceInfo == nil {
		return
	}

	madid := task.CommonParams.GoogleADID
	anno_id := madid
	extinfo := task.CommonParams.FbExtinfo
	fbc := task.CommonParams.Fbc
	fbp := task.CommonParams.Fbp

	actionSource := "app"
	if fbp != "" {
		actionSource = "website"
	}

	eventName := "Purchase"
	eventId := ""
	customData := (*fbconversionsapi.CustomData)(nil)
	if task.TaskName == "Register" {
		eventName = "CompleteRegistration"
		eventId = strconv.Itoa(task.CommonParams.UserID)
		customData = nil
	} else if task.RevenueParams != nil {
		eventId = task.RevenueParams.OrderID
		customData = &fbconversionsapi.CustomData{
			Currency: task.RevenueParams.Currency,
			Value:    task.RevenueParams.Amount,
		}
	}

	event := fbconversionsapi.Event{
		EventName: eventName,
		EventTime: time.Now().Unix(),
		UserData: fbconversionsapi.UserData{
			ClientIpAddress: task.CommonParams.UserIP,
			ClientUserAgent: task.CommonParams.UserAgent,
			ExternalId:      strconv.Itoa(task.CommonParams.UserID),
			Madid:           madid,
			AnonId:          anno_id,
			Fbc:             fbc,
			Fbp:             fbp,
		},
		CustomData:   customData,
		EventId:      eventId,
		ActionSource: actionSource,
		AppData: fbconversionsapi.AppData{
			AdvertiserTrackingEnabled:  true,
			ApplicationTrackingEnabled: true,
			ExtInfo:                    extinfo,
		},
	}

	gatask := &fbconversionsapi.Task{}
	gatask.FbPixelId = sourceInfo.FbPixelId
	gatask.FbAccessToken = sourceInfo.FbAccessToken
	gatask.Event = event

	if gatask.FbPixelId == "" || gatask.FbAccessToken == "" || (event.UserData.Madid == "" && event.UserData.Fbp == "") {
		return
	}

	err := gatask.Execute()
	htask := &gatask.HTask

	if err != nil {
		llog.Get().Error().Err(err).Int("Status", htask.Status).Interface("OutData", htask.OutData).Str("URL", htask.URL).Interface("In", htask.In).Str("Source", task.CommonParams.Source).Int("UserID", task.CommonParams.UserID).Str("TaskName", task.TaskName).Msg("dotForFacebook-Failed")
	} else {
		llog.Get().Info().Int("Status", htask.Status).Interface("OutData", htask.OutData).Str("URL", htask.URL).Interface("In", htask.In).Str("Source", task.CommonParams.Source).Int("UserID", task.CommonParams.UserID).Str("TaskName", task.TaskName).Msg("dotForFacebook-Succeeded")
	}

	// 人工充值打点
	if task.RevenueParams != nil && task.RevenueParams.IsManualRecharge {
		eventTitle := "✅人工充值打点成功(Facebook)"
		if err != nil {
			eventTitle = "❌人工充值打点失败(Facebook)"
		}
		source := task.CommonParams.Source
		userID := task.CommonParams.UserID
		rechargeRemark := task.RevenueParams.RechargeRemark
		adjustResp := task.RespBody
		rawQuery := ijson.MustMarshalButString(htask.In)
		s := fmt.Sprintf("%s %s %d %s\n%s\n%s\n%s", eventTitle, source, userID, rechargeRemark, adjustResp, rawQuery, "")
		alertbotsdk.SendAlert("sourceconf", s)
	}
}

func dotForRoibest(task *adjustsdk.Task) {
	linkId := task.CommonParams.RoibestLinkId

	if linkId == "" {
		return
	}

	sourceInfo := lsourceconfig.Hold().GetSourceInfo(task.CommonParams.Source)
	if sourceInfo == nil {
		return
	}

	// 充值和注册需要打点
	// https://www.roibest.com/docs/api/event-report.html
	var event *roibestapi.Event
	if task.RevenueParams == nil {
		if task.TaskName == "Register" {
			event = &roibestapi.Event{
				LinkId:    linkId,
				EventName: "CompleteRegistration",
			}
		}
	} else {
		event = &roibestapi.Event{
			LinkId:    linkId,
			EventName: "Purchase",
			Extra: roibestapi.CustomData{
				Currency: task.RevenueParams.Currency,
				Value:    task.RevenueParams.Amount,
			},
		}
	}

	if event == nil {
		return
	}

	gatask := &roibestapi.Task{}
	gatask.Event = *event

	err := gatask.Execute()
	htask := &gatask.HTask

	if err != nil {
		llog.Get().Error().Err(err).Int("Status", htask.Status).Interface("OutData", htask.OutData).Str("URL", htask.URL).Interface("In", htask.In).Str("Source", task.CommonParams.Source).Int("UserID", task.CommonParams.UserID).Str("TaskName", task.TaskName).Msg("dotForRoibest-Failed")
	} else {
		llog.Get().Info().Int("Status", htask.Status).Interface("OutData", htask.OutData).Str("URL", htask.URL).Interface("In", htask.In).Str("Source", task.CommonParams.Source).Int("UserID", task.CommonParams.UserID).Str("TaskName", task.TaskName).Msg("dotForRoibest-Succeeded")
	}

	// 人工充值打点
	if task.RevenueParams != nil && task.RevenueParams.IsManualRecharge {
		eventTitle := "✅人工充值打点成功(Roibest)"
		if err != nil {
			eventTitle = "❌人工充值打点失败(Roibest)"
		}
		source := task.CommonParams.Source
		userID := task.CommonParams.UserID
		rechargeRemark := task.RevenueParams.RechargeRemark
		adjustResp := task.RespBody
		rawQuery := ijson.MustMarshalButString(htask.In)
		s := fmt.Sprintf("%s %s %d %s\n%s\n%s\n%s", eventTitle, source, userID, rechargeRemark, adjustResp, rawQuery, "")
		alertbotsdk.SendAlert("sourceconf", s)
	}
}

// exportToPrometheus 格式化输出到 prometheus 格式
func exportToPrometheus() []byte {
	return nil
}

func healthHandler(w http.ResponseWriter, r *http.Request) {
	w.Write([]byte("OK"))
}

// metricsHandler 输出 prometheus 格式的指标
func metricsHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/plain; version=0.0.4; charset=utf-8")
	w.Write(exportToPrometheus())
}

func versionHandler(w http.ResponseWriter, r *http.Request) {
	w.Write(buildinfo.GetBuildInfoBytes())
}
