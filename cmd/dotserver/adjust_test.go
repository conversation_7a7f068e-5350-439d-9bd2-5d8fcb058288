package main

import (
	"fmt"
	"testing"
	"time"
)

func TestDotUserRegister(t *testing.T) {
	fmt.Println("TestDotUserRegister")
	initLog()
	initAdjustClient()
	adjustClient.UsingSandbox = true
	msg := `{"T":"DotUserRegister","V":{"CreatedAt":"2019-11-27T00:00:00+08:00","Source":"rummyblitz","UserID":"123456","UserIP":"127.0.0.1","GoogleADID":"123456","AdjustADID":"123456","AndroidID":"123456"}}`
	handleMsg(msg)
}

func TestTimeNano(t *testing.T) {
	t.Log("xxx", time.Now().UnixNano())
}
