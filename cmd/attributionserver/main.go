package main

import (
	"bufio"
	"context"
	"database/sql"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"regexp"
	"strconv"
	"strings"
	"syscall"
	"time"
	"yylbe/pkg/base/ijson"
	"yylbe/pkg/base/llog"
	"yylbe/pkg/buildinfo"
	"yylbe/pkg/config/laddrconfig"
	"yylbe/pkg/config/lconfigchanged"
	"yylbe/pkg/config/lsourceconfig"
	"yylbe/pkg/db/attributiondb"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	_ "github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"
	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog"
)

// 简介
// -----------------
// 这个服务是用来接收 Adjust 的回调的，记录用户是从哪个广告系列 (compaign) 来的

const serviceName = "attributionserver"

// 暴露一个 HTTP 端口，用于监控该服务
var exportportFlag = flag.Int("exportport", 3303, "")

var httpServer *http.Server

func initLog() {
	// 设置环境变量来改变整个程序的默认本地时间。
	os.Setenv("TZ", "Asia/Kolkata")
	location, err := time.LoadLocation("Asia/Kolkata")
	if err != nil {
		panic(err)
	}
	// 设置整个进程的默认时区
	time.Local = location

	llog.SetPathFuncByService(serviceName)

	if laddrconfig.Hold().HostConfigV2.IsPro {
		zerolog.SetGlobalLevel(zerolog.InfoLevel)
	} else {
		zerolog.SetGlobalLevel(zerolog.DebugLevel)
	}

	zoneName, zoneOffset := time.Now().Zone()
	llog.Get().Info().Str("zoneName", zoneName).Int("zoneOffset", zoneOffset).Msg("initLog")

	llog.Get().Info().Interface("laddrconfig", laddrconfig.Hold()).Msg("laddrconfig")
}

var rdb *redis.Client

func initRedis() {
	addr := laddrconfig.Hold().RedisConfig.QueueMaster
	llog.Get().Info().Interface("addr", addr).Msg("initRedis")
	rdb = redis.NewClient(&redis.Options{
		Addr:     addr.Addr,
		Username: addr.Username,
		Password: addr.Password,
		DB:       addr.DB,
	})
}

func initWatchConfigChanged() {
	lsourceconfig.Reload(rdb)

	lconfigchanged.ListenConfigChanged(rdb, onConfigChanged)
}

func onConfigChanged(configChangedReasons lconfigchanged.ConfigChangedReasons) {
	lsourceconfig.Reload(rdb)
}

var mydb *sqlx.DB

func initMySQL2() {
	dsn := laddrconfig.Hold().MysqlConfig.MainMySQL
	llog.Get().Info().Str("dsn", dsn).Msg("initMySQL2.BEGIN")

	db, err := sqlx.Connect("mysql", dsn)
	if err != nil {
		panic(err)
	}

	db.SetMaxIdleConns(100)
	db.SetMaxOpenConns(100)
	db.SetConnMaxLifetime(time.Minute)

	mydb = db
}

func migrateMySQL() {
}

func main() {
	initLog()
	initRedis()
	initWatchConfigChanged()
	initMySQL2()
	migrateMySQL()

	go listenSignals()

	go doDataCompensation()

	// 关闭调试日志
	gin.SetMode(gin.ReleaseMode)
	// 创建一个新的路由器
	router := gin.New()
	// 如果你还希望使用恢复中间件，可以手动添加
	router.Use(gin.Recovery())

	router.GET("/health", healthHandler)
	router.GET("/metrics", metricsHandler)
	router.GET("/version", versionHandler)
	router.GET("/attribution", attributionHandler)
	router.GET("/adjmp", adjmpHandler)

	addr := "0.0.0.0:" + strconv.Itoa(*exportportFlag)
	httpServer = &http.Server{Addr: addr, Handler: router}

	httpServer.ListenAndServe()
	llog.Get().Info().Msg("MAIN_EXIT")
}

func listenSignals() {
	c := make(chan os.Signal, 2)
	signal.Notify(c, syscall.SIGTERM, syscall.SIGINT)
	for {
		s := <-c
		switch s {
		case syscall.SIGTERM, syscall.SIGINT:
			llog.Get().Info().Str("s", s.String()).Msg("GOT_SIGNAL")
			if httpServer != nil {
				httpServer.Shutdown(context.TODO())
			}
			return
		}
	}
}

// exportToPrometheus 格式化输出到 prometheus 格式
func exportToPrometheus() []byte {
	return nil
}

func healthHandler(c *gin.Context) {
	c.String(http.StatusOK, "OK\n")
}

// metricsHandler 输出 prometheus 格式的指标
func metricsHandler(c *gin.Context) {
	c.Data(http.StatusOK, "text/plain; version=0.0.4; charset=utf-8", exportToPrometheus())
}

func versionHandler(c *gin.Context) {
	s := fmt.Sprintf("%s\n%d\n%s\n%s", serviceName, *exportportFlag, laddrconfig.Hold().HostConfigV2.NodeName, buildinfo.GetBuildInfoString())
	c.String(http.StatusOK, s)
}

type AttributionParams struct {
	// 前端包名
	// app_name=com.teenpatti.nova
	AppName    string `form:"app_name"`
	AppToken   string `form:"app_token"`
	AppVersion string `form:"app_version"`
	// Adjust SDK version (per app)
	AdjustSDKVersion string `form:"sdk_version"`

	// 归因
	// fb_install_referrer_campaign_group_name=5.24-pic
	// Facebook 广告系列名
	// The campaign_group_id value received from FB in deeplinks
	FacebookDeeplinkCampaignGroupID string `form:"fb_deeplink_campaign_group_id"`
	FacebookCampaignGroupID         string `form:"fb_install_referrer_campaign_group_id"`
	FacebookCampaignGroupName       string `form:"fb_install_referrer_campaign_group_name"`
	FacebookCampaignName            string `form:"fb_install_referrer_campaign_name"`
	GoogleCampaignID                string `form:"google_ads_campaign_id"`
	// Google Ads campaign ID
	// Google Ads campaign type
	GoogleCampaignType string `form:"google_ads_campaign_type"`
	// Google Ads campaign name
	GoogleCampaignName string `form:"google_ads_campaign_name"`
	// tracker_name=Unattributed&tracker=unattr
	// Current tracker name as defined in the Adjust dashboard
	TrackerName string `form:"tracker_name"`
	// Adjust tracker token
	TrackerToken string `form:"tracker"`
	// Network name, as taken from the tracker
	NetworkName string `form:"network_name"`
	// Network type
	NetworkType string `form:"network_type"`
	// Campaign name, as taken from the tracker
	CampaignName string `form:"campaign_name"`
	// 1 if organic traffic, 0 if non-organic
	IsOrganic string `form:"is_organic"`
	// https://help.adjust.com/zh/article/user-referrals?src=search_page
	Label string `form:"label"`

	// 识别用户的各种 ID
	AdjustADID string `form:"adid"`
	// gps_adid=ef432e51-5fab-406c-9d22-66c40b498733
	GoogleADID string `form:"gps_adid"`
	// android_id=3a4b5c6d7e8f9a0b
	AndroidID string `form:"android_id"`

	// Mobile country code: three-digit code identifying a device's country.
	// Use with the mobile network code to identify a device's carrier.
	MCC int16 `form:"mcc"`
	// Mobile network code: two-digit code.
	// Use with the mobile country code to identify a device's carrier.
	MNC int16 `form:"mnc"`
	// 用户地理位置信息
	// ip_address=************
	IPAddress string `form:"ip_address"`
	// country=in
	Country string `form:"country"`
	// country_subdivision=Uttar+Pradesh
	CountrySubdivision string `form:"country_subdivision"`
	// city=Lucknow
	City string `form:"city"`
	// Device internet service provider
	ISP string `form:"isp"`

	// 用户设备信息
	// Device type
	DeviceType string `form:"device_type"`
	// Device manufacturer name
	DeviceManufacturer string `form:"device_manufacturer"`
	// device_name=GalaxyA20s
	DeviceName string `form:"device_name"`
	// ID of the user-agent returned by Device Atlas
	DeviceAtlasID string `form:"device_atlas_id"`
	// CPU type
	CPUType string `form:"cpu_type"`
	// Hardware name
	HardwareName string `form:"hardware_name"`
	// Device model number
	DeviceModel string `form:"device_model"`
	// Device two-character language code
	DeviceLanguage string `form:"language"`
	// Device region code
	DeviceRegion string `form:"region"`
	// Device time zone
	DeviceTimezone string `form:"timezone"`
	// Device operating system
	OSName string `form:"os_name"`
	// Operating system version number
	OSVersion string `form:"os_version"`

	// Incoming raw user agent
	UserAgent string `form:"user_agent"`
	// Adjust SDK 版本信息
	SDKVersion string `form:"sdk_version"`

	// 时间戳
	CreatedAt time.Time `form:"created_at" time_format:"unix"`

	// 事件
	// activity_kind=event
	// This will allow you to determine the data point, such as impression, click, install, rejected install, session, and more
	ActivityKind string `form:"activity_kind"`
	// event_name=register&event=ltb3qn
	EventName  string `form:"event_name"`
	EventToken string `form:"event"`
}

func attributionHandler(c *gin.Context) {
	c.String(http.StatusOK, "")

	aparams := &AttributionParams{}
	err := c.ShouldBindQuery(aparams)
	if err != nil {
		llog.Get().Error().Err(err).Str("RawQuery", c.Request.URL.RawQuery).Msg("attributionHandler.QUERY_PARSE_ERROR")
		return
	}

	handleAParams(c.Request.URL.RawQuery, aparams)
}

type AdjmpParams struct {
	Package string `form:"package"`
}

func adjmpHandler(c *gin.Context) {
	aparams := &AdjmpParams{}
	err := c.ShouldBindQuery(aparams)
	if err != nil {
		llog.Get().Error().Err(err).Str("RawQuery", c.Request.URL.RawQuery).Msg("adjmpHandler-ParamsError")
		return
	}

	llog.Get().Info().Str("RawQuery", c.Request.URL.RawQuery).Interface("aparams", aparams).Msg("adjmpHandler-Succeeded")

	playUrl := "https://play.google.com/store/apps/details?id=" + aparams.Package

	c.Header("Referrer-Policy", "no-referrer")
	c.Redirect(302, playUrl)

	handleAdjmpParams(aparams)

}

func handleAdjmpParams(aparams *AdjmpParams) {

}

func handleRawQuery(RawQuery string) {
	aparams := &AttributionParams{}

	req, _ := http.NewRequest("GET", "http://localhost?"+RawQuery, nil)

	err := binding.Query.Bind(req, aparams)

	if err != nil {
		llog.Get().Error().Err(err).Str("RawQuery", RawQuery).Msg("attributionHandler.QUERY_PARSE_ERROR")
		return
	}

	handleAParams(RawQuery, aparams)
}

func handleAParams(RawQuery string, aparams *AttributionParams) {

	source := lsourceconfig.Hold().AdjustGetSourceByAppToken(aparams.AppToken)

	if source == "" {
		llog.Get().Error().Str("RawQuery", RawQuery).Interface("aparams", aparams).Msg("attributionHandler.SOURCE_NOT_FOUND")
		return
	}

	llog.Get().Info().Str("RawQuery", RawQuery).Interface("aparams", aparams).Str("source", source).Msg("attributionHandler.OK")

	if attributionCanWriteDB(aparams) {
		attributionWriteDB3(aparams, source)
	}
}

type LogEntry struct {
	RawQuery string `json:"RawQuery"`
}

func doDataCompensation() {
	// 一行一行地读取 tmp/fuse.log 文件，按 JSON 格式解析

	file, err := os.Open("tmp/fuse.log")
	if err != nil {
		llog.Get().Error().Err(err).Msg("doDataCompensation.NO_FILE")
		return
	}
	defer file.Close()

	llog.Get().Info().Msg("doDataCompensation.Begin")

	count := 0

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		count++

		line := scanner.Text()
		var entry LogEntry
		err := ijson.API.Unmarshal([]byte(line), &entry)
		if err != nil {
			llog.Get().Error().Err(err).Int("count", count).Msg("doDataCompensation.PARSE_FAIL")
			continue
		}

		if len(entry.RawQuery) > 0 {
			handleRawQuery(entry.RawQuery)
		}
	}

	if err := scanner.Err(); err != nil {
		llog.Get().Error().Err(err).Int("count", count).Msg("doDataCompensation.SCAN_FAIL")
		return
	}

	llog.Get().Info().Int("count", count).Msg("doDataCompensation.OK")
}

func attributionCanWriteDB(aparams *AttributionParams) bool {

	if aparams.ActivityKind == "event" {
		return false
		if aparams.EventName == "s_register" {
			return true
		}
		if aparams.EventName == "s_login" {
			return true
		}
	} else if aparams.ActivityKind == "install" {
		return true
	} else if aparams.ActivityKind == "session" {
		return true
	}

	return false
}

func attributionWriteDB2(aparams *AttributionParams, source string) {
	// 目前要求必须有 GoogleADID
	if aparams.GoogleADID == "" {
		return
	}

	obj := &attributiondb.UserAttribution2{
		GoogleADID: sql.NullString{String: aparams.GoogleADID, Valid: aparams.GoogleADID != ""},
		AdjustADID: sql.NullString{String: aparams.AdjustADID, Valid: aparams.AdjustADID != ""},
		AndroidID:  sql.NullString{String: aparams.AndroidID, Valid: aparams.AndroidID != ""},

		Source: source,
	}

	if aparams.FacebookCampaignGroupName != "" {
		obj.Media = sql.NullString{String: "Facebook", Valid: true}
		obj.Campaign = sql.NullString{String: aparams.FacebookCampaignGroupName, Valid: true}
		obj.CampaignChannel = sql.NullString{String: campaignToCampaignChannel(aparams.FacebookCampaignGroupName), Valid: true}
	} else if aparams.GoogleCampaignName != "" {
		obj.Media = sql.NullString{String: "Google", Valid: true}
		obj.Campaign = sql.NullString{String: aparams.GoogleCampaignName, Valid: true}
		obj.CampaignChannel = sql.NullString{String: campaignToCampaignChannel(aparams.GoogleCampaignName), Valid: true}
	} else {
		obj.Media = sql.NullString{String: "Organic", Valid: true}
		obj.Campaign = sql.NullString{String: "Organic", Valid: true}
		obj.CampaignChannel = sql.NullString{String: "Organic", Valid: true}
	}

	obj.AdjustSDK = sql.NullString{String: aparams.SDKVersion, Valid: aparams.SDKVersion != ""}

	query1 := ""
	if aparams.GoogleADID != "" {
		query1 += " AND (google_adid is NULL OR google_adid = :google_adid)"
	}
	if aparams.AdjustADID != "" {
		query1 += " AND (adjust_adid is NULL OR adjust_adid = :adjust_adid)"
	}
	if aparams.AndroidID != "" {
		query1 += " AND (android_id is NULL OR android_id = :android_id)"
	}
	query1 = "SELECT id, google_adid, adjust_adid, android_id, source, media, campaign, campaign_channel, adjust_sdk FROM t_user_attribution WHERE 1" + query1 + " LIMIT 1;"
	old := &attributiondb.UserAttribution2{}
	{
		// http://jmoiron.github.io/sqlx/
		rows, err := mydb.NamedQuery(query1, obj)
		if err == nil {
			if rows.Next() {
				err = rows.StructScan(old)
			}
			rows.Close()
		}

		if err == sql.ErrNoRows {
			llog.Get().Info().Interface("obj", obj).Interface("old", old).Str("query1", query1).Msg("attributionWriteDB2.SELECT_EMPTY")
		} else if err != nil {
			llog.Get().Error().Err(err).Interface("obj", obj).Str("query1", query1).Msg("attributionWriteDB2.SELECT_FAIL")
		} else {
			llog.Get().Info().Interface("obj", obj).Interface("old", old).Str("query1", query1).Msg("attributionWriteDB2.SELECT_OK")
		}
	}

	// 构建 SQL 查询语句
	query := `
		INSERT INTO t_user_attribution (google_adid, adjust_adid, android_id, source, media, campaign, campaign_channel, adjust_sdk)
		VALUES (:google_adid, :adjust_adid, :android_id, :source, :media, :campaign, :campaign_channel, :adjust_sdk) AS new
		ON DUPLICATE KEY UPDATE
		adjust_sdk = COALESCE(new.adjust_sdk, t_user_attribution.adjust_sdk),
		adjust_adid = COALESCE(t_user_attribution.adjust_adid, new.adjust_adid),
		android_id = COALESCE(t_user_attribution.android_id, new.android_id);
	`

	// 执行带有 ON DUPLICATE KEY UPDATE 的插入操作
	res, err := mydb.NamedExec(query, obj)
	if err != nil {
		llog.Get().Error().Err(err).Interface("obj", obj).Msg("attributionWriteDB2.FAIL")
		return
	}

	lastInsertId, _ := res.LastInsertId()
	rowsAffected, _ := res.RowsAffected()
	llog.Get().Info().Interface("obj", obj).Int64("lastInsertId", lastInsertId).Int64("rowsAffected", rowsAffected).Msg("attributionWriteDB2.OK")
}

func attributionCalcUpdateMedia(obj *attributiondb.UserAttribution2, old *attributiondb.UserAttribution2) bool {

	// 补充广告信息
	// 可以：无效升级为有效，自然量升级为广告量
	// 不可以：广告量降级为自然量
	// 不可以：代理量升级为广告量

	/*
		// 必须在 24 小时之内才能升级
		elapsedSeconds := int(time.Since(old.CreatedAt) / time.Second)
		if elapsedSeconds > 86400 {
			return false
		}
	*/

	// 完全相等就不需要更新了
	if obj.Media == old.Media && obj.CampaignChannel == old.CampaignChannel {
		return false
	}
	if !obj.Media.Valid {
		return false
	}

	// 不可以：代理量升级为广告量
	if old.Media.String == "Promo" {
		return false
	}

	// 允许更新到 FacebookBug
	if obj.Media.String == "FacebookBug" {
		return true
	}

	if !old.Media.Valid || old.Media.String == "NetworkBug" || old.Media.String == "Organic" {
		return true
	}
	if !obj.Media.Valid || obj.Media.String == "NetworkBug" || obj.Media.String == "Organic" {
		return false
	}
	if old.CampaignChannel.String == "NoReferrer" || old.CampaignChannel.String == "" {
		return true
	}
	if obj.CampaignChannel.String == "NoReferrer" || obj.CampaignChannel.String == "" {
		return false
	}
	return false
}

func attributionCalcRealMatchDesc(obj *attributiondb.UserAttribution2, old *attributiondb.UserAttribution2) string {
	desc := ""
	if obj.GoogleADID.String != "" && old.GoogleADID.String != "" {
		if obj.GoogleADID == old.GoogleADID {
			desc += "MatchGpsAdid,"
		} else {
			desc += "DiffGpsAdid,"
		}
	}
	if obj.AdjustADID.String != "" && old.AdjustADID.String != "" {
		if obj.AdjustADID == old.AdjustADID {
			desc += "MatchAdid,"
		} else {
			desc += "DiffAdid,"
		}
	}
	if obj.AndroidID.String != "" && old.AndroidID.String != "" {
		if obj.AndroidID == old.AndroidID {
			desc += "MatchAndroidId,"
		} else {
			desc += "DiffAndroidId,"
		}
	}
	if obj.IPAddress.String != "" && old.IPAddress.String != "" {
		if obj.IPAddress == old.IPAddress {
			desc += "MatchIpAddress,"
		} else {
			desc += "DiffIpAddress,"
		}
	}
	if obj.UserAgent.String != "" && old.UserAgent.String != "" {
		if obj.UserAgent == old.UserAgent {
			desc += "MatchUserAgent,"
		} else {
			desc += "DiffUserAgent,"
		}
	}
	return desc
}

func attributionCalcCanCoverOld(obj *attributiondb.UserAttribution2, old *attributiondb.UserAttribution2) bool {
	if obj.GoogleADID.String != "" && old.GoogleADID.String != "" && old.GoogleADID != obj.GoogleADID {
		return false
	}
	if obj.AdjustADID.String != "" && old.AdjustADID.String != "" && old.AdjustADID != obj.AdjustADID {
		return false
	}
	if obj.AndroidID.String != "" && old.AndroidID.String != "" && old.AndroidID != obj.AndroidID {
		return false
	}
	// IPAddress 变化比较快，变了则覆盖更新，不新建一行
	return true
}

func attributionCalcUpdateMap(obj *attributiondb.UserAttribution2, old *attributiondb.UserAttribution2, canCoverOld bool) (*zerolog.Event, string) {
	m := zerolog.Dict()
	sql := ""

	// 旧的标识如果有效则保留旧的
	if old.GoogleADID.String == "" && obj.GoogleADID.String != "" {
		m.Str("google_adid", obj.GoogleADID.String)
		sql += ", google_adid = :google_adid"
	}
	if old.AdjustADID.String == "" && obj.AdjustADID.String != "" {
		m.Str("adjust_adid", obj.AdjustADID.String)
		sql += ", adjust_adid = :adjust_adid"
	}
	if old.AndroidID.String == "" && obj.AndroidID.String != "" {
		m.Str("android_id", obj.AndroidID.String)
		sql += ", android_id = :android_id"
	}

	// 补充广告信息
	// 可以：无效升级为有效，自然量升级为广告量
	// 不可以：广告量降级为自然量
	// 必须在 24 小时之内才能升级
	if attributionCalcUpdateMedia(obj, old) {
		m.Str("media", obj.Media.String)
		m.Str("campaign", obj.Campaign.String)
		m.Str("campaign_channel", obj.CampaignChannel.String)
		m.Time("attributed_at", obj.AttributedAt.Time)
		sql += ", media = :media, campaign = :campaign, campaign_channel = :campaign_channel, attributed_at = :attributed_at"
	}

	// 补充推广码信息
	if old.UpPromoCode.String == "" && obj.UpPromoCode.String != "" {
		m.Str("up_promo_code", obj.UpPromoCode.String)
		sql += ", up_promo_code = :up_promo_code"
	}

	// 补充 AdjustSDK 版本信息
	if old.AdjustSDK.String == "" && obj.AdjustSDK.String != "" {
		m.Str("adjust_sdk", obj.AdjustSDK.String)
		sql += ", adjust_sdk = :adjust_sdk"
	}

	// 补充 mcc mnc
	if !old.MCC.Valid && obj.MCC.Valid {
		m.Int16("mcc", obj.MCC.Int16)
		m.Int16("mnc", obj.MNC.Int16)
		sql += ", mcc = :mcc, mnc = :mnc"
	}

	// 补充地理位置
	if (!old.Country.Valid && obj.Country.Valid) || (!old.Subdivision.Valid && obj.Subdivision.Valid) || (!old.City.Valid && obj.City.Valid) || (!old.ISP.Valid && obj.ISP.Valid) {
		m.Str("country", obj.Country.String)
		m.Str("subdivision", obj.Subdivision.String)
		m.Str("city", obj.City.String)
		m.Str("isp", obj.ISP.String)
		sql += ", country = :country, subdivision = :subdivision, city = :city, isp = :isp"
	}

	// IP 地址备查
	// 可以覆盖更新
	if (!old.IPAddress.Valid || canCoverOld) && obj.IPAddress.Valid && obj.IPAddress != old.IPAddress {
		m.Str("ip_address", obj.IPAddress.String)
		sql += ", ip_address = :ip_address"
	}

	// 补充浏览器用户代理
	// 可以覆盖更新
	if (old.UserAgent.String == "" || canCoverOld) && obj.UserAgent.String != "" && obj.UserAgent != old.UserAgent {
		m.Str("user_agent", obj.UserAgent.String)
		sql += ", user_agent = :user_agent"
	}

	// 设备信息
	if !old.DeviceType.Valid && obj.DeviceType.Valid {
		m.Str("device_type", obj.DeviceType.String)
		sql += ", device_type = :device_type"
	}
	if !old.DeviceManufacturer.Valid && obj.DeviceManufacturer.Valid {
		m.Str("device_manufacturer", obj.DeviceManufacturer.String)
		sql += ", device_manufacturer = :device_manufacturer"
	}
	if !old.DeviceName.Valid && obj.DeviceName.Valid {
		m.Str("device_name", obj.DeviceName.String)
		sql += ", device_name = :device_name"
	}

	// 操作系统信息
	if !old.OSName.Valid && obj.OSName.Valid {
		m.Str("os_name", obj.OSName.String)
		sql += ", os_name = :os_name"
	}
	if !old.OSVersion.Valid && obj.OSVersion.Valid {
		m.Str("os_version", obj.OSVersion.String)
		sql += ", os_version = :os_version"
	}

	if len(sql) > 0 {
		sql = sql[2:]
	}
	return m, sql
}

// 以 moloco 为前缀，不区分大小写，忽略开头空白
var reNetworkNameMolocoPrefix = regexp.MustCompile(`(?i)^\s*moloco`)

var reNetworkNameDove = regexp.MustCompile(`-dove$`)
var reNetworkNameXender = regexp.MustCompile(`Xender`)

// network_name=TeenPatti+bodhi-web2apk&tracker_name=TeenPatti+bodhi-web2apk%3A%3Aztcl-fb1-tpwebtoapk-0301-aeo-test%28120206747342010670%29%3A%3A0301-aeo-test-t%28120206747342620670%29%3A%3Ayp15%28120206896505910670%29&tracker=1ap61xcn&campaign_name=ztcl-fb1-tpwebtoapk-0301-aeo-test%28120206747342010670%29
var reNetworkNameW2A = regexp.MustCompile(`-web2apk$`)

// 代理归因约定
// network 名字模式 teentest-promo
// label=p-erwfwx-1       p- 前缀表示这个代理分享业务，   -1 后者表示分享到 whatsapp      类似这样
var reNetworkNamePromo = regexp.MustCompile(`-promo$`)

func parsePromoLabel(label string) (up_promo_code string, share_channel int) {
	if strings.HasPrefix(label, "p-") {
		parts := strings.Split(label, "-")
		if len(parts) >= 3 {
			up_promo_code = parts[1]
			share_channel, _ = strconv.Atoi(parts[2])
		}
	}
	return
}

// 后台渠道自定义参数可以配置这个 url
// https://域名/playstore_web/teentest/index.html?token=1cok97da&label=__label__
// https://域名/promo_web/teentest/index.html?t=1cok97da&l=__label__

// 我们有“用户推荐”的功能，支持通过添加label的方式，用户可以邀请好友下载应用并领取奖励；
// 但归因信息不会直接显示在我们的报告后台，需要通过归因回传或者原始数据回传来获取。
// 具体参考这里，可以先看下哈：
// https://help.adjust.com/zh/article/user-referrals?src=search_page

// const share_by_link = 1;
// const share_by_whatsapp = 2;
// const share_by_facebook = 3;
// const share_by_youtube = 4;
// const share_by_sys = 5;
// const share_by_telegram = 6;
func shareChannelToString(share_channel int) string {
	switch share_channel {
	case 1:
		return "link"
	case 2:
		return "whatsapp"
	case 3:
		return "facebook"
	case 4:
		return "youtube"
	case 5:
		return "sys"
	case 6:
		return "telegram"
	}
	return "sharech" + strconv.Itoa(share_channel)
}

func attributionWriteDB3(aparams *AttributionParams, source string) {
	obj := &attributiondb.UserAttribution2{
		GoogleADID: sql.NullString{String: aparams.GoogleADID, Valid: aparams.GoogleADID != ""},
		AdjustADID: sql.NullString{String: aparams.AdjustADID, Valid: aparams.AdjustADID != ""},
		AndroidID:  sql.NullString{String: aparams.AndroidID, Valid: aparams.AndroidID != ""},

		Source: source,
	}

	/*
		当 network_name=Unattributed 时，确定归因到 Facebook，此时 fb_install_referrer_campaign_group_name 可能有，也可能没有
		当 network_name=Organic 时，确定归因到自然量，此时 fb_install_referrer_campaign_group_name 可能有，也可能没有
		当 network_name=Google 时，确定归因到谷歌，此时 fb_install_referrer_campaign_group_name 可能有，也可能没有
		如果只用到 Facebook、Google，没用到其他广告平台，那么 network_name 只有 Unattributed Organic Google 三种取值
	*/

	if aparams.NetworkName == "Unattributed" {
		obj.Media = sql.NullString{String: "Facebook", Valid: true}
		if aparams.FacebookCampaignGroupName != "" {
			obj.Campaign = sql.NullString{String: aparams.FacebookCampaignGroupName, Valid: true}
			obj.CampaignChannel = sql.NullString{String: campaignToCampaignChannel(aparams.FacebookCampaignGroupName), Valid: true}
		} else {
			obj.Campaign = sql.NullString{String: "NoReferrer", Valid: true}
			obj.CampaignChannel = sql.NullString{String: "NoReferrer", Valid: true}
		}
	} else if aparams.NetworkName == "Google Ads ACI" {
		obj.Media = sql.NullString{String: "Google", Valid: true}
		if aparams.GoogleCampaignName != "" {
			obj.Campaign = sql.NullString{String: aparams.GoogleCampaignName, Valid: true}
			obj.CampaignChannel = sql.NullString{String: campaignToCampaignChannel(aparams.GoogleCampaignName), Valid: true}
		} else if aparams.CampaignName != "" {
			obj.Campaign = sql.NullString{String: aparams.CampaignName, Valid: true}
			obj.CampaignChannel = sql.NullString{String: campaignToCampaignChannel(aparams.CampaignName), Valid: true}
		} else {
			obj.Campaign = sql.NullString{String: "NoReferrer", Valid: true}
			obj.CampaignChannel = sql.NullString{String: "NoReferrer", Valid: true}
		}
	} else if reNetworkNameW2A.MatchString(aparams.NetworkName) {
		obj.Media = sql.NullString{String: "W2A", Valid: true}
		if aparams.CampaignName != "" {
			obj.Campaign = sql.NullString{String: aparams.CampaignName, Valid: true}
			obj.CampaignChannel = sql.NullString{String: campaignToCampaignChannel(aparams.CampaignName), Valid: true}
		} else {
			obj.Campaign = sql.NullString{String: "NoReferrer", Valid: true}
			obj.CampaignChannel = sql.NullString{String: "NoReferrer", Valid: true}
		}
	} else if reNetworkNameDove.MatchString(aparams.NetworkName) {
		obj.Media = sql.NullString{String: "Dove", Valid: true}
		if aparams.CampaignName != "" {
			obj.Campaign = sql.NullString{String: aparams.CampaignName, Valid: true}
			obj.CampaignChannel = sql.NullString{String: campaignToCampaignChannel(aparams.CampaignName), Valid: true}
		} else {
			obj.Campaign = sql.NullString{String: "NoReferrer", Valid: true}
			obj.CampaignChannel = sql.NullString{String: "NoReferrer", Valid: true}
		}
	} else if reNetworkNameXender.MatchString(aparams.NetworkName) {
		obj.Media = sql.NullString{String: "Xender", Valid: true}
		if aparams.CampaignName != "" {
			obj.Campaign = sql.NullString{String: aparams.CampaignName, Valid: true}
			obj.CampaignChannel = sql.NullString{String: campaignToCampaignChannel(aparams.CampaignName), Valid: true}
		} else {
			obj.Campaign = sql.NullString{String: "NoReferrer", Valid: true}
			obj.CampaignChannel = sql.NullString{String: "NoReferrer", Valid: true}
		}
	} else if reNetworkNamePromo.MatchString(aparams.NetworkName) {
		obj.Media = sql.NullString{String: "Promo", Valid: true}
		up_promo_code, share_channel := parsePromoLabel(aparams.Label)
		share_channel_str := shareChannelToString(share_channel)
		if up_promo_code != "" {
			obj.Campaign = sql.NullString{String: aparams.Label, Valid: true}
			obj.CampaignChannel = sql.NullString{String: share_channel_str, Valid: true}
			obj.UpPromoCode = sql.NullString{String: up_promo_code, Valid: true}
		} else {
			obj.Campaign = sql.NullString{String: "NoReferrer", Valid: true}
			obj.CampaignChannel = sql.NullString{String: "NoReferrer", Valid: true}
		}
	} else if reNetworkNameMolocoPrefix.MatchString(aparams.NetworkName) {
		// USER_AD_TYPE_MOLOCO
		// network_name=moloco-11-0707
		// campaign_name=Moloco_TeenPattiDuel-3Patti+Rummy_Android_IND_ROAS
		obj.Media = sql.NullString{String: "Moloco", Valid: true}
		obj.Campaign = sql.NullString{String: "Moloco", Valid: true}
		obj.CampaignChannel = sql.NullString{String: "Moloco", Valid: true}
	} else if aparams.NetworkName == "sharechat" {
		// network_name=sharechat&tracker_name=sharechat%3A%3ASC_TeenPattiBodhi_Video_28Oct23%3A%3ASharechat%3A%3ASC_Video_TPB_HIN_021_28OCT23&tracker=156g6q3v&campaign_name=SC_TeenPattiBodhi_Video_28Oct23
		obj.Media = sql.NullString{String: "ShareChat", Valid: true}
		obj.Campaign = sql.NullString{String: aparams.CampaignName, Valid: true}
		obj.CampaignChannel = sql.NullString{String: "ShareChat", Valid: true}
	} else if aparams.NetworkName == "Organic" || aparams.NetworkName == "Google Organic Search" {
		if aparams.FacebookCampaignGroupName != "" {
			obj.Media = sql.NullString{String: "FacebookBug", Valid: true}
			obj.Campaign = sql.NullString{String: aparams.FacebookCampaignGroupName, Valid: true}
			obj.CampaignChannel = sql.NullString{String: campaignToCampaignChannel(aparams.FacebookCampaignGroupName), Valid: true}
		} else {
			obj.Media = sql.NullString{String: "Organic", Valid: true}
			obj.Campaign = sql.NullString{String: "Organic", Valid: true}
			obj.CampaignChannel = sql.NullString{String: "Organic", Valid: true}
		}
	} else {
		obj.Media = sql.NullString{String: "NetworkBug", Valid: true}
		obj.Campaign = sql.NullString{String: "NetworkBug", Valid: true}
		obj.CampaignChannel = sql.NullString{String: "NetworkBug", Valid: true}
	}

	obj.AttributedAt = sql.NullTime{Time: time.Now(), Valid: true}

	obj.AdjustSDK = sql.NullString{String: aparams.SDKVersion, Valid: aparams.SDKVersion != ""}

	obj.MCC = sql.NullInt16{Int16: aparams.MCC, Valid: aparams.MCC != 0}
	obj.MNC = sql.NullInt16{Int16: aparams.MNC, Valid: aparams.MNC != 0}
	obj.Country = sql.NullString{String: aparams.Country, Valid: aparams.Country != ""}
	obj.Subdivision = sql.NullString{String: aparams.CountrySubdivision, Valid: aparams.CountrySubdivision != ""}
	obj.City = sql.NullString{String: aparams.City, Valid: aparams.City != ""}
	obj.ISP = sql.NullString{String: aparams.ISP, Valid: aparams.ISP != ""}
	obj.IPAddress = sql.NullString{String: aparams.IPAddress, Valid: aparams.IPAddress != ""}

	obj.UserAgent = sql.NullString{String: aparams.UserAgent, Valid: aparams.UserAgent != ""}
	// 不知道为啥会出现这种，毫无意义呀
	if strings.HasPrefix(obj.UserAgent.String, "Go-http-client") {
		obj.UserAgent = sql.NullString{String: "", Valid: false}
	}

	obj.DeviceType = sql.NullString{String: aparams.DeviceType, Valid: aparams.DeviceType != ""}
	obj.DeviceManufacturer = sql.NullString{String: aparams.DeviceManufacturer, Valid: aparams.DeviceManufacturer != ""}
	obj.DeviceName = sql.NullString{String: aparams.DeviceName, Valid: aparams.DeviceName != ""}
	obj.OSName = sql.NullString{String: aparams.OSName, Valid: aparams.OSName != ""}
	obj.OSVersion = sql.NullString{String: aparams.OSVersion, Valid: aparams.OSVersion != ""}

	query1 := ""
	query1Desc := ""
	if aparams.GoogleADID != "" {
		query1 += " OR google_adid = :google_adid"
		query1Desc += "google_adid,"
	}
	if aparams.AdjustADID != "" {
		query1 += " OR adjust_adid = :adjust_adid"
		query1Desc += "adjust_adid,"
	}
	if aparams.AndroidID != "" {
		query1 += " OR android_id = :android_id"
		query1Desc += "android_id,"
	}
	if aparams.IPAddress != "" {
		// 有 user_agent 了，唯一性比较强，不需要时间限制了  AND a.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
		// 有时间限制的话，不方便测试服测试，因为这个 ip 这个手机一直用的
		query1 += " OR (ip_address = :ip_address AND user_agent = :user_agent)"
		//query1 += " OR (user_agent = :user_agent AND created_at >= DATE_SUB(NOW(), INTERVAL 3 MINUTE) AND created_at <= DATE_ADD(NOW(), INTERVAL 3 MINUTE))"
		query1Desc += "ip_address,"
	}

	if query1 == "" {
		llog.Get().Error().Str("ActivityKind", aparams.ActivityKind).Interface("obj", obj).Msg("attributionWriteDB3-SelectOld-CondNotFound")
		return
	}

	orderBy := ""
	if aparams.IPAddress != "" {
		// 发现这样就不会全表扫描了
		orderBy = " ORDER BY user_agent, created_at DESC "
	}

	query1 = "SELECT id, uid, google_adid, adjust_adid, android_id, source, media, campaign, campaign_channel, up_promo_code, adjust_sdk, mcc, mnc, country, subdivision, city, isp, ip_address, user_agent, device_type, device_manufacturer, device_name, os_name, os_version, attributed_at, created_at FROM t_user_attribution WHERE source = :source AND (0 " + query1 + ")" + orderBy + " LIMIT 11;"

	canCoverOld := false
	numRows := 0

	// http://jmoiron.github.io/sqlx/
	rows, err := mydb.NamedQuery(query1, obj)
	if err == nil {
		for rows.Next() {
			old := &attributiondb.UserAttribution2{}
			err := rows.StructScan(old)

			if err != nil {
				llog.Get().Error().Err(err).Str("ActivityKind", aparams.ActivityKind).Interface("obj", obj).Str("query1Desc", query1Desc).Msg("attributionWriteDB3-SelectOld-StructScanFailed")
				break
			}

			matchDesc := attributionCalcRealMatchDesc(obj, old)
			thisCanCoverOld := attributionCalcCanCoverOld(obj, old)
			canCoverOld = canCoverOld || thisCanCoverOld
			numRows += 1

			upmap, upsql := attributionCalcUpdateMap(obj, old, canCoverOld)

			if len(upsql) == 0 {
				llog.Get().Info().Str("ActivityKind", aparams.ActivityKind).Bool("thisCanCoverOld", thisCanCoverOld).Str("matchDesc", matchDesc).Interface("obj", obj).Interface("old", old).Msg("attributionWriteDB3-SelectOld-NotNeedUpdate")
			} else {
				// 更新通过旧的 id 主键匹配
				obj.ID = old.ID

				// 这种情况需要更新
				query2 := "UPDATE t_user_attribution SET " + upsql + " WHERE id = :id;"
				res, err := mydb.NamedExec(query2, obj)

				if err != nil {
					llog.Get().Error().Err(err).Str("ActivityKind", aparams.ActivityKind).Bool("thisCanCoverOld", thisCanCoverOld).Str("matchDesc", matchDesc).Dict("upmap", upmap).Interface("obj", obj).Interface("old", old).Msg("attributionWriteDB3-SelectOld-UpdateFailed")
				} else {
					lastInsertId, _ := res.LastInsertId()
					rowsAffected, _ := res.RowsAffected()
					llog.Get().Info().Str("ActivityKind", aparams.ActivityKind).Bool("thisCanCoverOld", thisCanCoverOld).Str("matchDesc", matchDesc).Dict("upmap", upmap).Interface("obj", obj).Interface("old", old).Int64("lastInsertId", lastInsertId).Int64("rowsAffected", rowsAffected).Msg("attributionWriteDB3-SelectOld-UpdateOK")
				}
			}

		}
		rows.Close()
	}

	if err == sql.ErrNoRows || (err == nil && !canCoverOld) {
		// 这种情况简单插入新行即可
		query2 := "INSERT INTO t_user_attribution (google_adid, adjust_adid, android_id, source, media, campaign, campaign_channel, up_promo_code, adjust_sdk, mcc, mnc, country, subdivision, city, isp, ip_address, user_agent, device_type, device_manufacturer, device_name, os_name, os_version, attributed_at) VALUES (:google_adid, :adjust_adid, :android_id, :source, :media, :campaign, :campaign_channel, :up_promo_code, :adjust_sdk, :mcc, :mnc, :country, :subdivision, :city, :isp, :ip_address, :user_agent, :device_type, :device_manufacturer, :device_name, :os_name, :os_version, CURRENT_TIMESTAMP(3));"

		res, err := mydb.NamedExec(query2, obj)

		if err != nil {
			llog.Get().Info().Str("ActivityKind", aparams.ActivityKind).Str("EventName", aparams.EventName).Int("numRows", numRows).Bool("canCoverOld", canCoverOld).Interface("obj", obj).Str("query1Desc", query1Desc).Msg("attributionWriteDB3-SelectOld-InsertNewFailed")
		} else {
			lastInsertId, _ := res.LastInsertId()
			rowsAffected, _ := res.RowsAffected()
			llog.Get().Info().Str("ActivityKind", aparams.ActivityKind).Str("EventName", aparams.EventName).Int("numRows", numRows).Bool("canCoverOld", canCoverOld).Interface("obj", obj).Str("query1Desc", query1Desc).Int64("lastInsertId", lastInsertId).Int64("rowsAffected", rowsAffected).Msg("attributionWriteDB3-SelectOld-InsertNewOK")
		}
	} else if err != nil {
		llog.Get().Error().Err(err).Str("ActivityKind", aparams.ActivityKind).Str("EventName", aparams.EventName).Interface("obj", obj).Str("query1Desc", query1Desc).Msg("attributionWriteDB3-SelectOld-Failed")
	}

}

// 用正则提取开头的单词部分，包括汉字，不包括下划线，开头如果有非单词部分则跳过
var reCampaignChannel = regexp.MustCompile(`[\p{L}\p{N}]+`)

// 渠道名_投手名_系列创建时间(月.日)_广告类型(MAI/AEO/VO)_自定义内容
// 例如zt_sydney_5.29_AEO_pic
func campaignToCampaignChannel(campaign string) string {
	campaignChannel := reCampaignChannel.FindString(campaign)
	return campaignChannel
}
