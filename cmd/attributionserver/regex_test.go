package main

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestReCampaignChannel(t *testing.T) {
	assert.Equal(t, campaignToCampaignChannel("zt_sydney_5.29_AEO_pic"), "zt")
	assert.Equal(t, campaignToCampaignChannel("SHESING- 印度-联运8号-fb._IN_AND_AEO_Pelle_0609_2"), "SHESING")
	assert.Equal(t, campaignToCampaignChannel(" SHESING- 印度-联运8号-fb._IN_AND_AEO_Pelle_0609_2"), "SHESING")
	assert.Equal(t, campaignToCampaignChannel("印度-联运8号-fb._IN_AND_AEO_Pelle_0609_2"), "印度")
}
