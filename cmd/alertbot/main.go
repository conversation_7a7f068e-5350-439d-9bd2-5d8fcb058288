package main

import (
	"context"
	"errors"
	"flag"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"
	"yylbe/pkg/base/ijson"
	"yylbe/pkg/base/lhttp"
	"yylbe/pkg/base/llog"
	"yylbe/pkg/buildinfo"
	"yylbe/pkg/config/laddrconfig"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"
)

// 简介
// -----------------
// 这个服务是用来接收 Adjust 的回调的，记录用户是从哪个广告系列 (compaign) 来的

const serviceName = "alertbot"

// 暴露一个 HTTP 端口，用于监控该服务
var exportportFlag = flag.Int("exportport", 3305, "")

var httpServer *http.Server

func initLog() {
	llog.SetPathFuncByService(serviceName)

	if laddrconfig.Hold().HostConfigV2.IsPro {
		zerolog.SetGlobalLevel(zerolog.InfoLevel)
	} else {
		zerolog.SetGlobalLevel(zerolog.DebugLevel)
	}
}

func main() {
	initLog()

	go listenSignals()

	gin.SetMode(gin.ReleaseMode)
	router := gin.Default()
	router.GET("/health", healthHandler)
	router.GET("/metrics", metricsHandler)
	router.GET("/version", versionHandler)
	router.GET("/send", sendHandler)
	router.POST("/send", sendHandlerPost)
	router.POST("/sns", snsHandler)
	router.POST("/alertmanager", alertmanagerHandler)
	//router.GET("/", alertmanagerHandler)
	//router.POST("/", alertmanagerHandler)

	h, err := TgWebHook()
	if err != nil {
		llog.Get().Error().Err(err).Msg("TgWebHook Init Error")
	} else {
		router.Any("/tg/webhook", gin.WrapH(h))
	}

	addr := "0.0.0.0:" + strconv.Itoa(*exportportFlag)
	httpServer = &http.Server{Addr: addr, Handler: router}

	httpServer.ListenAndServe()
	llog.Get().Info().Msg("MAIN_EXIT")
}

func listenSignals() {
	c := make(chan os.Signal, 2)
	signal.Notify(c, syscall.SIGTERM, syscall.SIGINT)
	for {
		s := <-c
		switch s {
		case syscall.SIGTERM, syscall.SIGINT:
			llog.Get().Info().Str("s", s.String()).Msg("GOT_SIGNAL")
			if httpServer != nil {
				httpServer.Shutdown(context.TODO())
			}
			return
		}
	}
}

// exportToPrometheus 格式化输出到 prometheus 格式
func exportToPrometheus() []byte {
	return nil
}

func healthHandler(c *gin.Context) {
	c.String(http.StatusOK, "OK\n")
}

// metricsHandler 输出 prometheus 格式的指标
func metricsHandler(c *gin.Context) {
	c.Data(http.StatusOK, "text/plain; version=0.0.4; charset=utf-8", exportToPrometheus())
}

func versionHandler(c *gin.Context) {
	s := fmt.Sprintf("%s\n%d\n%s\n%s", serviceName, *exportportFlag, laddrconfig.Hold().HostConfigV2.NodeName, buildinfo.GetBuildInfoString())
	c.String(http.StatusOK, s)
}

type SendParams struct {
	Level string `form:"level"`
	Text  string `form:"text"`
}

var telegramSamplerMap = map[string]*zerolog.BurstSampler{}

// telegramSamplerMap 的锁
var telegramSamplerMapLock sync.Mutex

func telegramAllowSend(level string) bool {
	return true

	telegramSamplerMapLock.Lock()
	defer telegramSamplerMapLock.Unlock()
	sampler, ok := telegramSamplerMap[level]
	if !ok {
		sampler = &zerolog.BurstSampler{
			Burst:  10,
			Period: time.Minute,
		}
		telegramSamplerMap[level] = sampler
	}
	return sampler.Sample(zerolog.ErrorLevel)
}

var ErrTelegramBanned = errors.New("ErrTelegramBanned")

// https://telegram-bot-sdk.readme.io/reference/sendmessage
// Telegram 机器人的 API 确实有一些限制和约束，主要是为了防止滥用和确保服务的稳定性。
// 对于群组，机器人每秒最多可以发送 30 条消息。
// 消息最长 4096 字节
// 如果机器人在短时间内发送的消息被大量用户标记为垃圾信息，它可能会受到暂时的禁止。
func sendToTelegram(level string, text string) {
	return

	level = "debug"

	telegramChatID := laddrconfig.Hold().TelegramBotConfig.GetTelegramChatIdByLevel(level)
	if telegramChatID == "" {
		llog.Get().Error().Str("level", level).Str("text", text).Msg("sendToTelegram-ChatIdNotFound")
		return
	}

	normalizedText := text
	if len(normalizedText) > 4096 {
		normalizedText = normalizedText[:4096]
	}

	// 发送 POST form 请求
	// "chat_id=-1001943783556&text=my sample text"
	form := url.Values{}
	// disable_notification 不管用
	form.Add("chat_id", telegramChatID)
	form.Add("text", normalizedText)
	// Markdown 需要注意语法，不好用
	// Bad Request: can't parse entities: Can't find end of the entity
	//form.Add("parse_mode", "Markdown")
	// 为了报警信息里面带链接
	// 但有些错误文本里面有 xml 标签，此时就不能用 HTML 格式了
	// 上架下架的消息里面只有 a 标签
	//if strings.Contains(normalizedText, "</") {
	if strings.HasPrefix(normalizedText, "<pre>") || strings.Contains(normalizedText, "</a>") {
		form.Add("parse_mode", "HTML")
	}
	form.Add("disable_web_page_preview", "true")

	task := lhttp.Task{}
	task.Timeout = time.Second * 60
	task.URL = laddrconfig.Hold().TelegramBotConfig.TelegramBotUrl + "/sendMessage"
	task.Method = "POST"
	task.InHeader = http.Header{
		"Content-Type": []string{"application/x-www-form-urlencoded"},
	}
	task.In = form.Encode()

	err := ErrTelegramBanned
	if telegramAllowSend(level) {
		backoff := 60
		for i := 0; i < 3; i++ {
			err = task.Do(context.TODO())
			if err == nil {
				break
			}

			time.Sleep(time.Second * time.Duration(backoff))
			backoff *= 2
			if backoff > 60 {
				backoff = 60
			}
		}
	}

	if err != nil {
		llog.Get().Error().Err(err).Interface("task", task).Msg("sendToTelegram-Failed")
	} else {
		llog.Get().Info().Interface("task", task).Msg("sendToTelegram-Succeeded")
	}
}

type WorkItem struct {
	Level string
	Text  string
}

type Worker struct {
	WorkerName string
	WorkItems  []WorkItem
	Overflow   int
	Mutex      sync.Mutex
	Cond       *sync.Cond
}

var workerMap = map[string]*Worker{}
var workerMapLock sync.Mutex

func addWorkItem(level string, text string) {
	// 这里要负责拆解太长的文本，拆解边界要选在换行，最大块不要超过 4096 字节

	const maxChunkSize = 4000

	if len(text) < maxChunkSize {
		internalAddWorkItem(level, text, false)
		return
	}

	hasPre := strings.HasPrefix(text, "<pre>")
	lines := strings.Split(text, "\n")
	currentChunk := ""

	for _, line := range lines {
		if len(currentChunk)+len(line)+1 > maxChunkSize {
			internalAddWorkItem(level, currentChunk, hasPre)
			currentChunk = ""
		}
		if currentChunk != "" {
			currentChunk += "\n"
		}
		currentChunk += line
	}

	if currentChunk != "" {
		internalAddWorkItem(level, currentChunk, hasPre)
	}
}

func wrapPre(text string) string {
	if !strings.HasPrefix(text, "<pre>") {
		text = "<pre>" + text
	}
	if !strings.HasSuffix(text, "</pre>") {
		text = text + "</pre>"
	}
	return text
}

func internalAddWorkItem(level string, text string, hasPre bool) {
	if hasPre {
		text = wrapPre(text)
	}

	workerName := laddrconfig.Hold().TelegramBotConfig.GetWorkerNameByLevel(level)

	llog.Get().Info().Str("workerName", workerName).Str("level", level).Str("text", text).Msg("addWorkItem")

	workerMapLock.Lock()
	worker, ok := workerMap[workerName]
	if !ok {
		worker = &Worker{
			WorkerName: workerName,
		}
		worker.Cond = sync.NewCond(&worker.Mutex)
		workerMap[workerName] = worker
		go workerMain(worker)
	}
	workerMapLock.Unlock()

	worker.Mutex.Lock()
	if len(worker.WorkItems) < 1000 {
		worker.WorkItems = append(worker.WorkItems, WorkItem{
			Level: level,
			Text:  text,
		})
		worker.Cond.Signal() // 有新工作项可用
	} else {
		worker.Overflow++
	}
	worker.Mutex.Unlock()
}

func processWorkerItems(workerName string, workerItems []WorkItem, overflow int) {
	i := 0
	for i < len(workerItems) {
		textLs := []string{}
		totalLen := 0

		// 合并一些后面的，减少对飞机的调用次数
		for {
			s := workerItems[i].Text

			// 合并之前判断
			if len(textLs) > 0 {
				if totalLen+len(s)+1 >= 4096 {
					break
				}
				if strings.HasPrefix(s, "<pre>") {
					break
				}
			}

			//s += fmt.Sprintf(" || %s", laddrconfig.Hold().HostConfigV2.EnvName)
			textLs = append(textLs, s)
			totalLen += len(s) + 1
			i++

			// 合并之后判断
			if i >= len(workerItems) || totalLen >= 4096 || strings.HasPrefix(s, "<pre>") {
				break
			}
		}

		if overflow > 0 {
			textLs = append(textLs, fmt.Sprintf("🚨 ... %d more ...", overflow))
		}

		text := strings.Join(textLs, "\n")

		sendToTelegram(workerName, text)
	}
}

/*
Telegram 限制包括：
When sending messages inside a particular chat, avoid sending more than one message per second.
Also note that your bot will not be able to send more than 20 messages per minute to the same group.
*/
func workerMain(worker *Worker) {
	for {
		worker.Mutex.Lock()
		for len(worker.WorkItems) == 0 {
			worker.Cond.Wait() // 等待直到有工作项可用
		}
		worker.Mutex.Unlock()

		// 延迟一小会再处理，可能收集更多条一起发送
		time.Sleep(time.Second * 5)

		worker.Mutex.Lock()
		for len(worker.WorkItems) == 0 {
			worker.Cond.Wait() // 等待直到有工作项可用
		}
		// 取出工作项列表
		workItems := worker.WorkItems
		worker.WorkItems = nil
		overflow := worker.Overflow
		worker.Overflow = 0
		worker.Mutex.Unlock()

		processWorkerItems(worker.WorkerName, workItems, overflow)
	}
}

func sendHandler(c *gin.Context) {
	params := &SendParams{}
	err := c.ShouldBindQuery(params)
	if err != nil {
		llog.Get().Error().Err(err).Str("RawQuery", c.Request.URL.RawQuery).Msg("sendHandler-ParseParamsFailed")
		return
	}

	addWorkItem(params.Level, params.Text)

	c.String(http.StatusOK, "")
}

func sendHandlerPost(c *gin.Context) {

	data, err := c.GetRawData()

	if err != nil {
		llog.Get().Error().Err(err).Str("RequestURI", c.Request.RequestURI).Msg("sendHandlerPost-GetRawData-Fail")
		return
	}

	var params SendParams
	err = ijson.API.Unmarshal(data, &params)
	if err != nil {
		llog.Get().Error().Err(err).Str("RequestURI", c.Request.RequestURI).Bytes("data", data).Msg("sendHandlerPost-Unmarshal-Fail")
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	addWorkItem(params.Level, params.Text)

	c.String(http.StatusOK, "")
}

// SNSMessage 代表来自 SNS 的消息结构
type SNSMessage struct {
	Type             string `json:"Type"`
	MessageId        string `json:"MessageId"`
	Token            string `json:"Token"`
	TopicArn         string `json:"TopicArn"`
	Subject          string `json:"Subject"`
	Message          string `json:"Message"`
	SubscribeURL     string `json:"SubscribeURL"`
	Timestamp        string `json:"Timestamp"`
	SignatureVersion string `json:"SignatureVersion"`
	Signature        string `json:"Signature"`
	SigningCertURL   string `json:"SigningCertURL"`
	UnsubscribeURL   string `json:"UnsubscribeURL"`
	// 根据需要可以添加更多字段
}

var reSnsBanned = regexp.MustCompile(`Backing up DB instance|Creating automated snapshot|Automated snapshot created|Finished DB Instance backup|ElastiCache:SnapshotComplete`)

func snsHandler(c *gin.Context) {

	data, err := c.GetRawData()

	llog.Get().Info().Str("RequestURI", c.Request.RequestURI).Bytes("data", data).Err(err).Msg("snsHandler")

	if err != nil {
		llog.Get().Error().Err(err).Str("RequestURI", c.Request.RequestURI).Msg("snsHandler-GetRawData-Fail")
		return
	}

	var msg SNSMessage
	err = ijson.API.Unmarshal(data, &msg)
	if err != nil {
		llog.Get().Error().Err(err).Str("RequestURI", c.Request.RequestURI).Bytes("data", data).Msg("snsHandler-Unmarshal-Fail")
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 处理 SNS 订阅确认请求
	if msg.Type == "SubscriptionConfirmation" {
		confirmSubscription(msg.SubscribeURL)
	} else if msg.Type == "Notification" {
		if !reSnsBanned.MatchString(msg.Message) {
			text := msg.Subject + "\n" + msg.Message
			hostname, _ := os.Hostname()
			text = text + "\n-- 💻 " + hostname
			addWorkItem("sns", text)
		}
	}

	// 响应 SNS 确认接收消息
	c.JSON(http.StatusOK, gin.H{"status": "ok"})
}

// confirmSubscription 访问 SNS 提供的确认 URL 来确认订阅
func confirmSubscription(subscribeURL string) {
	resp, err := http.Get(subscribeURL)
	if err != nil {
		llog.Get().Error().Err(err).Msg("snsHandler-confirmSubscription-Failed")
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		llog.Get().Error().Int("StatusCode", resp.StatusCode).Msg("snsHandler-confirmSubscription-Succeeded")
	} else {
		llog.Get().Error().Int("StatusCode", resp.StatusCode).Msg("snsHandler-confirmSubscription-Failed")
	}
}

type AlertGroup struct {
	Receiver          string            `json:"receiver"`
	Status            string            `json:"status"`
	Alerts            []Alert           `json:"alerts"`
	GroupLabels       map[string]string `json:"groupLabels"`
	CommonLabels      map[string]string `json:"commonLabels"`
	CommonAnnotations map[string]string `json:"commonAnnotations"`
	ExternalURL       string            `json:"externalURL"`
}

type Alert struct {
	Status       string            `json:"status"`
	Labels       map[string]string `json:"labels"`
	Annotations  map[string]string `json:"annotations"`
	StartsAt     string            `json:"startsAt"`
	EndsAt       string            `json:"endsAt"`
	GeneratorURL string            `json:"generatorURL"`
	Fingerprint  string            `json:"fingerprint"`
}

func alertmanagerHandler(c *gin.Context) {

	data, err := c.GetRawData()

	if err != nil {
		llog.Get().Error().Err(err).Str("RequestURI", c.Request.RequestURI).Msg("alertmanagerHandler-GetRawData-Fail")
		return
	}

	alertGroup := &AlertGroup{}
	err = ijson.API.Unmarshal(data, alertGroup)
	if err != nil {
		llog.Get().Error().Err(err).Str("RequestURI", c.Request.RequestURI).Msg("alertmanagerHandler-Unmarshal-Fail")
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	items := []string{}

	// 报警条目按状态稳定排序，报警在前，解决在后
	sort.SliceStable(alertGroup.Alerts, func(i, j int) bool {
		return alertGroup.Alerts[i].Status < alertGroup.Alerts[j].Status
	})

	level := alertGroup.CommonLabels["team"]

	for _, alert := range alertGroup.Alerts {
		status := alert.Status // "firing" "resolved"
		alertname := alert.Labels["alertname"]
		description := alert.Annotations["description"]

		if !shouldAlertByLevelAlertname(level, alertname) {
			continue
		}

		if description == "" {
			description = alertname
		}
		icon := "🚨"
		if status == "resolved" {
			icon = "✅"
		}
		item := fmt.Sprintf("%s %s", icon, description)
		items = append(items, item)
	}

	text := ""
	if len(items) > 0 {
		text = strings.Join(items, "\n")

		hostname, _ := os.Hostname()
		text = text + "\n-- 💻 " + hostname

		addWorkItem(level, text)
	}

	llog.Get().Info().Str("RequestURI", c.Request.RequestURI).Interface("alertGroup", alertGroup).Str("s", text).Str("Receiver", alertGroup.Receiver).Msg("alertmanagerHandler-Succeeded")

	c.String(http.StatusOK, "")
}

func shouldAlertByLevelAlertname(level string, alertname string) bool {
	// 测试服暂时屏蔽报警，以后再研究如何方便判断是否报警
	if !laddrconfig.Hold().HostConfigV2.IsPro {
		return false
	}

	return true
}

var tokenRegex = regexp.MustCompile("^.*api.telegram.org/bot(.*)$")

func TgWebHook() (http.HandlerFunc, error) {
	var url, token string
	cnf := laddrconfig.Hold()

	url = cnf.TelegramBotConfig.TelegramWebhookUrl
	if url == "" {
		return nil, errors.New("TelegramWebhookUrl is empty")
	}
	url += "/tg/webhook"

	//从那个配置的链接里面拿出来token
	matches := tokenRegex.FindStringSubmatch(cnf.TelegramBotConfig.TelegramBotUrl)
	if len(matches) > 1 {
		token = matches[1]
	}
	return initTelegramWebHook(context.Background(), url, token)
}
