package main

import (
	"context"
	"fmt"
	"net/http"
	"os/exec"
	"strconv"
	"strings"
	"yylbe/pkg/base/llog"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

/*
	增加一个处理群消息回调的逻辑
	比如在群里发 /pay 就立马进行一次数据播报
			 发 /syncAdjust 就立马进行 adjust后台设置回调
*/

func initTelegramWebHook(ctx context.Context, url, apiToken string) (http.HandlerFunc, error) {
	opts := []bot.Option{}
	b, err := bot.New(apiToken, opts...)
	if nil != err {
		// panics for the sake of simplicity.
		// you should handle this error properly in your code.
		return nil, err
	}

	b.SetWebhook(ctx, &bot.SetWebhookParams{
		URL: url,
	})

	for _, cmd := range allCmd {
		f := cmd.work
		b.RegisterHandler(bot.HandlerTypeMessageText, cmd.cmd, bot.MatchTypeExact, func(ctx context.Context, b *bot.Bot, update *models.Update) {
			msg, err := f(update)
			if err != nil {
				b.SendMessage(ctx, &bot.SendMessageParams{
					ChatID: update.Message.Chat.ID,
					Text:   fmt.Sprintf("执行失败:%v", err),
				})
			} else {
				b.SendMessage(ctx, &bot.SendMessageParams{
					ChatID: update.Message.Chat.ID,
					Text:   msg,
				})
			}
		})
	}

	b.RegisterHandler(bot.HandlerTypeMessageText, "/help", bot.MatchTypeExact, help)

	llog.Get().Info().Str("url", url).Str("token", apiToken).Msg("Init Telegram WebHook Success")

	go b.StartWebhook(ctx)
	return b.WebhookHandler(), nil
}

type TelegramCommand struct {
	cmd  string
	desc string
	work func(update *models.Update) (string, error)
}

var allCmd []*TelegramCommand

func newCmd(cmd, desc string, work func(update *models.Update) (string, error)) {
	allCmd = append(allCmd, &TelegramCommand{
		cmd:  cmd,
		desc: desc,
		work: work,
	})
}

func init() {
	newCmd("/chatId", "获取当前群聊ID", getChatId)
	newCmd("/pay", "获取支付报告", getPayReport)
	newCmd("/syncAdjust", "同步Adjust后台设置", syncAdjustCallBack)
}

func help(ctx context.Context, b *bot.Bot, update *models.Update) {
	var msg strings.Builder
	msg.WriteString("当前支持的命令:\n")
	for _, cmd := range allCmd {
		msg.WriteString(fmt.Sprintf("%s:%s\n", cmd.cmd, cmd.desc))
	}
	b.SendMessage(ctx, &bot.SendMessageParams{
		ChatID: update.Message.Chat.ID,
		Text:   msg.String(),
	})
}
func getChatId(update *models.Update) (string, error) {
	return strconv.FormatInt(update.Message.Chat.ID, 10), nil
}

var payReportShell = `
cd /opt/yylbe
. ./vars.sh
cd php/console
php think pay_report
`

func getPayReport(*models.Update) (string, error) {
	outPut, err := exec.Command("/bin/sh", "-c", payReportShell).CombinedOutput()
	if err != nil {
		return "", fmt.Errorf("%s\n%v", string(outPut), err)
	}
	return "", nil
}

var syncAdjustShell = `
cd /opt/yylbe
. ./vars.sh
php81 hp '\llogic\source\CronAdjustSync'
`

func syncAdjustCallBack(*models.Update) (string, error) {
	outPut, err := exec.Command("/bin/sh", "-c", syncAdjustShell).CombinedOutput()
	if err != nil {
		return "", fmt.Errorf("%s\n%v", string(outPut), err)
	}
	return "", nil
}
