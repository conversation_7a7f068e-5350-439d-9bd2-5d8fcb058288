package main

import (
	"flag"
	"net/http"
	"sort"
	"strconv"
	"yylbe/pkg/base/ijson"
	"yylbe/pkg/base/llog"
	"yylbe/pkg/buildinfo"
	"yylbe/pkg/config/laddrconfig"
	"yylbe/pkg/rdb/sdRDB"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog"
	"golang.org/x/exp/maps"
)

// 简介
// -----------------
// https://prometheus.io/docs/prometheus/latest/configuration/configuration/#http_sd_config

const serviceName = "sdserver"

// 暴露一个 HTTP 端口，用于监控该服务
var exportportFlag = flag.Int("exportport", 3322, "")

// curl "http://************:3322/sd/register?service=node_exporter&port=9100&hostname=`hostname`"
// curl "http://************:3322/sd/discover?service=node_exporter"; echo
// curl "http://127.0.0.1:3322/version"

var httpServer *http.Server

func initLog() {
	llog.SetPathFuncByService(serviceName)

	if laddrconfig.Hold().HostConfigV2.IsPro {
		zerolog.SetGlobalLevel(zerolog.InfoLevel)
	} else {
		zerolog.SetGlobalLevel(zerolog.DebugLevel)
	}

	llog.Get().Info().Interface("laddrconfig", laddrconfig.Hold()).Msg("laddrconfig")
}

var rdb *redis.Client

func initRedis() {
	addr := laddrconfig.Hold().RedisConfig.QueueMaster
	llog.Get().Info().Interface("addr", addr).Msg("initRedis")
	rdb = redis.NewClient(&redis.Options{
		Addr:     addr.Addr,
		Username: addr.Username,
		Password: addr.Password,
		DB:       addr.DB,
	})
}

func main() {
	initLog()
	initRedis()

	gin.SetMode(gin.ReleaseMode)
	router := gin.Default()
	router.GET("/health", healthHandler)
	router.GET("/metrics", metricsHandler)
	router.GET("/version", versionHandler)
	router.GET("/sd/register", sdRegisterHandler)
	router.GET("/sd/discover", sdDiscoverHandler)

	addr := "0.0.0.0:" + strconv.Itoa(*exportportFlag)
	httpServer = &http.Server{Addr: addr, Handler: router}

	httpServer.ListenAndServe()
	llog.Get().Info().Msg("MAIN_EXIT")
}

// exportToPrometheus 格式化输出到 prometheus 格式
func exportToPrometheus() []byte {
	return nil
}

func healthHandler(c *gin.Context) {
	c.String(http.StatusOK, "OK\n")
}

// metricsHandler 输出 prometheus 格式的指标
func metricsHandler(c *gin.Context) {
	c.Data(http.StatusOK, "text/plain; version=0.0.4; charset=utf-8", exportToPrometheus())
}

func versionHandler(c *gin.Context) {
	s := buildinfo.GetBuildInfoString()
	s = string(ijson.MustMarshalButString(laddrconfig.Hold())) + "\n" + s
	c.String(http.StatusOK, s)
}

type SDRegisterParams struct {
	Service  string `form:"service"`
	IP       string `form:"ip"`
	Port     int    `form:"port"`
	Hostname string `form:"hostname"`
}

func sdRegisterHandler(c *gin.Context) {
	params := &SDRegisterParams{}
	err := c.ShouldBindQuery(params)
	if err != nil {
		llog.Get().Error().Err(err).Str("RawQuery", c.Request.URL.RawQuery).Msg("sdRegisterHandler.QUERY_PARSE_ERROR")
		return
	}
	llog.Get().Info().Str("RawQuery", c.Request.URL.RawQuery).Msg("sdRegisterHandler.BEGIN")

	ip := params.IP
	if ip == "" {
		// 请求方未指定 IP 则用请求方与我联系的 IP 地址
		ip = c.ClientIP()
	}

	err = sdRDB.Register(c.Request.Context(), rdb, params.Service, ip, params.Port, sdRDB.SDInfo{
		Hostname: params.Hostname,
	})

	if err != nil {
		c.String(http.StatusInternalServerError, err.Error())
		return
	}

	c.String(http.StatusOK, "")
}

type SDDiscoverParams struct {
	Service string `form:"service"`
	Unique  int    `form:"unique"`
}
type TargetGroup struct {
	Targets []string          `json:"targets"`
	Labels  map[string]string `json:"labels"`
}

func sdDiscoverHandler(c *gin.Context) {
	params := &SDDiscoverParams{}
	err := c.ShouldBindQuery(params)
	if err != nil {
		llog.Get().Error().Err(err).Str("RawQuery", c.Request.URL.RawQuery).Msg("sdDiscoverHandler.QUERY_PARSE_ERROR")
		return
	}
	llog.Get().Info().Str("RawQuery", c.Request.URL.RawQuery).Msg("sdDiscoverHandler.BEGIN")

	m, err := sdRDB.Discover(c.Request.Context(), rdb, params.Service, 120)
	if err != nil {
		c.String(http.StatusInternalServerError, err.Error())
		return
	}

	// targetGroups := []TargetGroup{}
	// for field, info := range m {
	// 	targetGroup := TargetGroup{
	// 		Targets: []string{field},
	// 		Labels: map[string]string{
	// 			"hostname": info.Hostname,
	// 		},
	// 	}
	// 	targetGroups = append(targetGroups, targetGroup)
	// }

	targets := maps.Keys(m)
	if params.Unique != 0 && len(targets) > 1 {
		sort.Strings(targets)
		targets = targets[:1]
	}

	targetGroup := TargetGroup{
		Targets: targets,
	}
	targetGroups := []TargetGroup{targetGroup}

	llog.Get().Info().Str("RequestURI", c.Request.RequestURI).Interface("targetGroups", targetGroups).Msg("sdDiscoverHandler.END")
	c.JSON(http.StatusOK, targetGroups)
}
