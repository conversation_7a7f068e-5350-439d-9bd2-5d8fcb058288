package main

import (
	"bufio"
	"errors"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"
	"yylbe/pkg/base/llog"
	"yylbe/pkg/buildinfo"
	"yylbe/pkg/config/laddrconfig"
	"yylbe/pkg/sdk/alertbotsdk"

	"github.com/gin-gonic/gin"
	_ "github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"
	"github.com/rs/zerolog"
)

// 简介
// -----------------
// MySQL 异步写，一行一个 SQL

const serviceName = "mybuf"

// 暴露一个端口给本机，收集指标数据
var cmdportFlag = flag.Int("cmdport", 3324, "")

// 暴露一个端口给 prometheus，prometheus 定时拉取指标数据
var exportportFlag = flag.Int("exportport", 3325, "")

var httpServer *http.Server

func initLog() {
	llog.SetPathFuncByService(serviceName)

	if laddrconfig.Hold().HostConfigV2.IsPro {
		zerolog.SetGlobalLevel(zerolog.InfoLevel)
		//zerolog.SetGlobalLevel(zerolog.DebugLevel)
	} else {
		zerolog.SetGlobalLevel(zerolog.DebugLevel)
	}
}

var mydb *sqlx.DB

func initMySQL2() {
	dsn := laddrconfig.Hold().MysqlConfig.MainMySQL
	//dsn = strings.Replace(dsn, "127", "122", 1)
	llog.Get().Info().Str("dsn", dsn).Msg("initMySQL2.BEGIN")

	db, err := sqlx.Connect("mysql", dsn)
	if err != nil {
		llog.Get().Err(err).Str("dsn", dsn).Msg("initMySQL2-Failed")
		panic(err)
	}

	db.SetMaxIdleConns(100)
	db.SetMaxOpenConns(100)
	db.SetConnMaxLifetime(time.Minute)

	mydb = db
}

func migrateMySQL() {
}

func main() {
	initLog()
	initMySQL2()
	migrateMySQL()

	go listenSignals()
	go ingesterMain()
	go consumerMain()

	gin.SetMode(gin.ReleaseMode)
	router := gin.Default()
	router.GET("/health", healthHandler)
	router.GET("/metrics", metricsHandler)
	router.GET("/version", versionHandler)

	addr := "0.0.0.0:" + strconv.Itoa(*exportportFlag)
	httpServer = &http.Server{Addr: addr, Handler: router}

	httpServer.ListenAndServe()
	llog.Get().Info().Msg("MAIN_EXIT")

}

func listenSignals() {
	c := make(chan os.Signal, 2)
	signal.Notify(c, syscall.SIGTERM, syscall.SIGINT)
	for {
		s := <-c
		switch s {
		case syscall.SIGTERM, syscall.SIGINT:
			llog.Get().Info().Str("s", s.String()).Msg("GOT_SIGNAL")

			// Shutdown 不可控，强制 Close 比较好
			if httpServer != nil {
				//httpServer.Shutdown(context.TODO())
				httpServer.Close()
			}

			// TCP 服务器那里肯定有点损伤，包括操作系统网络缓冲区中的内容
			// 这里先尽快重启恢复，丢失一点数据

			flush(true)
			flush(true)

			return
		}
	}
}

func ingesterMain() {
	addr := "0.0.0.0:" + strconv.Itoa(*cmdportFlag)
	l, err := net.Listen("tcp4", addr)
	if err != nil {
		fmt.Println("Error listening:", err.Error())
		os.Exit(1)
	}
	defer l.Close()

	llog.Get().Info().Str("addr", addr).Msg("ingesterMain.LISTEN")

	for {
		conn, err := l.Accept()
		if err != nil {
			fmt.Println("Error accepting: ", err.Error())
			os.Exit(1)
		}
		go handleConnection(conn)
	}
}

func handleConnection(conn net.Conn) {
	defer conn.Close()

	llog.Get().Info().Str("RemoteAddr", conn.RemoteAddr().String()).Msg("handleConnection.Accept")

	scanner := bufio.NewScanner(conn)
	for scanner.Scan() {
		s := scanner.Text()
		//fmt.Println(s)
		onTextLine(s)
	}

	llog.Get().Info().Str("RemoteAddr", conn.RemoteAddr().String()).Msg("handleConnection.Close")
}

func onTextLine(s string) {
	if len(s) == 0 {
		return
	}

	myBufPush(s)
}

var myBufMutex sync.Mutex
var myBuf []string

func myBufPush(s string) {
	myBufMutex.Lock()
	myBuf = append(myBuf, s)
	myBufMutex.Unlock()
}

func myBufGetLen() int {
	myBufMutex.Lock()
	n := len(myBuf)
	myBufMutex.Unlock()
	return n
}

func myBufSwap() []string {
	myBufMutex.Lock()
	tmp := myBuf
	myBuf = nil
	myBufMutex.Unlock()
	return tmp
}

func flush(force bool) {
	tmp := myBufSwap()
	if len(tmp) == 0 {
		return
	}

	startTime := time.Now()
	processMyBuf(tmp)
	logProcess("", tmp, startTime, nil, "flush")
}

func logProcess(group string, tmp []string, startTime time.Time, err error, msg string) {
	costUs := time.Since(startTime).Microseconds()
	slow := ""
	if costUs >= 1*1000000 {
		slow = "-Slow"
	}
	if err != nil {
		llog.Get().Error().Err(err).Int64("costUs", costUs).Int("num", len(tmp)).Str("group", group).Msg(msg + slow)
	} else {
		llog.Get().Info().Int64("costUs", costUs).Int("num", len(tmp)).Str("group", group).Msg(msg + slow)
	}
}

/*
经过测试，1 到 20 条约 5ms，50 条约 10ms，100 条约 15ms，200 条约 30ms，可见 20 条一组是比较合适的
单条 5ms，看来写入挺慢的，估计是 INSERT ON DUPLICATE KEY UPDATE 的锅
*/

func processMyBuf(buf []string) {
	// 按表名分组
	tableMap := make(map[string][]string)
	for _, s := range buf {
		group := extractTableName(s)
		tableMap[group] = append(tableMap[group], s)
	}

	for group, tmp := range tableMap {
		processGroup(group, tmp)
	}
}

var reTableName = regexp.MustCompile(`(?i)\b(?:FROM|INTO|UPDATE|JOIN|TABLE|EXISTS)\s+?\W?(\w+)?`)

// 通过正则提取表名
func extractTableName(sql string) string {
	matches := reTableName.FindStringSubmatch(sql)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

func processGroup(group string, tmp []string) {
	// 限制条数，分割执行
	chunkSize := 20
	// 一般用 20 一组，能处理 20 * (1s/5ms) = 4000 QPS，有压力时自适应扩大分组大小
	if len(tmp) >= 4000 {
		// 100 * (1s/15ms) = 6666 QPS
		// 200 * (1s/30ms) = 6666 QPS
		chunkSize = 100
	}

	for i := 0; i < len(tmp); i += chunkSize {
		end := i + chunkSize
		if end > len(tmp) {
			end = len(tmp)
		}
		processChunk(group, tmp[i:end])
	}
}

func processChunk(group string, tmp []string) {
	err := submitBulk(group, tmp)
	if err != nil {
		submitOneByOne(tmp)
	}
}

var fusingDeadline time.Time
var ErrFusing = errors.New("ErrFusing")

// 采样机制，避免日志过多
var submitBulkItemSampler = &zerolog.BurstSampler{
	Burst:  100,
	Period: time.Minute,
}

func submitBulk(group string, tmp []string) error {
	if !fusingDeadline.IsZero() && time.Now().Before(fusingDeadline) {
		return ErrFusing
	}

	startTime := time.Now()

	tx, err := mydb.Beginx()
	if err != nil {
		logProcess(group, tmp, startTime, err, "submitBulk-BeginxFailed")
		return err
	}

	sql := strings.Join(tmp, "\n")

	_, err = tx.Exec(sql)
	if err != nil {
		_ = tx.Rollback()
		logProcess(group, tmp, startTime, err, "submitBulk-ExecFailed")
		return err
	}

	err = tx.Commit()
	if err != nil {
		logProcess(group, tmp, startTime, err, "submitBulk-CommitFailed")
		return err
	}

	logProcess(group, tmp, startTime, nil, "submitBulk-Succeeded")

	for _, s := range tmp {
		if submitBulkItemSampler.Sample(zerolog.InfoLevel) {
			llog.Get().Info().Str("sql", s).Msg("submitBulk-Item")
		}
	}

	return nil
}

func submitOneByOne(tmp []string) error {
	for _, s := range tmp {
		submitOne(s)
	}
	return nil
}

func submitOne(s string) error {
	startTime := time.Now()

retry:
	var err error
	if !fusingDeadline.IsZero() && time.Now().Before(fusingDeadline) {
		err = ErrFusing
	} else {
		_, err = mydb.Exec(s)
		if checkRetry(err, startTime) {
			goto retry
		}
	}

	costUs := time.Since(startTime).Microseconds()
	slow := ""
	if costUs >= 1*1000000 {
		slow = "-Slow"
	}

	if err != nil {
		llog.Get().Error().Err(err).Str("sql", s).Int64("costUs", costUs).Msg("submitOne-ExecFailed" + slow)
		return err
	}
	llog.Get().Info().Str("sql", s).Int64("costUs", costUs).Msg("submitOne-Succeeded" + slow)
	return nil
}

func checkRetry(err error, startTime time.Time) bool {
	// 如果没有错误，或者不是网络错误，就不重试
	if err == nil || !isNetError(err) {
		fusingDeadline = time.Time{}
		return false
	}

	n := myBufGetLen()
	if n > 300*1000 {
		// 缓冲内容太多，就熔断，把队列写盘
		fusingDeadline = time.Now().Add(time.Second * 10)
		llog.Get().Err(err).Int("n", n).Msg("checkRetry-StartFusing")
		alertbotsdk.SendAlert("debug", fmt.Sprintf("❌ mybuf-StartFusing: n=%d err=%s", n, err.Error()))
		// 不再重试
		return false
	}

	// 重试时间太长了不再重试
	// 在数据库升级时需要等接近 2 分钟
	if time.Since(startTime) > time.Second*120 {
		llog.Get().Err(err).Int("n", n).Msg("checkRetry-TimeoutNoRetry")
		alertbotsdk.SendAlert("debug", fmt.Sprintf("❌ mybuf-TimeoutNoRetry: n=%d err=%s", n, err.Error()))
		return false
	}

	// 延迟后重试
	llog.Get().Err(err).Int("n", n).Msg("checkRetry-NetErrorRetryLater")
	time.Sleep(time.Second * 2)
	return true
}

func isNetError(err error) bool {
	if _, ok := err.(net.Error); ok {
		return true
	}
	return false
}

func consumerMain() {
	for {
		flush(false)
		time.Sleep(time.Second * 2)
	}
}

// exportToPrometheus 格式化输出到 prometheus 格式
func exportToPrometheus() []byte {
	return nil
}

func healthHandler(c *gin.Context) {
	c.String(http.StatusOK, "OK\n")
}

// metricsHandler 输出 prometheus 格式的指标
func metricsHandler(c *gin.Context) {
	data := exportToPrometheus()
	llog.Get().Debug().Bytes("data", data).Str("RemoteAddr", c.Request.RemoteAddr).Msg("metricsHandler")
	c.Data(http.StatusOK, "text/plain; version=0.0.4; charset=utf-8", data)
}

func versionHandler(c *gin.Context) {
	c.String(http.StatusOK, buildinfo.GetBuildInfoString())
}
