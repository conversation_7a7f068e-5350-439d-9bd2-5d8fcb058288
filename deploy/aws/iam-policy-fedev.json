{"Version": "2012-10-17", "Statement": [{"Sid": "AllowReadGlobal", "Action": ["s3:ListAllMyBuckets", "s3:ListBucket", "s3:GetBucketLocation"], "Effect": "Allow", "Resource": ["arn:aws:s3:::*"]}, {"Sid": "AllowRead", "Effect": "Allow", "Action": ["s3:List*", "s3:Get*"], "Resource": ["arn:aws:s3:::teenpattiduelsss", "arn:aws:s3:::teenpattiduelsss/*"]}, {"Sid": "SomeAllowWrite", "Effect": "Allow", "Action": ["s3:*"], "Resource": ["arn:aws:s3:::teenpattiduelsss/apks/*", "arn:aws:s3:::teenpattiduelsss/agame/*", "arn:aws:s3:::teenpattiduelsss/h5_release/*", "arn:aws:s3:::teenpattiduelsss/h5/*", "arn:aws:s3:::teenpattiduelsss/config/*", "arn:aws:s3:::teenpattiduelsss/hot_update/*", "arn:aws:s3:::teenpattiduelsss/htmlprivacy/*", "arn:aws:s3:::teenpattiduelsss/playstore_web/*", "arn:aws:s3:::teenpattiduelsss/promo_web/*", "arn:aws:s3:::teenpattiduelsss/remote_bundle/*", "arn:aws:s3:::teenpattiduelsss/remoteHeadImg/*", "arn:aws:s3:::teenpattiduelsss/store/*", "arn:aws:s3:::teenpattiduelsss/tmp1d/*", "arn:aws:s3:::teenpattiduelsss/wwwrummyblitz/*", "arn:aws:s3:::teenpattiduelsss/youtubeVideo/*"]}, {"Sid": "OtherBucketsAllow", "Effect": "Allow", "Action": ["s3:*"], "Resource": ["arn:aws:s3:::tptest3sss/*", "arn:aws:s3:::tptest3log/*", "arn:aws:s3:::teenpattiduellog/*"]}, {"Sid": "AllowListCloudFront", "Effect": "Allow", "Action": ["cloudfront:List*"], "Resource": "*"}, {"Sid": "AllowReadAndInvalidateCloudFront", "Effect": "Allow", "Action": ["cloudfront:Get*", "cloudfront:CreateInvalidation"], "Resource": "*"}, {"Sid": "AllowSimpleA<PERSON>na", "Effect": "Allow", "Action": ["athena:StartQueryExecution", "athena:Get*"], "Resource": "*"}, {"Sid": "AllowReadCloudWatch", "Effect": "Allow", "Action": ["cloudwatch:Get*", "cloudwatch:Describe*", "cloudwatch:List*"], "Resource": "*"}, {"Sid": "AllowReadSNS", "Effect": "Allow", "Action": ["sns:Get*", "sns:List*"], "Resource": "*"}, {"Effect": "Allow", "Action": ["codecommit:Get*", "codecommit:BatchGet*", "codecommit:List*", "codecommit:Describe*", "codecommit:Put*", "codecommit:Commit*", "codecommit:Git*"], "Resource": "*"}]}