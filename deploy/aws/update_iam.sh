




echo "这个脚本设计用于在开发环境下在项目根目录手动运行，要求环境变量配置好 AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY"




# 查看用户列表
aws iam list-users --no-cli-pager

# 查看具体用户
aws iam get-user --user-name fedev --no-cli-pager
aws iam list-access-keys --user-name fedev --no-cli-pager



# 子账号给前端开发公用
# 前端账号要严格限定权限，s3 的可写目录要白名单指定

aws iam create-user --no-cli-pager --user-name fedev
aws iam create-login-profile --no-cli-pager --user-name fedev --password PASSWORDPASSWORDPASSWORD
aws iam create-access-key --no-cli-pager --user-name fedev

aws iam get-user-policy --user-name fedev --policy-name RestrictFrontendRiskWrite --no-cli-pager

aws iam put-user-policy --user-name fedev --policy-name RestrictFrontendRiskWrite --policy-document file://deploy/aws/iam-policy-fedev.json --no-cli-pager

aws iam list-user-policies --user-name fedev --no-cli-pager
aws iam get-user-policy --user-name fedev --policy-name RestrictFrontendRiskWrite --no-cli-pager



# 后端开发个人账号
# 这些可以读：ec2 vpc rds elasticcache cloudwatch cloudfront route53 route53domains s3 athena certificatemanager codecommit
# s3 可以写但不能增删 bucket
# athena 可以查询
# codecommit 可以写但不能增删代码库
# iam 可以查看自己、修改控制台密码、添加 ssh key

# https://docs.aws.amazon.com/AmazonS3/latest/userguide/access-policy-language-overview.html
# https://docs.aws.amazon.com/AmazonS3/latest/API/API_Operations.html

aws iam create-user --no-cli-pager --user-name bedev
aws iam create-login-profile --no-cli-pager --user-name bedev --password PASSWORDPASSWORDPASSWORD

aws iam put-user-policy --user-name bedev --policy-name RestrictedBackendDeveloper --policy-document file://deploy/aws/iam-policy-bedev.json --no-cli-pager




# 子账号给上传图片使用，包括客服、活动、手动

aws iam create-user --no-cli-pager --user-name imgup
aws iam create-access-key --no-cli-pager --user-name imgup

aws iam put-user-policy --user-name imgup --policy-name RestrictedImageUploader --policy-document file://deploy/aws/iam-policy-imgup.json --no-cli-pager




# 高权限机器人账号用于自动化管理任务

aws iam create-user --no-cli-pager --user-name bot
aws iam create-access-key --no-cli-pager --user-name bot

aws iam attach-user-policy --no-cli-pager --user-name bot --policy-arn arn:aws:iam::aws:policy/AmazonEC2FullAccess
aws iam attach-user-policy --no-cli-pager --user-name bot --policy-arn arn:aws:iam::aws:policy/AmazonRoute53DomainsFullAccess
aws iam attach-user-policy --no-cli-pager --user-name bot --policy-arn arn:aws:iam::aws:policy/AmazonRoute53FullAccess
aws iam attach-user-policy --no-cli-pager --user-name bot --policy-arn arn:aws:iam::aws:policy/AmazonS3FullAccess
aws iam attach-user-policy --no-cli-pager --user-name bot --policy-arn arn:aws:iam::aws:policy/AmazonVPCFullAccess
aws iam attach-user-policy --no-cli-pager --user-name bot --policy-arn arn:aws:iam::aws:policy/CloudFrontFullAccess
aws iam attach-user-policy --no-cli-pager --user-name bot --policy-arn arn:aws:iam::aws:policy/AWSCertificateManagerFullAccess
aws iam attach-user-policy --no-cli-pager --user-name bot --policy-arn arn:aws:iam::aws:policy/AWSBillingConductorReadOnlyAccess
aws iam attach-user-policy --no-cli-pager --user-name bot --policy-arn arn:aws:iam::aws:policy/AWSBillingReadOnlyAccess

aws iam list-attached-user-policies --no-cli-pager --user-name bot






