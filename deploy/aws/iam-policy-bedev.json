{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["ec2:Get*", "ec2:List*", "ec2:Describe*", "vpc:Get*", "vpc:List*", "vpc:Describe*", "rds:Get*", "rds:List*", "rds:Describe*", "pi:Get*", "pi:List*", "pi:Describe*", "elasticache:Get*", "elasticache:List*", "elasticache:Describe*", "cloudwatch:Get*", "cloudwatch:List*", "cloudwatch:Describe*", "cloudfront:Get*", "cloudfront:List*", "cloudfront:Describe*", "route53:Get*", "route53:List*", "route53:Describe*", "route53domains:Get*", "route53domains:List*", "route53domains:Describe*", "s3:Get*", "s3:List*", "s3:Describe*", "athena:Get*", "athena:BatchGet*", "athena:List*", "athena:Describe*", "certificatemanager:Get*", "certificatemanager:List*", "certificatemanager:Describe*", "iam:Get*", "iam:List*", "iam:Describe*"], "Resource": "*"}, {"Effect": "Allow", "Action": ["s3:Put*", "s3:List*", "athena:StartQueryExecution", "athena:GetQueryResults", "codecommit:Get*", "codecommit:BatchGet*", "codecommit:List*", "codecommit:Describe*", "codecommit:Put*", "codecommit:Commit*", "codecommit:Git*"], "Resource": "*"}, {"Effect": "Allow", "Action": ["iam:ChangePassword", "iam:UpdateUser", "iam:UpdateLoginProfile", "iam:Get<PERSON>ser", "iam:UploadSSHPublicKey", "iam:ListSSHPublicKeys", "iam:UpdateSSHPublicKey", "iam:DeleteSSHPublicKey"], "Resource": "arn:aws:iam::*:user/${aws:username}"}]}