<?php
declare(strict_types=1);

namespace deploy\dns;



/**
 * 脚本配置域名解析
 * 因为涉及到具体的域名、IP 地址，所以放到 deploy 目录
 */
class SetupDns
{



    public static function SetTestMonoDomainRecords()
    {
        $domain_name = "tptest3.click";
        $ip = "***********";
        $subs = [
            "www" => $ip,
            "console" => $ip,
            "kefu" => $ip,

            "pay" => $ip,

            "attribution" => $ip,

            "gmshuzhi" => $ip,
            "gmkefu" => $ip,
            "gmconsole" => $ip,
            "grafana" => $ip,
            "victoriametrics" => $ip,
            "metabase" => $ip,
            "vmalert" => $ip,
            "alertmanager" => $ip,
            "clickhouse" => $ip,
            "alertbot" => $ip,

            "anyurlproxy" => $ip,
        ];

        $records = array_map(fn($sub, $ip) => ["type" => "A", "sub" => $sub, "value" => $ip, "ttl" => 60], array_keys($subs), $subs);
        \llogic\source\AwsRoute53Api::AwsUpdateDomainRecords($domain_name, $records);
    }




    public static function SetProDomainRecords()
    {

        $ip_map = [
            "main1" => "**************",
            "vice1" => "*************",
            "vice1-inner" => "***********",
            "gate1" => "***********",
            "gate2" => "**************",
            "paygate2" => "*************",
            "cdn1" => "**************",
        ];

        $domain_map = [
            "tpcbpro3.click" => [
                "attribution" => "main1",
                "cdn" => "cdn1",
            ],
            "tpgmpro3.click" => [
                "pay" => "main1",
                "gmshuzhi" => "main1",
                "gmkefu" => "main1",
                "gmconsole" => "main1",

                "grafana" => "vice1",
                "victoriametrics" => "vice1",
                "metabase" => "vice1",
                "vmalert" => "vice1",
                "alertmanager" => "vice1",
                "clickhouse" => "vice1",
                "alertbot" => "vice1",

                "gitea" => "vice1",
                "gitea-inner" => "vice1-inner",
            ],
            "tpafacepro3.click" => [
                "www" => "gate1",
                "console" => "gate1",
                "kefu" => "gate1",
            ],
            "tpgoldmaster.click" => [
                "www" => "gate2",
                "console" => "gate2",
                "kefu" => "gate2",
            ],
            "tppaygatepro3.click" => [
                "pay" => "paygate2",
                "anyurlproxy" => "paygate2",
            ],

        ];


        foreach ($domain_map as $domain_name => $subs) {
            foreach ($subs as $sub => $node_role) {
                $ip = $ip_map[$node_role] ?? "";
                if (empty($ip)) {
                    echo "error: $domain_name $node_role\n";
                    continue;
                }
                $subs[$sub] = $ip;
            }
            $records = array_map(fn($sub, $ip) => ["type" => "A", "sub" => $sub, "value" => $ip, "ttl" => 600], array_keys($subs), $subs);
            echo "update: $domain_name " . json_encode($records) . "\n";
            \llogic\source\AwsRoute53Api::AwsUpdateDomainRecords($domain_name, $records);
        }


    }









    /**
     * 正式迁服切域名的【回滚】操作：切回【阿里云】预测试 crazyslots
     */

    public static function CrazyslotsDnsPointToAliyunAliyunAliyun()
    {

        $domain_name = "crazyslots1.top";
        // curl -v --location "https://www.crazyslots1.top/center" --resolve "www.crazyslots1.top:443:*************"
        // curl -v --location "https://www.crazyslots1.top/privacy/cardsclash.html" --resolve "www.crazyslots1.top:443:*************"
        $ip = "*************";
        $subs = [
            "www" => $ip,
            "console" => $ip,
            "kefu" => $ip,
        ];

        $records = array_map(fn($sub, $ip) => ["type" => "A", "sub" => $sub, "value" => $ip, "ttl" => 60], array_keys($subs), $subs);
        \llogic\source\AwsRoute53Api::AwsUpdateDomainRecords($domain_name, $records);

    }




    /**
     * 正式迁服切域名的【执行】操作：切到【亚马逊】预测试 crazyslots
     */

    public static function CrazyslotsDnsPointToAwsAwsAws()
    {

        $domain_name = "crazyslots1.top";
        // pro3gate3  接入机  teenpattiduel.com  玩家可见    ***********  *************
        // curl --location 'https://www.crazyslots1.top/center' --resolve 'www.crazyslots1.top:443:*************'
        // curl --location 'https://www.crazyslots1.top/privacy/cardsclash.html' --resolve 'www.crazyslots1.top:443:*************'
        $ip = "*************";
        $subs = [
            "www" => $ip,
            "console" => $ip,
            "kefu" => $ip,
        ];

        $records = array_map(fn($sub, $ip) => ["type" => "A", "sub" => $sub, "value" => $ip, "ttl" => 60], array_keys($subs), $subs);
        \llogic\source\AwsRoute53Api::AwsUpdateDomainRecords($domain_name, $records);

    }



    /**
     * 正式迁服切域名的【执行】操作：切到【亚马逊】
     */

    public static function DnsPointToAwsAwsAws()
    {

        /*
        www.teenpattiduel.com  A 面流量经 CloudFront 转发到这里、主要的 B 面流量、htmlprivacy
        console.teenpattiduel.com  一部分 B 面接口
        kefu.teenpattiduel.com  一部分客服接口
        attribution.teenpattihive.com  Adjust 回调流量
        pay.teenpattihive.com  支付回调流量
        */

        $domain_name = "teenpattiduel.com";
        // pro3gate3  接入机  teenpattiduel.com  玩家可见    ***********  *************
        // curl --location 'https://www.teenpattiduel.com/center' --resolve 'www.teenpattiduel.com:443:*************'
        // curl --location 'https://www.teenpattiduel.com/privacy/cardsclash.html' --resolve 'www.teenpattiduel.com:443:*************'
        $ip = "*************";
        $subs = [
            "www" => $ip,
            "console" => $ip,
            "kefu" => $ip,
        ];

        $records = array_map(fn($sub, $ip) => ["type" => "A", "sub" => $sub, "value" => $ip, "ttl" => 600], array_keys($subs), $subs);
        \llogic\source\AwsRoute53Api::AwsUpdateDomainRecords($domain_name, $records);


        $domain_name = "teenpattihive.com";
        $subs = [
            // pro3main1  非游戏部分 ***********  **************
            // curl --location 'https://attribution.teenpattihive.com/version' --resolve 'attribution.teenpattihive.com:443:**************'
            "attribution" => "**************",
            // pro3paygate2  支付接入机  pay.tppaygatepro3.click  三方可见  ***********  *************
            // curl --location 'https://pay.teenpattihive.com/PayNotifyFromDoiPay' --header 'Content-Type: application/json' --data '{"orderNo": "202402031802122795035649312","payNo": "20230517091505836326qpD9LUVhN","amount": 100,"status": "2"}' --resolve 'pay.teenpattihive.com:443:*************'
            "pay" => "*************",
        ];


        $records = array_map(fn($sub, $ip) => ["type" => "A", "sub" => $sub, "value" => $ip, "ttl" => 600], array_keys($subs), $subs);
        \llogic\source\AwsRoute53Api::AwsUpdateDomainRecords($domain_name, $records);

    }




    /**
     * 给 B 面域名 tpgoldmaster.click teenpattiduel.com 增加 nginx 分流，让 pro3gate2 pro3gate3 平分流量
     * 目前 teenpattiduel.com 对应的 pro3gate3 流量非常小
     */

    public static function SetupTeenpattiduelCom()
    {

        $domain_name = "teenpattiduel.com";
        // pro3gate3  接入机  teenpattiduel.com  玩家可见    ***********  *************
        // pro3gate2  接入机  tpgoldmaster.click  玩家可见   ***********  **************
        // curl --location "https://www.teenpattiduel.com/center" --resolve "www.teenpattiduel.com:443:*************"
        // curl --location "https://www.teenpattiduel.com/center" --resolve "www.teenpattiduel.com:443:**************"
        // curl --location "https://www.teenpattiduel.com/privacy/cardsclash.html" --resolve "www.teenpattiduel.com:443:*************"
        $ip = "*************,**************";
        $subs = [
            "www" => $ip,
            "console" => $ip,
            "kefu" => $ip,
        ];

        $records = array_map(fn($sub, $ip) => ["type" => "A", "sub" => $sub, "value" => $ip, "ttl" => 600], array_keys($subs), $subs);
        \llogic\source\AwsRoute53Api::AwsUpdateDomainRecords($domain_name, $records);
    }


    /**
     * 给 B 面域名 tpgoldmaster.click teenpattiduel.com 增加 nginx 分流，让 pro3gate2 pro3gate3 平分流量
     * 目前 teenpattiduel.com 对应的 pro3gate3 流量非常小
     */

    public static function SetupTpgoldmasterClick()
    {

        $domain_name = "tpgoldmaster.click";
        // pro3gate3  接入机  teenpattiduel.com  玩家可见    ***********  *************
        // pro3gate2  接入机  tpgoldmaster.click  玩家可见   ***********  **************
        // curl --location "https://www.tpgoldmaster.click/center" --resolve "www.tpgoldmaster.click:443:*************"
        // curl --location "https://www.tpgoldmaster.click/center" --resolve "www.tpgoldmaster.click:443:**************"
        // curl --location "https://www.tpgoldmaster.click/privacy/cardsclash.html" --resolve "www.tpgoldmaster.click:443:*************"
        $ip = "**************,*************";
        $subs = [
            "www" => $ip,
            "console" => $ip,
            "kefu" => $ip,
        ];

        $records = array_map(fn($sub, $ip) => ["type" => "A", "sub" => $sub, "value" => $ip, "ttl" => 600], array_keys($subs), $subs);
        \llogic\source\AwsRoute53Api::AwsUpdateDomainRecords($domain_name, $records);
    }

}
