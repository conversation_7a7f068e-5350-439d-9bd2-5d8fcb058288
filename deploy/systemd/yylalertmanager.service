[Unit]
Description=yylalertmanager
After=syslog.target network-online.target remote-fs.target nss-lookup.target
Wants=network-online.target

[Install]
WantedBy=multi-user.target

[Service]
Type=simple
User=root
WorkingDirectory=/data/runtime/alertmanager
ExecStart=/usr/local/bin/alertmanager --config.file=/opt/yylbe/deploy/prometheus/alertmanager.yml --web.external-url=https://alertmanager.teenpattihive.com
Restart=always
RestartSec=2s

