[Unit]
Description=yylkefubiz
After=syslog.target network-online.target remote-fs.target nss-lookup.target
Wants=network-online.target

[Install]
WantedBy=multi-user.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/yylbe/php/kefu
ExecStart=/usr/bin/php81 -d apc.enable_cli=1 -d apc.shm_size=8M -d opcache.enable_cli=0 /opt/yylbe/php/kefu/kefubiz.php start
# 友好 SIGQUIT=3  强制 SIGINT=2
KillSignal=SIGINT
# 友好 SIGUSR2=12  强制 SIGUSR1=10
ExecReload=/bin/kill -SIGUSR2 $MAINPID
# 如果服务无法在此时间内完成启动或停止，Systemd 将尝试强制终止服务，并根据 Restart 指令决定是否重启服务。
TimeoutSec=5
Restart=always
RestartSec=2s

