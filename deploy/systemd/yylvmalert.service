[Unit]
Description=yylvmalert
After=syslog.target network-online.target remote-fs.target nss-lookup.target
Wants=network-online.target

[Install]
WantedBy=multi-user.target

[Service]
Type=simple
User=root
WorkingDirectory=/data/runtime/vmalert
ExecStart=/usr/local/bin/vmalert-prod -configCheckInterval=1m -rule=/opt/yylbe/deploy/prometheus/rules.d/*.yml -datasource.url=http://127.0.0.1:8428 -notifier.url=http://127.0.0.1:9093 -remoteWrite.url=http://127.0.0.1:8428 -remoteRead.url=http://127.0.0.1:8428 -external.url=https://vmalert.teenpattihive.com
Restart=always
RestartSec=2s

