[Unit]
Description=yylnginx
After=syslog.target network-online.target remote-fs.target nss-lookup.target
Wants=network-online.target

[Install]
WantedBy=multi-user.target

[Service]
Type=forking
User=root
WorkingDirectory=/opt/yylbe/deploy/nginx/runtime
PIDFile=/opt/yylbe/deploy/nginx/runtime/logs/nginx.pid
ExecStartPre=/usr/bin/openresty -t -q -g 'daemon on; master_process on;'
ExecStart=/usr/bin/openresty -g 'daemon on; master_process on;' -p /opt/yylbe/deploy/nginx/runtime
ExecReload=/bin/kill -s HUP $MAINPID
ExecStop=/bin/kill -s QUIT $MAINPID
PrivateTmp=true
Restart=always
RestartSec=2s
