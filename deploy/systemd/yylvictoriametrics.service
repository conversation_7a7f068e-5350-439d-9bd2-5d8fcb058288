[Unit]
Description=yylvictoriametrics
After=syslog.target network-online.target remote-fs.target nss-lookup.target
Wants=network-online.target

[Install]
WantedBy=multi-user.target

[Service]
Type=simple
User=root
WorkingDirectory=/data/runtime/victoria-metrics-data
# %H 表示主机名
ExecStart=/usr/local/bin/victoria-metrics-prod -storageDataPath /data/runtime/victoria-metrics-data -retentionPeriod=2y -selfScrapeInterval=1m  -promscrape.config /opt/yylbe/deploy/prometheus/prometheus.yml -vmalert.proxyURL=http://127.0.0.1:8880
Restart=always
RestartSec=2s

