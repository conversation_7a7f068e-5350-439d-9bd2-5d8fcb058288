[Unit]
Description=yylfpmus
After=syslog.target network-online.target remote-fs.target nss-lookup.target
Wants=network-online.target

[Install]
WantedBy=multi-user.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/yylbe/deploy/fpm

ExecStart=/opt/remi/php81/root/usr/sbin/php-fpm --fpm-config /opt/yylbe/deploy/fpm/fpmus.conf --nodaemonize --allow-to-run-as-root

ExecReload=/bin/kill -USR2 $MAINPID
Restart=always
RestartSec=2s


