[Unit]
Description=yylvector
After=syslog.target network-online.target remote-fs.target nss-lookup.target
Wants=network-online.target

[Install]
WantedBy=multi-user.target

[Service]
Type=simple
User=root
WorkingDirectory=/data/runtime/vector
ExecStart=/bin/sh -c "exec /usr/local/bin/vector --config /opt/yylbe/deploy/vector/$(hostname | cut -d '-' -f 1).toml"
Restart=always
RestartSec=2s
TimeoutStopSec=9s


