[Unit]
Description=yylnodeexporter
After=syslog.target network-online.target remote-fs.target nss-lookup.target
Wants=network-online.target

[Install]
WantedBy=multi-user.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/yylbe
ExecStart=/usr/local/bin/node_exporter --collector.disable-defaults --collector.cpu --collector.loadavg --collector.meminfo --collector.vmstat --collector.diskstats --collector.filesystem --collector.netdev --collector.netstat --collector.sockstat --collector.time --collector.stat --collector.os --collector.uname --collector.textfile --collector.textfile.directory=/data/metrics
Restart=always
RestartSec=2s
TimeoutStopSec=9s


