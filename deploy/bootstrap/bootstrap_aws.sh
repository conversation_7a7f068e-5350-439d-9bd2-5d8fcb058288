

################################################################################
# 已经安装好的工具
################################################################################

yum install -y bzip2 unzip lsof

# bind-utils 主要包含 dig nslookup
yum install -y bind-utils

yum install -y jq

yum install -y iftop iotop

# stress --vm 2 --vm-bytes 4G --timeout 10s
# perl -e 'my $x = "x" x 9000000000;'
yum install -y stress



################################################################################
# 需要安装的工具
################################################################################

yum install -y htop iotop telnet

yum install -y git

yum install -y gcc gcc-c++ make

# mysql 8.0 客户端，不包括服务器
# https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/CHAP_GettingStarted.CreatingConnecting.MySQL.html#CHAP_GettingStarted.Connecting.MySQL
# dnf install -y mariadb105

# mysql 8.0 客户端，不包括服务器
# https://dev.mysql.com/doc/mysql-yum-repo-quick-guide/en/
echo '
[mysql80-community]
name=MySQL 8.0 Community Server
baseurl=http://repo.mysql.com/yum/mysql-8.0-community/el/9/$basearch/
enabled=1
gpgcheck=0
' > /etc/yum.repos.d/mysql-community.repo
dnf install -y mysql-community-client

# redis 6.0 客户端
dnf install -y redis6
# 包括服务器但没有被启用
systemctl status redis6.service
# 需要做一个标准名字
ln -s /usr/bin/redis6-cli /usr/bin/redis-cli

# https://docs.aws.amazon.com/linux/al2023/ug/java.html
yum install -y java-22-amazon-corretto


################################################################################
# lrzsz 用于简单的上传下载文件
################################################################################

# lrzsz 非常古老，ec2 默认不支持，但又非常有用，所以得手动编译安装
wget https://ohse.de/uwe/releases/lrzsz-0.12.20.tar.gz
tar -xzf lrzsz-0.12.20.tar.gz
cd lrzsz-0.12.20
./configure
make
sudo make install
sudo ln -s /usr/local/bin/lrz /usr/local/bin/rz
sudo ln -s /usr/local/bin/lsz /usr/local/bin/sz




################################################################################
# 下载代码
################################################################################

cd /opt


# 如果是线上机器则用内网地址 $(hostname) 以 pro 为前缀 posix sh
if hostname | grep -q "^pro"; then
git clone http://deploybot:<EMAIL>/yylorg/yylbe.git
else
git clone http://deploybot:<EMAIL>/yylorg/yylbe.git
fi


cd /opt/yylbe

git branch -d release-aws
git checkout --track origin/release-aws

. ./vars.sh
yylinstallenv


