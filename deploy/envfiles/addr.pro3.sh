

################################################################################
# 基于命令行选项的 redis mysql clickhouse 配置
# 以及 redis mysql 的 nginx 转发配置供开发人员访问
# aws 的 redis 没有外网地址，不启用 tls 也没有密码，所以需要 openresty 包装一下
# aws 的 mysql 可以有外网地址但有额外费用，所以也经过 nginx 转发
################################################################################

wredis='-h pro3redis1.q1nju9.ng.0001.aps1.cache.amazonaws.com'
roredis='-h pro3redis1-ro.q1nju9.ng.0001.aps1.cache.amazonaws.com'
wmysql='-h"pro3mysql1.c7cyw6uu2bnh.ap-south-1.rds.amazonaws.com" -u"admin" -p"wuap232UIEFH3dsuuo" yyldb'
romysql='-h"pro3mysql1slave2.c7cyw6uu2bnh.ap-south-1.rds.amazonaws.com" -u"rouser" -p"asfdhw623576UYEQIwgewweeqw" yyldb'
# 用于把某些日志表增量同步到 clickhouse，不指定则默认用 wmysql
romysql_lag_sync='-h"pro3mysql1slave2.c7cyw6uu2bnh.ap-south-1.rds.amazonaws.com" -u"rouser" -p"asfdhw623576UYEQIwgewweeqw" yyldb'
wclickhouse='--host *********** --user default --password CSL9Rq0F0x --port 9001'
roclickhouse='--host *********** --user rouser --password pktzz51tWiIV --port 9001'

g_proxy_password_redis1=gdf239898UUWEUDSLF2323
g_proxy_addr_redis1=pro3redis1-ro.q1nju9.ng.0001.aps1.cache.amazonaws.com:6379
g_proxy_addr_mysql1=pro3mysql1slave2.c7cyw6uu2bnh.ap-south-1.rds.amazonaws.com:3306



################################################################################
# 内部服务地址配置
################################################################################

g_alertbot_url=http://***********:3305
g_sdserver_url=http://***********:3322
g_prometheus_url=http://***********:8428

g_chbuf_url=tcp://***********:3320
g_mybuf_url=tcp://***********:3324

g_pay_third_url_prefix=

g_serverreg_inner_ips=***********
g_centerreg_inner_ips=***********
g_serverhttp_inner_ip=***********
g_console_inner_ip=***********
g_pay_inner_ip=***********



################################################################################
# nginx upstream 配置
################################################################################

g_upstream_main_ips=***********
g_upstream_ossprivacy_addr=teenpattiduelsss.s3.ap-south-1.amazonaws.com
g_upstream_ossdownload_addr=teenpattiduelsss.s3.ap-south-1.amazonaws.com

g_http_upstreams="
upstream upstream_serverws {
  server ***********:$g_upstream_serverws_port weight=100;
  server ***********:$g_upstream_serverws_port weight=100;
}
upstream upstream_console {
  server ***********:$g_upstream_console_port weight=100;
  keepalive 300;
}

"

g_stream_upstreams="

"



################################################################################
# 内部服务后台管理账号配置
################################################################################

# 某些内部服务如 victoriametrics 需要通过 nginx 加简单的账号密码
# account1:password1 account2:password2 ...
g_htpasswd_kvlist="test1:victoriaMETRICS999 test2:victoriaMETRICS999"

# 数值后台账号密码表
# account1:password1 account2:password2 ...
g_gmshuzhi_account_password_map="admin:djh676dDDFuTY769 admin01:pzkj@123..."



################################################################################
# 三方服务账号配置
################################################################################

# aws 高权限机器人账号
# 权限包括：S3 Route53 Route53Domain CloudFront
g_aws_key="********************"
g_aws_secret="e/6Kp9lIh7Q+Nkwfoxh+ctdE73144u+iWVjnwtOn"

# aws 注册域名时使用的邮箱，这个是关键信息，其他的无所谓
g_aws_domain_contact_email="<EMAIL>"

# Adjust 登录参数
g_adjust_login_account="<EMAIL>"
g_adjust_login_password="xxx"

# 阿里云访问密钥
# 权限包括：域名
g_aliyun_admin_access_key="LTAI5tKofdn81oXzGAe57ZG3"
g_aliyun_admin_access_secret="******************************"

# 短信服务配置：颂量
g_sms_conf_songliang='
url=https://api.itniotech.com
app_id=ddxW2uHt
app_key=ruZj9W9hvXvagblGIlNwipJ2WXdvah6U
app_secret=niuiq5VpSoEX56OHOjIuPOvwscVIUtwV
'

# 短信服务配置：牛信
g_sms_conf_niuxin='
url=https://api2.nxcloud.com
appkey=
accessKey=
'

# 谷歌商店地址，用于检测上架下架
g_google_play_url='https://play.google.com'




################################################################################
# 对象存储相关配置
################################################################################

# 上架下架检测依赖于这个 Excel 文件
g_tishenbao_xlsx_url="https://teenpattiduelsss.s3.ap-south-1.amazonaws.com/htmlprivacy/提审包.xlsx"

# 有一个综合的 aws s3 存储桶用于静态资源下载、客服系统图片上传等
# 同时用于拼接 aws s3 存储桶地址，用于 CloudFront 转发
g_aws_s3_region="ap-south-1"
g_aws_s3_bucket="teenpattiduelsss"

# 用户访问文件资源的地址
# https://sss.teenpattiduel.com/remoteHeadImg/user_head_20.png
g_oss_access_url="https://sss.teenpattiduel.com"

# 内网访问文件资源的地址
g_oss_inner_url="https://teenpattiduelsss.s3.ap-south-1.amazonaws.com"

# center 域名通过 CloudFront 访问 S3
# 左边用于路径前缀匹配，支持 * ? 通配符
# 右边是 S3 的路径，支持 <source> 表示渠道名
# 映射方式不是把左边替换为右边，而是把右边拼接到左边前面
g_cf_s3_path_map='/h5/*: /static/*:/agame/<source>'
#g_cf_s3_path_map='/h5/*:'

# CloudFront 接入点访问日志存到哪个桶
g_cloudfront_logging_bucket="teenpattiduellog.s3.amazonaws.com"



################################################################################
# 对外域名相关配置
################################################################################

# Adjust 归因回调地址
g_adjust_callback_url='https://attribution.teenpattihive.com'
g_adjust_callback_url_by_source='
tptest=https://attribution.tptest3.click
tppro=https://attribution.tpcbpro3.click
'

# Telegram 机器人的 webhook 地址
g_telegram_webhook_url='https://alertbot.tpgmpro3.click'

# 创建 CloudFront 接入点时使用该地址作为回源地址
# 安全起见，这个在测试环境和本地开发环境下也要和线上保持相同的值，避免测试环境加渠道把线上的写坏了
g_cloudfront_origin_url="http://www.teenpattiduel.com"
# 但允许个别渠道使用不同的回源地址，方便测试环境试验
g_cloudfront_origin_url_by_source='
tppro=https://www.tpafacepro3.click
'

# 简单对外地址，用于本地局域网开发或北京测试服，为空表示局域网地址
g_simple_outer_ip='127.0.0.1'

# 三方支付回调后端的地址，为空则默认用地址端口
g_pay_callback_url='https://pay.tppaygatepro3.click'

# 这几个地址用于内部后台系统的相互跳转链接，为空则默认用地址端口
g_gmconsole_link_url='https://gmconsole.tpgmpro3.click'
g_grafana_link_url='https://grafana.tpgmpro3.click'

# B 面的接入域名，不分渠道，统一的，用于 channel_service 接口返回的各个地址
# 为空则表示不用域名接入，那只能通过 IP 地址和端口接入了
# 有些支付通道要求把前端回调的域名设置到商户后台，也用这个配置，所以这个域名不能随便乱改
g_b_domain_name='tpgoldmaster.click'

# 有些域名较为特殊，不能创建接入点，它们是接入点的转发目标
g_cloudfront_domain_blacklist='
teenpattiduel.com
tpgoldmaster.click
'


# 有些支付通道要求把前端回调的域名设置到商户后台，所以这里用注释记录一下
# console.tpgoldmaster.click
# 有些支付通道要求把后端回调的域名设置到商户后台，所以这里用注释记录一下
# pay.tppaygatepro3.click




################################################################################
# alertbot 配置
#
# https://core.telegram.org/bots
# To get started, message @BotFather on Telegram to register your bot and receive its authentication token.
# 机器人改名字在里面 /setname 按提示操作即可
#
# 获取 chat/id 的办法
# 在目标群说一句话，然后调用机器人的拉取更新，然后在返回信息里面找 chat/id 是个负数
# curl https://api.telegram.org/bot11111111111:KKKKKKKKKKKKKKKKKKKKKKKKKKKKK/getUpdates
#
# 机器人发消息接口
# curl -X POST "https://api.telegram.org/bot11111111111:KKKKKKKKKKKKKKKKKKKKKKKKKKKKK/sendMessage" -d "chat_id=-111111111111111&text=Hello"
#
# alertbot 3305
# curl "http://127.0.0.1:3305/send?text=Hello&level=debug"
################################################################################
alertbot_tg_bot_url="https://api.telegram.org/bot5928554682:AAFNwviUA-DNsSFl480T1d0S4WtRys-fyho"

# debug 是兜底的
alertbot_tg_level_map="debug=-1001625462005 线上调试  apppublish=-4765903402 上架下架  alert=-1002227363201 线上报警  pay=-4697355665 支付报警  payreport=-4771095625 数据播报  auditaccess=-4787231685 疑似审核  sourceconf=-4666519460 拆包配置  cost=-1002179634910 费用管理"



################################################################################
# servertimer 配置
# 定时器生产消费机制
# 用一个 ZSET 表示一个定时器堆
# 对于一个堆，有多个生产者，一个消费者，也可以有多个消费者
# 扩容时先扩消费者数目，再扩生产者对于堆个数的认识
# 不能缩容，因为有些房间长期存在，并且房间绑定了某个定时器堆
# Windows 只支持一个 worker，所以在 Windows 上定时器堆只能有一个
################################################################################

# 一台机器的定时器堆消费者数
g_servertimer_worker_count=128
# 一个定时器堆只能被一个消费者处理，可以把定时器堆按编号分段分配给不同的机器
g_servertimer_start_heap_id=0
# 定时器堆生产者数
g_timer_heap_count=128



################################################################################
# 可选功能开关
################################################################################

# 是否允许注册域名，为了避免误操作，只在必要的环境下开启
g_fs_allow_register_domain=1

# 定时进行三方支付通道的查询余额，这是个重量级操作，三方那边还可能限流，所以只在必要的环境下开启
g_fs_cron_query_balance_from_third_pay=1

# 是否允许真正执行刪除 Adjust 后台的没用 App
g_fs_allow_really_delete_adjust_app=1





