

set -e
#set -x

# https://stackoverflow.com/questions/29832037/how-to-get-script-directory-in-posix-sh
g_this_dir=$(CDPATH= cd -- "$(dirname -- "$0")" && pwd)
g_yylenv_dir=$(realpath "$g_this_dir/../yylenv")

. "$g_yylenv_dir/api.sh"



wmysql -e '

delete from gm_stat_summary_v2 where date=CURDATE() - INTERVAL 1 DAY;


';

php /opt/yylbe/php/console/think summary_v2 
php /opt/yylbe/php/console/think room_statistics_query


