# 付费分布 修复历史数据

#!/bin/bash

start_date="2024-02-01"
end_date="2024-02-18"

start_timestamp=$(date -d "$start_date" +%s)
end_timestamp=$(date -d "$end_date" +%s)

current_timestamp=$start_timestamp

while [[ $current_timestamp -lt $end_timestamp ]]; do
    current_date=$(date -d "@$current_timestamp" +%Y-%m-%d)
    next_timestamp=$((current_timestamp + 86400))
    next_date=$(date -d "@$next_timestamp" +%Y-%m-%d)
    command="php81 php/console/think recharge_behavior_query $current_date $next_date"
    echo $command
    eval $command
    current_timestamp=$next_timestamp
done

