# 初始化线上代理排行榜数据

cd /opt/yylbe

. ./vars.sh

generate_commands() {
    local start_date="$1"
    local end_date="$2"
    local interval="$3"
    local type="$4"

    local commands=()

    case "$type" in
        "daily")
            while [[ "$start_date" < "$end_date" ]]; do
                next_date=$(date -d "$start_date + 1 day" +%Y-%m-%d)
                commands+=("php php/console/think promo_ranking $start_date $next_date $interval")
                start_date="$next_date"
            done
            ;;
        "weekly")
            while [[ "$start_date" < "$end_date" ]]; do
                next_date=$(date -d "$start_date + 1 week" +%Y-%m-%d)
                commands+=("php php/console/think promo_ranking $start_date $next_date $interval")
                start_date="$next_date"
            done
            ;;
        "monthly")
            while [[ "$start_date" < "$end_date" ]]; do
                next_date=$(date -d "$start_date + 1 month" +%Y-%m-%d)
                commands+=("php php/console/think promo_ranking $start_date $next_date $interval")
                start_date="$next_date"
            done
            ;;
        *)
            echo "Unsupported type: $type"
            exit 1
            ;;
    esac

    for cmd in "${commands[@]}"; do
        echo "$cmd"
        eval "$cmd"
    done
}

generate_commands "2024-01-01" "2024-06-01" 3 "monthly"
generate_commands "2024-04-08" "2024-06-17" 2 "weekly"
generate_commands "2024-05-29" "2024-06-19" 1 "daily"




# 月
# php php/console/think promo_ranking 2024-01-01 2024-02-01 3
# php php/console/think promo_ranking 2024-02-01 2024-03-01 3
# php php/console/think promo_ranking 2024-03-01 2024-04-01 3
# php php/console/think promo_ranking 2024-04-01 2024-05-01 3
# php php/console/think promo_ranking 2024-05-01 2024-06-01 3

# 周
# php php/console/think promo_ranking 2024-04-08 2024-04-15 2
# php php/console/think promo_ranking 2024-04-15 2024-04-22 2
# php php/console/think promo_ranking 2024-04-22 2024-04-29 2
# php php/console/think promo_ranking 2024-04-29 2024-05-06 2
# php php/console/think promo_ranking 2024-05-06 2024-05-13 2
# php php/console/think promo_ranking 2024-05-13 2024-05-20 2
# php php/console/think promo_ranking 2024-05-20 2024-05-27 2
# php php/console/think promo_ranking 2024-05-27 2024-06-03 2
# php php/console/think promo_ranking 2024-06-03 2024-06-10 2
# php php/console/think promo_ranking 2024-06-10 2024-06-17 2

# 日
# php php/console/think promo_ranking 2024-05-29 2024-05-30 1
# php php/console/think promo_ranking 2024-05-30 2024-05-31 1
# php php/console/think promo_ranking 2024-05-31 2024-06-01 1
# php php/console/think promo_ranking 2024-06-01 2024-06-02 1
# php php/console/think promo_ranking 2024-06-02 2024-06-03 1
# php php/console/think promo_ranking 2024-06-03 2024-06-04 1
# php php/console/think promo_ranking 2024-06-04 2024-06-05 1
# php php/console/think promo_ranking 2024-06-05 2024-06-06 1
# php php/console/think promo_ranking 2024-06-06 2024-06-07 1
# php php/console/think promo_ranking 2024-06-07 2024-06-08 1
# php php/console/think promo_ranking 2024-06-08 2024-06-09 1
# php php/console/think promo_ranking 2024-06-09 2024-06-10 1
# php php/console/think promo_ranking 2024-06-10 2024-06-11 1
# php php/console/think promo_ranking 2024-06-11 2024-06-12 1
# php php/console/think promo_ranking 2024-06-12 2024-06-13 1
# php php/console/think promo_ranking 2024-06-13 2024-06-14 1
# php php/console/think promo_ranking 2024-06-14 2024-06-15 1
# php php/console/think promo_ranking 2024-06-15 2024-06-16 1
# php php/console/think promo_ranking 2024-06-16 2024-06-17 1
# php php/console/think promo_ranking 2024-06-17 2024-06-18 1
# php php/console/think promo_ranking 2024-06-18 2024-06-19 1
