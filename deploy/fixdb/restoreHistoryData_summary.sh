
set -e
#set -x

# https://stackoverflow.com/questions/29832037/how-to-get-script-directory-in-posix-sh
g_this_dir=$(CDPATH= cd -- "$(dirname -- "$0")" && pwd)
g_yylenv_dir=$(realpath "$g_this_dir/../yylenv")

. "$g_yylenv_dir/api.sh"



wmysql -e '


DELETE FROM gm_stat_summary_v2;



';


# 每日数据统计 恢复历史数据




# 把5.12号 - 5.31号的广告投放数据 从老表拉到新表
php /opt/yylbe/php/ldb/mysql/migrate/230612.import_fb_gg_cost.php

# 至此 历史投放金额数据已经有了 接下来 恢复每日数据统计5.12到6.12的的历史数据  
php /opt/yylbe/php/console/think summary_v2 2023-05-12 
php /opt/yylbe/php/console/think summary_v2 2023-05-13 
php /opt/yylbe/php/console/think summary_v2 2023-05-14 
php /opt/yylbe/php/console/think summary_v2 2023-05-15 
php /opt/yylbe/php/console/think summary_v2 2023-05-16 
php /opt/yylbe/php/console/think summary_v2 2023-05-17 
php /opt/yylbe/php/console/think summary_v2 2023-05-18 
php /opt/yylbe/php/console/think summary_v2 2023-05-19 
php /opt/yylbe/php/console/think summary_v2 2023-05-20 
php /opt/yylbe/php/console/think summary_v2 2023-05-21 
php /opt/yylbe/php/console/think summary_v2 2023-05-22 
php /opt/yylbe/php/console/think summary_v2 2023-05-23 
php /opt/yylbe/php/console/think summary_v2 2023-05-24 
php /opt/yylbe/php/console/think summary_v2 2023-05-25 
php /opt/yylbe/php/console/think summary_v2 2023-05-26 
php /opt/yylbe/php/console/think summary_v2 2023-05-27 
php /opt/yylbe/php/console/think summary_v2 2023-05-28 
php /opt/yylbe/php/console/think summary_v2 2023-05-29 
php /opt/yylbe/php/console/think summary_v2 2023-05-30 
php /opt/yylbe/php/console/think summary_v2 2023-05-31 
php /opt/yylbe/php/console/think summary_v2 2023-06-01 
php /opt/yylbe/php/console/think summary_v2 2023-06-02 
php /opt/yylbe/php/console/think summary_v2 2023-06-03 
php /opt/yylbe/php/console/think summary_v2 2023-06-04 
php /opt/yylbe/php/console/think summary_v2 2023-06-05 
php /opt/yylbe/php/console/think summary_v2 2023-06-06 
php /opt/yylbe/php/console/think summary_v2 2023-06-07 
php /opt/yylbe/php/console/think summary_v2 2023-06-08 
php /opt/yylbe/php/console/think summary_v2 2023-06-09 
php /opt/yylbe/php/console/think summary_v2 2023-06-10 
php /opt/yylbe/php/console/think summary_v2 2023-06-11 
php /opt/yylbe/php/console/think summary_v2 2023-06-12 
php /opt/yylbe/php/console/think summary_v2 2023-06-13 

echo "执行完毕！"
