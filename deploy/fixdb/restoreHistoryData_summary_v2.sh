# 每日数据统计 修复历史数据

#!/bin/bash

start_date="2023-05-12"
end_date="2024-02-12"

start_timestamp=$(date -d "$start_date" +%s)
end_timestamp=$(date -d "$end_date" +%s)

current_timestamp=$start_timestamp

while [[ $current_timestamp -le $end_timestamp ]]; do
    current_date=$(date -d "@$current_timestamp" +%Y%m%d)
    command="php81 /opt/yylbe/php/console/think summary_v2 $current_date"
    echo $command
    eval $command
    current_timestamp=$((current_timestamp + 86400))
done

