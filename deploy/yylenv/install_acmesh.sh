
set -e
#set -x


# https://stackoverflow.com/questions/29832037/how-to-get-script-directory-in-posix-sh
g_this_dir=$(CDPATH= cd -- "$(dirname -- "$0")" && pwd)
g_cppenv_dir=$(realpath "$g_this_dir/../cppenv")

. "$g_cppenv_dir/cppenv_shbase.sh"



# https://github.com/acmesh-official/acme.sh/releases
# 安装好之后自定有个定时任务给证书续期
install_acmesh() {
  proxy=
  if curl -s ipinfo.io | grep Asia/Shanghai; then
    proxy=https://mirror.ghproxy.com/
  fi

  decompress_file --cd acmesh-3.0.7.tar.gz "$proxy"https://github.com/acmesh-official/acme.sh/archive/refs/tags/3.0.7.tar.gz --strip-components 1

  # 这个脚本可以重复执行的
  # 安装位置是会产生运行时数据的 ~/.acme.sh
  # 重复执行没问题，添加 crontab 定时任务，每天一次，时间在安装时随机
  # 29 14 * * * "/root/.acme.sh"/acme.sh --cron --home "/root/.acme.sh" > /dev/null
  # 我们不需要这个定时任务，而是自己控制定时任务的执行时机
  # 重复执行没问题，修改 .bashrc
  # . "/root/.acme.sh/acme.sh.env"
  # 这个 env 脚本做的事情是
  # export LE_WORKING_DIR="/root/.acme.sh"
  # alias acme.sh="/root/.acme.sh/acme.sh"
  ./acme.sh --install --nocron

  # 默认 ca 是 zerossl，这个需要用邮箱注册账号，不如 letsencrypt 省事
  # ~/.acme.sh/acme.sh --register-account -m <EMAIL>
  ~/.acme.sh/acme.sh --set-default-ca --server letsencrypt

  cat > ~/.acme.sh/notify/mynotify.sh <<'EOF'
  mynotify_send() {
    _subject="$1"
    _content="$2"
    # 0: success, 1: error 2($RENEW_SKIP): skipped
    _statusCode="$3"
    echo "acmesh: $_subject: $_content"
  }
EOF
  # --notify-level  0-禁用 1-错误 2-默认错误和续签 3-全部
  # --notify-mode  0-默认批量 1-单个
  ~/.acme.sh/acme.sh --set-notify --notify-hook mynotify --notify-level 2 --notify-mode 1

}

# DNS 方式申请证书的优点
# 1、不要求域名已经解析到这里，迁移时可以在目标机器上提前准备好证书
# 2、不要求 nginx 服务，不然 nginx 配置得分两阶段，先 http 可用再处理 https

# 示例申请阿里云权限
cat > /dev/null <<COMMENT

https://ram.console.aliyun.com/users
RAM 访问控制 --> 用户 --> 创建用户
登录名称 acmesh
显示名称 acmesh
控制台访问 X
OpenAPI 调用访问 √

用户登录名称 <EMAIL>
AccessKey ID LTAI5tKWogzLwcZiCijdtEfZ
AccessKey Secret ******************************

添加权限
AliyunHTTPDNSFullAccess  管理HTTPDNS的权限
AliyunDNSFullAccess  管理云解析(DNS)的权限
  
COMMENT



# 示例申请证书
example_issue_cert() {
  main_domain=teentest.top
  export Ali_Key="LTAI5tKWogzLwcZiCijdtEfZ"
  export Ali_Secret="******************************"
  ~/.acme.sh/acme.sh --issue --dns dns_ali -d $main_domain -d *.$main_domain --server letsencrypt

  # 在证书被定时任务更新后，会自动拷贝到指定位置，并自动执行指定命令
  mkdir -p ~/.acme.sh/mycerts
  ~/.acme.sh/acme.sh --install-cert -d $main_domain --key-file ~/.acme.sh/mycerts/$main_domain.key.pem --fullchain-file ~/.acme.sh/mycerts/$main_domain.cert.pem --reloadcmd "echo reloadcmd"

  # openssl x509 -in /root/.acme.sh/mycerts/teentest.top.cert.pem -text -noout
}



main_call lazy_call install_acmesh 1002


