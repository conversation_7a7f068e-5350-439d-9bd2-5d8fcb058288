



# php 8.1 共存安装
install_php81_aliyun() {
  if type php81 2>/dev/null; then
    return
  fi
  grep "Alibaba Cloud Linux" /etc/os-release || return

  # 这个阿里云镜像在境内安装也是慢，所以还是用官方源
  #rpm -ivh --nodeps https://mirrors.aliyun.com/remi/enterprise/remi-release-8.rpm
  rpm -ivh --nodeps https://rpms.remirepo.net/enterprise/remi-release-8.rpm

  dnf update -y dnf libdnf

  sed -i 's/PLATFORM_ID="platform:al8"/PLATFORM_ID="platform:el8"/g' /etc/os-release

  # First let’s discover what versions of PHP 7 are available on Remi:
  sudo dnf module list php
  # 这些模块里面都有 php 这个名字，所以得切换
  sudo dnf -y module reset php
  sudo dnf -y module enable php:remi-8.1
  # 共存安装
  sudo dnf install -y php81 php81-php-fpm

  # 不用默认的 fpm 服务
  systemctl status php81-php-fpm.service
  systemctl stop php81-php-fpm.service
  systemctl disable php81-php-fpm.service
  systemctl status php81-php-fpm.service

  # 其他的库
  sudo yum -y install libmaxminddb
  sudo yum -y install php81-php-{cli,fpm,mysqlnd,zip,devel,gd,mbstring,curl,xml,pear,bcmath,json,opcache,redis,memcache,pecl-seaslog,pecl-xdebug3,pgsql,pdo,pecl-rdkafka6,pecl-apcu,maxminddb,gmp,pecl-event}

  sed -i 's/PLATFORM_ID="platform:el8"/PLATFORM_ID="platform:al8"/g' /etc/os-release

  php81 --version
  php81 --modules

  # composer 有点问题，在 remi 仓库里面也有它，但是它绑定默认 php 版本的
  # 所以 php8 共存安装不能安装 composer

  # php81 -r 'phpinfo();' | head -33
  # Loaded Configuration File => /etc/opt/remi/php81/php.ini

  # 修改 php 默认时区为印度时间
  # 因为 date_default_timezone_set 对于 SeasLog 在 fpm 上表现不稳定
  grep timezone /etc/opt/remi/php81/php.ini
  #sed -r -b "s/^;?date\.timezone =.*/date.timezone = Asia\/Shanghai/g" "/etc/opt/remi/php81/php.ini" -i
  sed -r -b "s/^;?date\.timezone =.*/date.timezone = Asia\/Kolkata/g" "/etc/opt/remi/php81/php.ini" -i
  grep timezone /etc/opt/remi/php81/php.ini
}


install_php81_aliyun


