





yylgeodb() {
  sh php/ldb/geoip2/install.sh
}


yylexport() {
  git pull
  env_name="$g_env_name" romysql="$romysql" bash php/ldb/mysql/dbconfigbak/exportdbconfigbak.sh
  php81 hp php/ldb/configbak/ExportConfig.php
  git status
  git add --all
  git commit -a -m yylexport
  git push
}

yylexportdbschema() {
  git pull
  env_name="$g_env_name" romysql="$romysql" bash php/ldb/mysql/dbconfigbak/exportdbschema.sh
  git status
  git add --all
  git commit -a -m yylexport
  git push
}




# timer 与对应的 service 放在同一行

yylls() {

  systemctl list-timers --all | cut -c29-42,71- | grep yyl

  systemctl list-units --all | grep -E '^\s*yyl' | awk -v g_services="$g_services" '

BEGIN {
    n = split(g_services, expected_list, /\s+/);
    for (i = 1; i <= n; i++) {
        if (expected_list[i] != "") {
            expected_map[expected_list[i]] = "true";
        }
    }
}
{
    unit=$1;
    load=$2;
    active=$3;
    name=unit;
    sub(/\.\w+$/, "", name);
    if (unit ~ /\.timer$/) {
        timers[name] = unit " " load " " active;
    } else {
        services[name] = unit " " load " " active;
    }
}
END {
    print "EXPECTED UNIT LOAD ACTIVE service sLOAD sACTIVE";
    print "-------- ---------------------- ------ ------ ---------------------- ------ ------";
    name_count = asorti(services, service_names);
    for (i = 1; i <= name_count; i++) {
        name = service_names[i];
        mark=(name in expected_map) ? expected_map[name] : "FALSE";
        if (name in timers) {
            print mark " " timers[name] " " services[name];
        } else {
            print mark " " services[name];
        }
    }
    name_count = asorti(expected_map, expected_names);
    for (i = 1; i <= name_count; i++) {
        name = expected_names[i];
        if (!(name in services)) {
            print expected_map[name] " " name " MISSING";
        }
    }
    print "-------- ---------------------- ------ ------ ---------------------- ------ ------";
    print "- GotTotal:", length(services);
    print "- ExpectedTotal:", length(expected_map);
}

' | column -t

echo "- Expected: $g_services"

  echo "--------------------------------"
  netstat -n | awk '/^tcp/ {++S[$NF]} END {for(a in S) print a, S[a]}'

  ls -l deploy/nginx/runtime/conf/sites-enabled
}


yylremoveobsoleteservices() {
  for ser in $g_obsolete_services; do
    scremove "$ser"
  done
}

yylinstallservices() {
  for ser in $g_services; do
    scinstall "$ser"
  done
}




yylstart() {
  for ser in $g_services; do
    scstart "$ser"
  done
}

yylstop() {
  for ser in $g_services; do
    scstop "$ser"
  done
}

yylrestartall() {
  for ser in $g_services; do
    screstart "$ser"
  done
}



yylpull() {
  cd "$g_work_dir"

  last_hash="$(git rev-parse HEAD)"
  cur_branch="$(git rev-parse --abbrev-ref HEAD)"

  echo "yylpull: git pull: $cur_branch / $last_hash"
  if ! timeout 300 git pull; then
    send_alert error "❌ 拉取代码 $cur_branch / $last_hash"
    return 1
  fi

  cur_hash="$(git rev-parse HEAD)"

  if [ "$last_hash" != "$cur_hash" ]; then
    send_alert debug "✅ 拉取代码 $cur_branch 从 $last_hash 到 $cur_hash"
    echo "yylpull: reload vars.sh"
    . ./vars.sh
  else
    echo "yylpull: skip: $cur_branch 从 $last_hash 到 $cur_hash"
  fi
}


yylapply() {
  tmp_dir="$g_yylenv_dir/tmp"
  mkdir -p "$tmp_dir"
  last_hash_file="$tmp_dir/last_git_commit_hash.txt"

  last_hash=$(cat "$last_hash_file" 2>/dev/null || true)
  cur_hash="$(git rev-parse HEAD)"

  if [ "$cur_hash" != "$last_hash" ]; then
    # 先更新文件，表示最多应用一次
    echo "$cur_hash" > "$last_hash_file"

    echo "yylapply: APPLY: $last_hash_file $cur_branch 从 $last_hash 到 $cur_hash"

    cur_branch=$(git rev-parse --abbrev-ref HEAD)
    if sh "$g_yylenv_dir/on_git_commit_changed.sh"; then
      send_alert debug "✅ 更新代码 $cur_branch 从 $last_hash 到 $cur_hash"
    else
      err=$?
      send_alert debug "❌ 更新代码 $cur_branch 从 $last_hash 到 $cur_hash err=$err"
    fi
  else
    echo "yylapply: skip: $cur_branch 从 $last_hash 到 $cur_hash"
  fi
}

