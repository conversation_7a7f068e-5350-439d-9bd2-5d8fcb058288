


# 这个脚本预期是被 api.sh source 的，而 api.sh 要求当前目录切换到项目目录！！！
silently_source_env_file() {
  if [ -f "deploy/envfiles/$1" ]; then
    . "deploy/envfiles/$1"
  fi
}
. deploy/envfiles/addr.sh
silently_source_env_file "addr.$g_env_name.sh"
silently_source_env_file "addr.$g_env_name.$g_real_hostname.sh"


g_addr_sh_file="$(pwd)/deploy/envfiles/addr.sh"

yylvars() {
  # 提取文件中的变量名
  VARS=$(grep -oP '^\w+(?==)' "$g_addr_sh_file")

  # 循环遍历变量名并打印它们的值
  for VAR in $VARS
  do
    eval VAL='"'\$$VAR'"'
    
    case "$VAL" in
      *[!0-9]*)  # 非空非纯数字
        echo $VAR="'""$VAL""'"
        ;;
      *)  # 纯数字或空
        echo "$VAR=$VAL"
        ;;
    esac
  done
}


# api_config_addr.sh 后面不需要根据 g_env_name 分派设置各种地址了，已经迁移到 deploy/envfiles 目录了


# mysqldump 不支持引号
wmysql=$(echo "$wmysql" | tr -d '"')
romysql=$(echo "$romysql" | tr -d '"')


alias wredis="redis-cli $wredis"
alias roredis="redis-cli $roredis"
alias wmysql="mysql $wmysql"
alias romysql="mysql $romysql"
alias wclickhouse="clickhouse-client -mn $wclickhouse"
alias roclickhouse="clickhouse-client -mn $roclickhouse"

