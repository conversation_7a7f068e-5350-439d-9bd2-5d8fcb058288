

send_alert() {
  level="$1"
  text="$2"
  my_public_ip=$(curl -s ipinfo.io/ip || echo "my_public_ip")
  my_inner_ip=$({ ip -o route get to ******* || echo "src *******"; } | sed -n 's/.*src \([0-9.]\+\).*/\1/p')
  my_hostname=$(hostname || echo "my_hostname")

  text="$text
-- 💻 $my_hostname $my_inner_ip $my_public_ip"

  # --connect-timeout 1 for service down
  # curl -sS [options] to make curl silent but still show errors when they occur.
  (set -x; curl --connect-timeout 1 -sS --get --data-urlencode "level=$level" --data-urlencode "text=$text" "$g_alertbot_url/send") || true
}


