

set -e
#set -x


# https://stackoverflow.com/questions/29832037/how-to-get-script-directory-in-posix-sh
g_this_dir=$(CDPATH= cd -- "$(dirname -- "$0")" && pwd)
g_cppenv_dir=$(realpath "$g_this_dir/../cppenv")
g_linenv_dir=$(realpath "$g_this_dir/../linenv")
g_yylenv_dir=$(realpath "$g_this_dir/../yylenv")

. "$g_cppenv_dir/cppenv_shbase.sh"


. "$g_yylenv_dir/api.sh"



if [ "$g_env_class" = "test" ]; then
  sh "$g_work_dir/ddl.sh"
  yylinstall
fi



# 每台机器都要安装的软件包
sh "$g_yylenv_dir/install_acmesh.sh"


# 给证书续签定时任务设置回调
install_acmesh_mynotify() {

  cat > ~/.acme.sh/notify/mynotify.sh <<'EOF'

mynotify_send() {
  _subject="$1"
  _content="$2"
  # 0: success, 1: error 2($RENEW_SKIP): skipped
  _statusCode="$3"
  _statusIcon=$([ "$_statusCode" -eq 1 ] && echo "❌" || echo "✅")
  _text="$_statusIcon 证书续签 $_subject: $_content"
  echo "$_text"
  cd /opt/yylbe
  . deploy/yylenv/api.sh
  send_alert debug "$_text"
}

EOF

  # --notify-level  0-禁用 1-错误 2-默认错误和续签 3-全部
  # --notify-mode  0-默认批量 1-单个
  ~/.acme.sh/acme.sh --set-notify --notify-hook mynotify --notify-level 2 --notify-mode 1

}

lazy_call install_acmesh_mynotify 1002



# 检查更新 nginx 配置
sh deploy/nginx/install.sh




if [ "$g_env_class" = "test" ]; then
  yylrestartall
fi




