

# 这个脚本预期就是被 source 的，可能被其他脚本，也可能被交互式 shell，所以不能 set -e
# 调用方必须将当前目录切换到项目目录！！！
g_work_dir=$(pwd)
g_yylenv_dir="$g_work_dir/deploy/yylenv"
g_cppenv_dir="$g_work_dir/deploy/cppenv"






. "$g_yylenv_dir/api_config_host.sh"
. "$g_yylenv_dir/api_config_addr.sh"
. "$g_yylenv_dir/api_config_service.sh"
. "$g_yylenv_dir/api_config_yylinstall.sh"
. "$g_yylenv_dir/api_send_alert.sh"
. "$g_yylenv_dir/api_util_version.sh"
. "$g_yylenv_dir/api_util_sc.sh"
. "$g_yylenv_dir/api_util_yyl.sh"
. "$g_yylenv_dir/api_util_wm.sh"




