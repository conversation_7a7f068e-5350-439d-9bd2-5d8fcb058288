
set -e
#set -x


# 安装命令，启动服务
# https://www.how2shout.com/linux/how-to-install-docker-on-amazon-linux-2023/
install_docker() {
  yum install -y docker

  systemctl start docker
  systemctl enable docker
  which docker


    
  # docker 数据目录
  docker info | grep "Docker Root Dir"

  cat >>/etc/docker/daemon.json <<EOF
{
    "data-root": "/data/runtime/docker"
}
EOF

}


if ! type docker; then
  install_docker
fi


