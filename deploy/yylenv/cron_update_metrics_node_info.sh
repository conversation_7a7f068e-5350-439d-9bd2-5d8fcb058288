
set -e

# 更新该机器相关的指标
mkdir -p /data/metrics


case "$(hostname)" in
  # 如果主机名以 pro1gate- 开头，则是接入机
  pro1gate-*)
    node_role=gate
    ;;
  pro1main*)
    node_role=main
    ;;
  pro1vice*)
    node_role=vice
    ;;
  dev2bj1 | test*mono*)
    node_role=mono
    ;;
  pro*gate*)
    node_role=gate
    ;;
  pro*paygate*)
    node_role=paygate
    ;;
  pro*main*)
    node_role=main
    ;;
  pro*game*)
    node_role=game
    ;;
  pro*vice*)
    node_role=vice
    ;;
  *)
    node_role=unknown
    ;;
esac

node_name="$(hostname)"

echo "yyl_node_info{node_role=\"$node_role\",node_name=\"$node_name\"} 1" > /data/metrics/yyl_node_info.prom


git_commit=`git rev-parse HEAD`
git_branch=`git rev-parse --abbrev-ref HEAD`
git_time=$(TZ='Asia/Shanghai' git log -1 --format=%cd --date=format-local:'%Y-%m-%dT%H:%M:%S%z')

echo "yyl_node_git_info{git_commit=\"$git_commit\",git_branch=\"$git_branch\",git_time=\"$git_time\"} 1" > /data/metrics/yyl_node_git_info.prom


# 如果当前是第 0 分钟，或者没有更新过 yyl_node_public_ip，则更新一下
if [[ ! -e /data/metrics/yyl_node_public_ip.prom ]] || [[ "$(date +%M)" == "00" ]]; then
  node_public_ip=$(curl -s ipinfo.io/ip)
  echo "yyl_node_public_ip{node_public_ip=\"$node_public_ip\"} 1" > /data/metrics/yyl_node_public_ip.prom
fi



rm -f /data/metrics/yyl_node_gate_install_ok.prom

# 安装状态默认 100 表示无意义
yyl_node_install_status=100
# 对于接入机来说，200 成功，400 失败尚未成功
if [[ "$node_role" == "gate" ]] || [[ "$node_role" == "paygate" ]]; then
  yyl_node_install_status=400
  if [[ -e /opt/yylbe/deploy/nginx/runtime/conf/GATE_INSTALL_OK ]]; then
    yyl_node_install_status=200
  fi
fi
echo "yyl_node_install_status $yyl_node_install_status" > /data/metrics/yyl_node_install_status.prom


