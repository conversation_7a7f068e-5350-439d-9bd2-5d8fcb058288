
# 同步版本，后面的代码最多一次执行，失败了就失败了
# no_save 选项只是检测判断，不更新版本文件
sync_version_if_diff() {
  key="${1:?ArgShouldExist}"
  value="${2:?ArgShouldExist}"
  no_save="${3:-}"
  file_path="$HOME/a/cppenv/version/$key.txt"
  file_content=$(cat "$file_path" 2>/dev/null)
  if [ "$file_content" != "$value" ]; then
    if [ "$no_save" = "no_save" ]; then
      return 0 # success, is diff
    fi
    mkdir -p "$(dirname "$file_path")"
    echo "$value" > "$file_path"
    return 0 # success, is diff
  fi
  return 1 # fail, is not diff, is same
}


