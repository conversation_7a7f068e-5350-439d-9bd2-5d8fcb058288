
set -e
#set -x


# node_exporter 确保特定版本被安装
# https://github.com/prometheus/node_exporter
node_exporter_installed_version=$(node_exporter --version 2>/dev/null | awk 'NR==1{print $3}')
if [[ $node_exporter_installed_version != "1.8.1" ]]; then
  cd ~
  rm -r -f tmp/node_exporter
  mkdir -p tmp/node_exporter
  cd ~/tmp/node_exporter
  
  cpu=""
  case $(uname -m) in
      x86_64) cpu="amd64" ;;
      *)      cpu="arm64" ;;
  esac

  wget https://mirror.ghproxy.com/https://github.com/prometheus/node_exporter/releases/download/v1.8.1/node_exporter-1.8.1.linux-$cpu.tar.gz
  tar xfz node_exporter-1.8.1.linux-$cpu.tar.gz
  rm -f /usr/local/bin/node_exporter
  cd node_exporter-1.8.1.linux-$cpu
  cp node_exporter -t /usr/local/bin/
  cd ~
  rm -r -f tmp/node_exporter

fi







