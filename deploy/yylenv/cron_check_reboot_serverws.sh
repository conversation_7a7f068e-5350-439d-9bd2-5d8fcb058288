

set -e
#set -x

# https://stackoverflow.com/questions/29832037/how-to-get-script-directory-in-posix-sh
this_dir=$(CDPATH= cd -- "$(dirname -- "$0")" && pwd)
work_dir=$(realpath "$this_dir/../..")
cd "$work_dir"
. "$work_dir/deploy/yylenv/api.sh"





cron_check_reboot_serverws() {

  if [ "$g_prometheus_url" = "" ]; then
    return
  fi

  query="yyl_concurrent_users"

  # {"status":"success","data":{"resultType":"vector","result":[{"metric":{"__name__":"yyl_concurrent_users","instance":"***********:3311","job":"servermetrics"},"value":[1718547196,"2458"]}]},"stats":{"seriesFetched": "1","executionTimeMsec":0}}
  response=$(curl -s -G --data-urlencode "query=$query" "$g_prometheus_url/api/v1/query")

  # 提取 status 字段
  status=$(echo $response | jq -r '.status')

  # 检查 status 字段是否为 success
  if [ "$status" != "success" ]; then
    echo "Prometheus query failed: $response"
    return
  fi

  # 提取指标值
  value=$(echo $response | jq -r '.data.result[0].value[1]')

  # 检查 jq 是否成功提取值
  if [[ $? -ne 0 || "$value" == "null" ]]; then
    echo "Failed to parse the response or metric value is null: $value"
    return
  fi

  # 检查值是否低于健康线
  health_line=200
  if (( $(echo "$value < $health_line" | bc -l) )) && (( $(echo "$value >= 0" | bc -l) )); then
    echo "The value of the metric is $value and it is between 0 and $health_line."
    send_alert alert "❌ 发现在线低于 $health_line 执行重启 serverws"
    screstart yylserverws
  fi


}



case "$g_node_name" in

  DISABLEpro3main1 | DISABLEpro3main2)
    cron_check_reboot_serverws
    ;;

esac





