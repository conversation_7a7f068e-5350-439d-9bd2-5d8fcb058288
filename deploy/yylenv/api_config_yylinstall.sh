





yylinstallenv() {

  
  # 每台机器都要安装的软件包

  sh "$g_cppenv_dir"/cppenv_install4linux_go.sh

  # 但目前没有使用 docker 的地方
  # bash "g_yylenv_dir"/install_docker.sh

  bash "$g_yylenv_dir"/install_acmesh.sh
  bash "$g_yylenv_dir"/install_node_exporter.sh
  bash "$g_yylenv_dir"/install_openresty.sh
  bash "$g_yylenv_dir"/install_php81_aliyun.sh
  bash "$g_yylenv_dir"/install_php81_aws.sh
  bash "$g_yylenv_dir"/install_git_config.sh


  # 每台机器都安装这个地址数据库
  sh "$g_work_dir"/php/ldb/geoip2/install.sh



  case "$g_node_role" in
    main | mono)

      # kefu 被 php-fpm 执行，有目录权限问题
      mkdir -p "$g_work_dir"/php/kefu/runtime
      chmod 777 "$g_work_dir"/php/kefu/runtime

      # pay 被 php-fpm 执行，有目录权限问题
      mkdir -p "$g_work_dir"/php/pay/runtime
      chmod 777 "$g_work_dir"/php/pay/runtime

      ;;
  esac



  case "$g_node_role" in
    game | mono)

      # api 被 php-fpm 执行，有目录权限问题
      mkdir -p "$g_work_dir"/php/api/session_tmp
      chmod 777 "$g_work_dir"/php/api/session_tmp

      ;;
  esac



  case "$g_node_role" in
    vice | mono)

      mkdir -p /data/runtime/victoria-metrics-data
      mkdir -p /data/runtime/victoria-logs-data
      mkdir -p /data/runtime/grafana
      mkdir -p /data/runtime/alertmanager
      mkdir -p /data/runtime/vmalert

      ;;
  esac


  mkdir -p /data/runtime/vector

  mkdir -p /data/logs



}






yylinstall() {
  echo "################################ yylinstall ################################"
  echo "################ yylremoveobsoleteservices ################"
  yylremoveobsoleteservices
  echo "################ yylinstallenv ################"
  yylinstallenv
  echo "################ deploy/nginx/install.sh ################"
  bash "$g_work_dir"/deploy/nginx/install.sh
  echo "################ yylinstallservices ################"
  yylinstallservices
}



