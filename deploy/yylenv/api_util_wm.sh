






wmstatus() {

echo "============================================================"
date +%Y-%m-%d\ %H:%M:%S\ %z
echo "============================================================"

netstat -n | awk '/^tcp/ {++S[$NF]} END {for(a in S) print a, S[a]}'

if echo "$g_services" | grep -qw "yylkefureg"; then
  echo && echo
  php php/kefu/kefureg.php status | awk 'NF'
fi

if echo "$g_services" | grep -qw "yylkefuws"; then
  echo && echo
  php php/kefu/kefuws.php status | awk 'NF'
fi

if echo "$g_services" | grep -qw "yylkefubiz"; then
  echo && echo
  php php/kefu/kefubiz.php status | awk 'NF'
fi


if echo "$g_services" | grep -qw "yylservertimer"; then
  echo && echo
  php php/server/servertimer.php status | awk 'NF'
fi

if echo "$g_services" | grep -qw "yylserverbiz"; then
  echo && echo
  php php/server/serverbiz.php status | awk 'NF'
fi

if echo "$g_services" | grep -qw "yylserverws"; then
  echo && echo
  php php/server/serverws.php status | awk 'NF'
fi

if echo "$g_services" | grep -qw "yylserverreg"; then
  echo && echo
  php php/server/serverreg.php status | awk 'NF'
fi

if echo "$g_services" | grep -qw "yylserverhttp"; then
  echo && echo
  php php/server/serverhttp.php status | awk 'NF'
fi

if echo "$g_services" | grep -qw "yylcenter"; then
  echo && echo
  php php/center/center.php status | awk 'NF'
fi

}



