


tmp_service_file_is_diff() {
  ! diff -q  "$g_work_dir/deploy/systemd/tmp/$1.service" "/usr/lib/systemd/system/$1.service" >/dev/null 2>&1
}

tmp_timer_file_is_diff() {
  ! diff -q  "$g_work_dir/deploy/systemd/tmp/$1.timer" "/usr/lib/systemd/system/$1.timer" >/dev/null 2>&1
}


service_file_is_diff() {
  ! diff -q  "$g_work_dir/deploy/systemd/$1.service" "/usr/lib/systemd/system/$1.service" >/dev/null 2>&1
}

cat_service_need_daemon_reload() {
  systemctl cat "$1.service" 2>&1 >/dev/null | grep 'service changed on disk, the version systemd has loaded is outdated' >/dev/null
}

cat_timer_need_daemon_reload() {
  systemctl cat "$1.timer" 2>&1 >/dev/null | grep 'timer changed on disk, the version systemd has loaded is outdated' >/dev/null
}

service_file_is_cat_diff() {
  # 必须用 bash 执行，如果用 sh 执行会报错
  # ! diff -q <(systemctl cat "$1.service" 2>/dev/null | tail -n +2) "/usr/lib/systemd/system/$1.service" >/dev/null 2>&1

  tempfile=$(mktemp)
  systemctl cat "$1.service" 2>/dev/null | tail -n +2 > "$tempfile"
  ! diff -q "$tempfile" "/usr/lib/systemd/system/$1.service" >/dev/null 2>&1
  result=$?
  rm -f "$tempfile"
  return $result
}


install_service() {
  echo "######## install_service $1"

  # 检查文件是否存在
  if [ ! -f "$g_work_dir/deploy/systemd/$1.service" ]; then
    echo "ERROR: FileNotFound: $g_work_dir/deploy/systemd/$1.service"
    return 1
  fi

  if service_file_is_diff "$1"; then
    rm -f "/usr/lib/systemd/system/$1.service"
    echo cp "$g_work_dir/deploy/systemd/$1.service" "/usr/lib/systemd/system/$1.service"
    cp "$g_work_dir/deploy/systemd/$1.service" "/usr/lib/systemd/system/$1.service"
  fi
  
  if systemctl is-enabled "$1.service" >/dev/null; then
    echo "already enabled"
  else
    echo systemctl enable "$1.service"
    systemctl enable "$1.service"
  fi

  if systemctl is-active "$1.service" >/dev/null; then
    echo "already active"
  else
    echo systemctl start "$1.service"
    systemctl start "$1.service"
  fi

  if cat_service_need_daemon_reload "$1"; then
    echo systemctl daemon-reload
    systemctl daemon-reload
    #send_alert debug "⚠️ NEED restart $1"
  fi
}


install_timer_by_file() {
  if tmp_timer_file_is_diff "$1"; then
    rm -f "/usr/lib/systemd/system/$1.timer"
    echo cp "$g_work_dir/deploy/systemd/tmp/$1.timer" "/usr/lib/systemd/system/$1.timer"
    cp "$g_work_dir/deploy/systemd/tmp/$1.timer" "/usr/lib/systemd/system/$1.timer"
  fi
  if tmp_service_file_is_diff "$1"; then
    rm -f "/usr/lib/systemd/system/$1.service"
    echo cp "$g_work_dir/deploy/systemd/tmp/$1.service" "/usr/lib/systemd/system/$1.service"
    cp "$g_work_dir/deploy/systemd/tmp/$1.service" "/usr/lib/systemd/system/$1.service"
  fi
  if systemctl is-enabled "$1.timer" >/dev/null && systemctl is-active "$1.timer" >/dev/null; then
    echo "already enabled and active"
  else
    # Failed to get unit file state for yylcron0233.timer: No such file or directory
    echo systemctl disable "$1.timer"
    systemctl disable "$1.timer"
    # enable and start
    # 如果已经启动过了，则再次执行不会有任何影响
    echo systemctl enable --now "$1.timer"
    systemctl enable --now "$1.timer"
  fi

  if cat_timer_need_daemon_reload "$1" || cat_service_need_daemon_reload "$1"; then
    echo systemctl daemon-reload
    systemctl daemon-reload
    #send_alert debug "⚠️ NEED restart $1"
  fi
}


# https://www.freedesktop.org/software/systemd/man/latest/systemd.timer.html
install_cron() {
  echo "######## install_cron $1"
  echo "$2"
  
  name="${1:?ArgShouldExist}"
  spec="${2:?ArgShouldExist}"

  shfile="$g_work_dir/deploy/cron/${name:3}.sh"
  if [ ! -f "$shfile" ]; then
    echo "ERROR: FileNotFound: $shfile"
    return 1
  fi

  mkdir -p "$g_work_dir/deploy/systemd/tmp"
  cat >"$g_work_dir/deploy/systemd/tmp/$1.timer" <<EOF

[Unit]
Description=$name.timer
# Allow manual start
RefuseManualStart=no
# Allow manual stop
RefuseManualStop=no

[Timer]
$spec

[Install]
WantedBy=timers.target

EOF

  cat >"$g_work_dir/deploy/systemd/tmp/$1.service" <<EOF

[Unit]
Description=$name
After=syslog.target network-online.target remote-fs.target nss-lookup.target
Wants=network-online.target

[Service]
Type=simple
User=root
WorkingDirectory=$g_work_dir
ExecStart=/bin/sh $shfile

# https://www.freedesktop.org/software/systemd/man/systemd.resource-control.html
# https://unix.stackexchange.com/questions/494843/how-to-limit-a-systemd-service-to-play-nice-with-the-cpu
# CentOS7 不支持
#CPUWeight=20
#CPUQuota=85%
#IOWeight=20

EOF

  install_timer_by_file "$1"

}









remove_service() {
  echo "######## remove_service $1"

  # 如果内存中有这个服务才执行内存中的操作
  if systemctl cat "$1.service" >/dev/null 2>&1; then
    systemctl stop "$1.service" && echo "$1.service stopped successfully" || echo "failed to stop $1.service"
    systemctl disable "$1.service" && echo "$1.service disabled successfully" || echo "failed to disable $1.service"
    rm -f "/usr/lib/systemd/system/$1.service"
    systemctl daemon-reload && echo "systemctl daemon-reload successfully" || echo "failed to systemctl daemon-reload"
  fi
  # 确保文件系统中没有这个服务
  rm -f "/usr/lib/systemd/system/$1.service"
}

remove_timer() {
  echo "######## remove_timer $1"

  if systemctl cat "$1.timer" >/dev/null 2>&1; then
    systemctl stop "$1.timer" && echo "$1.timer stopped successfully" || echo "failed to stop $1.timer"
    systemctl disable "$1.timer" && echo "$1.timer disabled successfully" || echo "failed to disable $1.timer"
  fi
  rm -f "/usr/lib/systemd/system/$1.timer"

  remove_service "$1"
}







start_service() {
  echo "######## start_service $1"

  if [ ! -e "/usr/lib/systemd/system/$1.service" ]; then
    echo "ERROR: FileNotFound: /usr/lib/systemd/system/$1.service"
    return 1
  fi
  
  if systemctl is-active "$1.service" >/dev/null; then
    echo "already active"
  else
    echo systemctl start "$1.service"
    systemctl start "$1.service"
    return $?
  fi

}

start_timer() {
  echo "######## start_timer $1"

  if [ ! -e "/usr/lib/systemd/system/$1.timer" ]; then
    echo "ERROR: FileNotFound: /usr/lib/systemd/system/$1.timer"
    return 1
  fi
  
  if systemctl is-active "$1.timer" >/dev/null; then
    echo "already active"
  else
    echo systemctl start "$1.timer"
    systemctl start "$1.timer"
    return $?
  fi

}




stop_service() {
  echo "######## stop_service $1"

  if ! systemctl cat "$1.service" >/dev/null 2>&1; then
    echo "ERROR: ServiceNotFound: $1.service"
    return 1
  fi

  if systemctl is-active "$1.service" >/dev/null; then
    echo systemctl stop "$1.service"
    systemctl stop "$1.service"
    return $?
  else
    echo "already stopped"
  fi
}

stop_timer() {
  echo "######## stop_timer $1"

  if ! systemctl cat "$1.timer" >/dev/null 2>&1; then
    echo "ERROR: TimerNotFound: $1.timer"
    return 1
  fi

  if systemctl is-active "$1.timer" >/dev/null; then
    echo systemctl stop "$1.timer"
    systemctl stop "$1.timer"
    return $?
  else
    echo "already stopped"
  fi

}




restart_service() {
  echo "######## restart_service $1"

  echo systemctl restart "$1.service"
  systemctl restart "$1.service"
}


reload_service() {
  echo "######## reload_service $1"

  if ! systemctl cat "$1.service" >/dev/null 2>&1; then
    echo "ERROR: ServiceNotFound: $1.service"
    return 1
  fi

  if systemctl is-active "$1.service" >/dev/null; then
    echo systemctl reload "$1.service"
    systemctl reload "$1.service"
    return $?
  else
    echo systemctl start "$1.service"
    systemctl start "$1.service"
    return $?
  fi
}












scinstall() {
  spec_var="g_cron_spec__$1"
  spec_val=$(eval "echo \$$spec_var")
  if [ -z "$spec_val" ]; then
    install_service "$1"
  else
    install_cron "$1" "$spec_val"
  fi
}

scremove() {
  spec_var="g_cron_spec__$1"
  spec_val=$(eval "echo \$$spec_var")
  if [ -z "$spec_val" ]; then
    remove_service "$1"
  else
    remove_timer "$1"
  fi
}


scstart() {
  spec_var="g_cron_spec__$1"
  spec_val=$(eval "echo \$$spec_var")
  if [ -z "$spec_val" ]; then
    start_service "$1"
    return $?
  else
    start_timer "$1"
    return $?
  fi
}

scstop() {
  spec_var="g_cron_spec__$1"
  spec_val=$(eval "echo \$$spec_var")
  if [ -z "$spec_val" ]; then
    stop_service "$1"
    return $?
  else
    stop_timer "$1"
    return $?
  fi
}

screstart() {
  spec_var="g_cron_spec__$1"
  spec_val=$(eval "echo \$$spec_var")
  if [ -z "$spec_val" ]; then
    restart_service "$1"
  fi
}


screload() {
  spec_var="g_cron_spec__$1"
  spec_val=$(eval "echo \$$spec_var")
  if [ -z "$spec_val" ]; then
    reload_service "$1"
  fi
}




sclogf() {
  journalctl -u "$1".service -f
}

sclog() {
  journalctl -u "$1".service
}

scstatus() {
  systemctl --no-pager status "$1".service
}

scls() {
  systemctl list-units --all | grep "$1"
}











