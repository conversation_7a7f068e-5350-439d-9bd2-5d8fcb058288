

set -e
#set -x

# https://stackoverflow.com/questions/29832037/how-to-get-script-directory-in-posix-sh
g_this_dir=$(CDPATH= cd -- "$(dirname -- "$0")" && pwd)
g_yylenv_dir=$(realpath "$g_this_dir/../yylenv")

. "$g_yylenv_dir/api.sh"






install_php83_aws() {
  #type php83 && return


  ################################################################################
  # 安装 php 及其扩展
  ################################################################################

  dnf install -y php8.3-{cli,fpm,devel} php-pear gcc

  yum -y install php8.3-{opcache,mbstring,gmp,bcmath,zip,xml,gd,pdo,mysqlnd,pgsql,ffi}

  pear update-channels
  pecl update-channels

  ## curl json 核心内置
  ## rdkafka memcache event 没用到
  ## xdebug seaslog apcu maxminddb redis 必须的

  rm -f /etc/php.d/15-xdebug.ini
  yes no | pecl upgrade xdebug-3.3.2
  echo 'zend_extension=xdebug.so' > /etc/php.d/15-xdebug.ini

  rm -f /etc/php.d/40-seaslog.ini
  # 不要 SeasLog 了

  rm -f /etc/php.d/40-apcu.ini
  yes no | pecl upgrade apcu-5.1.24
  echo 'extension=apcu.so' > /etc/php.d/40-apcu.ini

  # 用于 ip 地理位置查询
  yum -y install libmaxminddb-devel
  rm -f /etc/php.d/40-maxminddb.ini
  yes no | pecl upgrade maxminddb-1.12.0
  echo 'extension=maxminddb.so' > /etc/php.d/40-maxminddb.ini

  # 图片操作目前没用
  dnf -y install ImageMagick-devel
  rm -f /etc/php.d/40-imagick.ini
  yes no | pecl upgrade imagick-3.7.0
  echo 'extension=imagick.so' > /etc/php.d/40-imagick.ini

  # redis 及其依赖

  rm -f /etc/php.d/40-igbinary.ini
  rm -f /etc/php.d/50-redis.ini

  # redis 默认依赖 igbinary
  yes no | pecl upgrade igbinary-3.2.16

  dnf install -y redis6-devel lz4-devel
  # https://pecl.php.net/package/redis
  # pecl upgrade redis 幂等执行也会报错
  if ! pecl list | grep "redis\s*6.1.0"; then
    yes no | pecl upgrade redis-6.1.0
  fi

  echo 'extension=igbinary.so' > /etc/php.d/40-igbinary.ini
  echo 'extension=redis.so' > /etc/php.d/50-redis.ini

  rm -f /etc/php.d/40-rdkafka.ini
  # 不要 rdkafka 了


  # https://pecl.php.net/package/event
  # 用于优化 workerman 网络性能
  # https://www.workerman.net/doc/workerman/install/install.html
  # 注意提示：Include libevent OpenSSL support [yes] : 时输入no回车，其它直接敲回车就行
  # workerman 要求不启用该扩展的 openssl 支持
  # Enable internal debugging in Event [no] : 
  # Enable sockets support in Event [yes] : 
  # libevent installation prefix [/usr] : 
  # Include libevent's pthreads library and enable thread safety support in Event [no] : 
  # Include libevent protocol-specific functionality support including HTTP, DNS, and RPC [yes] : 
  # Include libevent OpenSSL support [yes] : no
  # PHP Namespace for all Event classes [no] : 
  # openssl installation prefix [yes] :
  yum -y install libevent-devel
  rm -f /etc/php.d/40-event.ini
  echo "no
yes
/usr
no
yes
no
no
yes
" | pecl upgrade event-3.1.4
  echo 'extension = event.so' > /etc/php.d/40-event.ini


  # php 版本查看
  php --modules
  php --version
  php -r 'phpinfo();' | head -33

  # php81 这个符号得有 
  ln -sf /usr/bin/php /usr/bin/php81
  # 暂时兼容一下这个符号
  mkdir -p /opt/remi/php81/root/usr/sbin
  ln -sf /usr/sbin/php-fpm /opt/remi/php81/root/usr/sbin/php-fpm

  # composer 安装
  sudo curl -sS https://getcomposer.org/installer | php
  mv -f composer.phar /usr/local/bin/composer
  export  COMPOSER_ALLOW_SUPERUSER=1
  composer -V
  composer diagnose

  # 不用默认的 php-fpm 服务
  systemctl status php-fpm.service || true
  systemctl stop php-fpm.service
  systemctl disable php-fpm.service

  # 修改 php 默认时区为印度时间
  # 因为 date_default_timezone_set 对于 SeasLog 在 fpm 上表现不稳定
  grep timezone /etc/php.ini
  #sed -r -b "s/^;?date\.timezone =.*/date.timezone = Asia\/Shanghai/g" "/etc/php.ini" -i
  sed -r -b "s/^;?date\.timezone =.*/date.timezone = Asia\/Kolkata/g" "/etc/php.ini" -i
  grep timezone /etc/php.ini




}



if grep "Amazon Linux" /etc/os-release; then
  if sync_version_if_diff install_php83_aws 1 no_save; then
    echo "install_php83_aws"
    
    install_php83_aws

    sync_version_if_diff install_php83_aws 1
  fi
fi

