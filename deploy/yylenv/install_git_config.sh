
set -e
#set -x

# https://stackoverflow.com/questions/29832037/how-to-get-script-directory-in-posix-sh
g_this_dir=$(CDPATH= cd -- "$(dirname -- "$0")" && pwd)
g_yylenv_dir=$(realpath "$g_this_dir/../yylenv")

. "$g_yylenv_dir/api.sh"



if sync_version_if_diff install_git_config 1; then
  echo "install_git_config"
  #cd /opt/yylbe


  git config --global pull.rebase false
  git config --global user.email "root@$(hostname)"
  git config --global user.name "root"

fi


