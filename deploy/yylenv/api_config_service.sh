


g_obsolete_services="yylcron10s yylcron15s yylcron0600 yylcron0800 yylvictorialogs yylalertchannel yylvector yylfpm"


case "$g_node_role" in
  main)
    g_obsolete_services="$g_obsolete_services yylvectorr"
    ;;
esac



# https://www.freedesktop.org/software/systemd/man/latest/systemd.time.html
g_cron_spec__yylcron1m="OnCalendar=*-*-* *:*:00"
g_cron_spec__yylcron5m="OnCalendar=*:0/5"
g_cron_spec__yylcron30m="OnCalendar=*:0,30"
# 印度时间的每小时 35 分
g_cron_spec__yylcron1h="OnCalendar=*:05"
g_cron_spec__yylcron1h29="OnCalendar=*:29"
g_cron_spec__yylcron0233="OnCalendar=*-*-* 02:33:00"
g_cron_spec__yylcron0500="OnCalendar=*-*-* 05:00:00"
g_cron_spec__yylcron1030="OnCalendar=*-*-* 10:30:00"


g_services="yylcron1m yylcron5m yylcron30m yylcron1h yylcron1h29 yylcron0233 yylcron0500 yylcron1030"
g_services="$g_services yylnginx yylnodeexporter"



add_service_list_by_role() {
  case "$1" in
    main)
      g_services="$g_services yylpushmetrics yylfpmus yylserverreg yylkefureg yylkefuws yylcenter yylserverhttp yylservercheck yylkefubiz yylcronwm yylpmconsumer yyldotserver yylattributionserver yylmybuf"
      ;;
    game)
      g_services="$g_services yylpushmetrics yylfpmus yylserverreg yylserverws yylserverbiz yylservertimer"
      ;;
    vice)
      g_services="$g_services yylpushmetrics yylgitea yylmetabase yylgrafana yylvictoriametrics yylvmalert yylalertmanager yylalertbot yylservermetrics yylchbuf yylsdserver"
      ;;
  esac

}

case "$g_node_role" in
  mono)
    add_service_list_by_role main
    add_service_list_by_role game
    add_service_list_by_role vice
    g_services=$(echo "$g_services" | tr -s '[:space:]' '\n' | grep -Ev '^(yylgitea)$' | tr '\n' ' ')
    ;;
  *)
    add_service_list_by_role "$g_node_role"
    ;;
esac



# yylservertimer 目前不支持多节点
case "$g_node_name" in
  pro3game3)
    # g_services 移除 yylservertimer
    g_services=$(echo "$g_services" | tr -s '[:space:]' '\n' | grep -Ev '^(yylservertimer)$' | tr '\n' ' ')
    ;;
esac

# 测试服移除 yylmetabase
case "$g_node_role" in
  mono)
    g_services=$(echo "$g_services" | tr -s '[:space:]' '\n' | grep -Ev '^(yylmetabase)$' | tr '\n' ' ')
    ;;
esac



# 去重会乱序，没有重复的，不需要去重
# g_services=$(echo "$g_services" | tr -s '[:space:]' '\n' | sort | uniq | tr '\n' ' ')
# g_obsolete_services=$(echo "$g_obsolete_services" | tr -s '[:space:]' '\n' | sort | uniq | tr '\n' ' ')


# 去重但保持顺序
g_services=$(echo "$g_services" | tr -s '[:space:]' '\n' | awk '!seen[$0]++' | tr '\n' ' ')
g_obsolete_services=$(echo "$g_obsolete_services" | tr -s '[:space:]' '\n' | awk '!seen[$0]++' | tr '\n' ' ')


