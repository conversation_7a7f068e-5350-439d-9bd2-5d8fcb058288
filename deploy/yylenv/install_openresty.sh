
set -e
#set -x


# 只负责相关命令可用，不负责安装服务
# https://openresty.org/en/linux-packages.html#alibaba-cloud-linux
# https://openresty.org/en/linux-packages.html#amazon-linux
install_openresty() {
  if grep "Amazon Linux" /etc/os-release; then
    curl -k -o /etc/yum.repos.d/openresty.repo https://openresty.org/package/amazon/openresty.repo
  else
    curl -k -o /etc/yum.repos.d/openresty.repo https://openresty.org/package/alinux/openresty.repo
  fi

  yum check-update
  yum --disablerepo="*" --enablerepo="openresty" list available
  yum install -y openresty openresty-resty openresty-opm openresty-doc

  # 不用它的默认服务
  systemctl stop openresty.service
  systemctl disable openresty.service

  which openresty
  which resty
  which opm
  which restydoc
}


if ! type openresty; then
  install_openresty
fi


