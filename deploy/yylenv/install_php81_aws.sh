

set -e
#set -x

# https://stackoverflow.com/questions/29832037/how-to-get-script-directory-in-posix-sh
g_this_dir=$(CDPATH= cd -- "$(dirname -- "$0")" && pwd)
g_yylenv_dir=$(realpath "$g_this_dir/../yylenv")

. "$g_yylenv_dir/api.sh"






install_php81_aws() {
  #type php81 && return


  ################################################################################
  # 安装 php 及其扩展
  ################################################################################

  dnf install -y php8.1-{cli,fpm,devel} php-pear gcc

  yum -y install php8.1-{opcache,mbstring,gmp,bcmath,zip,xml,gd,pdo,mysqlnd,pgsql}

  pear update-channels
  pecl update-channels

  ## curl json 核心内置
  ## rdkafka memcache event 没用到
  ## xdebug seaslog apcu maxminddb redis 必须的

  rm -f /etc/php.d/15-xdebug.ini
  yes no | pecl upgrade xdebug-3.3.2
  echo 'zend_extension=xdebug.so' > /etc/php.d/15-xdebug.ini

  rm -f /etc/php.d/40-seaslog.ini
  yes no | pecl upgrade seaslog-2.2.0
  echo 'extension=seaslog.so' > /etc/php.d/40-seaslog.ini

  rm -f /etc/php.d/40-apcu.ini
  yes no | pecl upgrade apcu-5.1.23
  echo 'extension=apcu.so' > /etc/php.d/40-apcu.ini

  # 用于 ip 地理位置查询
  yum -y install libmaxminddb-devel
  rm -f /etc/php.d/40-maxminddb.ini
  yes no | pecl upgrade maxminddb-1.11.1
  echo 'extension=maxminddb.so' > /etc/php.d/40-maxminddb.ini

  # redis 及其依赖

  rm -f /etc/php.d/40-igbinary.ini
  rm -f /etc/php.d/50-redis.ini

  # redis 默认依赖 igbinary
  yes no | pecl upgrade igbinary-3.2.15

  dnf install -y redis6-devel lz4-devel
  # https://pecl.php.net/package/redis
  # pecl upgrade redis 幂等执行也会报错
  if ! pecl list | grep "redis\s*5.3.7"; then
    yes no | pecl upgrade redis-5.3.7
  fi

  echo 'extension=igbinary.so' > /etc/php.d/40-igbinary.ini
  echo 'extension=redis.so' > /etc/php.d/50-redis.ini

  # https://docs.confluent.io/platform/current/installation/installing_cp/rhel-centos.html#get-the-software
  echo '[Confluent]
name=Confluent repository
baseurl=https://packages.confluent.io/rpm/7.6
gpgcheck=0
enabled=1

[Confluent-Clients]
name=Confluent Clients repository
baseurl=https://packages.confluent.io/clients/rpm/centos/9/$basearch
gpgcheck=0
enabled=1
'> /etc/yum.repos.d/confluent.repo
  # https://pecl.php.net/package/rdkafka
  # https://github.com/arnaud-lb/php-rdkafka
  yum -y install librdkafka-devel
  # pecl upgrade rdkafka 幂等执行也会报错
  if ! pecl list | grep "rdkafka\s*6.0.2"; then
    yes no | pecl upgrade rdkafka-6.0.2
  fi
  echo 'extension=rdkafka.so' > /etc/php.d/40-rdkafka.ini


  # https://pecl.php.net/package/event
  # 用于优化 workerman 网络性能
  # https://www.workerman.net/doc/workerman/install/install.html
  # 注意提示：Include libevent OpenSSL support [yes] : 时输入no回车，其它直接敲回车就行
  # workerman 要求不启用该扩展的 openssl 支持
  # Enable internal debugging in Event [no] : 
  # Enable sockets support in Event [yes] : 
  # libevent installation prefix [/usr] : 
  # Include libevent's pthreads library and enable thread safety support in Event [no] : 
  # Include libevent protocol-specific functionality support including HTTP, DNS, and RPC [yes] : 
  # Include libevent OpenSSL support [yes] : no
  # PHP Namespace for all Event classes [no] : 
  # openssl installation prefix [yes] :
  yum -y install libevent-devel
  rm -f /etc/php.d/40-event.ini
  echo "no
yes
/usr
no
yes
no
no
yes
" | pecl upgrade event-3.1.3
  echo 'extension = event.so' > /etc/php.d/40-event.ini


  # php 版本查看
  php --modules
  php --version
  php -r 'phpinfo();' | head -33

  # php81 这个符号得有 
  ln -sf /usr/bin/php /usr/bin/php81
  # 暂时兼容一下这个符号
  mkdir -p /opt/remi/php81/root/usr/sbin
  ln -sf /usr/sbin/php-fpm /opt/remi/php81/root/usr/sbin/php-fpm

  # composer 安装
  sudo curl -sS https://getcomposer.org/installer | php
  mv -f composer.phar /usr/local/bin/composer
  export  COMPOSER_ALLOW_SUPERUSER=1
  composer -V
  composer diagnose

  # 不用默认的 php-fpm 服务
  systemctl status php-fpm.service || true
  systemctl stop php-fpm.service
  systemctl disable php-fpm.service

  # 修改 php 默认时区为印度时间
  # 因为 date_default_timezone_set 对于 SeasLog 在 fpm 上表现不稳定
  grep timezone /etc/php.ini
  #sed -r -b "s/^;?date\.timezone =.*/date.timezone = Asia\/Shanghai/g" "/etc/php.ini" -i
  sed -r -b "s/^;?date\.timezone =.*/date.timezone = Asia\/Kolkata/g" "/etc/php.ini" -i
  grep timezone /etc/php.ini




}



if grep "Amazon Linux" /etc/os-release; then
  if sync_version_if_diff install_php81_aws 1 no_save; then
    echo "install_php81_aws"
    
    install_php81_aws

    sync_version_if_diff install_php81_aws 1
  fi
fi

