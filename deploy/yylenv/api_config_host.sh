

g_real_hostname="$(hostname)"
g_node_name="$g_real_hostname"
g_internal_ip="$(ifconfig | grep 'inet ' | grep -v '127.0.0.1' | awk '{print $2}')"
if [ "${OS:-}" = "Windows_NT" ] || [ "$(uname)" = "Darwin" ]; then
  g_node_name="local1mono1"
  g_env_name="local1"
  g_env_global_name="$g_real_hostname"
else
  g_env_name=$(echo "$g_node_name" | sed -En 's/^([a-z]+[0-9]+)[a-z]+.+/\1/p')
  g_env_global_name="$g_env_name"
fi
g_env_class=$(echo "$g_node_name" | sed -En 's/^([a-z]+)[0-9]+[a-z]+.+/\1/p')
g_node_role=$(echo "$g_node_name" | sed -En 's/^[a-z]+[0-9]+([a-z]+).+/\1/p')
g_node_local_name=$(echo "$g_node_name" | sed -En 's/^[a-z]+[0-9]+([a-z]+.+)/\1/p')



if [ "$g_node_name" = "dev2bj1" ]; then
  g_env_class="test"
  g_node_role="mono"
fi

# echo "g_real_hostname=$g_real_hostname"
# echo "g_node_name=$g_node_name"
# echo "g_env_name=$g_env_name"
# echo "g_env_global_name=$g_env_global_name"
# echo "g_env_class=$g_env_class"
# echo "g_node_role=$g_node_role"
# echo "g_node_local_name=$g_node_local_name"


