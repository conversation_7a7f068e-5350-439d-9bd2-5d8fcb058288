# curl http://172.16.2.198:3301/metrics
# curl http://127.0.0.1:3301/metrics
# curl http://172.16.2.198:8000/index.php?op=metrics
# servermetrics  curl http://127.0.0.1:3311/metrics

# https://victoriametrics.teenpattihive.com/
# After changes were made, trigger config re-read with the command:
# curl https://victoriametrics.teenpattihive.com/-/reload
# curl http://127.0.0.1:8428/-/reload

scrape_configs:
  - job_name: pushmetrics
    #static_configs:
    #  - targets: ["172.16.2.237:3301"]
    #  - targets: ["172.16.2.198:3301"]
    #  - targets: ["127.0.0.1:3301"]
    http_sd_configs:
      - url: "http://127.0.0.1:3322/sd/discover?service=pushmetrics"

  - job_name: servermetrics
    #static_configs:
    #  - targets: ["127.0.0.1:3311"]
    http_sd_configs:
      - url: "http://127.0.0.1:3322/sd/discover?service=servermetrics&unique=1"
    scrape_timeout: 25s # 这个目前好多 redis 访问

  - job_name: nodes
    http_sd_configs:
      - url: "http://127.0.0.1:3322/sd/discover?service=node_exporter"
    # metrics_path 字段默认为 /metrics
    # The HTTP resource path on which to fetch metrics from targets.
