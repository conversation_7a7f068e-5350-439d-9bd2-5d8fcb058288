# Rules within a group are run sequentially at a regular interval.
groups:
  - # The name of the group.
    # Must be unique within a file.
    # 这个名字不重要，我们就用一个分组就行了
    name: example
    # How often rules in the group are evaluated.
    # [ interval: <duration> | default = global.evaluation_interval ]
    rules:
      - alert: 无充值回调近十分钟
        expr: sum(increase(yyl_pay_recharge_callback_success_total{manual='0'}[10m])) == 0 and sum(increase(yyl_pay_recharge_callback_success_total{manual='0'}[180m])) > 40 unless ((hour() + 8) % 24 >= 3 and (hour() + 8) % 24 < 9)
        labels:
          team: alert

      - alert: 充值到账波动
        expr: sum(increase(yyl_pay_deposit_request_total[15m])) < sum(increase(yyl_pay_recharge_callback_success_total[15m])) - 22
        labels:
          team: alertX
        annotations:
          description: >-
            充值到账波动  十五分钟到账数 {{$value}}  十五分钟回调数
            {{ query "sum(increase(yyl_pay_recharge_callback_success_total[15m]))" | first | value }}

      - alert: 最近总代收成功率低
        expr: >
          (
            (
              sum (increase(yyl_pay_payin_sdk_callback_success_total[15m]))
              /
              sum (increase(yyl_pay_payin_sdk_start_result_total{result=~'1|0'}[15m] offset 1m))
            )
            and sum (increase(yyl_pay_payin_sdk_start_result_total{result=~'1|0'}[15m] offset 1m)) >= 10
            or
            (
              sum (increase(yyl_pay_payin_sdk_callback_success_total[1h]))
              /
              sum (increase(yyl_pay_payin_sdk_start_result_total{result=~'1|0'}[1h] offset 1m))
            )
            and sum (increase(yyl_pay_payin_sdk_start_result_total{result=~'1|0'}[1h] offset 1m)) >= 10
          ) < 0.2
        # unless on () ((hour() + 8) % 24 >= 3 and (hour() + 8) % 24 < 9)
        for: 2m
        annotations:
          description: >-
            最近总代收成功率低 {{$labels.sdk}} {{$value| humanizePercentage}} {{ query (printf "sum (increase(yyl_pay_payin_sdk_callback_success_total{sdk='%s'}[15m]))" $labels.sdk)  | first | value}}/{{ query (printf "sum (increase(yyl_pay_payin_sdk_start_result_total{sdk='%s',result=~'1|0'}[15m] offset 1m))" $labels.sdk) | first | value }} <a href="https://grafana.teenpattihive.com/d/f4b193a5-35c3-41da-bed9-cffe8e90d198/5pSv5LuY5oql6K2m?orgId=1&theme=light&viewPanel=23">图表</a>
        labels:
          team: alert

      - alert: 最近代收成功率低
        expr: >
          (
            (
              sum by (sdk) (increase(yyl_pay_payin_sdk_callback_success_total[15m]))
              /
              sum by (sdk) (increase(yyl_pay_payin_sdk_start_result_total{result=~'1|0'}[15m] offset 1m))
            )
            and sum by (sdk) (increase(yyl_pay_payin_sdk_start_result_total{result=~'1|0'}[15m] offset 1m)) >= 10
            or
            (
              sum by (sdk) (increase(yyl_pay_payin_sdk_callback_success_total[1h]))
              /
              sum by (sdk) (increase(yyl_pay_payin_sdk_start_result_total{result=~'1|0'}[1h] offset 1m))
            )
            and sum by (sdk) (increase(yyl_pay_payin_sdk_start_result_total{result=~'1|0'}[1h] offset 1m)) >= 10
          ) < 0.2
        # unless on () ((hour() + 8) % 24 >= 3 and (hour() + 8) % 24 < 9)
        for: 2m
        annotations:
          description: >-
            最近代收成功率低 {{$labels.sdk}} {{$value| humanizePercentage}} {{ query (printf "sum by (sdk) (increase(yyl_pay_payin_sdk_callback_success_total{sdk='%s'}[15m]))" $labels.sdk)  | first | value}}/{{ query (printf "sum by (sdk) (increase(yyl_pay_payin_sdk_start_result_total{sdk='%s',result=~'1|0'}[15m] offset 1m))" $labels.sdk) | first | value }} {{ query (printf "sum by (sdk) (increase(yyl_pay_payin_sdk_callback_success_total{sdk='%s'}[1h]))" $labels.sdk)  | first | value}}/{{ query (printf "sum by (sdk) (increase(yyl_pay_payin_sdk_start_result_total{sdk='%s',result=~'1|0'}[1h] offset 1m))" $labels.sdk) | first | value }} <a href="https://grafana.teenpattihive.com/d/f4b193a5-35c3-41da-bed9-cffe8e90d198/5pSv5LuY5oql6K2m?orgId=1&theme=light&viewPanel=24">图表</a>
        labels:
          team: alertX

      - alert: 代收成功率低近十五分钟
        expr: >
          (
            (
              sum by (sdk) (increase(yyl_pay_payin_sdk_callback_success_total[15m]))
              /
              sum by (sdk) (increase(yyl_pay_payin_sdk_start_result_total{result=~'1|0'}[15m] offset 1m))
            ) < 0.2
            and sum by (sdk) (increase(yyl_pay_payin_sdk_start_result_total{result=~'1|0'}[15m] offset 1m)) >= 6
            or
            (
              sum by (sdk) (increase(yyl_pay_payin_sdk_callback_success_total[15m]))
              /
              sum by (sdk) (increase(yyl_pay_payin_sdk_start_result_total{result=~'1|0'}[15m] offset 1m))
            ) < 0.3
            and sum by (sdk) (increase(yyl_pay_payin_sdk_start_result_total{result=~'1|0'}[15m] offset 1m)) >= 30
          )
          unless on () ((hour() + 8) % 24 >= 3 and (hour() + 8) % 24 < 9)
        for: 2m
        annotations:
          description: >-
            代收成功率低近十五分钟 {{$labels.sdk}} {{$value| humanizePercentage}} {{ query (printf "sum by (sdk) (increase(yyl_pay_payin_sdk_callback_success_total{sdk='%s'}[15m]))" $labels.sdk)  | first | value}}/{{ query (printf "sum by (sdk) (increase(yyl_pay_payin_sdk_start_result_total{sdk='%s',result=~'1|0'}[15m] offset 1m))" $labels.sdk) | first | value }} <a href="https://grafana.teenpattihive.com/d/f4b193a5-35c3-41da-bed9-cffe8e90d198/5pSv5LuY5oql6K2m?orgId=1&theme=light&viewPanel=4">图表</a>
        labels:
          team: alertX

      - alert: 代收成功率低近一小时
        expr: >
          (
            (
              sum by (sdk) (increase(yyl_pay_payin_sdk_callback_success_total[1h]))
              /
              sum by (sdk) (increase(yyl_pay_payin_sdk_start_result_total{result=~'1|0'}[1h] offset 1m))
            ) < 0.2
            and sum by (sdk) (increase(yyl_pay_payin_sdk_start_result_total{result=~'1|0'}[1h] offset 1m)) >= 6
            or
            (
              sum by (sdk) (increase(yyl_pay_payin_sdk_callback_success_total[1h]))
              /
              sum by (sdk) (increase(yyl_pay_payin_sdk_start_result_total{result=~'1|0'}[1h] offset 1m))
            ) < 0.3
            and sum by (sdk) (increase(yyl_pay_payin_sdk_start_result_total{result=~'1|0'}[1h] offset 1m)) >= 30
          )
          and on () ((hour() + 8) % 24 >= 3 and (hour() + 8) % 24 < 9)
        annotations:
          description: >-
            代收成功率低近一小时 {{$labels.sdk}} {{$value| humanizePercentage}} {{ query (printf "sum by (sdk) (increase(yyl_pay_payin_sdk_callback_success_total{sdk='%s'}[1h]))" $labels.sdk)  | first | value}}/{{ query (printf "sum by (sdk) (increase(yyl_pay_payin_sdk_start_result_total{sdk='%s',result=~'1|0'}[1h] offset 1m))" $labels.sdk) | first | value }} <a href="https://grafana.teenpattihive.com/d/f4b193a5-35c3-41da-bed9-cffe8e90d198/5pSv5LuY5oql6K2m?orgId=1&theme=light&viewPanel=6">图表</a>
        labels:
          team: alertX
