# Rules within a group are run sequentially at a regular interval.
groups:
  - # The name of the group.
    # Must be unique within a file.
    # 这个名字不重要，我们就用一个分组就行了
    name: sms
    # How often rules in the group are evaluated.
    # [ interval: <duration> | default = global.evaluation_interval ]
    rules:
      - alert: 短信通道余额低于最近两周消耗
        expr: >
          PullSMSProviderBalance < (PullSMSProviderBalance offset 2w - PullSMSProviderBalance) and PullSMSProviderBalance offset 2w > 0
          and on() ((hour() + 8) % 24 >= 10 and (hour() + 8) % 24 < 12)
        labels:
          team: alert
        annotations:
          description: >-
            短信通道余额低于最近两周消耗 {{$labels.provider}}
            {{ printf "%.2f" $value }} <
            {{ printf "%.2f" (query "PullSMSProviderBalance offset 2w - PullSMSProviderBalance" | first | value) }}
