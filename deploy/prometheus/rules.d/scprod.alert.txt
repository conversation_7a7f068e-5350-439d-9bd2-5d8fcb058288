# Rules within a group are run sequentially at a regular interval.
groups:
  - # The name of the group.
    # Must be unique within a file.
    # 这个名字不重要，我们就用一个分组就行了
    name: example
    # How often rules in the group are evaluated.
    # [ interval: <duration> | default = global.evaluation_interval ]
    rules:
      - # The name of the alert.
        # Must be a valid metric name.
        # 发到 Alertmanager 后就是 labels.alertname
        alert: 有 FATAL 日志
        # The PromQL expression to evaluate.
        # Every evaluation cycle this is evaluated at the current time,
        # and all resultant time series become pending/firing alerts.
        # 加 ceil 避免报警文本中一堆小数
        expr: 'ceil(increase(llog_in_n{level="FATAL"}[1m])) > 9990'
        # Alerts are considered firing once they have been returned for this long.
        # Alerts which have not yet fired for long enough are considered pending.
        # [ for: <duration> | default = 0s ]
        # 可选的 for 参数用于过滤掉瞬时抖动

        # The labels clause allows specifying a set of additional labels to be attached to the alert.
        # Any existing conflicting labels will be overwritten.
        # The label values can be templated.
        labels:
          # [ <labelname>: <tmpl_string> ]
          severity: critical
        # The annotations clause specifies a set of informational labels
        # that can be used to store longer additional information such as alert descriptions or runbook links.
        # The annotation values can be templated.
        annotations:
          # [ <labelname>: <tmpl_string> ]
          description: >-
            {{$labels.hostname}}{{$labels.cwd}}/
            有 {{$value}} 条 FATAL 日志

      - alert: 在线一分钟环比陡降
        expr: '-(online_count{app_id="abcdef"} - online_count{app_id="abcdef"} offset 1m) / online_count{app_id="abcdef"} offset 1m >= 0.05'
        labels:
          severity: critical
        annotations:
          description: >-
            {{$labels.app_id}} 在线一分钟环比陡降 {{$value | humanizePercentage}}
            {{ query "online_count{app_id='abcdef'} offset 1m" | first | value }} ->
            {{ query "online_count{app_id='abcdef'}" | first | value }}

      - alert: 调试常驻集ll内存陡降
        expr: |
          (
          container_memory_rss{image!="",container!~"abc|def"} -
          container_memory_rss{image!="",container!~"abc|def"} offset 2m
          ) /
          container_memory_rss{image!="",container!~"abc|def"} offset 2m
          < -10.05

        labels:
          severity: critical
        annotations:
          description: >-
            {{$labels.namespace}}/{{$labels.container}} 调试常驻集ll内存陡降 {{$value | humanizePercentage}} {{$labels.instance}}/{{$labels.pod}}

      - alert: 常驻集内存陡降
        expr: |
          (
          container_memory_rss{image!="",container!~"abc|def"} -
          container_memory_rss{image!="",container!~"abc|def"} offset 2m
          ) /
          container_memory_rss{image!="",container!~"abc|def"} offset 2m
          < -0.3

        labels:
          severity: critical
        annotations:
          description: >-
            {{$labels.namespace}}/{{$labels.container}} 常驻集内存陡降 {{$value | humanizePercentage}} {{$labels.instance}}/{{$labels.pod}}

      - alert: 常驻集内存陡降九成
        # 适合 Go 服务，有 GC 经常陡降幅度很大
        expr: |
          (
          container_memory_rss{image!="",container=~"abc|def"} -
          container_memory_rss{image!="",container=~"abc|def"} offset 2m
          ) /
          container_memory_rss{image!="",container=~"abc|def"} offset 2m
          < -0.9

        labels:
          severity: critical
        annotations:
          description: >-
            {{$labels.namespace}}/{{$labels.container}} 常驻集内存陡降九成 {{$value | humanizePercentage}} {{$labels.instance}}/{{$labels.pod}}
