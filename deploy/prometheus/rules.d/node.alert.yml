# Rules within a group are run sequentially at a regular interval.
groups:
  - # The name of the group.
    # Must be unique within a file.
    # 这个名字不重要，我们就用一个分组就行了
    name: node
    # How often rules in the group are evaluated.
    # [ interval: <duration> | default = global.evaluation_interval ]
    rules:
      - alert: 硬盘快满了
        # node_filesystem_free_bytes 包括为超级用户保留的空间
        # node_filesystem_avail_bytes 和 df -h 一致
        expr: >-
          (
            (node_filesystem_size_bytes{fstype!~"tmpfs"} - node_filesystem_avail_bytes{fstype!~"tmpfs"}) / node_filesystem_size_bytes{fstype!~"tmpfs"} > 0.7
          ) 
          * on(instance) group_left(nodename) 
          node_uname_info
        labels:
          team: alert
        annotations:
          description: "硬盘快满了  {{ $labels.nodename }}  {{ $labels.instance }}  {{ $labels.device }}  {{ $value | humanizePercentage }}"

      - alert: 内存占用太多了
        expr: >-
          (
            (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.7
          ) 
          * on(instance) group_left(nodename) 
          node_uname_info
        for: 2m
        labels:
          team: debug
        annotations:
          description: >-
            内存占用太多了  {{ $labels.nodename }}  {{ $labels.instance }}  {{ $value | humanizePercentage }} =
            {{ query (printf "(node_memory_MemTotal_bytes{instance='%s'} - node_memory_MemAvailable_bytes{instance='%s'}) / 1024 / 1024 / 1024" $labels.instance $labels.instance)  | first | value | printf "%.1f" }}GB /
            {{ query (printf "node_memory_MemTotal_bytes{instance='%s'} / 1024 / 1024 / 1024" $labels.instance) | first | value | printf "%.1f" }}GB

      - alert: 网络入口流量异常
        expr: >-
          (
            rate(node_network_receive_bytes_total[2m]) / 1024 / 1024 > 50
          ) 
          * on(instance) group_left(nodename) 
          node_uname_info
        for: 2m
        labels:
          team: debug
        annotations:
          description: >-
            网络入口流量异常  {{ $labels.nodename }}  {{ $labels.instance }}  {{ $labels.device }}  {{ printf "%.1f" $value }}MB/s

      - alert: 网络出口流量异常
        expr: >-
          (
            rate(node_network_transmit_bytes_total[2m]) / 1024 / 1024 > 60
          ) 
          * on(instance) group_left(nodename) 
          node_uname_info
        for: 2m
        labels:
          team: debug
        annotations:
          description: >-
            网络出口流量异常  {{ $labels.nodename }}  {{ $labels.instance }}  {{ $labels.device }}  {{ printf "%.1f" $value }}MB/s

      - alert: 磁盘读流量异常
        expr: >-
          (
            rate(node_disk_read_bytes_total[2m]) / 1024 / 1024 > 50
          ) 
          * on(instance) group_left(nodename) 
          node_uname_info
        for: 2m
        labels:
          team: debug
        annotations:
          description: >-
            磁盘读流量异常  {{ $labels.nodename }}  {{ $labels.instance }}  {{ $labels.device }}  {{ printf "%.1f" $value }}MB/s

      - alert: 磁盘写流量异常
        expr: >-
          (
            rate(node_disk_written_bytes_total[2m]) / 1024 / 1024 > 50
          ) 
          * on(instance) group_left(nodename) 
          node_uname_info
        for: 2m
        labels:
          team: debug
        annotations:
          description: >-
            磁盘写流量异常  {{ $labels.nodename }}  {{ $labels.instance }}  {{ $labels.device }}  {{ printf "%.1f" $value }}MB/s

      - alert: 磁盘读延迟异常
        expr: >-
          (
            rate(node_disk_read_time_seconds_total[1m]) / rate(node_disk_reads_completed_total[1m]) > 0.050
          ) 
          * on(instance) group_left(nodename) 
          node_uname_info
        for: 2m
        labels:
          team: alert
        annotations:
          description: >-
            磁盘读延迟异常  {{ $labels.nodename }}  {{ $labels.instance }}  {{ $labels.device }}  {{ printf "%.3f" $value }}s

      - alert: 磁盘写延迟异常
        expr: >-
          (
            rate(node_disk_write_time_seconds_total[1m]) / rate(node_disk_writes_completed_total[1m]) > 0.050
          ) 
          * on(instance) group_left(nodename) 
          node_uname_info
        for: 2m
        labels:
          team: alert
        annotations:
          description: >-
            磁盘写延迟异常  {{ $labels.nodename }}  {{ $labels.instance }}  {{ $labels.device }}  {{ printf "%.3f" $value }}s

      - alert: 处理器占用高
        expr: >-
          (
            sum by (instance) (avg by (mode, instance) (rate(node_cpu_seconds_total{mode!="idle"}[2m]))) * 100 > 50
          ) 
          * on(instance) group_left(nodename) 
          node_uname_info
        for: 2m
        labels:
          team: debug
        annotations:
          description: >-
            处理器占用高  {{ $labels.nodename }}  {{ $labels.instance }}  {{ printf "%.0f" $value }}%

      - alert: HostCpuHighIowait
        expr: >-
          (
            avg by (instance) (rate(node_cpu_seconds_total{mode="iowait"}[5m])) * 100 > 10
          ) 
          * on(instance) group_left(nodename) 
          node_uname_info
        for: 0m
        labels:
          team: debug
        annotations:
          description: >-
            HostCpuHighIowait  {{ $labels.nodename }}  {{ $labels.instance }}  {{ printf "%.0f" $value }}%  
            A high iowait means that you are disk or network bound.

      - alert: HostOomKillDetected
        expr: >-
          (
            increase(node_vmstat_oom_kill[1m]) > 0
          ) 
          * on(instance) group_left(nodename) 
          node_uname_info
        for: 0m
        labels:
          team: debug
        annotations:
          description: >-
            HostOomKillDetected  {{ $labels.nodename }}  {{ $labels.instance }}  {{ printf "%.0f" $value }}

      - alert: 网络读错误百分比
        expr: >-
          (
            rate(node_network_receive_errs_total[2m]) / rate(node_network_receive_packets_total[2m]) * 100 > 1
          ) 
          * on(instance) group_left(nodename) 
          node_uname_info
        for: 2m
        labels:
          team: debug
        annotations:
          description: >-
            网络读错误百分比  {{ $labels.nodename }}  {{ $labels.instance }}  {{ printf "%.0f" $value }}%

      - alert: 网络写错误百分比
        expr: >-
          (
            rate(node_network_transmit_errs_total[2m]) / rate(node_network_transmit_packets_total[2m]) * 100 > 1
          ) 
          * on(instance) group_left(nodename) 
          node_uname_info
        for: 2m
        labels:
          team: debug
        annotations:
          description: >-
            网络写错误百分比  {{ $labels.nodename }}  {{ $labels.instance }}  {{ printf "%.0f" $value }}%

      - alert: TIME_WAIT 太多
        expr: >-
          (
            node_sockstat_TCP_tw > 20000
          ) 
          * on(instance) group_left(nodename) 
          node_uname_info
        for: 0m
        labels:
          team: alert
        annotations:
          description: >-
            TIME_WAIT 太多  {{ $labels.nodename }}  {{ $labels.instance }}  {{ printf "%.0f" $value }}

      - alert: TCP Socket 太多
        expr: >-
          (
            node_sockstat_TCP_alloc > 40000
          ) 
          * on(instance) group_left(nodename) 
          node_uname_info
        for: 0m
        labels:
          team: alert
        annotations:
          description: >-
            TCP Socket 太多  {{ $labels.nodename }}  {{ $labels.instance }}  {{ printf "%.0f" $value }}
