# Rules within a group are run sequentially at a regular interval.
groups:
  - # The name of the group.
    # Must be unique within a file.
    # 这个名字不重要，我们就用一个分组就行了
    name: users
    # How often rules in the group are evaluated.
    # [ interval: <duration> | default = global.evaluation_interval ]
    rules:
      - alert: 在线人数快速下降
        expr: (yyl_concurrent_users - yyl_concurrent_users offset 1m) / yyl_concurrent_users offset 1m < -0.1
        labels:
          team: alert
        annotations:
          description: >-
            在线人数快速下降 {{$value | humanizePercentage}}
            {{ query "yyl_concurrent_users offset 1m" | first | value }} ->
            {{ query "yyl_concurrent_users" | first | value }}

      - alert: 在线人数五分钟下降
        expr: (yyl_concurrent_users - yyl_concurrent_users offset 5m) / yyl_concurrent_users offset 5m < -0.12 unless on() ((hour() + 8) % 24 >= 5 and (hour() + 8) % 24 < 8)
        labels:
          team: alert
        annotations:
          description: >-
            在线人数五分钟下降 {{$value | humanizePercentage}}
            {{ query "yyl_concurrent_users offset 5m" | first | value }} ->
            {{ query "yyl_concurrent_users" | first | value }}

      - alert: 在线人数五分钟剧烈下降
        expr: (yyl_concurrent_users - yyl_concurrent_users offset 5m) / yyl_concurrent_users offset 5m < -0.20
        labels:
          team: alert
        annotations:
          description: >-
            在线人数五分钟剧烈下降 {{$value | humanizePercentage}}
            {{ query "yyl_concurrent_users offset 5m" | first | value }} ->
            {{ query "yyl_concurrent_users" | first | value }}

      - alert: 新增走平
        expr: (sum(yyl_daily_source_register_users) - sum(yyl_daily_source_register_users offset 2m)) / sum(yyl_daily_source_register_users offset 2m) == 0
        for: 60m
        labels:
          team: alert
        annotations:
          description: >-
            新增走平
