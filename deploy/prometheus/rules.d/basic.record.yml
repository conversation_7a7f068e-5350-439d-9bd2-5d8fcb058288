# https://prometheus.io/docs/practices/rules/

# A prefix that tells you what the aggregation level of the metric is,
# listing the relevant labels that are still present on the output metric.
# The original name of the metric that is being aggregated,
# with e.g. _total suffixes from counter metrics being dropped after applying a rate() function.
# The type of aggregation (dimensional aggregation, type of rate, etc.) that is being applied to the metric.

# For example, a recording rule that computes the following expression:
# sum by(job, path) (rate(http_requests_total{job="my-job"}[5m]))
# ...might have the output metric name:
# path:http_requests:rate5m

# Rules within a group are run sequentially at a regular interval.
groups:
  - # The name of the group.
    # Must be unique within a file.
    # 这个名字不重要，我们就用一个分组就行了
    name: BasicRecordGroup
    # How often rules in the group are evaluated.
    # [ interval: <duration> | default = global.evaluation_interval ]
    rules:
      #- record: path:requests:rate5m
      #  expr: sum without (instance)(instance_path:requests:rate5m{job="myjob"})
