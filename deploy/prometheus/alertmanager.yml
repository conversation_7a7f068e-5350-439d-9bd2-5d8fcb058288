# https://prometheus.io/docs/prometheus/latest/configuration/configuration/
# pkill -HUP alertmanager

global:
  # ResolveTimeout is the default value used by alertmanager if the alert does
  # not include EndsAt, after this time passes it can declare the alert as resolved if it has not been updated.
  # This has no impact on alerts from Prometheus, as they always include EndsAt.
  resolve_timeout: 5m
route:
  receiver: DebugAlerts
  # 把一组报警合并为一条消息
  # 对应到 webhook_config 发消息的 groupLabels 字段
  group_by: [team, alertname]
  # 当一个 AlertGroup 新建后，它会等待一段时间（group_wait 参数)，再触发第一次 Notification Pipeline
  # Allows to wait for an inhibiting alert to arrive or collect more initial alerts for the same group.
  group_wait: 30s
  # 假如这个 AlertGroup 持续存在，那么之后每隔一段时间（group_interval 参数)，都会触发一次 Notification Pipeline
  # 假如这个 AlertGroup 持续存在，那么之后每隔一段时间（group_interval 参数)，都会触发一次 Notification Pipeline
  group_interval: 1m
  # 去重是根据接收组加 Alert 对象
  repeat_interval: 1h
  routes:
    - receiver: ABCLogic
      matchers:
        - alertname = 常驻集内存陡降
        - namespace = abc-logic
      group_interval: 1m

inhibit_rules:
  - source_match:
      severity: critical
    target_match:
      severity: warning
    equal:
      - alertname
      - instance

receivers:
  - # Receiver is a named configuration of one or more notification integrations.
    name: send_resolved
    webhook_configs:
      - # Whether or not to notify about resolved alerts.
        send_resolved: true
        # The endpoint to send HTTP POST requests to.
        url: "http://127.0.0.1:3305/alertmanager"
        # The HTTP client's configuration.
        # [ http_config: <http_config> | default = global.http_config ]
  - # Receiver is a named configuration of one or more notification integrations.
    name: no_send_resolved
    webhook_configs:
      - # Whether or not to notify about resolved alerts.
        send_resolved: false
        # The endpoint to send HTTP POST requests to.
        url: "http://127.0.0.1:3305/alertmanager"
        # The HTTP client's configuration.
        # [ http_config: <http_config> | default = global.http_config ]

  - name: DebugAlerts
    webhook_configs:
      - send_resolved: false
        url: "http://127.0.0.1:3305/alertmanager"

  - name: ABCLogic
    webhook_configs:
      - send_resolved: false
        url: "http://127.0.0.1:3305/alertmanager"
