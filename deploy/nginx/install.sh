#!/bin/bash



set -e
#set -x


# https://stackoverflow.com/questions/29832037/how-to-get-script-directory-in-posix-sh
this_dir=$(CDPATH= cd -- "$(dirname -- "$0")" && pwd)
work_dir=$(realpath "$this_dir/../..")
cd "$work_dir"
. "$work_dir/deploy/yylenv/api.sh"



echo "this_dir: $this_dir"

cd "$this_dir"
pwd






enable_site() {
  echo "enable_site" "$@"

  site_name="${1:?ArgShouldExist}"
  rm -f "conf.tmp/sites-enabled/$site_name.conf"
  mkdir -p conf.tmp/sites-enabled
  cp -f "conf.tmp/sites-available/$site_name.conf" "conf.tmp/sites-enabled/$site_name.conf"
}


enable_stream() {
  echo "enable_stream" "$@"

  name="${1:?ArgShouldExist}"
  rm -f "conf.tmp/streams-enabled/$name.conf"
  mkdir -p conf.tmp/streams-enabled
  cp -f "conf.tmp/streams-available/$name.conf" "conf.tmp/streams-enabled/$name.conf"
}





# 证书运作机制
# 代码提交时安装软件包，包括设置证书续签通知的回调
# 其他逻辑都在这个脚本里面，在代码提交时和每天上午被调用，包括申请证书、证书续签、组装配置、重新加载配置



# 证书是否有效
is_cert_valid_by_main_domain() {
  main_domain="${1:?ArgShouldExist}"
  cert_prefix=~/.acme.sh/mycerts/$main_domain
  
  # 两个文件必须存在
  if [ ! -f "$cert_prefix.key.pem" ]; then
    return 1
  fi
  if [ ! -f "$cert_prefix.cert.pem" ]; then
    return 1
  fi

  # set -e 的效果不会影响到出现在条件测试表达式中的命令。例如，if、while 语句中的命令或 &&、|| 连接的命令不会触发 set -e。

  # 证书格式合法并且在有效期内
  if ! openssl x509 -in $cert_prefix.cert.pem -checkend 0; then
    return 1
  fi

  # 证书文件和私钥文件的公钥匹配
  pubkey=$(openssl x509 -in $cert_prefix.cert.pem -noout -pubkey)
  pubout=$(openssl pkey -in $cert_prefix.key.pem -pubout)
  if [ "$pubkey" != "$pubout" ]; then
    return 1
  fi
}



# 确保主域名的泛域名证书被申请并定时维护好
ensure_main_domain_wildcard_cert() {
  # 主域名，例如 www.example.com 中的 example.com
  main_domain="${1:?ArgShouldExist}"

  # 重大变动发生后，需要重新申请证书，比如域名解析从阿里云迁移到亚马逊
  if [ "$need_renew_all_certs" = "" ]; then
    if sync_version_if_diff need_renew_all_certs 2; then
      need_renew_all_certs=1
    else
      need_renew_all_certs=0
    fi
  fi

  if [ "$need_renew_all_certs" = "0" ]; then
    # 如果证书已经准备好
    if is_cert_valid_by_main_domain "$main_domain"; then
      return
    fi
  fi

  # 域名解析全部迁移到亚马逊了
  export AWS_ACCESS_KEY_ID="$g_aws_key"
  export AWS_SECRET_ACCESS_KEY="$g_aws_secret"
  dns_vendor="dns_aws"

  # Domains not changed. 也会返回错误，所以忽略错误，利用下面的 --install-cert 来暴露错误
  if timeout 300s ~/.acme.sh/acme.sh --issue --dns "$dns_vendor" -d $main_domain -d *.$main_domain --server letsencrypt; then
    send_alert debug "✅ 证书申请成功 $main_domain *.$main_domain"
  else
    err=$?
    # 错误码 2 表示 Domains not changed.
    if [ ! "$err" -eq 2 ]; then
      return $err
    fi
  fi

  # https://letsencrypt.org/docs/rate-limits/
  # letsencrypt 频率限制概览：
  # 1. 每注册域每周最多 50 个证书
  # 2. 每账户每 3 小时最多 300 新订单
  # 3. 每周重复证书续订限制为 5
  # 4. API 端点 "new-nonce", "new-account", "new-order", "revoke-cert" 每秒限 20 次请求
  # 5. API 端点 "/directory" 及 "/acme" 目录每秒限 40 次请求
  sleep 0.1

  # 在证书被定时任务更新后，会自动拷贝到指定位置，并自动执行指定命令
  # 通过一个特殊文件是否存在还表示是否有证书发生变动 ~/.acme.sh/mycerts_update_flag
  # 细节：如果该命令失败，两个文件也可能被拷贝到目标位置，其中证书文件内容是空的
  mkdir -p ~/.acme.sh/mycerts
  ~/.acme.sh/acme.sh --install-cert -d $main_domain --key-file ~/.acme.sh/mycerts/$main_domain.key.pem --fullchain-file ~/.acme.sh/mycerts/$main_domain.cert.pem --reloadcmd "touch ~/.acme.sh/mycerts_update_flag"

}

remove_main_domain_wildcard_cert() {
  main_domain="${1:?ArgShouldExist}"

  rm -f ~/.acme.sh/mycerts/$main_domain.key.pem
  rm -f ~/.acme.sh/mycerts/$main_domain.cert.pem

  if [[ -e ~/.acme.sh/${main_domain}_ecc ]]; then
    ~/.acme.sh/acme.sh --remove -d $main_domain -d *.$main_domain || true
    rm -f -r ~/.acme.sh/${main_domain}_ecc
  fi
}



add_http_domain_conf() {
  domain_name="${1:?ArgShouldExist}"
  service_name="${2:?ArgShouldExist}"


  content=$(cat <<EOF

server {
  server_name $domain_name;

  listen 80;

  location ^~ /.well-known/acme-challenge/ {
      default_type "text/plain";
      root /var/www/html/;
  }

  location = /hello123 {
      default_type text/html;
      return 200 "<!DOCTYPE html><h2>hello123...</h2>\n";
  }

  include services/$service_name.conf;
}



EOF
  )

  echo "$content" > conf.tmp/http-domains-enabled/$domain_name.conf

  
}



add_https_domain_conf() {
  domain_name="${1:?ArgShouldExist}"
  service_name="${2:?ArgShouldExist}"

  main_domain=$(echo "$domain_name" | sed -E 's/^[^.]+\.([^.]+\.[^.]+)$/\1/')

  if ! ensure_main_domain_wildcard_cert "$main_domain"; then
    send_alert debug "❌ 证书失败 $domain_name *.$main_domain"
    return 1
  fi

  cert_prefix=~/.acme.sh/mycerts/$main_domain
  content=$(cat <<EOF

server {
  server_name $domain_name;

  listen 443 ssl;

  ssl_certificate $cert_prefix.cert.pem;
  ssl_certificate_key $cert_prefix.key.pem;

  # 从 certbot 参考来的安全参数
  include /opt/yylbe/deploy/nginx/options-ssl-nginx.conf;
  ssl_dhparam /opt/yylbe/deploy/nginx/ssl-dhparams.pem;

  include services/$service_name.conf;

}

EOF
  )

  echo "$content" > conf.tmp/https-domains-enabled/$domain_name.conf



}



remove_domain_https_conf() {
  domain_name="${1:?ArgShouldExist}"
  service_name="${2:?ArgShouldExist}"

  main_domain=$(echo "$domain_name" | sed -E 's/^[^.]+\.([^.]+\.[^.]+)$/\1/')

  remove_main_domain_wildcard_cert "$main_domain" 
}






add_upstream_conf() {
  upstream_name="${1:?ArgShouldExist}"
  inner_ips="${2:?ArgShouldExist}"
  port="${3:?ArgShouldExist}"

  servers=""
  for ip in $inner_ips; do
      servers="$servers
  server $ip:$port;"
  done


  content=$(cat <<EOF

upstream $upstream_name {
  $servers
  keepalive 300;
}

EOF
  )

  echo "$content" > conf.tmp/http-upstreams-enabled/$upstream_name.conf
}

add_g_http_upstreams() {
  echo "$g_http_upstreams" > conf.tmp/http-upstreams-enabled/g_http_upstreams.conf
}

add_g_stream_upstreams() {
  echo "$g_stream_upstreams" > conf.tmp/stream-upstreams-enabled/g_stream_upstreams.conf
}



# 因为 set 只能在 server/location/if 块中定义，不能在 http 块中定义，所以用 map 指令来实现
add_vars_conf() {
  content=""
  for var in "$@"; do
    eval val=\"\$$var\"
    content="$content
map \"\" \$$var {
  default \"$val\";
}"
  done
  echo "$content" > conf.tmp/common-enabled/vars.conf
}

add_resolver_conf() {
  echo resolver $(awk 'BEGIN{ORS=" "} $1=="nameserver" {print $2}' /etc/resolv.conf) " valid=300s ipv6=off;" > conf.tmp/common-enabled/resolver.conf
}


# 检查 DNS 解析的域名和自己的公网 IP 是否一致
check_dns_ready() {
  domain_name="${1:?ArgShouldExist}"

  dns_resolved_ip=$(dig +short $domain_name | tail -n 1)
  my_public_ip=$(curl -s ipinfo.io/ip)

  if [[ "$dns_resolved_ip" != "$my_public_ip" ]]; then
    echo "DNS_NOT_READY: dns_resolved_ip: $dns_resolved_ip my_public_ip: $my_public_ip"
    return
  fi
}


add_domain_name_service_name() {
  echo "add_domain_name_service_name" "$@"

  domain_name="${1:?ArgShouldExist}"
  service_name="${2:?ArgShouldExist}"

  add_http_domain_conf $domain_name $service_name
  add_https_domain_conf $domain_name $service_name || return

  check_dns_ready "$domain_name"
}


remove_domain_name_service_name() {
  echo "remove_domain_name_service_name" "$@"

  domain_name="${1:?ArgShouldExist}"
  service_name="${2:?ArgShouldExist}"

  remove_domain_http_conf $domain_name $service_name
  remove_domain_https_conf $domain_name $service_name
}


add_by_domain_name_for_gate() {
  domain_name_series="${1:?ArgShouldExist}"

  if [[ "$domain_name_series" != "rummyblitz1.com" ]]; then
    add_domain_name_service_name "www.$domain_name_series" "center"
  fi

  add_domain_name_service_name "center.$domain_name_series" "center"
  add_domain_name_service_name "console.$domain_name_series" "console"
  add_domain_name_service_name "kefu.$domain_name_series" "kefu"

}


check_gate_install_ok() {

  # if [[ ! -f conf/https-domains-enabled/center.$domain_name_series.conf ]]; then
  #   echo "CERTBOT_FAILED: center.$domain_name_series.conf"
  #   return
  # fi
  if [[ ! -f conf/https-domains-enabled/console.$domain_name_series.conf ]]; then
    echo "CERTBOT_FAILED: console.$domain_name_series.conf"
    return
  fi
  if [[ ! -f conf/https-domains-enabled/kefu.$domain_name_series.conf ]]; then
    echo "CERTBOT_FAILED: kefu.$domain_name_series.conf"
    return
  fi

  touch conf/GATE_INSTALL_OK
}


kvlist_to_htpasswd() {
  kvlist="${1:?ArgShouldExist}"
  htpasswd_file="${2:?ArgShouldExist}"

  rm -f "$htpasswd_file"
  for kv in $kvlist; do
    user=$(echo "$kv" | cut -d: -f1)
    pass=$(echo "$kv" | cut -d: -f2)
    echo "$user:$(openssl passwd -1 -salt "$user" "$pass")" >> "$htpasswd_file"
  done

}



add_domain_names() {

  # 代理内网地址适合用 ip 地址和 upstream 实现
  add_upstream_conf upstream_center "$g_upstream_main_ips" "$g_upstream_center_port"
  add_upstream_conf upstream_serverhttp "$g_upstream_main_ips" "$g_upstream_serverhttp_port"
  #add_upstream_conf upstream_serverws "$g_upstream_main_ips" "$g_upstream_serverws_port"
  #add_upstream_conf upstream_console "$g_upstream_main_ips" "$g_upstream_console_port"
  add_upstream_conf upstream_kefu "$g_upstream_main_ips" "$g_upstream_kefu_port"
  add_upstream_conf upstream_kefuws "$g_upstream_main_ips" "$g_upstream_kefuws_port"
  add_upstream_conf upstream_gmconsole "$g_upstream_main_ips" "$g_upstream_gmconsole_port"
  add_upstream_conf upstream_gmkefu "$g_upstream_main_ips" "$g_upstream_gmkefu_port"
  add_upstream_conf upstream_gmshuzhi "$g_upstream_main_ips" "$g_upstream_gmshuzhi_port"
  add_upstream_conf upstream_attributionserver "$g_upstream_main_ips" "$g_upstream_attributionserver_port"
  add_upstream_conf upstream_serverad "$g_upstream_main_ips" "$g_upstream_serverad_port"
  add_upstream_conf upstream_pay "$g_upstream_main_ips" "$g_upstream_pay_port"

  add_g_http_upstreams
  add_g_stream_upstreams


  # 代理外网地址适合用 resolver 和 nginx 变量实现
  add_resolver_conf
  
  add_vars_conf \
    g_proxy_password_redis1 \
    g_proxy_addr_redis1 \
    g_proxy_addr_mysql1 \
    g_vice_upstream_grafana_port \
    g_vice_upstream_victoriametrics_port \
    g_vice_upstream_vmalert_port \
    g_vice_upstream_alertmanager_port \
    g_vice_upstream_metabase_port \
    g_vice_upstream_gitea_port \
    g_vice_upstream_clickhouse_port \
    g_vice_upstream_alertbot_port \
    g_upstream_ossprivacy_addr g_upstream_ossdownload_addr


  if [[ "1" == "" ]]; then
    echo hello

  # 如果主机名以 pro1gate- 开头，那么就是接入机
  elif [[ "$(hostname)" =~ ^pro1gate- ]]; then
    # 剔除掉主机名前缀就是渠道名
    source_name="$(hostname)"
    source_name=${source_name#pro1gate-}
    domain_name="$(php81 ../get_domain_name_by_source_name.php $source_name | tail -1)"
    # 如果不符合域名正则表达式
    if [[ "$domain_name" =~ ^[a-z0-9]+(\.[a-z0-9]+)+$ ]]; then
      add_by_domain_name_for_gate "$domain_name"
    else
      echo "domain_name is invalid for source_name: $source_name"
    fi
  fi





  case "$g_node_role" in
    main | game | mono)
      enable_site api
      ;;
  esac


  case "$g_node_role" in
    main | mono)
    enable_site gmconsole
    enable_site console
    enable_site kefu
    enable_site pay
      ;;
  esac





  # 兼容一些老的的渠道
  if [ "1" = "" ]; then
    echo hello
  elif [ "$(hostname)" = "pro1main3" ]; then
    enable_site api

  elif [ "$(hostname)" = "pro1main2" ]; then
    enable_site api
    enable_site console
    enable_site kefu
    enable_site pay

    add_domain_name_service_name "pay.teenpattihive.com" "pay"

    # 这个内部服务和管理后台混着的，把内部服务暴露到外网了，需要区分一下 TODO
    add_domain_name_service_name "gmshuzhi.teenpattihive.com" "gmshuzhi"
    add_domain_name_service_name "gmkefu.teenpattihive.com" "gmkefu"
    add_domain_name_service_name "gmconsole.teenpattihive.com" "gmconsole"

    add_domain_name_service_name "attribution.teenpattihive.com" "attributionserver"

  elif [ "$(hostname)" = "pro1vice1" ]; then

    # 后台相关域名
    add_domain_name_service_name "grafana.teenpattihive.com" "grafana"
    add_domain_name_service_name "victoriametrics.teenpattihive.com" "victoriametrics"
    add_domain_name_service_name "metabase.teenpattihive.com" "metabase"
    add_domain_name_service_name "gitea.teenpattihive.com" "gitea"


    # 内网域名



    add_domain_name_service_name "playgoogle.teenpattihive.com" "playgoogle"
    add_domain_name_service_name "apitelegramorg.teenpattihive.com" "apitelegramorg"
    add_domain_name_service_name "vmalert.teenpattihive.com" "vmalert"
    add_domain_name_service_name "alertmanager.teenpattihive.com" "alertmanager"
    add_domain_name_service_name "gitea-inner.teenpattihive.com" "gitea"
    add_domain_name_service_name "clickhouse.teenpattihive.com" "clickhouse"


    # 在 cloudflare 的后面
    # cdn.tpduelcfcdn.click -- 域名解析 --> cloudflare -- 域名解析 --> cdn.teenpattihive.com --> pro1vice1 -- Host: cdn.tpduelcfcdn.click --> http ossdownload
    # http://cdn.teenpattihive.com/playstore_web/patihugewin/3PattiHugeWin.apk
    # http://cdn.tpduelcfcdn.click/playstore_web/patihugewin/3PattiHugeWin.apk
    # http://cdn.tpduelcfcdn.click/htmlprivacy/cardsclash.html
    # https://cdn.tpduelcfcdn.click/htmlprivacy/cardsclash.html
    # https://googleplay.p1o9.com/htmlprivacy/cardsclash.html
    add_domain_name_service_name "cdn.teenpattihive.com" "ossdownload"
    add_http_domain_conf "cdn.tpduelcfcdn.click" "ossdownload"
    add_http_domain_conf "googleplay.p1o9.com" "ossdownload"


  elif [ "$(hostname)" = "pro1gate-rummyblitz" ]; then
    add_domain_name_service_name "c.rummyblitz1.com" "center"



  elif [ "$g_node_name" = "dev2bj1" ]; then
    enable_site api
    enable_site console
    enable_site kefu
    enable_site pay
    enable_site dev2bjoss1

  elif [ "$g_node_name" = "test3mono1" ]; then
    enable_site api
    enable_site console
    enable_site kefu
    enable_site pay

    enable_stream proxyredis1
    enable_stream proxymysql1


    add_by_domain_name_for_gate "tptest3.click"

    add_domain_name_service_name "attribution.tptest3.click" "attributionserver"

    add_domain_name_service_name "pay.tptest3.click" "pay"

    # 这个内部服务和管理后台混着的，把内部服务暴露到外网了，需要区分一下 TODO
    add_domain_name_service_name "gmshuzhi.tptest3.click" "gmshuzhi"
    add_domain_name_service_name "gmkefu.tptest3.click" "gmkefu"
    add_domain_name_service_name "gmconsole.tptest3.click" "gmconsole"

    add_domain_name_service_name "grafana.tptest3.click" "grafana"
    add_domain_name_service_name "victoriametrics.tptest3.click" "victoriametrics"
    add_domain_name_service_name "metabase.tptest3.click" "metabase"
    add_domain_name_service_name "vmalert.tptest3.click" "vmalert"
    add_domain_name_service_name "alertmanager.tptest3.click" "alertmanager"
    add_domain_name_service_name "clickhouse.tptest3.click" "clickhouse"
    add_domain_name_service_name "alertbot.tptest3.click" "alertbot"

    add_domain_name_service_name "anyurlproxy.tptest3.click" "anyurlproxy"


  elif [ "$g_node_name" = "pro3gate1" ]; then
    add_by_domain_name_for_gate "tpafacepro3.click"

  elif [ "$g_node_name" = "pro3gate2" ]; then
    add_by_domain_name_for_gate "tpgoldmaster.click"
    add_by_domain_name_for_gate "teenpattiduel.com"

  elif [ "$g_node_name" = "pro3gate3" ]; then
    add_by_domain_name_for_gate "tpgoldmaster.click"
    add_by_domain_name_for_gate "teenpattiduel.com"

  elif [ "$g_node_name" = "pro3paygate2" ]; then
    add_domain_name_service_name "pay.tppaygatepro3.click" "pay"
    add_domain_name_service_name "pay.teenpattihive.com" "pay"
    
    add_domain_name_service_name "anyurlproxy.tppaygatepro3.click" "anyurlproxy"



  elif [ "$g_node_name" = "pro3cdn1" ]; then

    # 在 cloudflare 的后面
    # 对外域名 --> cloudflare --> 回源域名 --> pro3cdn1 -- Host: 对外域名 --> http ossdownload
    # 这个域名仅仅是解析到这里，但 cloudflare 回源到这个域名时用的 Host 不是这个域名
    add_domain_name_service_name "cdn.tpcbpro3.click" "ossdownload"
    # 这些域名解析到 cloudflare，这里只需支持这些域名的 http 接口即可，多支持 https 也没事，但这些域名不是解析到这里的
    add_http_domain_conf "cdn.tpduelcfcdn.click" "ossdownload"
    add_http_domain_conf "googleplay.p1o9.com" "ossdownload"



  elif [ "$g_node_name" = "pro3main1" ]; then
    enable_site api
    enable_site console
    enable_site kefu
    enable_site pay

    # 这个内部服务和管理后台混着的，把内部服务暴露到外网了，需要区分一下 TODO
    add_domain_name_service_name "gmshuzhi.tpgmpro3.click" "gmshuzhi"
    add_domain_name_service_name "gmkefu.tpgmpro3.click" "gmkefu"
    add_domain_name_service_name "gmconsole.tpgmpro3.click" "gmconsole"

    add_domain_name_service_name "attribution.tpcbpro3.click" "attributionserver"
    add_domain_name_service_name "attribution.teenpattihive.com" "attributionserver"


  elif [ "$g_node_name" = "pro3game1" ]; then
    enable_site api

  elif [ "$g_node_name" = "pro3game2" ]; then
    enable_site api

  elif [ "$g_node_name" = "pro3vice1" ]; then
    enable_stream proxyredis1
    enable_stream proxymysql1

    add_domain_name_service_name "grafana.tpgmpro3.click" "grafana"
    add_domain_name_service_name "victoriametrics.tpgmpro3.click" "victoriametrics"
    add_domain_name_service_name "metabase.tpgmpro3.click" "metabase"
    add_domain_name_service_name "vmalert.tpgmpro3.click" "vmalert"
    add_domain_name_service_name "alertmanager.tpgmpro3.click" "alertmanager"
    add_domain_name_service_name "clickhouse.tpgmpro3.click" "clickhouse"
    add_domain_name_service_name "alertbot.tpgmpro3.click" "alertbot"
    add_domain_name_service_name "gitea.tpgmpro3.click" "gitea"
    add_domain_name_service_name "gitea-inner.tpgmpro3.click" "gitea"




  fi



}




# 配置的静态部分
conf_src="$this_dir/confbiz"



# 将当前目录切换到运行时目录
# runtime 运行时数据目录
mkdir -p runtime
cd runtime


# 配置分静态部分和动态部分
# 静态部分通过 git 维护
# 动态部分是因为每台机器支持不同的域名和服务
# 在 conf.tmp 上组装整个配置，确保组装过程完成成功，才去替换掉 conf
# 如果把 conf 目录删掉，不会影响 nginx 正常运行，因为 nginx 需要的配置数据包括 ssl 证书都是在启动时加载到内存的
# 如果把 conf 目录删掉，也不会影响 reload，因为 reload 会失败，不影响服务正常运行
# 如果把 conf 目录原地刪除重建，如果一直失败，会影响证书自动续期后的 reload
# 如果把 conf 目录原地刪除重建，重启 nginx 会失败
rm -r -f conf.tmp
# 基于源代码开始构建
cp -r "$conf_src" conf.tmp

cp -r "$this_dir/baseconf" -t conf.tmp
mkdir -p conf.tmp/common-enabled
mkdir -p conf.tmp/http-upstreams-enabled
mkdir -p conf.tmp/stream-upstreams-enabled
mkdir -p conf.tmp/https-domains-enabled
mkdir -p conf.tmp/http-domains-enabled

# 它自己不会创建，需要手动创建
mkdir -p logs



need_reload=0



kvlist_to_htpasswd "$g_htpasswd_kvlist" conf.tmp/htpasswd
cmp -s conf.tmp/htpasswd conf/htpasswd || {
  echo "htpasswd need_reload: ----------------------------------------"
  need_reload=1
}



# 如果动态部分组装成功，才去比较整体是否有变化，有变化才去 reload
if add_domain_names; then

  rm -f -r tmp

  openresty -p `pwd` -c conf.tmp/nginx.conf -T > conf.tmp/nginx.T.conf

  cmp -s conf.tmp/nginx.T.conf conf/nginx.T.conf || {
    echo "ReloadNginx: ----------------------------------------"


    need_reload=1
  }



  if [[ "$(hostname)" =~ ^pro1gate- ]]; then
    check_gate_install_ok
  fi

else

  err=$?
  send_alert debug "❌ nginx 配置构建失败 err=$err"

  need_reload=0
fi


if [ "$need_reload" -eq 1 ]; then

  rm -f -r conf.last
  if [ -e conf ]; then
    mv conf conf.last
  fi
  mv conf.tmp conf

fi


# 这个脚本的运行时机：1、代码提交；2、每天上午；3、手动。
# 不太频繁，所以可以做一些较重的操作如证书续签。

# 证书续签
timeout 300s ~/.acme.sh/acme.sh --cron || true
# 如果有证书被续签，则会触发标记 mycerts_update_flag
# 如果脚本报错，则会被忽略





# 是否有证书续签？
if [ -e ~/.acme.sh/mycerts_update_flag ]; then
  need_reload=1
fi


# 重新加载配置
if [ "$need_reload" -eq 1 ]; then
  openresty -p `pwd` -t
  
  # 可能 yylnginx 并未启动
  if [ -e logs/nginx.pid ] && kill -0 "$(cat logs/nginx.pid)"; then
    send_alert debug "⚠️ nginx reload"
    openresty -p "$(pwd)" -s reload
  else
    send_alert debug "⚠️ NEED nginx start"
  fi

  # 重新加载配置成功之后，才刪除证书续签标记
  echo rm -f ~/.acme.sh/mycerts_update_flag
  rm -f ~/.acme.sh/mycerts_update_flag
fi


# 刪除废弃的 certbot 钩子
rm -f /etc/letsencrypt/renewal-hooks/deploy/reload_yylnginx.sh
rm -f /etc/letsencrypt/renewal-hooks/deploy/reload_yylnginxgate.sh






