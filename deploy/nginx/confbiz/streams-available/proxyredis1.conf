

# http://nginx.org/en/docs/stream/ngx_stream_core_module.html

server {
    listen 16301;
    
    # 定义变量，强制 nginx 在每个请求时都重新解析 dns
    set $backend "$g_proxy_addr_redis1";

    set $allowed_password "$g_proxy_password_redis1";  # 设置允许的密码

    preread_by_lua_block {
        local sock = assert(ngx.req.socket(true))

        local function read()
            return sock:receive("*l")
        end
        local function exit(msg, data)
            ngx.log(ngx.ERR, msg, data)
            return ngx.exit(ngx.ERROR)
        end
        local function check_password(password)
            if password ~= ngx.var.allowed_password then
                sock:send("-WRONGPASS invalid username-password pair\r\n")
                return exit("WrongPassword", password)
            end
            sock:send("+OK\r\n")
            ngx.log(ngx.INFO, "AuthSucceeded")
        end

        local data = read()
        if data == "*2" then
            data = read()
            if data ~= "$4" then
                return exit("ExpectAuthLen", data)
            end
            data = read()
            if data:upper() ~= "AUTH" then
                return exit("ExpectAuth", data)
            end
            data = read()
            if data:find("^%$%d+$") == nil then
                return exit("ExpectPasswordLen", data)
            end
            local password_len = tonumber(data:sub(2))
            data = read()
            if data:len() ~= password_len then
                return exit("WrongPasswordLen", data)
            end
            return check_password(data)
        end

        local m = ngx.re.match(data, "^AUTH\\s+(\\S+)$", "jo")
        if m then
            return check_password(sock, m[1])
        end

        return exit("RecvNonAuthData", data)
    }




    # 使用变量在每次请求时重新解析 dns
    proxy_pass $backend;
}
