

# 权限问题，不太会改，先用 root
user root;


worker_processes     auto;
worker_rlimit_nofile 65535;

# 我看gate上 老有进程 worker process is shutting down 因为nginx上有长连接 导致原来的进程一直没发关闭
# 每次更新配置文件都会导致 关闭旧work进程 启动新work进程 这个超时用来限定关闭旧work进程的时间
worker_shutdown_timeout 3600s;

include modules-enabled/*.conf;

events {
    multi_accept       on;
    worker_connections 65535;
}

http {
    charset                utf-8;
    sendfile               on;
    tcp_nopush             on;
    tcp_nodelay            on;
    types_hash_max_size    2048;
    types_hash_bucket_size 64;
    client_max_body_size   16M;

    # MIME
    include                baseconf/mime.types;
    default_type           application/octet-stream;


    # 默认情况下你可能会看到 Server: nginx/1.18.0 或 Server: openresty/1.19.3.1。
    # 只显示 Server: nginx 或 Server: openresty，但仍然会暴露使用的是哪个服务器软件。
    server_tokens          off;
    # 这个指令来自于 ngx_headers_more 模块，用于清除响应头中的 Server 字段
    more_clear_headers 'Server';

    # 预定义的 combined 格式只到 http_user_agent
    log_format main '$remote_addr - $remote_user [$time_local] "$request" $status $body_bytes_sent "$http_referer" "$http_user_agent" $request_length $bytes_sent $request_time $upstream_response_time $host $http_x_forwarded_host';

    # http://nginx.org/en/docs/http/ngx_http_log_module.html
    access_log logs/access.log main buffer=32k flush=5s;


    # http://nginx.org/en/docs/ngx_core_module.html#error_log
    # 开发时可以设置成 debug
    error_log logs/error.log error;

    include baseconf/source_name_getter.conf;

    # Load configs
    include                maps-enabled/*.conf;
    include                common-enabled/*.conf;
    include                http-upstreams-enabled/*.conf;
    include                sites-enabled/*.conf;
    include                http-domains-enabled/*.conf;
    include                https-domains-enabled/*.conf;


}


stream {
    # http://nginx.org/en/docs/stream/ngx_stream_log_module.html
    # http://nginx.org/en/docs/stream/ngx_stream_core_module.html#variables
    

    log_format proxy '$remote_addr [$time_local] '
                     '$protocol $status $bytes_sent $bytes_received '
                     '$session_time "$upstream_addr" '
                     '"$upstream_bytes_sent" "$upstream_bytes_received" "$upstream_connect_time" $server_port';

    access_log logs/stream.access.log proxy buffer=32k flush=5s;
    error_log logs/stream.error.log info;
    

    include                common-enabled/*.conf;
    include                stream-upstreams-enabled/*.conf;
    include                streams-enabled/*.conf;
}

