
# console 允许访问 Route::alias 的 api 接口、public 下的静态资源，不允许访问 index api gmpub fpmstatus


server {


    listen      8850;
    root        /opt/yylbe/php/console/public;

    # security
    include     baseconf/nginxconfig.io/security.conf;

    # index.php
    index       index.php;

    # 加 always 就总是有了，不然 404 Not Found 没有
    # 设置是否允许 cookie 传输
    add_header 'Access-Control-Allow-Credentials' 'true' always;
    # 允许请求地址跨域 * 做为通配符
    add_header 'Access-Control-Allow-Origin' '*' always;
    # 允许跨域的请求方法
    add_header 'Access-Control-Allow-Methods' '*' always;
    add_header 'Access-Control-Allow-Headers' '*' always;
    # 在这个时间范围内就不用再重复发起预检请求了
    add_header 'Access-Control-Max-Age' '86400' always;
    # 跨域到 127.0.0.1
    # 1. go to chrome://flags/#block-insecure-private-network-requests
    # 2. set Block insecure private network requests to Disabled
    add_header 'Access-Control-Allow-Private-Network' 'true' always;

    if ($request_method = "OPTIONS") {
        return 200;
    }


    # location 优先级 [=] > [^~] > [~/~*] > [空格]



    # 这里面有静态资源的 location 和 gzip 设置
    include baseconf/nginxconfig.io/general.conf;


    # 通用匹配，不符合的都拒绝
    location / {
        deny all;
    }




    # 处理 Route::alias 的 api 接口
    # http://nginx.org/en/docs/http/ngx_http_fastcgi_module.html
    # 不能设置这个变量 nginx: [emerg] the duplicate "fastcgi_script_name" variable
    location ~ ^(/\w+)$ {
        fastcgi_pass unix:/run/fpmus.sock;
        include baseconf/fastcgi_params;
        
        fastcgi_param SCRIPT_NAME /index.php;
        fastcgi_param SCRIPT_FILENAME $document_root/index.php;
        # 传递给 php 的 $_SERVER['PATH_INFO']
        fastcgi_param PATH_INFO $1;
    }

}
