server {
    listen      8870;
    root        /opt/yylbe/php/pay/public;

    # security
    include     baseconf/nginxconfig.io/security.conf;

    # index.php
    index       index.php;

    # 加 always 就总是有了，不然 404 Not Found 没有
    # 设置是否允许 cookie 传输
    add_header 'Access-Control-Allow-Credentials' 'true' always;
    # 允许请求地址跨域 * 做为通配符
    add_header 'Access-Control-Allow-Origin' '*' always;
    # 允许跨域的请求方法
    add_header 'Access-Control-Allow-Methods' '*' always;
    add_header 'Access-Control-Allow-Headers' '*' always;
    # 在这个时间范围内就不用再重复发起预检请求了
    add_header 'Access-Control-Max-Age' '86400' always;
    # 跨域到 127.0.0.1
    # 1. go to chrome://flags/#block-insecure-private-network-requests
    # 2. set Block insecure private network requests to Disabled
    add_header 'Access-Control-Allow-Private-Network' 'true' always;

    if ($request_method = "OPTIONS") {
        return 200;
    }



    # ThinkPHP
    location / {
        if (!-e $request_filename) {
           rewrite  ^/(.*)$  /index.php/$1  last;
           break;
        }
    }

    # additional config
    include baseconf/nginxconfig.io/general.conf;

    # handle .php
    location ~ \.php(/|$) {
        # 第一个捕获的值会重新赋值给 $fastcgi_script_name 变量
        # 第二个捕获到的值会自动重新赋值给 $fastcgi_path_info 变量
        fastcgi_split_path_info ^(.+\.php)(/.*)$;
        # 传递给 php 的 $_SERVER['PATH_INFO']
        fastcgi_param  PATH_INFO $fastcgi_path_info;
        
        fastcgi_index   index.php;
        fastcgi_pass unix:/run/fpmus.sock;
        include         baseconf/fastcgi.conf;
        
    }

    # handle .php
    location ~ ^/\w+$ {
        # 第一个捕获的值会重新赋值给 $fastcgi_script_name 变量
        # 第二个捕获到的值会自动重新赋值给 $fastcgi_path_info 变量
        fastcgi_split_path_info ^()(/.*)$;
        # 传递给 php 的 $_SERVER['PATH_INFO']
        fastcgi_param  PATH_INFO $fastcgi_path_info;

        fastcgi_param  SCRIPT_FILENAME    $document_root/index.php;
        fastcgi_param  SCRIPT_NAME    /index.php;
        
        fastcgi_index   index.php;
        fastcgi_pass unix:/run/fpmus.sock;
        include         baseconf/fastcgi_params;
        
    }

}
