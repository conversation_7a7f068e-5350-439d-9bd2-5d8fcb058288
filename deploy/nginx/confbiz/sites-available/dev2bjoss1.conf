


# 测试服 999 端口

server {
    listen      999;



    location / {
        set $endpoint "dev2bjoss1.oss-cn-beijing-internal.aliyuncs.com";

        rewrite ^/(.*)$ /$1 break;

        proxy_pass http://$endpoint;
        proxy_redirect http://$endpoint/ /;
        proxy_set_header Host $endpoint;

        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        proxy_hide_header Content-Disposition;
    }





}
