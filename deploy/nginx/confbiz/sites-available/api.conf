server {
    listen      8000;
    root        /opt/yylbe/php/api/taurus;

    # security
    include     baseconf/nginxconfig.io/security.conf;

    # index.php
    index       index.php;

    # 加 always 就总是有了，不然 404 Not Found 没有
    # 设置是否允许 cookie 传输
    add_header 'Access-Control-Allow-Credentials' 'true' always;
    # 允许请求地址跨域 * 做为通配符
    add_header 'Access-Control-Allow-Origin' '*' always;
    # 允许跨域的请求方法
    add_header 'Access-Control-Allow-Methods' '*' always;
    add_header 'Access-Control-Allow-Headers' '*' always;
    # 在这个时间范围内就不用再重复发起预检请求了
    add_header 'Access-Control-Max-Age' '86400' always;
    # 跨域到 127.0.0.1
    # 1. go to chrome://flags/#block-insecure-private-network-requests
    # 2. set Block insecure private network requests to Disabled
    add_header 'Access-Control-Allow-Private-Network' 'true' always;

    if ($request_method = "OPTIONS") {
        return 200;
    }


    # index.php fallback
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # additional config
    include baseconf/nginxconfig.io/general.conf;

    # handle .php
    location ~ \.php$ {
        fastcgi_pass unix:/run/fpmus.sock;
        include      baseconf/nginxconfig.io/php_fastcgi.conf;
    }



    location /gmpub/ {
        alias /data/gmpub/;

        # 禁止目录索引，防止列出目录中的文件
        autoindex off;

        try_files $uri =404;
    }


}
