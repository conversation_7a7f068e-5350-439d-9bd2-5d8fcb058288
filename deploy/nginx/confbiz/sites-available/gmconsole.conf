


# gmconsole 不允许访问 Route::alias 的 api 接口、允许访问 public 下的静态资源 index api gmpub fpmstatus
# php.ini 没有使用 open_basedir 限制目录访问

server {
    listen      8851;
    root        /opt/yylbe/php/console/public;

    # security
    include     baseconf/nginxconfig.io/security.conf;

    # index.php
    index       index.php;

    # 加 always 就总是有了，不然 404 Not Found 没有
    # 设置是否允许 cookie 传输
    add_header 'Access-Control-Allow-Credentials' 'true' always;
    # 允许请求地址跨域 * 做为通配符
    add_header 'Access-Control-Allow-Origin' '*' always;
    # 允许跨域的请求方法
    add_header 'Access-Control-Allow-Methods' '*' always;
    add_header 'Access-Control-Allow-Headers' '*' always;
    # 在这个时间范围内就不用再重复发起预检请求了
    add_header 'Access-Control-Max-Age' '86400' always;
    # 跨域到 127.0.0.1
    # 1. go to chrome://flags/#block-insecure-private-network-requests
    # 2. set Block insecure private network requests to Disabled
    add_header 'Access-Control-Allow-Private-Network' 'true' always;

    if ($request_method = "OPTIONS") {
        return 200;
    }


    # location 优先级 [=] > [^~] > [~/~*] > [空格]


    # 这里面有静态资源的 location 和 gzip 设置
    include baseconf/nginxconfig.io/general.conf;


    # 处理 /module/controller/action 形式的路径
    # 暂时得兼容 Route::rule 定义的额外路径 .*
    location ~ ^(/(?:index)/[^/]+/[^/]+.*)$ {
        fastcgi_pass unix:/run/fpmus.sock;
        include baseconf/fastcgi_params;
        
        fastcgi_param SCRIPT_NAME /index.php;
        fastcgi_param SCRIPT_FILENAME $document_root/index.php;
        # 传递给 php 的 $_SERVER['PATH_INFO']
        fastcgi_param PATH_INFO $1;
    }


    # 查看 fpm 状态页，参考 fpmus.conf
    location ~ ^/fpmstatus20232023$ {
        fastcgi_pass unix:/run/fpmus.sock;
        include baseconf/fastcgi_params;
    }


    # 方便访问脚本临时生成的文件
    location /gmpub/ {
        alias /data/gmpub/;

        # 禁止目录索引，防止列出目录中的文件
        autoindex off;

        try_files $uri =404;
    }

}
