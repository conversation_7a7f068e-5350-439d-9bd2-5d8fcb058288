

location = /center {
    proxy_pass http://upstream_center;
    proxy_set_header Host $host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

    proxy_http_version 1.1;                   #设置http版本为1.1
    proxy_set_header Connection "";           #设置Connection为长连接 
}

location = /game {
    proxy_pass http://upstream_serverhttp;
    proxy_set_header Host $host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

    proxy_http_version 1.1;                   #设置http版本为1.1
    proxy_set_header Connection "";           #设置Connection为长连接 
}


location = /ws {
    proxy_pass http://upstream_serverws;
    proxy_set_header Host $host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    
    proxy_read_timeout 60s;
    proxy_send_timeout 60s;
}

location = /kefuws {
    proxy_pass http://upstream_kefuws;
    proxy_set_header Host $host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    
    proxy_read_timeout 60s;
    proxy_send_timeout 60s;
}



# https://www.teenpattiboom1.com/privacy.html
# http://www.crazyslots1.top/privacy.html
location = /privacy.html {
    # 必须先 set 再 rewrite，否则报错 500 Internal Server Error，error.log 显示 invalid URL prefix in "http://"
    set $endpoint $g_upstream_ossprivacy_addr;
    
    rewrite .* /htmlprivacy/$forwarded_source_name.html break;
    
    proxy_pass http://$endpoint;
    proxy_redirect http://$endpoint/ /;
    proxy_set_header Host $endpoint;

    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
    proxy_hide_header Content-Disposition;
}



# 之前前端提包要一个隐私协议的 url，约定的是各个渠道的域名后面加 /privacy.html
# 后来前端提包要主页地址，同时网关前面加了 cloudfront，这里不太好识别渠道了，实际上也好识别渠道，但约定改成了域名后面加 /privacy/渠道名/任意文件

# http://www.crazyslots1.top/privacy/cardsclash.html



# 试试不指定 resolver 可以吗
# 如果指定 resolver 则必须指定 dns 服务器，没法使用系统默认的 dns 解析了
# https://github.com/DmitryFillo/nginx-proxy-pitfalls
# https://www.nginx.com/blog/dns-service-discovery-nginx-plus/
# 这里面说必须指定 resolver，我没指定怎么也能用？
# https://serverfault.com/questions/638822/nginx-resolver-address-from-etc-resolv-conf
# 这里提到 openresty 可以配置 resolver local=on; 所以我不指定也没事，但这个特性不支持 windows
# 用自动生成 conf 文件的办法吧
# echo resolver $(awk 'BEGIN{ORS=" "} $1=="nameserver" {print $2}' /etc/resolv.conf) " valid=300s ipv6=off;"
# upstream 块里面的域名得收费版才支持 resolve，所以 upstream 只适合 ip 地址的内部访问，对外访问得用这种变量的方式
# https://serverfault.com/questions/240476/how-to-force-nginx-to-resolve-dns-of-a-dynamic-hostname-everytime-when-doing-p/593003#593003
# proxy_pass 使用变量后，就不能修改路径前缀了，可以 rewrite 重写路径
# rewrite 的办法支持透传查询参数
location /privacy/ {
    #resolver ******* valid=300s;
    #resolver ************* ************* valid=30s ipv6=off;

    # dig @************* tphotupdate.oss-ap-south-1-internal.aliyuncs.com
    # curl -v http://tphotupdate.oss-ap-south-1-internal.aliyuncs.com/htmlprivacy/tpmasterapr.html
    # curl -v -H "Host: tphotupdate.oss-ap-south-1-internal.aliyuncs.com" http://***************/htmlprivacy/tpmasterapr.html

    # 必须先 set 再 rewrite，否则报错 500 Internal Server Error，error.log 显示 invalid URL prefix in "http://"
    set $endpoint $g_upstream_ossprivacy_addr;

    rewrite ^/\w+/(.*)$ /htmlprivacy/$1 break;

    proxy_pass http://$endpoint;
    proxy_redirect http://$endpoint/ /;
    proxy_set_header Host $endpoint;

    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    
    proxy_hide_header Content-Disposition;
}


# A 面静态资源  /static/ => /agame/$forwarded_source_name/
location /static/ {
    # 必须先 set 再 rewrite，否则报错 500 Internal Server Error，error.log 显示 invalid URL prefix in "http://"
    set $endpoint $g_upstream_ossprivacy_addr;

    # 这样写不行，看起来是 rewrite 里面不能用变量
    #rewrite ^/\w+/(.*)$ /agame/$forwarded_source_name/$1 break;
    #proxy_pass http://$endpoint;

    # 这样写不用变量是可以的
    #rewrite ^/\w+/(.*)$ /agame/pattiwallpaper/$1 break;

    # 那就 rewrite 去掉前缀，然后 proxy_pass 里面用变量
    rewrite ^/\w+/(.*)$ /$1 break;
    proxy_pass http://$endpoint/agame/$forwarded_source_name$uri$is_args$args;

    proxy_set_header Host $endpoint;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    
    proxy_hide_header Content-Disposition;
}





location = /hello {
    default_type text/html;
    return 200 "<!DOCTYPE html><h2>Hello!</h2>\n";
}



# 兜底转发，所有未匹配到特定路径的请求都会转发到 upstream_center
location / {
    proxy_pass http://upstream_center;
    proxy_set_header Host $host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
}





