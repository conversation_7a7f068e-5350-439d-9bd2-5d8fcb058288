



# http://anyurlproxy.tppaygatepro3.click/itispasswd321iwjkds/http://www.google.com/
# https://anyurlproxy.tppaygatepro3.click/itispasswd321iwjkds/https://www.google.com/
# http://anyurlproxy.tppaygatepro3.click/itispasswd321iwjkds/http://www.bing.com/
# http://anyurlproxy.tppaygatepro3.click/itispasswd321iwjkds/http://www.bing.com/
# https://anyurlproxy.tppaygatepro3.click/itispasswd321iwjkds/https://www.bing.com/
# http://anyurlproxy.tppaygatepro3.click/itispasswd321iwjkds/http://***********:3305/version
# https://anyurlproxy.tppaygatepro3.click/itispasswd321iwjkds/http://***********:3305/version
# https://anyurlproxy.tppaygatepro3.click/invalid

# https://serverfault.com/questions/983880/is-there-a-way-in-nginx-to-proxy-any-url
# 参考这里实现的任意 url 代理

# 可以反向代理任意网址的访问，不安全，目前仅用于调试支付，所以要限制访问者的 IP 地址
# nginx 如何限制访问者的 IP 地址为内网地址或者在特定的 IP 地址列表里面？
# allow 10.0.0.0/8;
# allow **********/12;
# allow ***********/16;
# allow ************;
# deny all;
# 但是这样怎么允许开发服访问呢？得把公司和家里的公网 IP 维护到这个列表，比较麻烦
# 哦实际上安全隐患主要是暴露内网，禁止代理到内网地址就行了

# 因为是脱掉前缀的，不太可能出现代理循环，所以不加 http 头来检测代理循环了，这样 http 头也干净一些

# openresty 支持 http_sub_module 但 sub_filter 是纯文本替换
# openresty -V 2>&1 | grep -o with-http_sub_module
# 可以用 lua 脚本来替换 html js css 里面的 url



# http://nginx.org/en/docs/http/ngx_http_core_module.html
# 有个支付通道有个头叫 merchant_key
underscores_in_headers on;



# resolver 已经在全局设置了，所以这里忽略
# 路径中的双斜线有时候会被浏览器替换为单斜线，所以都得支持
# 不能设置 X-Forwarded-For 因为就是模拟当前机器去访问的
location ~* ^/itispasswd321iwjkds/(?<pschema>https?):?/+(?<phost>[\w.:\-]+)(?<puri>/.*) {
    set $adr $pschema://$phost;

    # 禁止访问内网地址
    if ($phost_block_internal) {
        return 403;
        # nginx: [emerg] "default_type" directive is not allowed here
        # 这句不生效  add_header content-type text/html;
    }

    # 调试信息，返回 phost 和 phost_block_internal 的值
    # return 200 "<pre>Allowed: phost=$phost, phost_block_internal=$phost_block_internal</pre>";

    rewrite .* $puri break;

    proxy_pass $adr;
    # 这个默认就是打开的
    # http://nginx.org/en/docs/http/ngx_http_proxy_module.html#proxy_pass_request_headers
    proxy_pass_request_headers on;
    proxy_set_header Host $phost;
    # 让重定向的地址也是被代理的
    # http://nginx.org/en/docs/http/ngx_http_proxy_module.html#proxy_redirect
    proxy_redirect http /itispasswd321iwjkds/http;
    proxy_redirect / /itispasswd321iwjkds/$adr/;

    # 当代理的地址是 https 时，需要设置这个
    proxy_ssl_name $phost;
    proxy_ssl_server_name on;

    # https://anyurlproxy.tppaygatepro3.click/itispasswd321iwjkds/https://www.bing.com/
    # upstream sent too big header while reading response header from upstream
    # http://nginx.org/en/docs/http/ngx_http_proxy_module.html
    proxy_buffer_size 32k;
    # nginx: [emerg] "proxy_busy_buffers_size" must be less than the size of all "proxy_buffers" minus one buffer
    proxy_buffering off;
    proxy_buffers 4 32k;
    proxy_busy_buffers_size 32k;
}



error_page 403 /403;

location = /403 {
    default_type text/html;
    return 403 '<!DOCTYPE html><html><body><h1 style="background-color: yellow; border: 4px solid black; text-align: center;">403 Forbidden</h1></body></html>\n';
}


location / {
    default_type text/html;
    return 404 '<!DOCTYPE html><html><body><h1 style="background-color: yellow; border: 4px solid black; text-align: center;">404 Not Found</h1></body></html>\n';
}


