

location / {
    proxy_pass http://upstream_console;
    # https://www.f5.com/company/blog/nginx/avoiding-top-10-nginx-configuration-mistakes
    proxy_http_version 1.1;
    proxy_set_header "Connection" "";
    proxy_set_header Host $host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

        
    # gzip
    gzip            on;
    gzip_vary       on;
    gzip_proxied    any;
    gzip_comp_level 6;
    gzip_types      text/plain text/css text/xml application/json application/javascript application/rss+xml application/atom+xml image/svg+xml;


    gzip_min_length 8000;


}
