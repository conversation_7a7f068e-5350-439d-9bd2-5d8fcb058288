



# 在 cloudflare 的后面


location / {
    set $endpoint $g_upstream_ossdownload_addr;

    rewrite ^/(.*)$ /$1 break;

    proxy_pass http://$endpoint;
    proxy_redirect http://$endpoint/ /;
    proxy_set_header Host $endpoint;

    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    
    proxy_hide_header Content-Disposition;
    
    proxy_hide_header x-amz-id-2;
    proxy_hide_header x-amz-meta-mtime;
    proxy_hide_header x-amz-request-id;
    proxy_hide_header x-amz-server-side-encryption;
    

    # 确保只有一个 Cache-Control 头
    proxy_hide_header Cache-Control;
    # s-maxage 控制边缘缓存时间，max-age 控制浏览器缓存时间
    add_header Cache-Control "public, max-age=60, s-maxage=86400";


}







