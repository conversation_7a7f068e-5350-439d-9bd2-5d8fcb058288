


# nginx: [emerg] unknown "phost" variable
# set 不能用在 http 块下面，只能用在 server, location, if
server {
    set $phost $host;
}


# 定义禁止的内网 IP 范围
map $phost $phost_block_internal {
    default 0;  # 0 表示允许
    ***********:3305 1;
    ~^(10|127)\. 1;  # 10.x.x.x 和 127.x.x.x 内网 IP 地址
    ~^172\.(1[6-9]|2[0-9]|3[01])\. 1;  # 172.16.x.x - 172.31.x.x 内网 IP 地址
    ~^192\.168\. 1;  # 192.168.x.x 内网 IP 地址
    ~^169\.254\. 1;  # 169.254.x.x 内网 IP 地址
}




