



# 加 always 就总是有了，不然 404 Not Found 没有
# 设置是否允许 cookie 传输
add_header 'Access-Control-Allow-Credentials' 'true' always;
# 允许请求地址跨域 * 做为通配符
add_header 'Access-Control-Allow-Origin' '*' always;
# 允许跨域的请求方法
add_header 'Access-Control-Allow-Methods' '*' always;
add_header 'Access-Control-Allow-Headers' '*' always;
# 在这个时间范围内就不用再重复发起预检请求了
add_header 'Access-Control-Max-Age' '86400' always;
# 跨域到 127.0.0.1
# 1. go to chrome://flags/#block-insecure-private-network-requests
# 2. set Block insecure private network requests to Disabled
add_header 'Access-Control-Allow-Private-Network' 'true' always;

if ($request_method = "OPTIONS") {
    return 200;
}
