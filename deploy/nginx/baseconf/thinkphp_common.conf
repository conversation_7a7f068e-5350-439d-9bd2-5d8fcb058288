



# 在 php-fpm 上跑的 thinkphp 项目的几种路径模式


include baseconf/cors.conf;



# 对付类似这种路由
# Route::alias('monthlycardlist', 'api/Monthly_Card_Api/getMonthlyCardList');
location ~ ^/\w+$ {
    # 第一个捕获的值会重新赋值给 $fastcgi_script_name 变量
    # 第二个捕获到的值会自动重新赋值给 $fastcgi_path_info 变量
    fastcgi_split_path_info ^()(/.*)$;
    # 传递给 php 的 $_SERVER['PATH_INFO']
    fastcgi_param  PATH_INFO $fastcgi_path_info;

    fastcgi_param  SCRIPT_FILENAME    $document_root/index.php;
    fastcgi_param  SCRIPT_NAME    /index.php;
    
    fastcgi_index   index.php;
    fastcgi_pass unix:/run/fpmus.sock;
    include         baseconf/fastcgi_params;
    
}

# php 文件限制一下，只能通过 Route::alias() 来访问
#location ~ \.php(/|$) {
#    return 404;
#}

# 因为在 public 目录下，静态资源随意访问
# 好像不需要特别写规则了


# additional config
include baseconf/nginxconfig.io/general.conf;

# security
include     baseconf/nginxconfig.io/security.conf;





# 先加上下面这段，不然管理后台显示不出来

# ThinkPHP
location / {
    if (!-e $request_filename) {
        rewrite  ^/(.*)$  /index.php/$1  last;
        break;
    }
}

# handle .php
location ~ \.php(/|$) {
    # 第一个捕获的值会重新赋值给 $fastcgi_script_name 变量
    # 第二个捕获到的值会自动重新赋值给 $fastcgi_path_info 变量
    fastcgi_split_path_info ^(.+\.php)(/.*)$;
    # 传递给 php 的 $_SERVER['PATH_INFO']
    fastcgi_param  PATH_INFO $fastcgi_path_info;
    
    fastcgi_index   index.php;
    fastcgi_pass unix:/run/fpmus.sock;
    include         baseconf/fastcgi.conf;
    
}
