


# https://stackoverflow.com/questions/29832037/how-to-get-script-directory-in-posix-sh
gScriptDir=$(CDPATH= cd -- "$(dirname -- "$0")" && pwd)
. "$gScriptDir/cppenv_shbase.sh"



# https://windows.php.net/download
# https://stackoverflow.com/questions/1623914/what-is-thread-safe-or-non-thread-safe-in-php
# https://getcomposer.org/download/
# https://getcomposer.org/doc/00-intro.md
cppenv_install4_php_8_win() {
  rm -r -f "$gOptDir/cppenv/app/php"
  # 以 ISAPI 方式运行就用 TS 线程安全版
  # 以 FAST-CGI 或 PHP-FPM 或 CLI 方式运行就用 NTS 非线程安全版
  #app_file php php-7.4.33-nts-Win32-vc15-x64.zip https://windows.php.net/downloads/releases/php-7.4.33-nts-Win32-vc15-x64.zip
  #app_file php php-8.1.24-nts-Win32-vs16-x64.zip https://windows.php.net/downloads/releases/php-8.1.24-nts-Win32-vs16-x64.zip
  # 这个地址比较稳定，上面的地址容易失效
  # https://windows.php.net/downloads/releases/
  app_file php php-8.1.25-nts-Win32-vs16-x64.zip https://windows.php.net/downloads/releases/latest/php-8.1-nts-Win32-vs16-x64-latest.zip
  #app_file php php-8.2.11-nts-Win32-vs16-x64.zip https://windows.php.net/downloads/releases/php-8.2.11-nts-Win32-vs16-x64.zip

  cp -f "$gOptDir/cppenv/app/php/php.ini-development" "$gOptDir/cppenv/app/php/php.ini"
  sed -r -b "s/^;(extension_dir = .ext.)/\1/g" "$gOptDir/cppenv/app/php/php.ini" -i
  sed -r -b "s/^;(extension=curl)/\1/g" "$gOptDir/cppenv/app/php/php.ini" -i
  sed -r -b "s/^;(extension=pdo_mysql)/\1/g" "$gOptDir/cppenv/app/php/php.ini" -i
  sed -r -b "s/^;(extension=pdo_pgsql)/\1/g" "$gOptDir/cppenv/app/php/php.ini" -i
  sed -r -b "s/^;(extension=pgsql)/\1/g" "$gOptDir/cppenv/app/php/php.ini" -i
  sed -r -b "s/^;(extension=mbstring)/\1/g" "$gOptDir/cppenv/app/php/php.ini" -i
  sed -r -b "s/^;(extension=openssl)/\1/g" "$gOptDir/cppenv/app/php/php.ini" -i
  sed -r -b "s/^;(extension=sockets)/\1/g" "$gOptDir/cppenv/app/php/php.ini" -i
  sed -r -b "s/^;(extension=fileinfo)/\1/g" "$gOptDir/cppenv/app/php/php.ini" -i
  sed -r -b "s/^;(extension=gmp)/\1/g" "$gOptDir/cppenv/app/php/php.ini" -i
  sed -r -b "s/^;(extension=gd)/\1/g" "$gOptDir/cppenv/app/php/php.ini" -i
  
  # 默认时区得设置成北京时间
  sed -r -b "s/^;date\.timezone =/date.timezone = Asia\/Shanghai/g" "$gOptDir/cppenv/app/php/php.ini" -i

  sed -r -b "s/^memory_limit = .+/memory_limit = 1G/g" "$gOptDir/cppenv/app/php/php.ini" -i
  
  # 影响调试 max_execution_time = 30
  sed -r -b "s/^max_execution_time = 30/max_execution_time = 3333/g" "$gOptDir/cppenv/app/php/php.ini" -i

  download_file composer.phar https://getcomposer.org/download/2.6.3/composer.phar
  cp -f "$gOptDir/cppenv/download/composer.phar" -t "$gOptDir/cppenv/app/php/"
  echo '@"%~dp0php" "%~dp0composer.phar" %*'>"$gOptDir/cppenv/app/php/composer.bat"
  export PATH="$gOptDir/cppenv/app/php:$PATH"
  composer config -g repo.packagist composer https://mirrors.aliyun.com/composer/
  # composer config -g --unset repos.packagist
  # composer 的配置是在全局固定位置的
  # composer diagnose
  # %APPDATA%\Composer

  download_file composer-unused.phar https://mirror.ghproxy.com/https://github.com/composer-unused/composer-unused/releases/latest/download/composer-unused.phar
  cp -f "$gOptDir/cppenv/download/composer-unused.phar" -t "$gOptDir/cppenv/app/php/"
  echo '@"%~dp0php" "%~dp0composer-unused.phar" %*'>"$gOptDir/cppenv/app/php/composer-unused.bat"

  download_file phpstan.phar https://mirror.ghproxy.com/https://github.com/phpstan/phpstan/releases/download/2.0.2/phpstan.phar
  cp -f "$gOptDir/cppenv/download/phpstan.phar" -t "$gOptDir/cppenv/app/php/"
  echo '@"%~dp0php" "%~dp0phpstan.phar" %*'>"$gOptDir/cppenv/app/php/phpstan.bat"

  # https://docs.phpunit.de/en/10.0/installation.html#installing-phpunit
  # PHPUnit 9.6 requires PHP 7.3; using the latest version of PHP is highly recommended.
  # PHPUnit 10 需要 PHP 8
  download_file phpunit-10.3.5.phar https://phar.phpunit.de/phpunit-10.3.5.phar
  cp -f "$gOptDir/cppenv/download/phpunit-10.3.5.phar" "$gOptDir/cppenv/app/php/phpunit.phar"
  echo '@"%~dp0php" "%~dp0phpunit.phar" %*'>"$gOptDir/cppenv/app/php/phpunit.bat"

  # https://pecl.php.net/package/SeasLog/2.2.0/windows
  download_file php_seaslog-2.2.0-8.1-nts-vs16-x64.zip https://windows.php.net/downloads/pecl/releases/seaslog/2.2.0/php_seaslog-2.2.0-8.1-nts-vs16-x64.zip
  unzip -o -j -d "$gOptDir/cppenv/app/php/ext" "$gOptDir/cppenv/download/php_seaslog-2.2.0-8.1-nts-vs16-x64.zip" *.dll
  sed -r -b "s/extension=xsl/&\nextension=php_seaslog/" "$gOptDir/cppenv/app/php/php.ini" -i

  # https://pecl.php.net/package/redis/5.3.7/windows
  download_file php_redis-5.3.7-8.1-nts-vs16-x64.zip https://windows.php.net/downloads/pecl/releases/redis/5.3.7/php_redis-5.3.7-8.1-nts-vs16-x64.zip
  unzip -o -j -d "$gOptDir/cppenv/app/php/ext" "$gOptDir/cppenv/download/php_redis-5.3.7-8.1-nts-vs16-x64.zip" *.dll
  sed -r -b "s/extension=xsl/&\nextension=php_redis/" "$gOptDir/cppenv/app/php/php.ini" -i

  # https://pecl.php.net/package/rdkafka
  # https://pecl.php.net/package/rdkafka/6.0.1/windows
  download_file php_rdkafka-6.0.1-8.1-nts-vs16-x64.zip https://windows.php.net/downloads/pecl/releases/rdkafka/6.0.1/php_rdkafka-6.0.1-8.1-nts-vs16-x64.zip
  unzip -o -j -d "$gOptDir/cppenv/app/php/ext" "$gOptDir/cppenv/download/php_rdkafka-6.0.1-8.1-nts-vs16-x64.zip" php_rdkafka.dll
  unzip -o -j -d "$gOptDir/cppenv/app/php" "$gOptDir/cppenv/download/php_rdkafka-6.0.1-8.1-nts-vs16-x64.zip" librdkafka.dll
  sed -r -b "s/extension=xsl/&\nextension=rdkafka/" "$gOptDir/cppenv/app/php/php.ini" -i

  # https://pecl.php.net/package/apcu
  download_file php_apcu-5.1.21-8.1-nts-vs16-x64.zip https://windows.php.net/downloads/pecl/releases/apcu/5.1.21/php_apcu-5.1.21-8.1-nts-vs16-x64.zip
  unzip -o -j -d "$gOptDir/cppenv/app/php/ext" "$gOptDir/cppenv/download/php_apcu-5.1.21-8.1-nts-vs16-x64.zip" *.dll
  sed -r -b "s/extension=xsl/&\nextension=apcu/" "$gOptDir/cppenv/app/php/php.ini" -i

  # https://xdebug.org/wizard
  download_file php_xdebug-3.2.1-8.1-vs16-nts-x86_64.dll https://xdebug.org/files/php_xdebug-3.2.1-8.1-vs16-nts-x86_64.dll
  cp "$gOptDir/cppenv/download/php_xdebug-3.2.1-8.1-vs16-nts-x86_64.dll" "$gOptDir/cppenv/app/php/ext/php_xdebug.dll"
  # https://xdebug.org/docs/all_settings
  # develop  Enables Development Helpers including the overloaded var_dump().
  # debug  Enables Step Debugging. This can be used to step through your code while it is running, and analyse values of variables.
  sed -r -b "s/extension=xsl/&\nzend_extension = xdebug\nxdebug.mode = develop,debug\nxdebug.start_with_request = trigger/" "$gOptDir/cppenv/app/php/php.ini" -i

}


main_call cppenv_install4_php_8_win
