


# https://stackoverflow.com/questions/29832037/how-to-get-script-directory-in-posix-sh
gScriptDir=$(CDPATH= cd -- "$(dirname -- "$0")" && pwd)
. "$gScriptDir/cppenv_shbase.sh"





# https://www.sublimemerge.com/download
win_install_SublimeMerge() {
  taskkill /f /t /im sublime_merge.exe || true
  rm -r -f -- "$gOptDir/cppenv/app/SublimeMerge"
  app_file SublimeMerge sublime_merge_build_2092_x64.zip https://download.sublimetext.com/sublime_merge_build_2092_x64.zip
  win_crack_SublimeMerge
  winreg_add_path -a "$gOptDir/cppenv/app/SublimeMerge"
  winmenu_add_SublimeMerge
  
  mkdir -p "$gOptDir/cppenv/app/SublimeMerge/Data/Packages/User"
  cat >"$gOptDir/cppenv/app/SublimeMerge/Data/Packages/User/Preferences.sublime-settings" <<'EOF'


// Settings in here override those in "Default/Preferences.sublime-settings",
// and are overridden in turn by syntax-specific settings.
{

	"commit_graph_ordering": "commit_date",
	"diff_style": "inline",
	"expand_merge_commits_by_default": true,
	"expand_untracked_files_by_default": true,
	"side_bar_layout": "columns",
	"themed_title_bar": true,
	"display_author_date": false,
	"hardware_acceleration": "none",
	"time_format": "system",
	"update_check": false,
	"git_binary": "system",
}


EOF
}


# https://gist.github.com/maboloshi/feaa63c35f4c2baab24c9aaf9b3f4e47
# maboloshi/Crack Sublime Text and Sublime Merge.md
# 这里破解之后，还得手动去 Help -> Enter License 里面随便输入一下！！！
win_crack_SublimeMerge() {
  echo win_crack_SublimeMerge
  md5sum -c <(echo "0b2e4149714cdce4de603cc4004d23ab  $gOptDir/cppenv/app/SublimeMerge/sublime_merge.exe")
  echo win_crack_SublimeMerge: really patch

  printf '\x48\xC7\xC0\x19\x01\x00\x00\xC3' | dd of=$gOptDir/cppenv/app/SublimeMerge/sublime_merge.exe bs=1 seek=$((0x00025800)) conv=notrunc
  printf '\x90\x90\x90\x90\x90'             | dd of=$gOptDir/cppenv/app/SublimeMerge/sublime_merge.exe bs=1 seek=$((0x00028971)) conv=notrunc
  printf '\x90\x90\x90\x90\x90'             | dd of=$gOptDir/cppenv/app/SublimeMerge/sublime_merge.exe bs=1 seek=$((0x0002898A)) conv=notrunc
  printf '\xC3'                             | dd of=$gOptDir/cppenv/app/SublimeMerge/sublime_merge.exe bs=1 seek=$((0x00026FC4)) conv=notrunc
  printf '\xC3'                             | dd of=$gOptDir/cppenv/app/SublimeMerge/sublime_merge.exe bs=1 seek=$((0x00025421)) conv=notrunc
}

# HKEY_CURRENT_USER\Software\Classes  =  HKEY_CURRENT_USER\Software\Classes  +  HKEY_LOCAL_MACHINE\SOFTWARE\Classes
# 约定 ~ 路径和 WKSP 路径不能有空格
# 约定在 Windows 上用 busybox 这样能得到 C:/ D:/ 路径
winmenu_add_SublimeMerge() {
  echo winmenu_add_SublimeMerge
  win_need_busybox
  exewinpath=$(echo $gOptDir/cppenv/app/SublimeMerge/sublime_merge.exe | tr / \\)
  echo "exewinpath=$exewinpath"
  reg add "HKEY_CURRENT_USER\Software\Classes\Directory\shell\SublimeMerge" /ve /d SublimeMerge /f
  reg add "HKEY_CURRENT_USER\Software\Classes\Directory\shell\SublimeMerge" /v Icon /d "$exewinpath" /f
  reg add "HKEY_CURRENT_USER\Software\Classes\Directory\shell\SublimeMerge\command" /ve /d "$exewinpath %V" /f
  reg add "HKEY_CURRENT_USER\Software\Classes\Directory\Background\shell\SublimeMerge" /ve /d SublimeMerge /f
  reg add "HKEY_CURRENT_USER\Software\Classes\Directory\Background\shell\SublimeMerge" /v Icon /d "$exewinpath" /f
  reg add "HKEY_CURRENT_USER\Software\Classes\Directory\Background\shell\SublimeMerge\command" /ve /d "$exewinpath %V" /f
}



main_call win_install_SublimeMerge


