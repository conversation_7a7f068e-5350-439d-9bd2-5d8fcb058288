


# https://stackoverflow.com/questions/29832037/how-to-get-script-directory-in-posix-sh
gScriptDir=$(CDPATH= cd -- "$(dirname -- "$0")" && pwd)
. "$gScriptDir/cppenv_shbase.sh"



# 设计要点：
## 静态库
## 限定指令集为公司 CentOS6 服务器常见指令集


C_FLAGS_WIN_DEBUG="/arch:AVX /utf-8 /MDd /Z7 /Od /RTC1"
CXX_FLAGS_WIN_DEBUG="/arch:AVX /utf-8 /MDd /Z7 /Od /RTC1"
C_FLAGS_WIN_RELEASE="/arch:AVX /utf-8 /MD /Z7 /O1 /DNDEBUG"
CXX_FLAGS_WIN_RELEASE="/arch:AVX /utf-8 /MD /Z7 /O1 /DNDEBUG"
C_FLAGS_UNIX="-m64 -march=sandybridge -Os -g -DNDEBUG"
CXX_FLAGS_UNIX="-m64 -march=sandybridge -Os -g -DNDEBUG"
LINK_FLAGS_UNIX=""
BOOST_CXXFLAGS_WIN="/arch:AVX /utf-8"
BOOST_CXXFLAGS_UNIX="-Wno-deprecated-declarations"
BOOST_TOOLSET_UNIX=gcc
BOOST_OPTIMIZE_OPTS="address-model=64 instruction-set=sandy-bridge debug-symbols=on optimization=space link=static runtime-link=shared threading=multi -j 4"



if [[ "${OS:-}" == "Windows_NT" ]]; then

true

elif [[ "$(uname)" == "Darwin" ]]; then

export CC=clang
export CXX=clang++
C_FLAGS_UNIX="-m64 -march=sandybridge -Oz -g0 -DNDEBUG"
CXX_FLAGS_UNIX="-m64 -march=sandybridge -Oz -g0 -DNDEBUG"
LINK_FLAGS_UNIX=""
BOOST_TOOLSET_UNIX=clang
BOOST_OPTIMIZE_OPTS="address-model=64 instruction-set=sandy-bridge debug-symbols=off optimization=space link=static runtime-link=shared threading=multi -j 4"



elif [[ "$(uname)" == "Darwin" ]]; then

export CC=clang
export CXX=clang++
C_FLAGS_UNIX="-m64 -march=sandybridge -Oz -g0 -DNDEBUG"
CXX_FLAGS_UNIX="-m64 -march=sandybridge -Oz -g0 -DNDEBUG -stdlib=libc++"
LINK_FLAGS_UNIX="-stdlib=libc++ --rtlib=compiler-rt"
BOOST_TOOLSET_UNIX=clang
BOOST_OPTIMIZE_OPTS="address-model=64 instruction-set=sandy-bridge debug-symbols=off optimization=space link=static runtime-link=shared threading=multi -j 4"


elif [[ "1" ]]; then

export CC=clang
export CXX=clang++
C_FLAGS_UNIX="-m64 -march=sandybridge -Oz -g0 -DNDEBUG"
CXX_FLAGS_UNIX="-m64 -march=sandybridge -Oz -g0 -DNDEBUG"
LINK_FLAGS_UNIX=""
BOOST_TOOLSET_UNIX=clang
BOOST_OPTIMIZE_OPTS="address-model=64 instruction-set=sandy-bridge debug-symbols=off optimization=space link=static runtime-link=shared threading=multi -j 16"



else


# 发现 linux 上使用 clang 有挺多问题的，还是用回 gcc
export CC=gcc
export CXX=g++
C_FLAGS_UNIX="-m64 -march=sandybridge -Oz -g0 -DNDEBUG"
CXX_FLAGS_UNIX="-m64 -march=sandybridge -Oz -g0 -DNDEBUG"
LINK_FLAGS_UNIX=""
BOOST_TOOLSET_UNIX=gcc
BOOST_OPTIMIZE_OPTS="address-model=64 instruction-set=sandy-bridge debug-symbols=off optimization=space link=static runtime-link=shared threading=multi -j 4"



fi



# 使用 root 目录保存标记，方便整体打包 root
lazy_install_cpplib() {
  mkdir -p $gOptDir/cppenv/cpplibs
  # 如果存在标记则返回
  if [[ -e $gOptDir/cppenv/cpplibs/${1:?ArgShouldExist}__${2:?ArgShouldExist} ]]; then
    log_info ${1:?ArgShouldExist}__${2:?ArgShouldExist} exists
    return
  fi
  # 执行函数
  install_cpplib_"${1:?ArgShouldExist}"
  # 设置标记
  touch $gOptDir/cppenv/cpplibs/${1:?ArgShouldExist}__${2:?ArgShouldExist}
}


pre_check_requirements() {
  if [[ "${OS:-}" == "Windows_NT" ]]; then
    if [[ ! -e "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" ]]; then
      log_error Windows need Visual Studio 2022
      false
    fi
  fi
}


my_cmake_build() {
  rm -r -f build

  # "$@" 可以完美传递参数，保留里面的空格
  if [[ "${OS:-}" == "Windows_NT" ]]; then
    win_my_cmake_build "$@"
  else
    unix_my_cmake_build "$@"
  fi

#  cp -afv binstall/include/. $gOptDir/cppenv/cpplibs/include
#  cp -afv binstall/lib/. $gOptDir/cppenv/cpplibs/lib
}

# 在 Windows 上调用 cmake 进行构建，通用办法
# 输入参数 (config_opts, target_opts, install_opts)
win_my_cmake_build() {
  # Release 可以适配 Release MinSizeRel RelWithDebInfo，这里带调试信息，最终链接的时候就可以带也可以不带
  # Debug 可以适配 Debug
  # 主要是 msvc 需要严格区分 /MD /MDd 不能链接在一起
  # AddressSanitizer 暂未考虑，可能 Debug 也能适配？
  # 有些库如 zlib 需要这里指定 CMAKE_INSTALL_PREFIX，它不受 --install --prefix 控制
  # 常见警告屏蔽掉
  wopts="/wd4267 /wd4244 /D_WINSOCK_DEPRECATED_NO_WARNINGS"
  cmd <<EOF
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%

cmake -B build/Debug -DCMAKE_INSTALL_PREFIX="$(echo $gOptDir/cppenv/cpplibs)" -DCMAKE_BUILD_TYPE=Debug -DCMAKE_DEBUG_POSTFIX=d -DCMAKE_C_FLAGS_DEBUG="$C_FLAGS_WIN_DEBUG $wopts" -DCMAKE_CXX_FLAGS_DEBUG="$CXX_FLAGS_WIN_DEBUG $wopts" $1
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%
cmake --build build/Debug --config Debug -v -j 4 $2
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%
cmake --install build/Debug --config Debug --prefix "$(echo $gOptDir/cppenv/cpplibs)" $3
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%

cmake -B build/Release -DCMAKE_INSTALL_PREFIX="$(echo $gOptDir/cppenv/cpplibs)" -DCMAKE_BUILD_TYPE=Release -DCMAKE_C_FLAGS_RELEASE="$C_FLAGS_WIN_RELEASE $wopts" -DCMAKE_CXX_FLAGS_RELEASE="$CXX_FLAGS_WIN_RELEASE $wopts" $1
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%
cmake --build build/Release --config Release -v -j 4 $2
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%
cmake --install build/Release --config Release --prefix "$(echo $gOptDir/cppenv/cpplibs)" $3
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%

EOF
}

# 限制指令集到 sandybridge 这样公司一些老的机器也没问题
# The first Intel CPU to support BMI1 was Haswell in 2013. (Also introducing BMI2.)
# https://en.wikipedia.org/wiki/X86_Bit_manipulation_instruction_set
unix_my_cmake_build() {
  # -DCMAKE_INSTALL_LIBDIR=lib 不加这个，也会打到 lib64 目录
  cmake -B build/Release -DCMAKE_INSTALL_LIBDIR=lib -DCMAKE_INSTALL_PREFIX=$gOptDir/cppenv/cpplibs -DCMAKE_BUILD_TYPE=Release -DCMAKE_C_FLAGS_RELEASE="$C_FLAGS_UNIX" -DCMAKE_CXX_FLAGS_RELEASE="$CXX_FLAGS_UNIX" -DCMAKE_EXE_LINKER_FLAGS_RELEASE="$LINK_FLAGS_UNIX" $1
  cmake --build build/Release --config Release -v -j 4 $2
  cmake --install build/Release --config Release --prefix $gOptDir/cppenv/cpplibs $3
}



lazy_install_cpplib_zlib() { lazy_install_cpplib zlib 2_0_6 ; }
install_cpplib_zlib() {
  # https://github.com/zlib-ng/zlib-ng
  decompress_file --cd zlib-ng-2.0.6.tar.gz https://mirror.ghproxy.com/https://github.com/zlib-ng/zlib-ng/archive/refs/tags/2.0.6.tar.gz --strip-components 1
  # 静态链接、尽量不编译单元测试等多余的
  # 兼容 zlib 接口，为需要使用 zlib 的库提供支持
  config_opts="-DBUILD_SHARED_LIBS=OFF -DZLIB_COMPAT=ON -DZLIB_ENABLE_TESTS=OFF"
  my_cmake_build "$config_opts"
}



lazy_install_cpplib_zstd() { lazy_install_cpplib zstd 1_5_2 ; }
install_cpplib_zstd() {
  # libcurl 也依赖于 zstd，避免搜到系统的动态库，也得编译它
  # https://github.com/facebook/zstd
  # https://github.com/facebook/zstd/blob/dev/build/cmake/CMakeLists.txt
  decompress_file --cd zstd-1.5.2.tar.gz https://mirror.ghproxy.com/https://github.com/facebook/zstd/releases/download/v1.5.2/zstd-1.5.2.tar.gz --strip-components 1

  cd build/cmake
  # 静态链接、尽量不编译单元测试等多余的
  config_opts="-DZSTD_BUILD_SHARED=OFF -DZSTD_BUILD_PROGRAMS=OFF -DZSTD_BUILD_TESTS=OFF"
  my_cmake_build "$config_opts"
  cd ../..
}



lazy_install_cpplib_jemalloc() { lazy_install_cpplib jemalloc 5_3_0 ; }
install_cpplib_jemalloc() {
  if [[ "${OS:-}" == "Windows_NT" ]]; then
    # 假装有 jemalloc
    win_add_jemalloc_cmake
  else
    # jemalloc 可以帮助分析内存泄露
    unix_install_cpplib_jemalloc
    unix_add_jemalloc_cmake
  fi
}

unix_install_cpplib_jemalloc() {
  # https://github.com/jemalloc/jemalloc/releases
  decompress_file --cd jemalloc-5.3.0.tar.bz2 https://mirror.ghproxy.com/https://github.com/jemalloc/jemalloc/releases/download/5.3.0/jemalloc-5.3.0.tar.bz2 --strip-components 1

  # https://github.com/jemalloc/jemalloc/blob/dev/INSTALL.md
  CFLAGS="$C_FLAGS_UNIX" CXXFLAGS="$CXX_FLAGS_UNIX" LINKFLAGS="$LINK_FLAGS_UNIX" sh configure --prefix $gOptDir/cppenv/cpplibs --enable-prof --disable-cxx
  # 有个 aligned_alloc 报错的问题简单修复一下
  # conda 给 glibc 头文件打补丁把它弄成 static inline 了
  # https://github.com/microsoft/mimalloc/issues/219
  # https://github.com/conda/conda-build/issues/3165
  # https://github.com/bioconda/bioconda-recipes/pull/26349#issuecomment-773510782
  # 打补丁的 aligned_alloc 会转调 posix_memalign
  # jemalloc 也会重写 posix_memalign 这个符号，所以让 jemalloc 不重写 aligned_alloc 没问题
  sed -Ei 's/define je_aligned_alloc aligned_alloc/define je_aligned_alloc aligned_alloc_DUMMY/' include/jemalloc/jemalloc.h
  make install_include install_lib_static
}

# 在 Windows 上并未实际编译该库
win_add_jemalloc_cmake() {
  mkdir -p $gOptDir/cppenv/cpplibs/lib/cmake/jemalloc
  cat >$gOptDir/cppenv/cpplibs/lib/cmake/jemalloc/jemallocConfig.cmake <<EOF
if(NOT TARGET jemalloc::jemalloc)
  add_library(jemalloc::jemalloc STATIC IMPORTED)
  set_target_properties(jemalloc::jemalloc PROPERTIES
    INTERFACE_INCLUDE_DIRECTORIES "$(echo $gOptDir/cppenv/cpplibs/include)"
    IMPORTED_LOCATION_DEBUG "$(echo $gOptDir/cppenv/cpplibs/lib/zlibstaticd.lib)"
    IMPORTED_LOCATION_RELEASE "$(echo $gOptDir/cppenv/cpplibs/lib/zlibstatic.lib)"
  )
  message(STATUS "jemallocConfig: jemalloc::jemalloc")
endif()
EOF
}

unix_add_jemalloc_cmake() {
  mkdir -p $gOptDir/cppenv/cpplibs/lib/cmake/jemalloc
  cat >$gOptDir/cppenv/cpplibs/lib/cmake/jemalloc/jemallocConfig.cmake <<EOF
if(NOT TARGET jemalloc::jemalloc)
  add_library(jemalloc::jemalloc STATIC IMPORTED)
  set_target_properties(jemalloc::jemalloc PROPERTIES
    INTERFACE_INCLUDE_DIRECTORIES "$(echo $gOptDir/cppenv/cpplibs/include)"
    IMPORTED_LOCATION_RELEASE "$(echo $gOptDir/cppenv/cpplibs/lib/libjemalloc.a)"
  )
  target_compile_definitions(jemalloc::jemalloc INTERFACE HAVE_JEMALLOC=1)
  message(STATUS "jemallocConfig: jemalloc::jemalloc")
endif()
EOF
}



lazy_install_cpplib_openssl() { lazy_install_cpplib openssl 1_1_1p ; }
install_cpplib_openssl() {
  # 不支持 cmake，需要 perl 和 nasm，这俩能通过 micromamba 安装
  # 没有选项可以屏蔽掉 openssl.exe 的生成
  # https://www.openssl.org/source/
  # https://github.com/openssl/openssl
  # https://github.com/openssl/openssl/blob/master/NOTES-WINDOWS.md
  # https://github.com/openssl/openssl/blob/master/INSTALL.md
  # If you link with static OpenSSL libraries then you're expected to additionally link your application with 
  # WS2_32.LIB, GDI32.LIB, ADVAPI32.LIB, CRYPT32.LIB and USER32.LIB.
  # TLS compression is universally agreed to be insecure. 
  # This is due to the CRIME attack -- since which, every major browser vendor has removed support for TLS compression.
  # 默认开启了 no-comp 选项，所以不用管 zlib 问题
  #decompress_file --cd openssl-3.0.3.tar.gz https://www.openssl.org/source/openssl-3.0.3.tar.gz --strip-components 1
  decompress_file --cd openssl-1.1.1p.tar.gz https://www.openssl.org/source/openssl-1.1.1p.tar.gz --strip-components 1

  if [[ "${OS:-}" == "Windows_NT" ]]; then
    win_build_cpplib_openssl
  else
    unix_build_cpplib_openssl
  fi

  # 刪除无用文件
  rm -r -f binstall/lib/engines-3
  rm -r -f binstall/lib/engines-1_1
  rm -r -f binstall/lib/ossl-modules
  rm -r -f binstall/lib/ossl_static.pdb

  cp -afv binstall/include/. $gOptDir/cppenv/cpplibs/include
  cp -afv binstall/lib/. $gOptDir/cppenv/cpplibs/lib
}

# https://github.com/openssl/openssl/issues/9931
# if multiple CL.EXE write to the same .PDB file, please use /FS
win_build_cpplib_openssl() {
  # 不要在 cmd 输入里面加中文注释，因为这个 sh 文件是 utf8 编码，而 cmd 需要 gbk 编码
  cmd <<EOF
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%
where nmake
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%
where perl
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%

set installdir="%CD%/binstall"
rd /q /s build 2>nul
mkdir build

cd build && mkdir Debug && cd Debug
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%
:: Directory given with --prefix MUST be absolute
perl ..\..\Configure VC-WIN64A  --prefix="%installdir%" no-tests no-engine no-hw-padlock no-shared --debug $C_FLAGS_WIN_DEBUG
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%
:: We use /Z7, so no pdb. But install_sw need ossl_static.pdb.
echo 1>ossl_static.pdb
nmake install_sw
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%
cd ../..

rename binstall\lib\libcrypto.lib libcryptod.lib
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%
rename binstall\lib\libssl.lib libssld.lib
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%

cd build && mkdir Release && cd Release
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%
:: Directory given with --prefix MUST be absolute
perl ..\..\Configure VC-WIN64A  --prefix="%installdir%" no-tests no-engine no-hw-padlock no-shared --release $CXX_FLAGS_WIN_RELEASE
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%
:: We use /Z7, so no pdb. But install_sw need ossl_static.pdb.
echo 1>ossl_static.pdb
nmake install_sw
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%
cd ../..

perl    Configure LIST
EOF
}

unix_build_cpplib_openssl() {
which perl

buildtype=linux-x86_64
if [[ "$(uname)" == "Darwin" ]] && [[ "$(uname -m)" == "arm64" ]]; then
  buildtype=darwin64-arm64
elif [[ "$(uname)" == "Darwin" ]] && [[ "$(uname -m)" == "x86_64" ]]; then
  buildtype=darwin64-x86_64
fi

installdir="`pwd`/binstall"
rm -r -f build
mkdir build

cd build && mkdir Release && cd Release
# 若不指定 --libdir=lib 则会打到 lib64 目录里面去
# --openssldir 不指定，用默认的，这样在 Linux 上才能访问到系统安装的根证书
perl ../../Configure $buildtype  --prefix="$installdir" --libdir=lib no-tests no-engine no-hw-padlock no-shared --release $C_FLAGS_UNIX
# 在 CentOS6 试验发现 make -j4 会出错
make install_sw
cd ../..

}



lazy_install_cpplib_libssh2() { lazy_install_cpplib libssh2 1_10_0 ; }
install_cpplib_libssh2() {
  # https://github.com/libssh2/libssh2
  decompress_file --cd libssh2-1.10.0.tar.gz https://mirror.ghproxy.com/https://github.com/libssh2/libssh2/archive/refs/tags/libssh2-1.10.0.tar.gz --strip-components 1

  config_opts="-DBUILD_SHARED_LIBS=OFF -DCRYPTO_BACKEND=OpenSSL -DENABLE_ZLIB_COMPRESSION=OFF"
  my_cmake_build "$config_opts"
}



lazy_install_cpplib_curl() { lazy_install_cpplib curl 7_86_0 ; }
install_cpplib_curl() {

  # https://github.com/curl/curl/tree/master/winbuild
  # https://github.com/curl/curl/blob/master/docs/INSTALL.cmake
  # https://github.com/curl/curl-for-win  libssh2 vs libssh 选的 libssh2
  # When you check out code git and build it, as opposed from a released source code archive, 
  # you need to first run the buildconf.bat batch file (present in the source code root directory) to set things up.
  # When building an application that uses the static libcurl library on Windows, you must define CURL_STATICLIB.
  # The static library name has an '_a' suffix in the basename and the debug library name has a '_debug' suffix in the basename.
  # For example, libcurl_a_debug.lib is a static debug build of libcurl.
  # You must specify any additional dependencies needed by your build of static libcurl (eg: advapi32.lib;crypt32.lib;normaliz.lib;ws2_32.lib;wldap32.lib).
  decompress_file --cd curl-7_86_0.tar.gz https://mirror.ghproxy.com/https://github.com/curl/curl/archive/refs/tags/curl-7_86_0.tar.gz --strip-components 1

  # 使用 libssh2 安装的 cmake 配置文件，不用 curl 自带的这个，这个搜不到 libssh2d.lib
  rm -f CMake/FindLibSSH2.cmake
  sed -Ei 's/find_package\(LibSSH2\)/find_package(Libssh2 REQUIRED)\nset(LIBSSH2_FOUND 1)\nget_target_property(LIBSSH2_INCLUDE_DIR Libssh2::libssh2 INTERFACE_INCLUDE_DIRECTORIES)\nset(LIBSSH2_LIBRARY Libssh2::libssh2)/g' CMakeLists.txt
  sed -i '31i find_dependency(Libssh2)' CMake/curl-config.cmake.in

  zlib_opts="-DZLIB_USE_STATIC_LIBS=ON -DZLIB_ROOT=$(echo $gOptDir/cppenv/cpplibs) -DCMAKE_POLICY_DEFAULT_CMP0074=NEW"
  zlib_opts="-DZLIB_ROOT=$(echo $gOptDir/cppenv/cpplibs) -DCMAKE_POLICY_DEFAULT_CMP0074=NEW"
  ssh_opts="-DCURL_USE_LIBSSH2=ON -DLIBSSH2_ROOT=$(echo $gOptDir/cppenv/cpplibs)"
  if [[ "${OS:-}" == "Windows_NT" ]]; then
    # 这样使用系统安装的根证书
    ssl_opts="-DCURL_ENABLE_SSL=ON -DCURL_USE_SCHANNEL=ON"
  else
    ssl_opts="-DCURL_ENABLE_SSL=ON -DCURL_USE_OPENSSL=ON"
  fi
  config_opts="-DBUILD_CURL_EXE=OFF -DBUILD_SHARED_LIBS=OFF $zlib_opts $ssh_opts $ssl_opts"
  my_cmake_build "$config_opts"
  
  # 兼容公司项目中 #include "libcurl/curl.h" 的写法
  mkdir -p "$gOptDir/cppenv/cpplibs/include/libcurl"
  cat >$gOptDir/cppenv/cpplibs/include/libcurl/curl.h <<EOF
#pragma once
#include <curl/curl.h>
EOF
}



lazy_install_cpplib_boost() { lazy_install_cpplib boost 1_79_0 ; }
install_cpplib_boost() {
  date
  # https://www.boost.org/
  # https://www.boost.org/doc/libs/1_79_0/more/getting_started/windows.html
  # https://www.boost.org/doc/libs/1_79_0/tools/build/doc/html/index.html
  # https://sourceforge.net/projects/boost/files/boost/
  # https://jsproxy.xxx.workers.dev/https://boostorg.jfrog.io/artifactory/main/release/1.79.0/source/boost_1_79_0.tar.bz2
  # https://nchc.dl.sourceforge.net/project/boost/boost/1.79.0/boost_1_79_0.tar.bz2
  # https://liquidtelecom.dl.sourceforge.net/project/boost/boost/1.79.0/boost_1_79_0.tar.bz2
  # https://boostorg.jfrog.io/artifactory/main/release/1.79.0/source/boost_1_79_0.tar.bz2
  date
  decompress_file --cd boost_1_79_0.tar.bz2 https://boostorg.jfrog.io/artifactory/main/release/1.79.0/source/boost_1_79_0.tar.bz2 --strip-components 1
  date
  
  rm -r -f ./stage/lib
  if [[ "${OS:-}" == "Windows_NT" ]]; then
    cmd /c bootstrap.bat vc143
    toolset=msvc-14.3
    variant=debug,release
    cxxflags="$BOOST_CXXFLAGS_WIN"
    cat >>user-config.jam <<EOF
using zlib : 2.0.6 : <include>$(echo $gOptDir/cppenv/cpplibs/include) <name>zlibstaticd <search>$(echo $gOptDir/cppenv/cpplibs/lib) : <variant>debug ;
using zlib : 2.0.6 : <include>$(echo $gOptDir/cppenv/cpplibs/include) <name>zlibstatic <search>$(echo $gOptDir/cppenv/cpplibs/lib) : <variant>release ;
using zstd : 1.5.2 : <include>$(echo $gOptDir/cppenv/cpplibs/include) <name>zstd_staticd <search>$(echo $gOptDir/cppenv/cpplibs/lib) : <variant>debug ;
using zstd : 1.5.2 : <include>$(echo $gOptDir/cppenv/cpplibs/include) <name>zstd_static <search>$(echo $gOptDir/cppenv/cpplibs/lib) : <variant>release ;
EOF
  else
    # --with-toolset=TOOLSET    use specific TOOLSET to build B2 and as default for building Boost
    # 这个 bootstrap.sh 的 toolset 必须用自动检测的，强制用 clang 会有问题，因为没有选项去控制让它静态链接 libstdc++
    # ./b2: /lib64/libstdc++.so.6: version `GLIBCXX_3.4.20' not found (required by ./b2)
    # sh ./bootstrap.sh --with-toolset=$BOOST_TOOLSET_UNIX
    sh ./bootstrap.sh
    toolset=$BOOST_TOOLSET_UNIX
    variant=release
    cxxflags="$BOOST_CXXFLAGS_UNIX"
    # 阻止 has_external_iconv 构建成功，强制要求 iconv (libc)，避免 iconv (separate)
    sed -Ei 's/has_iconv.cpp iconv/FILE_NOT_FOUND.cpp iconv/g' libs/locale/build/Jamfile.v2
    cat >>user-config.jam <<EOF
using zlib : 2.0.6 : <include>$(echo $gOptDir/cppenv/cpplibs/include) <name>z <search>$(echo $gOptDir/cppenv/cpplibs/lib) ;
using zstd : 1.5.2 : <include>$(echo $gOptDir/cppenv/cpplibs/include) <name>zstd <search>$(echo $gOptDir/cppenv/cpplibs/lib) ;
EOF
  fi
  # install 拷贝头文件太慢，所以 stage 之后自己拷贝
  # msvc-14.0  vs2015
  # msvc-14.1  vs2017
  # msvc-14.2  vs2019
  # msvc-14.3  vs2022
  # 编出来自动没有 zlib 和 bzip2，目前也用不到这俩功能
  # /MT: libboost_regex-vc141-mt-s-1_65.lib
  # /MD: libboost_regex-vc141-mt-1_65.lib
  # --layout=versioned The default value is 'versioned' on Windows, and 'system' on Unix.
  # https://www.boost.org/doc/libs/1_78_0/tools/build/doc/html/index.html
  # -s var=value
  # Set the variable var to value in the global scope of the jam language interpreter, overriding variables imported from the environment.
  # https://www.boost.org/doc/libs/1_79_0/libs/iostreams/doc/installation.html
  # https://www.boost.org/doc/libs/1_79_0/libs/regex/doc/html/boost_regex/install.html


  cat user-config.jam

  date
  ./b2 --show-libraries || true
  #  --verbose 似乎没大用
  ./b2 toolset=$toolset $BOOST_OPTIMIZE_OPTS cxxflags="$cxxflags" --layout=tagged variant=$variant --abbreviate-paths --without-python --without-wave --without-graph_parallel --without-mpi -s NO_BZIP2=1 -s NO_LZMA=1 --disable-icu boost.locale.icu=off --user-config="user-config.jam" --prefix="$(echo $gOptDir/cppenv/cpplibs)" install
  date

  # 给 Boost::iostreams 打补丁，配置 cmake 间接链接我们编译的 zlib zstd 静态库
  sed -Ei '/if\(CMAKE_CONFIGURATION_TYPES\)/,$d' $gOptDir/cppenv/cpplibs/lib/cmake/boost_iostreams*/*variant*
  sed -i '$a include(CMakeFindDependencyMacro)\nfind_dependency(ZLIB)\nfind_dependency(zstd)\nset_property(TARGET Boost::iostreams APPEND PROPERTY INTERFACE_LINK_LIBRARIES "ZLIB::ZLIB;zstd::libzstd_static")' $gOptDir/cppenv/cpplibs/lib/cmake/boost_iostreams*/*variant*

  # mv -f 不能覆盖目录，所以得先删除目标目录
#  rm -f -r $gOptDir/cppenv/cpplibs/include/boost
#  mv -f boost $gOptDir/cppenv/cpplibs/include
#  mv -f stage/lib/*.* $gOptDir/cppenv/cpplibs/lib
#  cp -af stage/lib/cmake $gOptDir/cppenv/cpplibs/lib

}



lazy_install_cpplib_mysql() { lazy_install_cpplib mysql maria_3_3_3__5 ; }
install_cpplib_mysql() {
  # 得先把已安装文件删了，不然因为头文件查找定位到那里去，有编译错误
  rm -f -r "$gOptDir/cppenv/cpplibs/include/mariadb"
  rm -f -r "$gOptDir/cppenv/cpplibs/include/mysql"
  rm -f "$gOptDir/cppenv/cpplibs/include/mysql.h"
  # 看了 python 的 mysql 库实现，这个 mysql.h 就是暴露在搜索路径里的，不存在 mysql/mysql.h

  # https://mariadb.com/kb/en/mariadb-connector-c-333-release-notes/
  #decompress_file --cd mariadb-connector-c-3.3.3-src.tar.gz https://dlm.mariadb.com/2319728/Connectors/c/connector-c-3.3.3/mariadb-connector-c-3.3.3-src.tar.gz --strip-components 1
  decompress_file --cd mariadb-connector-c-3.3.3-src.tar.gz https://mirror.ghproxy.com/https://github.com/mariadb-corporation/mariadb-connector-c/archive/refs/tags/v3.3.3.tar.gz --strip-components 1

  # 打补丁解决 /MT 问题
  sed -Ei 's/NOT COMPILER_FLAGS STREQUAL/OFF AND NOT COMPILER_FLAGS STREQUAL/g' CMakeLists.txt
  # 打补丁解决 install pdb 文件的问题，我们用 /Z7 没有 pdb 文件了
  sed -Ei 's/IF\(MSVC\)/IF(OFF AND MSVC)/g' libmariadb/CMakeLists.txt
  # 在 Linux 上 file INSTALL cannot find "/root/a/cppenv/decompress/mariadb-connector-c-3.3.3-src.tar.gz/build/Debug/libmariadb/libmariadb.a"
  # 得打一下补丁
  sed -Ei 's/COMPONENT Development NAMELINK_ONLY/COMPONENT SharedLibraries NAMELINK_ONLY/g' libmariadb/CMakeLists.txt
  sed -Ei 's/COMPONENT Development/COMPONENT SharedLibraries/g' cmake/symlink.cmake
  sed -Ei 's/COMPONENT Development/COMPONENT SharedLibraries/g' mariadb_config/CMakeLists.txt
  # 得打一下补丁，不然关掉 auth_gssapi_client 会报错
  sed -Ei 's/IF.GSSAPI_SOURCES./IF(GSSAPI_SOURCES AND OFF)/g' plugins/auth/CMakeLists.txt

  # 搜索 REGISTER_PLUGIN 找到所有的插件
  # https://mariadb.com/kb/en/specifying-which-plugins-to-build/
  # https://mariadb.com/kb/en/configuration-settings-for-building-connectorc/
  plugins_static="sha256_password pvio_npipe pvio_shmem mysql_native_password mysql_old_password zlib pvio_socket"
  plugins_static=$(echo $plugins_static | tr '[a-z]' '[A-Z]' | sed -E 's/\w+/-DCLIENT_PLUGIN_\0=STATIC/g')
  plugins_off="dialog client_ed25519 caching_sha2_password auth_gssapi_client mysql_clear_password zstd remote_io"
  plugins_off=$(echo $plugins_off | tr '[a-z]' '[A-Z]' | sed -E 's/\w+/-DCLIENT_PLUGIN_\0=OFF/g')
  plugins_off="$plugins_off -DAUTH_GSSAPI_PLUGIN_TYPE=OFF -DREMOTEIO_PLUGIN_TYPE=OFF"
  plugins_default=""
  plugins_default=$(echo $plugins_default | tr '[a-z]' '[A-Z]' | sed -E 's/\w+/-DCLIENT_PLUGIN_\0=DEFAULT/g')
  # cmake --help-policy CMP0074
  zlib_opts="-DWITH_EXTERNAL_ZLIB=ON -DZLIB_USE_STATIC_LIBS=ON -DZLIB_ROOT=$(echo $gOptDir/cppenv/cpplibs) -DCMAKE_POLICY_DEFAULT_CMP0074=NEW"
  # -- MariaDB Connector C: INSTALL_LIBDIR=lib/mariadb  得让它直接放在 lib 目录
  config_opts="$plugins_static $plugins_off $plugins_default -DSKIP_TESTS=1 $zlib_opts -DINSTALL_LIBDIR=lib"
  my_cmake_build "$config_opts" "-t mariadbclient" "--component Development"

  if [[ "${OS:-}" == "Windows_NT" ]]; then
    win_add_mysql_cmake
  else
    unix_add_mysql_cmake
  fi
}

win_add_mysql_cmake() {
  mkdir -p $gOptDir/cppenv/cpplibs/lib/cmake/MariaDBClient
  cat >$gOptDir/cppenv/cpplibs/lib/cmake/MariaDBClient/MariaDBClientConfig.cmake <<EOF
if(NOT TARGET MariaDBClient::MariaDBClient)
  add_library(MariaDBClient::MariaDBClient STATIC IMPORTED)
  set_target_properties(MariaDBClient::MariaDBClient PROPERTIES
    INTERFACE_INCLUDE_DIRECTORIES "$(echo $gOptDir/cppenv/cpplibs/include/mariadb)"
    IMPORTED_LOCATION_DEBUG "$(echo $gOptDir/cppenv/cpplibs/lib/mariadbclientd.lib)"
    IMPORTED_LOCATION_RELEASE "$(echo $gOptDir/cppenv/cpplibs/lib/mariadbclient.lib)"
  )
  target_link_libraries(MariaDBClient::MariaDBClient INTERFACE Shlwapi)
  message(STATUS "MariaDBClientConfig: MariaDBClient::MariaDBClient")
endif()
EOF
}

unix_add_mysql_cmake() {
  mkdir -p $gOptDir/cppenv/cpplibs/lib/cmake/MariaDBClient
  cat >$gOptDir/cppenv/cpplibs/lib/cmake/MariaDBClient/MariaDBClientConfig.cmake <<EOF
if(NOT TARGET MariaDBClient::MariaDBClient)
  add_library(MariaDBClient::MariaDBClient STATIC IMPORTED)
  set_target_properties(MariaDBClient::MariaDBClient PROPERTIES
    INTERFACE_INCLUDE_DIRECTORIES "$(echo $gOptDir/cppenv/cpplibs/include/mariadb)"
    IMPORTED_LOCATION_RELEASE "$(echo $gOptDir/cppenv/cpplibs/lib/libmariadbclient.a)"
  )
  message(STATUS "MariaDBClientConfig: MariaDBClient::MariaDBClient")
endif()
EOF
}


# 编译 cppcheck 需要这个老版本的 pcre
lazy_install_cpplib_pcre() { lazy_install_cpplib pcre 8_45 ; }
install_cpplib_pcre() {
  # https://github.com/danmar/cppcheck
  decompress_file --cd pcre-8.45.tar.bz2 https://nchc.dl.sourceforge.net/project/pcre/pcre/8.45/pcre-8.45.tar.bz2 --strip-components 1

  config_opts="-DPCRE_SUPPORT_LIBBZ2=OFF -DPCRE_SUPPORT_LIBREADLINE=OFF -DPCRE_SUPPORT_LIBEDIT=OFF"
  my_cmake_build "$config_opts"
}



lazy_install_cpplib_cppcheck() { lazy_install_cpplib cppcheck 2_9 ; }
install_cpplib_cppcheck() {
  # https://github.com/danmar/cppcheck
  decompress_file --cd cppcheck-2.9.tar.gz https://mirror.ghproxy.com/https://github.com/danmar/cppcheck/archive/refs/tags/2.9.tar.gz --strip-components 1

  # 静态链接、尽量不编译单元测试等多余的
  config_opts="-DHAVE_RULES=ON -DUSE_MATCHCOMPILER=ON -DCPPCHK_GLIBCXX_DEBUG=OFF"
  my_cmake_build "$config_opts"
}


lazy_install_cpplib_ClangBuildAnalyzer() { lazy_install_cpplib ClangBuildAnalyzer 1_4_0 ; }
install_cpplib_ClangBuildAnalyzer() {
  # https://github.com/aras-p/ClangBuildAnalyzer
  decompress_file --cd ClangBuildAnalyzer-1.4.0.tar.gz https://mirror.ghproxy.com/https://github.com/aras-p/ClangBuildAnalyzer/archive/refs/tags/v1.4.0.tar.gz --strip-components 1

  config_opts=""
  my_cmake_build "$config_opts"
}


# 这个库会利用 civetweb 库暴露端口监听 http 请求
lazy_install_cpplib_prometheuscpp() { lazy_install_cpplib prometheuscpp 1_1_0 ; }
install_cpplib_prometheuscpp() {
  # https://github.com/jupp0r/prometheus-cpp
  # https://mirror.ghproxy.com/https://github.com/jupp0r/prometheus-cpp/archive/refs/tags/jupp0r/prometheus-cpp-1.1.0.tar.gz
  # 如果以后有别的地方用 civetweb，再把 civetweb 拆出来编译
  if [[ "${OS:-}" == "Windows_NT" ]]; then
    # 3rdparty/civetweb/test/nonlatin 里面有中文和韩文的文件名，在 Windows busybox 下处理不了，加 --exclude 解决
    # tar: can't create symlink 'cmake/project-import-cmake/sample_client.cc' to '../../push/tests/integration/sample_client.cc'  用 --exclude 解决不了
    # WinRAR 的 -x 选项必须用反斜线，这里写两个反斜线会被 sh 转义成一个反斜线
    decompress_file --cd --WinRAR prometheuscpp-1.1.0.tar.gz https://mirror.ghproxy.com/https://github.com/jupp0r/prometheus-cpp/releases/download/v1.1.0/prometheus-cpp-with-submodules.tar.gz -ep4prometheus-cpp-with-submodules\\ -xsample_*.cc -x*\\nonlatin\\
  else
    decompress_file --cd prometheuscpp-1.1.0.tar.gz https://mirror.ghproxy.com/https://github.com/jupp0r/prometheus-cpp/releases/download/v1.1.0/prometheus-cpp-with-submodules.tar.gz --strip-components 1 --exclude '*/3rdparty/civetweb/test/nonlatin/*' --exclude '*/sample_*.cc'
  fi

  # https://github.com/jupp0r/prometheus-cpp/blob/master/CMakeLists.txt
  config_opts="-DBUILD_SHARED_LIBS=OFF -DENABLE_PUSH=OFF -DENABLE_COMPRESSION=OFF -DENABLE_TESTING=OFF -DTHIRDPARTY_CIVETWEB_WITH_SSL=OFF -DOVERRIDE_CXX_STANDARD_FLAGS=OFF -DRUN_IWYU=OFF"
  my_cmake_build "$config_opts"
}




restore_rootdir() {
  if [[ -e $gOptDir/cppenv/cpplibs ]]; then
    log_info rootdir exists
    return
  fi
  if [[ ! -e rootdir/cpplibs-$CFOSARCH.txz ]]; then
    log_info no package rootdir/cpplibs-$CFOSARCH.txz
    return
  fi
  mkdir -p $gOptDir/cppenv
  time tar xJf rootdir/cpplibs-$CFOSARCH.txz -C $gOptDir/cppenv
}

install_rootdir() {
  restore_rootdir
  mkdir -p $gOptDir/cppenv/cpplibs/include
  mkdir -p $gOptDir/cppenv/cpplibs/lib
}

cleanup_decompressdir() {
  rm -r -f -- $gOptDir/cppenv/decompress
}



lazy_install() {
  pre_check_requirements
  sysenc_verbose_call install_rootdir
  sysenc_verbose_call lazy_install_cpplib_zlib 30
  sysenc_verbose_call lazy_install_cpplib_zstd 30
  sysenc_verbose_call lazy_install_cpplib_jemalloc 30
  sysenc_verbose_call lazy_install_cpplib_openssl 1000
  sysenc_verbose_call lazy_install_cpplib_libssh2 200
  sysenc_verbose_call lazy_install_cpplib_curl 300
  sysenc_verbose_call lazy_install_cpplib_boost 1200
  sysenc_verbose_call lazy_install_cpplib_mysql 60
  sysenc_verbose_call lazy_install_cpplib_pcre 60
  # cppcheck 和 ClangBuildAnalyzer 后来发现用处不大
  #sysenc_verbose_call lazy_install_cpplib_cppcheck 60
  #sysenc_verbose_call lazy_install_cpplib_ClangBuildAnalyzer 60
  sysenc_verbose_call lazy_install_cpplib_prometheuscpp 60

  #sysenc_verbose_call cleanup_decompressdir 300
  log_info FINISHED: dstdir=$gOptDir/cppenv
}

test_install() {
  pre_check_requirements
  install_rootdir
  #install_cpplib_zlib
  #install_cpplib_zstd
  #install_cpplib_jemalloc
  #install_cpplib_openssl
  install_cpplib_libssh2
  install_cpplib_curl
  install_cpplib_boost
  #install_cpplib_mysql
  
}

main_call lazy_install


