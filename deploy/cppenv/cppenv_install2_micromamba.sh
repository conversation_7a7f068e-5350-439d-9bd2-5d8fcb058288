


# https://stackoverflow.com/questions/29832037/how-to-get-script-directory-in-posix-sh
gScriptDir=$(CDPATH= cd -- "$(dirname -- "$0")" && pwd)
. "$gScriptDir/cppenv_shbase.sh"






lazy_install_micromamba() { lazy_call install_micromamba 221121 ; }
install_micromamba() {
  in_china=0
  if curl -s ipinfo.io | grep Asia/Shanghai; then
    in_china=1
  fi


  # 从官网下载即可 https://mamba.readthedocs.io/en/latest/installation.html
  # 也可以找镜像 https://anaconda.org/conda-forge/micromamba
  mkdir -p $gOptDir/cppenv/micromamba
  if [[ "${OS:-}" == "Windows_NT" ]]; then
    win_install_micromamba
  elif [[ "$(uname)" == "Darwin" ]] && [[ "$(uname -m)" == "arm64" ]]; then
    mac_install_micromamba
  elif [[ "$(uname)" == "Darwin" ]] && [[ "$(uname -m)" == "x86_64" ]]; then
    osx_install_micromamba
  else
    linux_install_micromamba
  fi

  if [ "$in_china" = "1" ]; then
    config_micromamba_bfsu
  else
    config_micromamba_bfsu
  fi
}

win_install_micromamba() {
  decompress_file micromamba.tar.bz2 https://micro.mamba.pm/api/micromamba/win-64/latest
  cp -f "$gOptDir/cppenv/decompress/micromamba.tar.bz2/Library/bin/micromamba.exe" -t "$gOptDir/cppenv/bin"

  export MAMBA_ROOT_PREFIX="$gOptDir/cppenv/micromamba"
  which micromamba
  micromamba shell init -s cmd.exe
  echo "micromamba activate">>"$gOptDir/cppenv/micromamba/condabin/mamba_hook.bat"

  # 广播 WM_SETTINGCHANGE 消息
  setx PYTHONDONTWRITEBYTECODE 1
}

mac_install_micromamba() {
  decompress_file micromamba.tar.bz2 https://micro.mamba.pm/api/micromamba/osx-arm64/latest
  mkdir -p "$gOptDir/cppenv/bin"
  cp -f "$gOptDir/cppenv/decompress/micromamba.tar.bz2/bin/micromamba" "$gOptDir/cppenv/bin"

  export MAMBA_ROOT_PREFIX="$gOptDir/cppenv/micromamba"
  which micromamba
  micromamba shell init -s bash
  micromamba shell init -s zsh

  sed -i -r 's/^micromamba activate$//g' ~/.bash_profile
  echo "micromamba activate">>~/.bash_profile
  sed -i -r 's/^micromamba activate$//g' ~/.zshrc
  echo "micromamba activate">>~/.zshrc
}

osx_install_micromamba() {
  decompress_file micromamba.tar.bz2 https://micro.mamba.pm/api/micromamba/osx-64/latest
  cp -f "$gOptDir/cppenv/decompress/micromamba.tar.bz2/bin/micromamba" -t "$gOptDir/cppenv/bin"

  export MAMBA_ROOT_PREFIX="$gOptDir/cppenv/micromamba"
  which micromamba
  micromamba shell init -s bash
  micromamba shell init -s zsh

  sed -i -r 's/^micromamba activate$//g' ~/.bash_profile
  echo "micromamba activate">>~/.bash_profile
  sed -i -r 's/^micromamba activate$//g' ~/.zshrc
  echo "micromamba activate">>~/.zshrc
}

linux_install_micromamba() {
  # 这个地址在公司服务器上不行 https://micro.mamba.pm/api/micromamba/linux-64/latest
  # https://anaconda.org/conda-forge/micromamba/files
  # https://anaconda.org/conda-forge/micromamba/1.0.0/download/linux-64/micromamba-1.0.0-0.tar.bz2
  # http://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/conda-forge/linux-64/micromamba-1.0.0-0.tar.bz2

  case $(uname -m) in
      x86_64) arch="linux-64" ;;
      *)      arch="linux-aarch64" ;;
  esac

  url="https://anaconda.org/conda-forge/micromamba/1.5.8/download/$arch/micromamba-1.5.8-0.tar.bz2"
  if [ "$in_china" = "1" ]; then
    url="http://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/conda-forge/$arch/micromamba-1.5.8-0.tar.bz2"
  fi

  decompress_file micromamba.tar.bz2 "$url"
  cp -f "$gOptDir/cppenv/decompress/micromamba.tar.bz2/bin/micromamba" -t "$gOptDir/cppenv/bin"

  export MAMBA_ROOT_PREFIX="$gOptDir/cppenv/micromamba"
  which micromamba
  micromamba shell init -s bash
}

# https://developer.aliyun.com/mirror/anaconda
# 阿里云在家里下载 openjdk 太慢了
config_micromamba_aliyun() {
  echo "

channels:
  - conda-forge
show_channel_urls: true
default_channels:
  - http://mirrors.aliyun.com/anaconda/pkgs/main
  - http://mirrors.aliyun.com/anaconda/pkgs/r
  - http://mirrors.aliyun.com/anaconda/pkgs/msys2
custom_channels:
  conda-forge: http://mirrors.aliyun.com/anaconda/cloud
  msys2: http://mirrors.aliyun.com/anaconda/cloud
  bioconda: http://mirrors.aliyun.com/anaconda/cloud
  menpo: http://mirrors.aliyun.com/anaconda/cloud
  pytorch: http://mirrors.aliyun.com/anaconda/cloud
  simpleitk: http://mirrors.aliyun.com/anaconda/cloud

">$gOptDir/cppenv/micromamba/.condarc

  micromamba info
  micromamba config sources
  micromamba config list

}


# 北京外国语大学
# 速度超快，可能用这个的人少
config_micromamba_bfsu() {
  echo "

channels:
  - conda-forge
show_channel_urls: true
default_channels:
  - http://mirrors.bfsu.edu.cn/anaconda/pkgs/main
  - http://mirrors.bfsu.edu.cn/anaconda/pkgs/r
  - http://mirrors.bfsu.edu.cn/anaconda/pkgs/msys2
custom_channels:
  conda-forge: http://mirrors.bfsu.edu.cn/anaconda/cloud
  msys2: http://mirrors.bfsu.edu.cn/anaconda/cloud
  bioconda: http://mirrors.bfsu.edu.cn/anaconda/cloud
  menpo: http://mirrors.bfsu.edu.cn/anaconda/cloud
  pytorch: http://mirrors.bfsu.edu.cn/anaconda/cloud
  simpleitk: http://mirrors.bfsu.edu.cn/anaconda/cloud

">$gOptDir/cppenv/micromamba/.condarc

  micromamba info
  micromamba config sources
  micromamba config list

}




main_call lazy_install_micromamba


