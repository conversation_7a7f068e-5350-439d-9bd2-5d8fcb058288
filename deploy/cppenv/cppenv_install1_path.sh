


# https://stackoverflow.com/questions/29832037/how-to-get-script-directory-in-posix-sh
gScriptDir=$(CDPATH= cd -- "$(dirname -- "$0")" && pwd)
. "$gScriptDir/cppenv_shbase.sh"





# https://github.com/aria2/aria2/releases
win_install_aria2() {
  download_file aria2-1.36.0-win-64bit-build1.zip https://mirror.ghproxy.com/https://github.com/aria2/aria2/releases/download/release-1.36.0/aria2-1.36.0-win-64bit-build1.zip
  unzip -o -j -d "$gOptDir/cppenv/bin" "$gOptDir/cppenv/download/aria2-1.36.0-win-64bit-build1.zip" *.exe
}


win_install_path() {
  winreg_add_path -a "$gOptDir/cppenv/bin"
  winreg_add_path -a "$gOptDir/cppenv/cpplibs/bin"
  winreg_add_path -a "$gOptDir/cppenv/app/php"
  winreg_add_path -a "$gOptDir/cppenv/app/deno"
  winreg_add_path -a "$gOptDir/cppenv/app/node"
  winreg_add_path -a "$APPDATA/npm"
  winreg_add_path -a "C:/Program Files/Redis"
  winreg_add_path -a "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin"
  winreg_add_path -a "C:/Program Files/7-zip"
  winreg_add_path -a "C:/Program Files/WinRAR"
  winreg_add_path -a "C:/Program Files/MySQL/MySQL Server 8.0/bin"


  # 用户级的 PATHEXT 不会自动合并系统级的，所以这里写全了
  pathexttmp=".COM;.EXE;.BAT;.CMD;.SH;.VBS;.VBE;.JS;.JSE;.PY;.PYW;.LNK;.WSF;.WSH;.MSC"
  if [[ "$pathexttmp" != "$PATHEXT" ]]; then
    echo setx PATHEXT "$pathexttmp"
    setx PATHEXT "$pathexttmp"
  fi

  log_info BEGIN broadcast WM_SETTINGCHANGE
  # 最后广播 WM_SETTINGCHANGE 消息
  setx PYTHONDONTWRITEBYTECODE 1
  log_info END broadcast WM_SETTINGCHANGE
}




if [[ "${OS:-}" == "Windows_NT" ]]; then
  main_call win_install_aria2
  main_call win_install_path
fi




