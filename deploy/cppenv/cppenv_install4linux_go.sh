

# https://stackoverflow.com/questions/29832037/how-to-get-script-directory-in-posix-sh
gScriptDir=$(CDPATH= cd -- "$(dirname -- "$0")" && pwd)
. "$gScriptDir/cppenv_shbase.sh"



# https://go.dev/doc/install
# http://mirrors.ustc.edu.cn/golang/
cppenv_install4linux_go() {
  cpu=amd64
  if [ "$(uname -m)" = "aarch64" ]; then
    cpu=arm64
  fi

  # 检测当前服务器的地理位置，香港和境外服务器都不走国内镜像
  if curl -s ipinfo.io | grep Asia/Shanghai; then
    srcurl=http://mirrors.ustc.edu.cn/golang/go1.22.2.linux-$cpu.tar.gz
  else
    srcurl=https://go.dev/dl/go1.22.2.linux-$cpu.tar.gz
  fi
  download_file go1.22.2.linux-$cpu.tar.gz $srcurl
  
  rm -rf /usr/local/go
  tar -C /usr/local -xzf $gOptDir/cppenv/download/go1.22.2.linux-$cpu.tar.gz

}



# 这是如何设置环境的示例
go_config() {
  export PATH=$PATH:/usr/local/go/bin
  
  if ! grep "Amazon Linux" /etc/os-release; then
    export GOPROXY=https://mirrors.aliyun.com/goproxy/,https://goproxy.cn,direct
  fi
  
  export GOSUMDB=off

  go env
  go version
}





main_call lazy_call cppenv_install4linux_go 1001



