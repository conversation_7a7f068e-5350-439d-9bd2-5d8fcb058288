


# 参考 conda-forge 的平台分类
# https://anaconda.org/conda-forge/go
CFOSARCH=linux-64
MYOSARCH=linux
gOptDir=~/a
if [[ "${OS:-}" == "Windows_NT" ]]; then
  CFOSARCH=win-64
  MYOSARCH=win
  # 设计 gOptDir 是因为 Windows 管理员的 ~ 目录和普通用户的不一样
  gOptDir=C:/a
elif [[ "$(uname)" == "Darwin" ]] && [[ "$(uname -m)" == "arm64" ]]; then
  CFOSARCH=osx-arm64
  MYOSARCH=mac
elif [[ "$(uname)" == "Darwin" ]] && [[ "$(uname -m)" == "x86_64" ]]; then
  CFOSARCH=osx-64
  MYOSARCH=osx
fi


#------------------------------------------------------------------------------#
# 日志
#------------------------------------------------------------------------------#
# 清空颜色
NC='\e[0m'
# 加粗前景色
RED='\e[1;31m'
GREEN='\e[1;32m'
YELLOW='\e[1;33m'
BLUE='\e[1;34m'
PURPLE='\e[1;35m'
CYAN='\e[1;36m'
WHITE='\e[1;37m'
# 背景色
BG_RED='\e[41m'
BG_GREEN='\e[42m'
BG_YELLOW='\e[43m'
BG_BLUE='\e[44m'
BG_PURPLE='\e[45m'
BG_CYAN='\e[46m'
BG_WHITE='\e[47m'

# 清空颜色
NC=$(printf '\033[0m')
# 加粗前景色
RED=$(printf '\033[1;31m')
GREEN=$(printf '\033[1;32m')
YELLOW=$(printf '\033[1;33m')
BLUE=$(printf '\033[1;34m')
PURPLE=$(printf '\033[1;35m')
CYAN=$(printf '\033[1;36m')
WHITE=$(printf '\033[1;37m')
# 加粗亮度前景色
HI_RED=$(printf '\033[1;91m')
HI_GREEN=$(printf '\033[1;92m')
HI_YELLOW=$(printf '\033[1;93m')
HI_BLUE=$(printf '\033[1;94m')
HI_PURPLE=$(printf '\033[1;95m')
HI_CYAN=$(printf '\033[1;96m')
HI_WHITE=$(printf '\033[1;97m')
# 背景色
BG_RED=$(printf '\033[41m')
BG_GREEN=$(printf '\033[42m')
BG_YELLOW=$(printf '\033[43m')
BG_BLUE=$(printf '\033[44m')
BG_PURPLE=$(printf '\033[45m')
BG_CYAN=$(printf '\033[46m')
BG_WHITE=$(printf '\033[47m')
# 加粗亮度背景色
BG_HI_RED=$(printf '\033[1;91m')
BG_HI_GREEN=$(printf '\033[1;92m')
BG_HI_YELLOW=$(printf '\033[1;93m')
BG_HI_BLUE=$(printf '\033[1;94m')
BG_HI_PURPLE=$(printf '\033[1;95m')
BG_HI_CYAN=$(printf '\033[1;96m')
BG_HI_WHITE=$(printf '\033[1;97m')
# VSCode 显示 ${BG_GREEN}${WHITE} 为绿底黑字可能是配色问题


# busybox 的 date 不支持 date '+%F %T'
# 日志函数只负责加时间戳，不负责写文件，写文件由包装函数负责
# echo -e 会解释所有参数的斜线，不适合 Windows 上的路径
# 特别想要颜色可以这样 log_info `printf "${RED}red${NC}"`
# 如果你想保持参数之间的原始空格或特殊字符，应该使用 "$@"。
# 如果你只想要一个使用空格分隔的参数串，则使用 "$*"。
log_debug() {
  #echo -e `date -Iseconds` DEBUG "$@"
  printf "`date -Iseconds` DEBUG %s\n" "$*"
}
log_info() {
  #echo -e `date -Iseconds` INFO "$@"
  # 此处使用 "$*" $* "$@" $@ 会有不同的效果
  printf "`date -Iseconds` ${BG_GREEN}${WHITE}INFO${NC} %s\n" "$*"
}
log_error() {
  #echo -e `date -Iseconds`${RED} ERROR${NC} "$@"
  printf "`date -Iseconds` ${BG_RED}${WHITE}ERROR${NC} %s\n" "$*"
}
# 通过条件 || 通过条件 || log_fatal something wrong
log_fatal() {
  printf "`date -Iseconds` ${BG_RED}${WHITE}FATAL${NC} %s\n" "$*"
  return 1
}
#==============================================================================#



# 显示开始和结束，开始时提示大概需要多少时间
log_call() {
  # 记录开始时间点
  log_info BEGIN ${1:?ArgShouldExist}, about ${2:-404}s...
  log_call__t1=`date +%s`
  # 执行函数
  set -x
  "${1:?ArgShouldExist}"
  set +x
  # 记录结束时间点
  elapsed=$(( `date +%s` - log_call__t1 ))
  log_info `printf "${CYAN}${elapsed}s${NC}"` END ${1:?ArgShouldExist}
}

# 程序输出在 Windows 上是 gbk 编码，在其它地方是 utf8 编码，这里得特别处理使日志文件为 utf8 编码
sysenc_silent_call() {
  mkdir -p "$gOptDir/cppenv/tmp"
  if [[ "${OS:-}" == "Windows_NT" ]]; then
    PYTHONUNBUFFERED=1 log_call "$@" 2>&1 | iconv -c -f gbk -t utf8 | tee -a "$gOptDir/cppenv/tmp/cppenv.log" | grep -E " INFO| ERROR" | iconv -c -f utf8 -t gbk
  else
    log_call "$@" 2>&1 | tee -a "$gOptDir/cppenv/tmp/cppenv.log" | grep --line-buffered --color=never -E " INFO| ERROR"
  fi
}

# 程序输出在 Windows 上是 gbk 编码，在其它地方是 utf8 编码，这里得特别处理使日志文件为 utf8 编码
sysenc_verbose_call() {
  mkdir -p "$gOptDir/cppenv/tmp"
  if [[ "${OS:-}" == "Windows_NT" ]]; then
    PYTHONUNBUFFERED=1 log_call "$@" 2>&1 | iconv -c -f gbk -t utf8 | tee -a "$gOptDir/cppenv/tmp/cppenv.log" | iconv -c -f utf8 -t gbk
  else
    log_call "$@" 2>&1 | tee -a "$gOptDir/cppenv/tmp/cppenv.log"
  fi
}

# 程序发现是 pipe 就用 utf8 编码，这里在 Windows 上得特别处理将其转成 gbk 输出，而且日志文件得保持 utf8 编码
# busybox 的 grep 不支持 --line-buffered --color=never
utf8enc_silent_call() {
  mkdir -p "$gOptDir/cppenv/tmp"
  if [[ "${OS:-}" == "Windows_NT" ]]; then
    PYTHONUNBUFFERED=1 log_call "$@" 2>&1 | tee -a "$gOptDir/cppenv/tmp/cppenv.log" | grep -E " INFO| ERROR" | iconv -c -f utf8 -t gbk
  else
    log_call "$@" 2>&1 | tee -a "$gOptDir/cppenv/tmp/cppenv.log" | grep --line-buffered --color=never -E " INFO| ERROR"
  fi
}

# 程序发现是 pipe 就用 utf8 编码，这里在 Windows 上得特别处理将其转成 gbk 输出，而且日志文件得保持 utf8 编码
# The problem is that libc will line-buffer when stdout to screen and block-buffer when stdout to a file, but no-buffer for stderr.
# 在 Windows 上没找到解决办法，在其它系统可以用 stdbuf 命令，但似乎也只能是调用方来使用 stdbuf -oL sh cppenv.sh call install_cppenv
# 在 Linux 上实际测试发现，不用 stdbuf -oL 刷新也挺及时的，为什么？
# 如果是 py 脚本则可以用环境变量 PYTHONUNBUFFERED
# Mambaforge-4.12.0-2-Windows-x86_64.exe pipe 的时候，前面的进度条用的 gbk 编码，后面的 mamba 画面用的 utf8 编码，不好处理
# 这里就简单的剪掉前面的进度条，使日志文件保持 utf8 编码
utf8enc_verbose_call() {
  mkdir -p "$gOptDir/cppenv/tmp"
  if [[ "${OS:-}" == "Windows_NT" ]]; then
    PYTHONUNBUFFERED=1 log_call "$@" 2>&1 | iconv -c -f utf8 -t utf8 | tee -a "$gOptDir/cppenv/tmp/cppenv.log" | iconv -c -f utf8 -t gbk
  else
    log_call "$@" 2>&1 | tee -a "$gOptDir/cppenv/tmp/cppenv.log"
  fi
}



# key__value 作为文件名的方式有问题，容易新旧两个版本来回重复执行
# 得让 value 作为文件内容，想重新执行就递增这个数字
lazy_call() {
  mkdir -p $gOptDir/cppenv/mark

  markfile="$gOptDir/cppenv/mark/${1:?ArgShouldExist}"
  [ -f "$markfile" ] && oldmark=$(cat "$markfile") || oldmark=0
  if [ "${2:?ArgShouldExist}" -le "$oldmark" ]; then
    log_debug AlreadyLatest $markfile
    return
  fi
  
  log_info ${HI_PURPLE}RUN${NC} ${1:?ArgShouldExist} ${2:?ArgShouldExist}
  # 执行函数
  "${1:?ArgShouldExist}"

  # 设置标记
  echo "${2:?ArgShouldExist}" > "$markfile"
}


main_call() {
  if echo ~ | grep ' '; then
    echo "ERROR: Space in home path."
  elif [ "$0" == "sh" ] || [ ! "${0##*.}" == "sh" ]; then
    echo "DETECT: It is sourcing this script."
    setup_envvars
  else

    setup_subshell
    setup_envvars

    "$@"
  fi
}


# 先删后加在开头加 add_path <path>
# 先删后加在末尾加 add_path -a <path>
add_path() {
  flag_append= && [[ "$1" == "-a" ]] && flag_append="$1" && shift
  #log_info add_path $flag_append ${1:?ArgShouldExist}
  added="${1:?ArgShouldExist}"
  if [[ "${OS:-}" == "Windows_NT" ]]; then
    p=";${PATH//;/;;};"
    p="${p//\\/\/}"
    # 在 busybox 中的 PATH 统一用斜线，所以最后不转回反斜线
    added="${added//\\/\/}"
    p="${p//;$added;/}"
    [[ "$flag_append" ]] && p="$p;$added;" || p=";$added;$p"
    p="${p//;;/;}"
    p="${p#;}"
    p="${p%;}"
    export PATH="$p"
  else
    p=":${PATH//:/::}:"
    p="${p//:$added:/}"
    [[ "$flag_append" ]] && p="$p:$added:" || p=":$added:$p"
    p="${p//::/:}"
    p="${p#:}"
    p="${p%:}"
    export PATH="$p"
  fi
}

# 先删后加在开头加 winreg_add_path <path>
# 先删后加在末尾加 winreg_add_path -a <path>
# 刪除 winreg_add_path -d <path>
winreg_add_path() {
  [[ "${OS:-}" == "Windows_NT" ]] || return
  flag_append= && [[ "$1" == "-a" ]] && flag_append="$1" && shift
  flag_delete && [[ "$1" == "-d" ]] && flag_delete="$1" && shift
  added="${1:?ArgShouldExist}"
  log_info winreg_add_path $flag_append $flag_delete "${added}"
  old=`reg query "HKEY_CURRENT_USER\Environment" /v Path`
  old=`echo "$old" | tail -1`
  [[ "$old" =~ "^ +Path +REG(_EXPAND)?_SZ +" ]]
  old=`echo "$old" | sed -Ee 's/^ +Path +REG(_EXPAND)?_SZ +//'`
  log_info old="$old"
  #return
  # https://tldp.org/LDP/abs/html/parameter-substitution.html
  # 反斜线用在模式里面是转义字符，所以得先转成斜线，完事了再转回来
  # 得先分隔符替换成两个，完事了再转回来，这样方便定界刪除多个元素
  p=";${old//;/;;};"
  p="${p//\\/\/}"
  added="${added//\\/\/}"
  p="${p//;$added;/}"
  if [[ -z "$flag_delete" ]]; then
    [[ "$flag_append" ]] && p="$p;$added;" || p=";$added;$p"
  fi
  p="${p//;;/;}"
  p="${p#;}"
  p="${p%;}"
  p="${p//\//\\}"
  log_info new="$p"
  #return
  # reg query "HKEY_CURRENT_USER\Environment" /reg:64
  # reg query "HKEY_CURRENT_USER\Environment" /reg:32
  # setx 有问题：警告: 正保存的数据被裁断到 1024 字符。
  # 在 64 位程序下 reg add 不指定 /reg:64 /reg:32 也可以，两个都能更新到
  reg add "HKEY_CURRENT_USER\Environment" /v Path /t REG_EXPAND_SZ /d "$p" /f
  # 广播 WM_SETTINGCHANGE 消息
  #setx PYTHONDONTWRITEBYTECODE 1
}

# 下载文件到目标位置，不缓存，用于下载易变的文件
# 输入参数 (dstpath srcurl)
fetch_file() {
  dstpath=${1:?ArgShouldExist}
  srcurl=${2:?ArgShouldExist}

  mkdir -p "$gOptDir/cppenv/tmp"
  tmppath=$gOptDir/cppenv/tmp/`basename "$dstpath"`.tmp
  rm -f -- "$tmppath"

  if which curl >/dev/null; then
    echo curl -C - -k -L -o "$tmppath" "$srcurl"
    curl -C - -k -L -o "$tmppath" "$srcurl"
  else
    echo wget -c -O "$tmppath" "$srcurl"
    wget -c -O "$tmppath" "$srcurl"
  fi

  rm -f -- "$dstpath"
  mv "$tmppath" "$dstpath"
}

# 下载文件到特定目录，用户指定文件命名
# 输入参数 (srcname srcurl)
download_file() {
  srcname=${1:?ArgShouldExist}
  srcurl=${2:?ArgShouldExist}
  dstpath=$gOptDir/cppenv/download/$srcname
  cachepath=download/$srcname
  if [[ -f $dstpath ]]; then return; fi
  mkdir -p $gOptDir/cppenv/download
  
  # 很多地方如 ghproxy 不支持断点续传
  rm -f $dstpath.tmp
  
  if [[ -e "$cachepath" ]]; then
    echo cp -f "$cachepath" $dstpath.tmp
    cp -f "$cachepath" $dstpath.tmp
  elif which curl >/dev/null; then
    echo curl -C - -k -L -o $dstpath.tmp $srcurl
    curl -C - -k -L -o $dstpath.tmp $srcurl
  else
    echo wget -c -O $dstpath.tmp $srcurl
    wget -c -O $dstpath.tmp $srcurl
  fi
  mv "$dstpath.tmp" "$dstpath"
}

# 下载文件到特定目录，并解压到特定目录，用户指定文件命名，解压目录名同文件命名
# 下载是惰性的，但是解压不是惰性的，解压是为了做个临时工作目录，用于后续编译等操作
# 输入参数 (opts..., srcname, srcurl, extra...)
# 开头选项是可选的，如果指定则必须按照特定的顺序
# 开头选项 --cd  表示切换到解压目录，如果成功是一定要切换过去的
# 开头选项 --WinRAR  强制使用 WinRAR 解压
# 开头选项 --7z  强制使用 7z 解压
# 末尾选项则透传给解压程序
# --strip-components 1  用于 tar 解压时去掉第一层目录
# -ep4texts\books 用于 WinRAR 解压时去掉目录前缀
# -j 用于 unzip 解压时去掉目录信息
decompress_file() {
  # 消费掉开头选项 --cd
  opt_cd=
  if [[ "$1" == "--cd" ]]; then
    opt_cd="$1"
    shift
  fi

  # 消费掉开头选项 --WinRAR
  opt_WinRAR=
  if [[ "$1" == "--WinRAR" ]]; then
    opt_WinRAR="$1"
    shift
  fi

  # 消费掉开头选项 --7z
  opt_7z=
  if [[ "$1" == "--7z" ]]; then
    opt_7z="$1"
    shift
  fi

  # 消费掉两个参数下载文件
  srcname=${1:?ArgShouldExist}
  decpath=$gOptDir/cppenv/decompress/$srcname
  download_file "$1" "$2"
  shift
  shift

  # 在 busybox 下试验，只有直接传 "$@" 这一种办法能保留单个参数中的空格
  # extra="$@" 这种有个中间变量的办法不行
  # $@ 没有引号包裹也不行

  # 清掉解压比较符合编译场景，解压目录相当于临时目录，不是常量缓存
  rm -r -f -- "$decpath"

  if [[ ! -e $decpath ]]; then

    # 准备好临时目录
    rm -r -f -- "$decpath.tmp"
    mkdir -p "$decpath.tmp"

    # 真正的解压过程
    # 有些 zip 包 busybox 解不了，得用 WinRAR 或者 7z
    if [[ "$opt_WinRAR" ]]; then
      log_info "C:\Program Files\WinRAR\WinRAR.exe" x "$@" "$dstpath" "$decpath.tmp"
      "C:\Program Files\WinRAR\WinRAR.exe" x "$@" "$dstpath" "$decpath.tmp"
    elif [[ "$opt_7z" ]]; then
      log_info "C:\Program Files\7-Zip\7z.exe" x "$@" "$dstpath" -o"$decpath.tmp"
      "C:\Program Files\7-Zip\7z.exe" x "$@" "$dstpath" -o"$decpath.tmp"
    else
      case "$srcname" in
        *.tar.bz2 | *.tbz)
          log_info tar xjf "$dstpath" -C "$decpath.tmp" "$@"
          tar xjf "$dstpath" -C "$decpath.tmp" "$@"
          ;;
        *.tar.xz | *.txz)
          log_info tar xJf "$dstpath" -C "$decpath.tmp" "$@"
          tar xJf "$dstpath" -C "$decpath.tmp" "$@"
          ;;
        *.tar.gz | *.tgz)
          log_info tar xzf "$dstpath" -C "$decpath.tmp" "$@"
          tar xzf "$dstpath" -C "$decpath.tmp" "$@"
          ;;
        *.zip)
          log_info unzip "$dstpath" "$@" -d "$decpath.tmp"
          unzip "$dstpath" "$@" -d "$decpath.tmp"
          ;;
        *)
          log_fatal unknown file to decompress: "$srcname"
          ;;
      esac
    fi

    # 原子化操作：临时目录升级为正式目录
    mv "$decpath.tmp" "$decpath"
  fi

  if [[ "$opt_cd" ]]; then
    cd "$decpath"
  fi
}

app_file() {
  # 消费掉第一个参数作为 app 目录下的名字
  appname=${1:?ArgShouldExist} && shift

  [[ -e $gOptDir/cppenv/app/$appname ]] && return
  srcname=${1:?ArgShouldExist}
  decompress_file "$@"
  mkdir -p $gOptDir/cppenv/app
  mv $gOptDir/cppenv/decompress/$srcname $gOptDir/cppenv/app/$appname
}


bin_file() {
  srcname=${1:?ArgShouldExist}

  [[ -e $gOptDir/cppenv/bin/$srcname ]] && return
  download_file "$@"
  mkdir -p $gOptDir/cppenv/bin
  cp -f $gOptDir/cppenv/download/$srcname $gOptDir/cppenv/bin/$srcname
}



setup_subshell() {
  # Fail fast and be aware of exit codes.
  # Use || true on programs that you intentionally let exit non-zero.
  set -eo pipefail
  #set -e
  # Using set -x and set +x to turn verbosity up/down.
  #set -x
}


win_need_busybox() {
  if [[ "${OS:-}" == "Windows_NT" ]] && [[ "`ls --help 2>&1 | grep BusyBox`" == "" ]]; then
    echo "ERROR: BusyBox is required for Windows."
    false
  fi
}



# https://www.intel.com/content/www/us/en/develop/documentation/cpp-compiler-developer-guide-and-reference/top/compilation/supported-environment-variables.html
# LD_LIBRARY_PATH (Linux) Specifies the location for shared objects (.so files).
# DYLD_LIBRARY_PATH (macOS*) Specifies the path for dynamic libraries.
# INCLUDE (Windows) Specifies the directories for the source header files (include files). 
# LIB (Windows) Specifies the directories for all libraries used by the compiler and linker.
# LIBRARY_PATH (Linux and macOS*) Specifies the path for libraries to be used during the link phase.
# CPATH (Linux and macOS*) Specifies the path to include directory for C/C++ compilations.
# C_INCLUDE_PATH (Linux and macOS*) Specifies path to include directory for C compilations.
# CPLUS_INCLUDE_PATH (Linux and macOS*) Specifies path to include directory for C++ compilations.
setup_envvars() {
  mkdir -p $gOptDir/cppenv/bin

  export CMAKE_PREFIX_PATH=$gOptDir/cppenv/cpplibs
  
  add_path $gOptDir/cppenv/bin

  if [[ "${OS:-}" == "Windows_NT" ]]; then
    export INCLUDE=$gOptDir/cppenv/cpplibs/include
    export LIB=$gOptDir/cppenv/cpplibs/lib
    
    export MAMBA_EXE="$gOptDir/cppenv/bin/micromamba.exe"
    export MAMBA_ROOT_PREFIX="$gOptDir/cppenv/micromamba"
  
  else
    #export C_INCLUDE_PATH=$gOptDir/cppenv/include:$gOptDir/cppenv/micromamba/include
    #export CPLUS_INCLUDE_PATH=$gOptDir/cppenv/include:$gOptDir/cppenv/micromamba/include
    #export LD_LIBRARY_PATH=$gOptDir/cppenv/lib:$gOptDir/cppenv/micromamba/lib
    export C_INCLUDE_PATH=$gOptDir/cppenv/cpplibs/include
    export CPLUS_INCLUDE_PATH=$gOptDir/cppenv/cpplibs/include
    
    # add LIBDIR to the 'LD_LIBRARY_PATH' environment variable during execution
    # add LIBDIR to the 'LD_RUN_PATH' environment variable during linking  这似乎是更好的办法，得到的可执行文件嵌入了 rpath，通用性强
    # use the '-Wl,-rpath -Wl,LIBDIR' linker flag  这个办法对编译器选项侵入性大
    # have your system administrator add LIBDIR to '/etc/ld.so.conf'  这个不一定有权限，也不环保
    #export LD_LIBRARY_PATH=$gOptDir/cppenv/cpplibs/lib
    export LD_RUN_PATH=$gOptDir/cppenv/micromamba/lib

    export MAMBA_EXE="$gOptDir/cppenv/bin/micromamba"
    export MAMBA_ROOT_PREFIX="$gOptDir/cppenv/micromamba"
  
    echo CONDA_SHLVL=$CONDA_SHLVL
    if [[ -e "$MAMBA_EXE" ]] && [[ "$CONDA_SHLVL" != "1" ]]; then
      # 执行 sh 脚本时并没有执行 ~/.bashrc
      # 所以这里得走这个 eval
      eval "$(micromamba shell hook --shell=bash)"
      micromamba activate
    fi

    export PATH=$PATH:/usr/local/go/bin
    export GOPROXY=https://mirrors.aliyun.com/goproxy/,https://goproxy.cn,direct
    export GOSUMDB=off


    [[ -f ~/.acme.sh/acme.sh.env ]] && . ~/.acme.sh/acme.sh.env


  fi

  add_path $gOptDir/cppenv/cpplibs/bin

  #log_info PATH="$PATH"

}







echo "--$0--"
# Git Bash 有毛病无法解决：会给参数加上单引号，某些程序如 msiexec 无法处理


if echo ~ | grep ' '; then
  echo "ERROR: Space in home path."
#elif [[ "${OS:-}" == "Windows_NT" ]] && [[ "`ls --help 2>&1 | grep BusyBox`" == "" ]]; then
#  echo "ERROR: BusyBox is required for Windows."
elif [ ! "$0" = "$BASH_SOURCE" ]; then
  echo "DETECT: It is sourcing this script."
  setup_envvars
else

  setup_subshell
  setup_envvars

  if [[ "$1" == "" ]]; then
    date
  elif [[ "$1" == "call" ]]; then
    shift
    funcname="$1"
    shift
    "$funcname" $@
    if [ $? = 127 ]; then
        echo "FUNCTION_NOT_FOUND:" "$funcname" >&2
    fi
  fi

fi



