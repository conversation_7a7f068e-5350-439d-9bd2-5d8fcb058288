


# https://stackoverflow.com/questions/29832037/how-to-get-script-directory-in-posix-sh
gScriptDir=$(CDPATH= cd -- "$(dirname -- "$0")" && pwd)
. "$gScriptDir/cppenv_shbase.sh"





lazy_install_pytools() { lazy_call install_pytools 240515 ; }
install_pytools() {
  if [[ "${OS:-}" == "Windows_NT" ]]; then
    win_install_pytools
  elif [[ "$(uname)" == "Darwin" ]]; then
    mac_install_pytools
  else
    linux_install_pytools
  fi

}

common_install_pytools_init_a() {
  a=
  
  # micromamba 的 base 环境可以安装最新版本的 python
  # https://anaconda.org/conda-forge/python
  a="$a python=3.12.3"
  

}

win_install_pytools() {
  common_install_pytools_init_a


  micromamba install -y $a
}

mac_install_pytools() {
  common_install_pytools_init_a
  
  micromamba install -y $a
}

linux_install_pytools() {
  common_install_pytools_init_a
  

  micromamba install -y $a
}



main_call lazy_install_pytools


