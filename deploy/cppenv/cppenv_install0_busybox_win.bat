
@echo off

set gOptDir=C:\a

echo win_install_busybox: BEGIN:%gOptDir%\cppenv


call :ensure_busybox
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%
call :install_shfile
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%



echo win_install_busybox: END: %gOptDir%\cppenv
exit /b %ERRORLEVEL%



:ensure_busybox
if exist "%gOptDir%\cppenv\bin\busybox-w32-FRP-4784-g5507c8744.exe" exit /b 0
:: https://frippery.org/busybox/
:: https://frippery.org/files/busybox/
call :download_file busybox-w32-FRP-4784-g5507c8744.exe https://frippery.org/files/busybox/busybox-w32-FRP-4784-g5507c8744.exe
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%
md "%gOptDir%\cppenv\bin"
echo ensure_busybox: "%dstpath%" to "%gOptDir%\cppenv\bin\"
copy /y "%dstpath%" "%gOptDir%\cppenv\bin\"
copy /y "%dstpath%" "%gOptDir%\cppenv\bin\busybox.exe"
exit /b 0


:install_shfile
echo install_shfile: .sh shfile "%gOptDir%\cppenv\bin\busybox-w32-FRP-4784-g5507c8744.exe"
:: HKEY_CLASSES_ROOT  =  HKEY_CURRENT_USER\Software\Classes  +  HKEY_LOCAL_MACHINE\SOFTWARE\Classes
:: HKEY_CURRENT_USER\Software\Classes has higher priority
:: ftype writes HKEY_LOCAL_MACHINE\SOFTWARE\Classes so it needs admin privileges
reg delete "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\FileExts\.sh" /f 2>nul
reg delete "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\RecentDocs\.sh" /f 2>nul
reg add HKEY_CURRENT_USER\Software\Classes\.sh /ve /d shfile /f
reg add "HKEY_CURRENT_USER\Software\Classes\shfile\shell\open\command" /ve /d "\"%gOptDir%\cppenv\bin\busybox-w32-FRP-4784-g5507c8744.exe\" sh \"%%L\" %%*" /f
reg add "HKEY_CURRENT_USER\Software\Classes\shfile\DefaultIcon" /ve /d "%SystemRoot%\System32\shell32.dll,-28" /f
reg add "HKEY_CURRENT_USER\Software\Classes\shfile\shell\runas" /v "HasLUAShield" /d "" /f
reg add "HKEY_CURRENT_USER\Software\Classes\shfile\shell\runas\command" /ve /d "\"%gOptDir%\cppenv\bin\busybox-w32-FRP-4784-g5507c8744.exe\" su -c \"sh '%%L'\"" /f
exit /b 0


:download_file
set srcname=%1
set srcurl=%2
set dstpath=%gOptDir%\cppenv\download\%srcname%
if exist "%CD%\download\%srcname%" goto :download_file__local
if exist "%dstpath%" exit /b 0
echo download_file: %srcurl% to %dstpath%
md "%gOptDir%\cppenv\download"
del /q "%dstpath%.tmp"
del /q "%dstpath%"
bitsadmin /CANCEL "MyJob"
bitsadmin /TRANSFER "MyJob" /PRIORITY FOREGROUND "%srcurl%" "%dstpath%.tmp"
if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%
move /y "%dstpath%.tmp" "%dstpath%"
exit /b 0
:download_file__local
set dstpath=%CD%\download\%srcname%
echo download_file: set dstpath to %dstpath%
exit /b 0


