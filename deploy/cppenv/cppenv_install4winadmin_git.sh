


# https://stackoverflow.com/questions/29832037/how-to-get-script-directory-in-posix-sh
gScriptDir=$(CDPATH= cd -- "$(dirname -- "$0")" && pwd)
. "$gScriptDir/cppenv_shbase.sh"





# https://git-scm.com/download/win
# 静默安装
# https://github.com/git-for-windows/git/wiki/Silent-or-Unattended-Installation  Silent or Unattended Installation
# https://github.com/git-for-windows/build-extra/blob/master/installer/install.iss
# 默认安装到 /dir="C:\Program Files\Git"
# CRLFOption=CRLFCommitAsIs 默认当然是保持换行符了
# 具体的代码库可以在 .gitattributes 里面设置自动转换换行符 * text=auto
# SSHOption=OpenSSH  为了使用 codeup
# SSHOption=Plink
# PathOption=BashOnly -- Use Git from Git Bash only
# PathOption=Cmd -- Use Git from the Windows Command Prompt
# PathOption=CmdTools -- Use Git and optional Unix tools from the Windows Command Prompt
# taskkill /f /im explorer.exe
# http://git-scm.com/docs/git-config
Git_win_install_admin() {
  download_file Git-2.40.0-64-bit.exe https://mirror.ghproxy.com/https://github.com/git-for-windows/git/releases/download/v2.40.0.windows.1/Git-2.40.0-64-bit.exe
  $gOptDir/cppenv/download/Git-2.40.0-64-bit.exe /VERYSILENT /NORESTART /NOCANCEL /SP- /CLOSEAPPLICATIONS /RESTARTAPPLICATIONS /COMPONENTS="icons,ext,ext\shellhere,ext\guihere,gitlfs,windowsterminal" CRLFOption=CRLFCommitAsIs SSHOption=OpenSSH PathOption=Cmd BashTerminalOption=ConHost
  
  Git_config
}



# TODO admin 权限下 git config 不对当前用户生效
Git_config() {
  git config --global http.sslVerify false
  git config --global --get core.autocrlf
  git config --global core.autocrlf false

}




main_call Git_win_install_admin


