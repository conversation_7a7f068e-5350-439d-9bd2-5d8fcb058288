


# https://stackoverflow.com/questions/29832037/how-to-get-script-directory-in-posix-sh
gScriptDir=$(CDPATH= cd -- "$(dirname -- "$0")" && pwd)
. "$gScriptDir/cppenv_shbase.sh"






lazy_install_pylibs() { lazy_call install_pylibs 220624 ; }
install_pylibs() {
  a=

  # 常用基本工具库
  a="$a rich"
  # 单元测试
  a="$a pytest pytest-asyncio"
  # 程序入口
  a="$a typer fire click"

  # twine 用于 pypi 发包
  a="$a twine"
  # black 用于 vscode 格式化 python 代码
  a="$a black"

  # msgpack 在 conda 上叫做 msgpack-python
  # lxml 用于 html 解析
  a="$a orjson msgpack-python lxml pydantic"

  # 同步访问
  a="$a gevent flask"
  # 同步访问的客户端库
  # redis 在 conda 上叫做 redis-py
  a="$a requests redis-py pika"
  # 异步相关库
  a="$a aiohttp aioredis asyncpg"

  # conda 上的 numpy 会依赖 mkl Intel Math Kernel Library
  # openpyxl 用于 pandas 解析 xlsx 文件
  a="$a numpy pandas openpyxl python-docx python-frontmatter"

  # 只有 noarch 的是最新版本
  # https://anaconda.org/conda-forge/jupyterlab
  a="$a jupyterlab=4.2.0"
  # https://anaconda.org/conda-forge/matplotlib
  a="$a matplotlib=3.8.4"
  # https://anaconda.org/conda-forge/seaborn
  a="$a seaborn=0.13.2"
  # https://anaconda.org/conda-forge/sympy
  a="$a sympy=1.11.1"



  # https://anaconda.org/conda-forge/boto3
  a="$a boto3=1.34.112"

  # https://anaconda.org/conda-forge/awscli
  a="$a awscli"


  if [[ "${OS:-}" == "Windows_NT" ]]; then
    # wxPython 在 conda 上叫做 wxpython 支持 osx-arm64
    # conda 没有 pyside6，conda 的 pyside2 不支持 osx-arm64
    a="$a pywin32 wxpython"
  elif [[ "$(uname)" == "Darwin" ]]; then
    a="$a wxpython"
  else
    a="$a "
  fi


  # 图像处理
  # https://anaconda.org/conda-forge/wand
  # 这里的 imagemagick 不支持 Windows
  #a="$a wand"
  # https://anaconda.org/conda-forge/pillow
  a="$a pillow=10.3.0"

  echo micromamba install -y $a
  micromamba install -y $a

  # suppress pip upgrade warning
  pip config set global.disable-pip-version-check true

  # https://mirrors.tuna.tsinghua.edu.cn/help/pypi/
  # pip 不全局设置清华镜像，这样方便使用官方源
  # pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
  # pip 用命令行参数使用清华镜像
  # pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -U
  # pip install -i https://mirrors.aliyun.com/pypi/simple/ -U

  # https://docs.conda.io/projects/conda/en/latest/user-guide/tasks/manage-environments.html#using-pip-in-an-environment
  # https://docs.conda.io/projects/conda/en/latest/user-guide/configuration/pip-interoperability.html
  # 必须同时使用 conda 和 pip
  # conda 针对科学计算库有优化的二进制包
  # pip 上的包比较全，发布方便
  # 上传包到 conda-forge 太麻烦了
  # https://conda-forge.org/docs/maintainer/adding_pkgs.html
  # 试验发现 pip 处理依赖时会使用 conda 安装的包，好像实际上 conda 下载包之后会调用 pip 来安装包

  a=
  # dewr  用于开发时自动重启程序
  #a="$a dewr"
  # pytest-asyncio 在 conda 上的版本比 pypi 的老
  a="$a pytest-asyncio"
  
  # playwright 相比 selenium 更加现代，用于使用实际浏览器抓取网页
  a="$a playwright panflute pandocfilters"
  
  # pip 上面的版本一般比较新
  a="$a httpx selectolax parsel"
  
  in_china=0
  if curl -s ipinfo.io | grep Asia/Shanghai; then
    in_china=1
  fi

  if [ "$in_china" = 1 ]; then
    echo pip install -i https://mirrors.aliyun.com/pypi/simple/ -U $a
    pip install -i https://mirrors.aliyun.com/pypi/simple/ -U $a
  else
    echo pip install -U $a
    pip install -U $a
  fi

  if [[ "${OS:-}" == "Windows_NT" ]]; then
    playwright install chromium
  elif [[ "$(uname)" == "Darwin" ]]; then
    playwright install chromium
  else
    # centos7 也装不了 playwright
    pwd
    ls
  fi
}





main_call lazy_install_pylibs

