


# https://stackoverflow.com/questions/29832037/how-to-get-script-directory-in-posix-sh
gScriptDir=$(CDPATH= cd -- "$(dirname -- "$0")" && pwd)
. "$gScriptDir/cppenv_shbase.sh"





lazy_install_cpptools() { lazy_call install_cpptools 221227 ; }
install_cpptools() {
  if [[ "${OS:-}" == "Windows_NT" ]]; then
    win_install_cpptools
  elif [[ "$(uname)" == "Darwin" ]]; then
    mac_install_cpptools
  else
    linux_install_cpptools
  fi

  # 有的 CentOS6 装不了 nodejs
  #config_nodejs
}

config_nodejs() {
  npm i -g pnpm
  # npm config set registry https://registry.npmjs.org/
  # https://developer.aliyun.com/mirror/NPM
  npm config set registry http://registry.npm.taobao.org
  npm config set package-lock false
  pnpm config -g set registry http://registry.npm.taobao.org
}

common_install_cpptools_init_a() {
  a=
  # https://anaconda.org/conda-forge/curl
  a="$a curl=8.1.2"
  # 用清华镜像安装 cmake 得指定版本，不然默认版本会比较老
  # https://anaconda.org/conda-forge/cmake
  a="$a cmake=3.26.4"
  # https://anaconda.org/conda-forge/make
  a="$a make=4.3"
  # https://anaconda.org/conda-forge/ninja
  a="$a ninja=1.11.0"
  # https://anaconda.org/conda-forge/jq  这里没有 win-64 版本
  #a="$a jq=1.6"
  # https://anaconda.org/conda-forge/sqlite
  a="$a sqlite=3.42.0"
  # Azul Zulu Builds of OpenJDK
  # 这个似乎更好，占用内存小
  # Oracle JDK 不能通过 micromamba 安装
  # https://www.azul.com/downloads/
  # https://anaconda.org/conda-forge/openjdk
  a="$a openjdk=20.0.0"
  # https://anaconda.org/conda-forge/graphviz
  # Warning: Could not load "Library\bin\gvplugin_pango.dll" - It was found, so perhaps one of its dependents was not.  Try ldd.
  a="$a graphviz=8.0.5"
  # https://anaconda.org/conda-forge/nodejs
  # 有的 CentOS6 装不了 nodejs
  a="$a nodejs=18.15.0"
  # https://anaconda.org/conda-forge/go
  # 这里的 go 版本更新不及时
  
  
  # micromamba 的 base 环境可以安装最新版本的 python
  # https://anaconda.org/conda-forge/python
  a="$a python=3.11.4"
  
  # https://github.com/openssl/openssl/blob/master/NOTES-PERL.md
  # 这里说至少需要 5.10.0，而 CentOS6 内置 perl=5.10.1
  # 但实际会报错 Can't locate IPC/Cmd.pm
  # 所以还是用 micromamba 安装新版 perl 吧
  # https://anaconda.org/conda-forge/perl
  a="$a perl=5.32.1"
  # for build openssl
  # https://anaconda.org/conda-forge/nasm
  a="$a nasm=2.14.02"
  
  # https://github.com/conda-forge/clangdev-feedstock
  a="$a clang-format"
  
  # https://anaconda.org/conda-forge/pandoc
  a="$a pandoc=3.1.3"
  
  # https://anaconda.org/conda-forge/imagemagick
  # 这里的 imagemagick 不支持 Windows
  #a="$a  imagemagick"

  # https://anaconda.org/conda-forge/pcre
  # 编译 cppcheck 需要 pcre
  #a="$a pcre=8.45 pcre2=10.40"
  
  # https://anaconda.org/conda-forge/ripgrep
  # 最快的文本搜索工具
  a="$a ripgrep=13.0.0"

}

win_install_cpptools() {
  common_install_cpptools_init_a


  micromamba install -y $a
}

mac_install_cpptools() {
  common_install_cpptools_init_a
  
  micromamba install -y $a
}

linux_install_cpptools() {
  common_install_cpptools_init_a
  
  # 这里安装的新版本 gcc 能在 CentOS6 运行
  # https://anaconda.org/conda-forge/gcc
  # https://anaconda.org/conda-forge/gxx
  # https://anaconda.org/conda-forge/gdb
  # https://anaconda.org/conda-forge/ccache
  # gdb=11.2 需要 glibc >=2.17，但是 CentOS6 的 glibc 是 2.12，查看 ldd --version
  # 支持 CentOS6，最高只能用 gdb=10.2，同时 gdb=10.2 不支持 python=3.10.5
  if [[ -e /etc/centos-release ]] && cat /etc/centos-release | grep 'release 6' ; then
    a="$a ccache=4.7.3 gcc=12.2.0 gxx=12.2.0"
  else
    a="$a ccache=4.8.1 gcc=13.1.0 gxx=13.1.0 gdb=12.1"
  fi
  # 一个平台上用一个编译环境，所以在 Linux 上不安装 clang 了
  # https://github.com/conda-forge/clangdev-feedstock
  # https://anaconda.org/conda-forge/clang
  a="$a clang=16.0.5 clangxx=16.0.5 clangdev=16.0.5 clang-format=16.0.5 clang-tools=16.0.5 python-clang=16.0.5 compiler-rt=16.0.5 llvm-tools=16.0.5"
  # 使用 libc++ 没有明显好处，静态链接它也麻烦，也牵扯到 boost 等库的编译，所以还是用 libstdc++
  # 这个 libcxx 的 Windows 版本也不高
  #a="$a libcxx=14.0.6"
  # 这个 libcxxabi 好像只适合 Linux
  #a="$a libcxxabi=14.0.6"

  # 编译机上面 git 版本不高
  #a="$a git=2.38.1"
  
  # 给王红璐使用
  # https://anaconda.org/conda-forge/cppcheck
  #a="$a cppcheck=2.7.5"

  micromamba install -y $a
}



main_call lazy_install_cpptools


