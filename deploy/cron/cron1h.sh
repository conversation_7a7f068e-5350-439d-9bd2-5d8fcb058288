


cd /opt/yylbe

. ./vars.sh


pay_report() {

  php81 hp '\llogic\source\CronAdjustSync'

  php81 hp php/llogic/gmtab/_Cron.php Hourly
}



stat_tasks() {

  # 代理系统用户池变更每日统计(实时数据)
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think pm_stat_d_user_pool today tomorrow >> /data/logs/pm_stat_d_user_pool.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《代理系统用户池变更每日统计(实时数据)》执行时间: $execution_time 秒"

  # 代理每日数据统计(实时数据)
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think promo_summary today tomorrow >> /data/logs/promo_summary.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《代理每日数据统计(实时数据)》执行时间: $execution_time 秒"

  # 代理佣金明细详情(实时数据)
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think promo_commission today tomorrow >> /data/logs/promo_commission.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《代理佣金明细详情(实时数据)》执行时间: $execution_time 秒"
    

}

case "$(hostname)" in
  DISABLEpro1main2 | pro3main1)
    pay_report
    stat_tasks
    ;;
  *)
    ;;
esac



