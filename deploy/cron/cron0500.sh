


cd /opt/yylbe

. ./vars.sh




echo "`date` cron0500 BEGIN"



stat_tasks() {

  start_time=$(date +%s)
  sh php/ldb/mysql/daily.sh
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "php/ldb/mysql/daily.sh: $execution_time 秒"

  start_time=$(date +%s)
  php81 hp php/ldb/mysql/CronGmDailyUser.php
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "CronGmDailyUser: $execution_time 秒"

  # 每日数据统计
  # jingzhao：每日数据统计要优先于CronGmDailyCohort.php运行，因为CronGmDailyCohort.php里面需要基于每日数据统计表 统计代理提现金额
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think summary_v2 >> /data/logs/summary2.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《每日数据统计》执行时间: $execution_time 秒"
  
  start_time=$(date +%s)
  php81 hp php/ldb/mysql/CronGmDailyCohort.php
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "CronGmDailyCohort: $execution_time 秒"



  # 支付通道统计
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think pay_channel_statistics >> /data/logs/pay_channel_statistics.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《支付通道统计》执行时间: $execution_time 秒"


  # 充值用户玩法统计
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think game_recharge_user_statistics >> /data/logs/game_recharge_user_statistics.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《充值用户玩法统计》执行时间: $execution_time 秒"


  # Super Deals统计(super deals)
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think super_deals_statistics >> /data/logs/super_deals_statistics.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《Super Deals统计》执行时间: $execution_time 秒"


  # 大R监测
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think rmb_player >> /data/logs/rmb_player.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《大R监测》执行时间: $execution_time 秒"



  # 分时充注统计
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think hour_recharge_register >> /data/logs/hour_recharge_register.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《分时充注统计》执行时间: $execution_time 秒"


  # 周卡统计
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think card_statistics  >> /data/logs/card_statistics.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《周卡统计》执行时间: $execution_time 秒"


  # 月卡统计
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think month_card_statistics  >> /data/logs/month_card_statistics.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《月卡统计》执行时间: $execution_time 秒"


  # 付费分布
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think recharge_behavior_query >> /data/logs/recharge_behavior_query.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《付费分布》执行时间: $execution_time 秒"
  

  # D赠送明细
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think dposit_give_register  >> /data/logs/dposit_give_register.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《D赠送明细》执行时间: $execution_time 秒"


  # bonus明细
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think bonus_statistics  >> /data/logs/bonus_statistics.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《bonus明细》执行时间: $execution_time 秒"

  
  # 玩法统计
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think game_statistics >> /data/logs/game_statistics.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《玩法统计》执行时间: $execution_time 秒"
  

  # 签到数据统计
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think sign_statistics >> /data/logs/sign_statistics.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《签到数据统计》执行时间: $execution_time 秒"
  
  
  # 代理系统用户池变更每日统计
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think pm_stat_d_user_pool >> /data/logs/pm_stat_d_user_pool.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《代理系统用户池变更每日统计》执行时间: $execution_time 秒"


  # 代理每日数据统计
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think promo_summary >> /data/logs/promo_summary.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《代理每日数据统计》执行时间: $execution_time 秒"


  # 代理佣金明细详情
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think promo_commission >> /data/logs/promo_commission.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《代理佣金明细详情》执行时间: $execution_time 秒"
    
    
}




case "$(hostname)" in

  pro1main2 | pro3main1 | dev2bj1 | test*mono*)
    stat_tasks
    ;;


esac



echo "`date` cron0500 END"

