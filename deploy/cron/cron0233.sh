


cd /opt/yylbe

. ./vars.sh

echo "`date` cron0233 BEGIN"



# find . -mtime +0 # find files modified greater than 24 hours ago
# find . -mtime +1 # find files modified more than 48 hours ago



case "$(hostname)" in

  dev2bj1 | dev2bj1 | test*mono*)
    find /data/logs -name '*.log' -type f -mtime +0 -exec echo rm -f -- {} \;
    find /data/logs -name '*.log' -type f -mtime +0 -exec rm -f -- {} \;

    truncate -s 0 /opt/yylbe/deploy/nginx/runtime/logs/access.log
    truncate -s 0 /opt/yylbe/deploy/nginx/runtime/logs/error.log
    
    find /opt/yylbe/php/kefu/runtime/log -name '*.log' -type f -mtime +1 -exec echo rm -f -- {} \;
    find /opt/yylbe/php/kefu/runtime/log -name '*.log' -type f -mtime +1 -exec rm -f -- {} \;
    
    ;;

  *)
    find /data/logs -name '*.log' -type f -mtime +0 -exec echo rm -f -- {} \;
    find /data/logs -name '*.log' -type f -mtime +0 -exec rm -f -- {} \;
    
    truncate -s 0 /opt/yylbe/deploy/nginx/runtime/logs/access.log
    truncate -s 0 /opt/yylbe/deploy/nginx/runtime/logs/error.log

    find /opt/yylbe/php/kefu/runtime/log -name '*.log' -type f -mtime +1 -exec echo rm -f -- {} \;
    find /opt/yylbe/php/kefu/runtime/log -name '*.log' -type f -mtime +1 -exec rm -f -- {} \;
    
    ;;

esac



stat_tasks() {




  # 资产统计
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think user_statistics_query >> /data/logs/user_statistics_query.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《资产统计》执行时间: $execution_time 秒"



  # 代理排行榜统计
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think promo_ranking >> /data/logs/promo_ranking.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《代理排行榜统计》执行时间: $execution_time 秒"


  # 代理每日佣金额统计(细分pmid)
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think promoter_stat >> /data/logs/promoter_stat.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《代理每日佣金额统计》执行时间: $execution_time 秒"

  
}

case "$(hostname)" in

  pro1main2 | pro3main1 | dev2bj1 | test*mono*)
    stat_tasks
    ;;


esac

echo "`date` cron0233 END"
