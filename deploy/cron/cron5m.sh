


cd /opt/yylbe

. ./vars.sh



sync_data() {
  php81 -d memory_limit=1024M -d zend.exception_ignore_args=1 -d zend.exception_string_param_max_len=512 hp php/ldb/clickhouse/CronUserWalletLog.php
  php81 -d memory_limit=1024M -d zend.exception_ignore_args=1 -d zend.exception_string_param_max_len=512 hp php/ldb/clickhouse/CronUserRoundLog.php
  php81 -d memory_limit=1024M -d zend.exception_ignore_args=1 -d zend.exception_string_param_max_len=512 hp php/ldb/clickhouse/CronWalletLogV2.php
  php81 -d memory_limit=1024M -d zend.exception_ignore_args=1 -d zend.exception_string_param_max_len=512 hp php/ldb/clickhouse/CronUser.php
}



case "$(hostname)" in

  # 如果主机名以 pro1gate- 开头，则是接入机
  pro1vice1 | pro3vice1 | dev2bj1 | test*mono*)
    sync_data
    ;;


  *)
    ;;

esac




sh deploy/yylenv/cron_log_server_status.sh
sh deploy/yylenv/cron_check_reboot_serverws.sh





stat_tasks() {
  # 定时查询代理提现订单状态
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think promo_query_order_status >> /data/logs/promo_query_order_status.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《查询代理提现订单状态》执行时间: $execution_time 秒"


  # 处理充值成功且未到账的订单
  start_time=$(date +%s)
  #/usr/bin/php81 /opt/yylbe/php/console/think pay_order_not_arrived >> /data/logs/pay_order_not_arrived.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《处理充值成功且未到账的订单》执行时间: $execution_time 秒"


  # 自动提现二审
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think AutoWithdrawAuditAgain > /dev/null 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《AutoWithdrawAuditAgain》执行时间: $execution_time 秒"

}

case "$(hostname)" in
  pro1main2 | pro3main1 | dev2bj1 | test*mono*)
    echo a_pay_out_states_check
    time php81 php/console/think a_pay_out_states_check >/dev/null

    php81 hp php/llogic/pay/CronPayRefetchBalanceMap.php
    stat_tasks
    ;;
  *)
    ;;
esac

