


cd /opt/yylbe

. ./vars.sh

echo "`date` cron1030 BEGIN"



stat_tasks() {

  # 检查更新 nginx 配置
  sh deploy/nginx/install.sh

}






case "$(hostname)" in

  *)
    stat_tasks
    ;;


esac






check_source_migration() {
  # 每分钟都执行，通过数据库状态控制惰性操作
  php81 hp php/llogic/source/CronSourceMigration.php TraverseAll

  php81 hp php/llogic/source/CronCostReport.php

}



case "$(hostname)" in
  pro1main2 | pro3main1)
    check_source_migration
    ;;
  *)
    ;;
esac






# 每台机器都安装这个地址数据库
sh php/ldb/geoip2/install.sh





echo "`date` cron1030 END"

