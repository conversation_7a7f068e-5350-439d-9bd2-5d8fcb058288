
cd /opt/yylbe

. ./vars.sh


case "$(hostname)" in
  DISABLEpro1main2 | pro3main1)
    php81 hp '\llogic\source\CronGooglePlayCheck'
    ;;
  *)
    ;;
esac





stat_tasks() {
  start_time=$(date +%s)
  php81 hp php/ldb/mysql/CronGmDailyUser.php TodayMain
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《CronGmDailyUser-TodayMain》执行时间: $execution_time 秒"

  # 休息两分钟 主从复制需要时间 为了避免数据差异 程序休息两分钟 确保数据同步
  sleep 120


  # 每日数据统计(实时数据 整点30分刷新一次)
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think summary_v2 today tomorrow >> /data/logs/summary_v2.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《每日数据统计(实时数据)》执行时间: $execution_time 秒"


  # 大R统计(实时数据 整点30分刷新一次)
  start_time=$(date +%s)
  /usr/bin/php81 /opt/yylbe/php/console/think rmb_player today >> /data/logs/rmb_player.log 2>&1
  end_time=$(date +%s)
  execution_time=$(expr $end_time - $start_time)
  echo "《大R统计(实时数据)》执行时间: $execution_time 秒"

}




case "$(hostname)" in
  pro3main1 | dev2bj1 | test*mono*)
    stat_tasks
    ;;
  *)
    ;;
esac



