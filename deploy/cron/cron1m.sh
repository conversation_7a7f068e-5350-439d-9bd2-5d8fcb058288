


cd /opt/yylbe


. deploy/yylenv/api.sh


. ./vars.sh


# 测试服更新检测频率高一些，放在 15s 那里了

case "$(hostname)" in

  # 如果主机名以 pro1gate- 开头，则是接入机
  dev2bj1 |pro1gate-*)
    yylupdate
    ;;


  *)
    ;;

esac




# 任意机器都做这个检测
# 这个检测不负责拉取代码，而是检测本地代码是否发生了变动，比如被手动拉取了最新代码
sh deploy/yylenv/cron_check_git_commit_changed.sh



# 服务注册 node_exporter 用于指标采集
curl -s "$g_sdserver_url/sd/register?service=node_exporter&port=9100&hostname=`hostname`"


# 更新该机器相关的指标
sh deploy/yylenv/cron_update_metrics_node_info.sh



case "$(hostname)" in
  pro1main2 | pro3main1 | dev2bj1 | test*mono*)
    php81 hp php/llogic/pay/CronPaySideCheck.php

    #每分钟统计一次各玩法收入
    #       1、营收数据折线图 redis：'revenue_data'
    php81 php/console/think revenue_data  >> /data/logs/revenue_data.log 2>&1


    php81 hp "\llogic\source\CronSourceMigration" CheckSyncSourceTable

    ;;
  *)
    ;;
esac




check_cloud_front() {
  # 每分钟都执行，通过数据库状态控制惰性操作
  php81 hp php/llogic/source/CronCloudFrontSync.php


  if [ -f /tmp/__YeeSourceConfigChangedTriggerCloudFrontCron__ ]; then
    rm -f /tmp/__YeeSourceConfigChangedTriggerCloudFrontCron__
    
    php81 hp '\llogic\source\CronAdjustSync'
  fi
}



case "$(hostname)" in
  DISABLEpro1main2 | pro3main1)
    check_cloud_front
    ;;
  *)
    ;;
esac
