


#### Grafana
# https://grafana.com/grafana/download?pg=oss-graf&plcmt=resources
cd ~
rm -r -f tmp/grafana
mkdir -p tmp/grafana
cd ~/tmp/grafana


cpu=""
case $(uname -m) in
    x86_64) cpu="amd64" ;;
    *)      cpu="arm64" ;;
esac

wget https://dl.grafana.com/enterprise/release/grafana-enterprise-11.0.0.linux-$cpu.tar.gz
tar -zxvf grafana-enterprise-11.0.0.linux-$cpu.tar.gz
mkdir -p /data/runtime

#mv grafana-v11.0.0 -T /data/runtime/grafana

#mkdir -p /data/runtime/grafana
#yes | cp grafana-v11.0.0 -r -f -T /data/runtime/grafana

mv /data/runtime/grafana/data -T grafana-v11.0.0/data
mv /data/runtime/grafana  /data/runtime/grafana.old
mv grafana-v11.0.0 -T /data/runtime/grafana

cd ~
rm -r -f tmp/grafana

# 安装插件
cd /data/runtime/grafana
./bin/grafana cli plugins install redis-datasource
./bin/grafana cli plugins install redis-app
#./bin/grafana cli plugins install redis-redis-explorer-app
./bin/grafana cli plugins install grafana-clickhouse-datasource

# 手动找到 anonymous 把 enabled 改为 true
# 手动找到 allow_embedding 改为 true
# /data/runtime/grafana/conf/defaults.ini
cat >/data/runtime/grafana/conf/custom.ini <<EOF

[auth.anonymous]
# enable anonymous access
enabled = true

[security]
# set to true if you want to allow browsers to render Grafana in a <frame>, <iframe>, <embed> or <object>. default is false.
allow_embedding = true

EOF

# https://grafana.com/docs/grafana/latest/upgrade-guide/upgrade-v10.0/
# 如何更新

systemctl status yylgrafana.service
systemctl stop yylgrafana.service
systemctl start yylgrafana.service

# 更新插件
cd /data/runtime/grafana
./bin/grafana cli plugins update-all


