
set -e
#set -x

# https://stackoverflow.com/questions/29832037/how-to-get-script-directory-in-posix-sh
g_this_dir=$(CDPATH= cd -- "$(dirname -- "$0")" && pwd)
g_yylenv_dir=$(realpath "$g_this_dir/../yylenv")

. "$g_yylenv_dir/api.sh"





# gitea 确保特定版本被安装
# https://docs.gitea.com/installation/install-from-binary
# https://dl.gitea.com/gitea/
# https://github.com/go-gitea/gitea/releases
gitea_installed_version=$(gitea --version 2>/dev/null | awk 'NR==1{print $3}' | awk -F'-' '{print $1}')
if [[ $gitea_installed_version != "1.22.0" ]]; then
  cd ~
  rm -r -f tmp/gitea
  mkdir -p tmp/gitea
  cd ~/tmp/gitea
    
  cpu=""
  case $(uname -m) in
      x86_64) cpu="amd64" ;;
      *)      cpu="arm64" ;;
  esac

  wget https://dl.gitea.com/gitea/1.22.0/gitea-1.22.0-linux-$cpu.xz
  xz -d gitea-1.22.0-linux-$cpu.xz
  rm -f /usr/local/bin/gitea
  chmod +x gitea-1.22.0-linux-$cpu
  cp gitea-1.22.0-linux-$cpu -T /usr/local/bin/gitea
  cd ~
  rm -r -f tmp/gitea

fi


# 检查用户是否存在
if ! id "git" >/dev/null 2>&1; then
  #adduser --system --shell /bin/bash --gecos 'Git Version Control' --group --disabled-password --home /home/<USER>
  echo groupadd --system -f git
  groupadd --system -f git
  echo useradd --system --shell /bin/bash --comment 'Git Version Control' --gid git -p '!' --home /home/<USER>
  useradd --system --shell /bin/bash --comment 'Git Version Control' --gid git -p '!' --home /home/<USER>
fi

# Create required directory structure
if [[ ! -e /data/runtime/gitea ]]; then
  echo mkdir -p /data/runtime/gitea/{custom,data,log}
  mkdir -p /data/runtime/gitea/{custom,data,log}
  chown -R git:git /data/runtime/gitea/
  chmod -R 750 /data/runtime/gitea/
fi
if [[ ! -e /home/<USER>
  echo mkdir -p /home/<USER>
  mkdir -p /home/<USER>
  chown -R git:git /home/<USER>/
  chmod -R 750 /home/<USER>/
fi
if [[ ! -e /etc/gitea ]]; then
  echo mkdir /etc/gitea
  mkdir /etc/gitea
  chown root:git /etc/gitea
  chmod 770 /etc/gitea
  # NOTE: /etc/gitea is temporarily set with write permissions for user git so that the web installer can write the configuration file.
  # After the installation is finished, it is recommended to set permissions to read-only using:
  # chmod 750 /etc/gitea
  # chmod 640 /etc/gitea/app.ini
fi








