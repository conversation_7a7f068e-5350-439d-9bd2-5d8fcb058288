
#### Metabase
mkdir /data/runtime/metabase
cd /data/runtime/metabase
mkdir plugins
cd plugins
# https://github.com/ClickHouse/metabase-clickhouse-driver/releases
curl -L -k -O https://github.com/ClickHouse/metabase-clickhouse-driver/releases/download/1.50.0/clickhouse.metabase-driver.jar
cd /data/runtime/metabase
# https://www.metabase.com/docs/latest/installation-and-operation/running-the-metabase-jar-file
# https://github.com/metabase/metabase/releases
curl -L -O -k https://downloads.metabase.com/v0.50.6/metabase.jar

# 1.4.0 v0.49.12  有时区问题
# select now(),toDate(now()),toString(toDate(now())),timeZone();
# 2024-5-28, 07:32  2024-5-27  2024-05-28  Asia/Kolkata



#### 但是这个 docker 版本不支持 arm64，所以在 aws 上还是改回不用 docker 的版本

#### Metabase 需要防止别人利用漏洞安装木马，所以用 docker 运行
# pstree -p 1207 | grep -oP '(?<=\()\d+(?=\))' | xargs echo kill
# https://www.metabase.com/docs/latest/installation-and-operation/running-metabase-on-docker
# 需要考虑时区、端口、存档、插件
# When you publish a container's ports it becomes available not only to the Docker host, but to the outside world as well.
# The metabase.db.trace.db is a debug/trace log - it’s not the query log - so it should be okay to remove that file.
# The query logs are stored in metabase.db.mv.db - including all other metadata.
# Note that H2 automatically appends .mv.db or .h2.db to the path you specify; do not include those in you path! In other words, MB_DB_FILE should be something like /path/to/metabase.db, rather than something like /path/to/metabase.db.mv.db (even though this is the file that actually gets created).

# https://github.com/metabase/metabase/releases
# 更新日志

mkdir /data/runtime/metabase2
cd /data/runtime/metabase2
mkdir plugins
cd plugins
rm -f clickhouse.metabase-driver.jar
# https://github.com/ClickHouse/metabase-clickhouse-driver/releases
wget https://github.com/ClickHouse/metabase-clickhouse-driver/releases/download/1.4.0/clickhouse.metabase-driver.jar
# https://hub.gitmirror.com/
# https://ghps.cc/
# https://gh.ddlc.top/

docker pull metabase/metabase:latest

chmod -R 777 /data/runtime/metabase2

docker stop metabase
docker rm metabase
docker run -d \
  --restart=unless-stopped \
  -p 12233:3000 \
  -v /data/runtime/metabase2/plugins:/plugins \
  -v /data/runtime/metabase2:/data \
  -e "MB_PLUGINS_DIR=/plugins" \
  -e "MB_DB_FILE=/data/metabase.db" \
  -e "JAVA_TIMEZONE=Asia/Kolkata" \
  --name metabase metabase/metabase

docker logs -f metabase

# 基本设置 友好的表单和字段名称 取消
# 更新 检查更新 取消
# 本地化 一周的第一天 星期一
# 本地化 日期格式 2018/1/7
# 本地化 日期分隔符 YYYY-M-D
# 本地化 时间格式 17:24(24 小时制)
# 本地化 数字 分隔样式 100000.00 这样显示桌号、UserID 好看点
# 
# https://www.metabase.com/docs/latest/databases/connections/mysql
# 添加 MYSQL 数据库
# Periodically refingerprint tables 开启
# 超时设置的长一些 connectTimeout=300

# 在容器内运行 metabase 添加 clickhouse 数据库需要使用内网地址 172.16.2.236

