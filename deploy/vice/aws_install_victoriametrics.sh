


#### VictoriaMetrics
cd ~
rm -r -f tmp/VictoriaMetrics
mkdir -p tmp/VictoriaMetrics
cd ~/tmp/VictoriaMetrics

cpu=""
case $(uname -m) in
    x86_64) cpu="amd64" ;;
    *)      cpu="arm64" ;;
esac

# https://github.com/VictoriaMetrics/VictoriaMetrics/releases/
wget https://github.com/VictoriaMetrics/VictoriaMetrics/releases/download/v1.101.0/victoria-metrics-linux-$cpu-v1.101.0.tar.gz
wget https://github.com/VictoriaMetrics/VictoriaMetrics/releases/download/v1.101.0/vmutils-linux-$cpu-v1.101.0.tar.gz
tar xfz victoria-metrics-linux-$cpu-v1.101.0.tar.gz
tar xfz vmutils-linux-$cpu-v1.101.0.tar.gz
cd /usr/local/bin
rm -f victoria-metrics-prod  vmagent-prod  vmalert-prod  vmauth-prod  vmbackup-prod  vmctl-prod  vmrestore-prod
cd ~/tmp/VictoriaMetrics
cp victoria-metrics-prod  vmagent-prod  vmalert-prod  vmauth-prod  vmbackup-prod  vmctl-prod  vmrestore-prod /usr/local/bin/
cd ~
rm -r -f tmp/VictoriaMetrics

