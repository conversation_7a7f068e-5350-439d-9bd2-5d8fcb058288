

#### Alertmanager
# https://github.com/prometheus/alertmanager/releases
cd ~
rm -r -f tmp/Alertmanager
mkdir -p tmp/Alertmanager
cd ~/tmp/Alertmanager


cpu=""
case $(uname -m) in
    x86_64) cpu="amd64" ;;
    *)      cpu="arm64" ;;
esac

wget https://github.com/prometheus/alertmanager/releases/download/v0.27.0/alertmanager-0.27.0.linux-$cpu.tar.gz
tar xfz alertmanager-0.27.0.linux-$cpu.tar.gz
rm -f /usr/local/bin/alertmanager /usr/local/bin/amtool
cd alertmanager-0.27.0.linux-$cpu
cp alertmanager amtool -t /usr/local/bin/
cd ~
rm -r -f tmp/Alertmanager


