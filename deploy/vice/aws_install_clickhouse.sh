





#### ClickHouse

systemctl disable clickhouse-server
systemctl stop clickhouse-server
systemctl status clickhouse-server
yum remove -y clickhouse-server
systemctl status clickhouse-server
systemctl daemon-reload
systemctl status clickhouse-server
rm -r -f /data/runtime/clickhouse
rm -rf /var/lib/clickhouse
rm -rf /etc/clickhouse-server

# https://clickhouse.com/docs/en/install#from-rpm-packages
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://packages.clickhouse.com/rpm/clickhouse.repo

mkdir -p /etc/clickhouse-server/config.d/
mkdir -p /etc/clickhouse-server/users.d/

cat >/etc/clickhouse-server/config.d/network.xml <<EOF
<?xml version="1.0"?>
<clickhouse>
    <listen_host>0.0.0.0</listen_host>
    <tcp_port>9001</tcp_port>
</clickhouse>
EOF

cat /etc/clickhouse-server/config.d/timezone.xml
cat >/etc/clickhouse-server/config.d/timezone.xml <<EOF
<?xml version="1.0"?>
<clickhouse>
  <timezone>Asia/Kolkata</timezone>
</clickhouse>
EOF

cat >/etc/clickhouse-server/users.d/default.xml <<EOF
<?xml version="1.0"?>
<clickhouse>
    <users>
        <default>
            <password>ewiuewoIYEFIQAEYW</password>
            <access_management>1</access_management>
        </default>
    </users>
</clickhouse>
EOF

# 给系统表增加 TTL
# https://clickhouse.com/docs/en/operations/system-tables
# 按天分区，只保留最近几天的数据
cat >/etc/clickhouse-server/config.d/ttl_system_tables.xml <<EOF
<?xml version="1.0"?>
<clickhouse>
    <asynchronous_metric_log>
        <database>system</database>
        <table>asynchronous_metric_log</table>
        <partition_by>event_date</partition_by>
        <ttl>event_date + INTERVAL 11 DAY DELETE</ttl>
        <flush_interval_milliseconds>75000</flush_interval_milliseconds>
    </asynchronous_metric_log>
    <metric_log>
        <database>system</database>
        <table>metric_log</table>
        <partition_by>event_date</partition_by>
        <ttl>event_date + INTERVAL 11 DAY DELETE</ttl>
        <flush_interval_milliseconds>75000</flush_interval_milliseconds>
    </metric_log>
    <part_log>
        <database>system</database>
        <table>part_log</table>
        <partition_by>event_date</partition_by>
        <ttl>event_date + INTERVAL 11 DAY DELETE</ttl>
        <flush_interval_milliseconds>75000</flush_interval_milliseconds>
    </part_log>
    <query_log>
        <database>system</database>
        <table>query_log</table>
        <partition_by>event_date</partition_by>
        <ttl>event_date + INTERVAL 11 DAY DELETE</ttl>
        <flush_interval_milliseconds>75000</flush_interval_milliseconds>
    </query_log>
    <trace_log>
        <database>system</database>
        <table>trace_log</table>
        <partition_by>event_date</partition_by>
        <ttl>event_date + INTERVAL 11 DAY DELETE</ttl>
        <flush_interval_milliseconds>75000</flush_interval_milliseconds>
    </trace_log>
    <processors_profile_log>
        <database>system</database>
        <table>processors_profile_log</table>
        <partition_by>event_date</partition_by>
        <ttl>event_date + INTERVAL 11 DAY DELETE</ttl>
        <flush_interval_milliseconds>75000</flush_interval_milliseconds>
    </processors_profile_log>
</clickhouse>
EOF

cat <<EOF
-- 先改配置，然后重启，然后 DROP 现有的表，然后重启

sudo systemctl --no-pager status clickhouse-server
sudo systemctl restart clickhouse-server
sudo systemctl --no-pager status clickhouse-server

DROP TABLE IF EXISTS system.asynchronous_metric_log;
DROP TABLE IF EXISTS system.metric_log;
DROP TABLE IF EXISTS system.part_log;
DROP TABLE IF EXISTS system.query_log;
DROP TABLE IF EXISTS system.trace_log;
DROP TABLE IF EXISTS system.processors_profile_log;

DROP TABLE IF EXISTS system.asynchronous_metric_log_0;
DROP TABLE IF EXISTS system.metric_log_0;
DROP TABLE IF EXISTS system.part_log_0;
DROP TABLE IF EXISTS system.query_log_0;
DROP TABLE IF EXISTS system.trace_log_0;
DROP TABLE IF EXISTS system.processors_profile_log_0;

EOF


yum install --nogpgcheck  -y clickhouse-server clickhouse-client

sudo systemctl enable clickhouse-server
sudo systemctl start clickhouse-server
sudo systemctl --no-pager status clickhouse-server

# journalctl -u clickhouse-server.service

while true; do
  if systemctl --quiet is-active clickhouse-server.service ; then
    echo "Service has started successfully."
    break
  fi
  echo "Waiting for service to start..."
  sleep 1
done

sleep 2

clickhouse-client -mn --port 9001 --password ewiuewoIYEFIQAEYW --query "CREATE USER rouser IDENTIFIED WITH plaintext_password BY 'ewuweweuiDJHJ'; GRANT SELECT ON *.* TO rouser;"

clickhouse-client -mn --port 9001 --password ewiuewoIYEFIQAEYW --query "SELECT now();"


# 后面在手动修改密码，上面的密码只是示例


