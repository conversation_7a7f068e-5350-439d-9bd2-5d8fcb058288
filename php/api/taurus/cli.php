<?php

$is_cli = (bool) preg_match("/cli/i", php_sapi_name());
if (!$is_cli) {
    exit('not cli exit');
}
//设置log
$service_name = 'api';
\lbase\Log::init($service_name);


if (!defined("ROOT_TOP_PATH")) {
    define("ROOT_TOP_PATH", __DIR__);
}
require_once(ROOT_TOP_PATH . "/run.php");
//加载配置文件
lib\Config::load();

$opName = $argv[1];
//url注册表中获取当前页面配置信息
$geturlRegistry = lib\UrlRegistryTable::get($opName);

if (!$geturlRegistry || !method_exists($geturlRegistry['class'], $geturlRegistry['function'])) {
    exit('function error exit');
}
$cls = $geturlRegistry['class'];
$fuc = $geturlRegistry['function'];
$html = $cls::$fuc();
exit($html ?: 'success....');
