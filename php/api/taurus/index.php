<?php
//error_reporting(0);
ob_start();
header("Content-type:text/html;charset=utf-8");

if (!defined("ROOT_TOP_PATH")) {
    define("ROOT_TOP_PATH", __DIR__);
}
require_once(ROOT_TOP_PATH . "/run.php");

//加载配置文件
lib\Config::load();

//获取当前执行页面key
$opName = lib\UrlRegistryTable::getOp();

//url注册表中获取当前页面配置信息
$geturlRegistry = lib\UrlRegistryTable::get($opName);

//未在url注册表注册跳转到404页面
if ($geturlRegistry === false) {
    //404页面
    lib\HTTPResponseHeader::set('404', true);
}

// 走到这里，opName 就是合法的
// 假设是 AIAPI 模块，后面根据条件再调整到 AIAdmin 模块
$req_mod = 'AIAPI';
$req_func = $opName;


$handlerExists = method_exists($geturlRegistry['class'], $geturlRegistry['function']);
$isCLI = (isset($geturlRegistry['cli']) && $geturlRegistry['cli']);
if (!$handlerExists || $isCLI) {
    //404页面
    lib\HTTPResponseHeader::set('404', true);
}

//当前页面是否需登录可访问
if ($geturlRegistry['verifyToken']) {
    $req_mod = 'AIAdmin';

    //api 接口授权认证
}

//启用session的地方
if (in_array($opName, ['login', 'ajax']) || $geturlRegistry['checkLogin']) {
    $req_mod = 'AIAdmin';

    //设置session
    $session_save_path = __DIR__ . '/../session_tmp';
    $expre_time = 8 * 1440;
    ini_set("session.gc_maxlifetime", $expre_time);
    ini_set('session.cookie_lifetime', $expre_time);
    session_set_cookie_params($expre_time);
    session_save_path($session_save_path);
    $ok = session_start();

    //setcookie(session_name(),session_id(),$expre_time,$session_save_path);

    // https://www.php.net/manual/en/function.setcookie.php
    // 按照文档，setcookie() 的第三个参数是 This is a Unix timestamp so is in number of seconds since the epoch.
    // 所以上面的代码有 bug，应该传个时间戳，而不是时间长度
    // 按照文档，第四个参数
    // The path on the server in which the cookie will be available on.
    // If set to '/', the cookie will be available within the entire domain.
    // If set to '/foo/', the cookie will only be available within the /foo/ directory and all sub-directories such as /foo/bar/ of domain.
    // The default value is the current directory that the cookie is being set in. 
    // 所以，第四个参数也有 bug，不能传本地文件的路径
    // 目前第四个参数的修改办法是省略不传
    setcookie(session_name(), session_id(), time() + $expre_time);
}


// 代码位置
$cls = $geturlRegistry['class'];
$fuc = $geturlRegistry['function'];


if ($isCLI) {
    $req_mod = 'AICLI';
}
\lbase\fpm\Server::on_request_begin($req_mod, $opName, $cls, $fuc);


//管理后台登录验证
if ($geturlRegistry['checkLogin']) {
    if (!isset($_SESSION['admin_info']) || empty($_SESSION['admin_info'])) {
        header("Location:" . lib\Url::getUrl('login'));
        exit();
    }
}



//执行逻辑
$html = $cls::$fuc();
//返回模板
$template = $html['template'] ?? '';

//没有模板  按照ajax格式返回
if (!$template) {
    lib\Request::returnApi($html);
    exit();
}

// 渲染模板遇到个问题，echo 意外的多了个换行
// 提前把这个类加载了是不是好点？
\lib\Url::getAjaxUrl('login');

$html['op'] = $opName;
define("TEMPLATES_FILE", $template);
lib\Tpl::get($html);
die;
