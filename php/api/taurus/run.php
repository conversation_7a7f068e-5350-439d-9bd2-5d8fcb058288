<?php

if (!defined("ROOT_TOP_PATH")) {
    define("ROOT_TOP_PATH", __DIR__);
}

require_once(ROOT_TOP_PATH . "/../../vendor/autoload.php");

\lbase\fpm\Server::init_log_ctx();
$service_name = 'api';
\lbase\Log::init($service_name);


//加载 函数
require_once(ROOT_TOP_PATH . "/../function/Function.php"); // TODO DEL

//加载核心模板文件
const TEMPLATE_PATH = ROOT_TOP_PATH . "/../template"; // TODO DEL

//加载常量定义文件 // TODO DEL
require_once(ROOT_TOP_PATH . "/../config/enum/_loader.php");


//加载 oss扩展
require_once(ROOT_TOP_PATH . "/../vendor/aliyuncs/oss-sdk-php/autoload.php");
