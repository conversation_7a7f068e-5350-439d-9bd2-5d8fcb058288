<?php

function http_request($url, $options, $method = 'POST', $header = [], $timeout = 60)
{

    $curl = curl_init();

    if ($method == 'GET') {
        $options = is_string($options) ? $options : http_build_query($options);
        curl_setopt($curl, CURLOPT_URL, "$url?$options"); // 请求的url
    }

    if ($method == 'POST') {
        curl_setopt($curl, CURLOPT_URL, $url); // 请求的url
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $options);
    }

    curl_setopt($curl, CURLOPT_TIMEOUT, $timeout); // 设置超时时间
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE); // https
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);

    if (!empty($header)) {
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
    }

    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    $output = curl_exec($curl);
    curl_close($curl);

    return json_decode($output, true);
}

$btime = microtime(true);
$url = 'http://127.0.0.1:80/index.php?op=drop_card';
$send_data = array(
    'hand' => [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 1],
    'xcard' => 0x01,
    'pnum' => 2,
    'rnum' => 0,
);
$options = array(
    'd' => $send_data,
);
echo ($url . ' ready to send http') . PHP_EOL;
$response = http_request($url, json_encode($options), 'POST', ['Content-Type:application/json;charset=UTF-8']);
echo ($url . ' callback from http ' . $response) . PHP_EOL;
//if(!isset($response['code']) || $response['code'] != ErrCode::SUCCEED){
//    return;
//}
//if(!isset($response['res']) || !isset($response['res']['drop'])){
//    return;
//}
//$check_drop_res = $response['res']['drop'];
echo ('耗时 ' . (microtime(true) - $btime)) . PHP_EOL;
?>

