
//公共使用美化版alert事件
function balert(title='',contents='',noticlass='info'){
	window.jQuery.NotificationApp.send(title, contents, "top-center", "rgba(0,0,0,0.2)", noticlass);
}



//公共form表单 ajax post提交
/**
 *	form_id  提交的表单id
 *	post_url 提交地址
 *	返回值 alert() 提示提交结果
 */
function ajax_post_form_submit(e,form_id,post_url,reload=false){
		var formData = new FormData($("#"+form_id)[0]);
		var title = $(e).html();
		$.ajax({
			type: 'post',
			url: post_url,
			data:formData,
			contentType: false,        /*不可缺*/
			processData: false,         /*不可缺*/
			beforeSend: function () {
				$(e).attr('disabled',"true");
				$(e).html('<span class="spinner-border spinner-border-sm mr-1" role="status" aria-hidden="true"></span>Loading...');
			},
			complete: function () {
				$(e).removeAttr("disabled");
				$(e).html(title);
			  //ajax请求完成，不管成功失败
			},
			success: function (res) {
				if(res.code){
					$("#add_admin_close_btn").click();
					balert('成功提示',res.message,'success');
					//刷新当前页面
					if(reload){
						setTimeout('window.location.reload()',1000);
					}
				}else{
					$("#add_admin_close_btn").click();
					balert('失败提示',res.message,'danger');
					if(res.message=='请登录后再操作！'){
						window.location.href = res.data.jumpUrl;
					}
				}
			}
		});
}


//公共form表单 ajax post提交
/**
 *	form_id  提交的表单id
 *	post_url 提交地址
 *	返回值 alert() 提示提交结果
 */
function ajax_post_data_submit(e,data,post_url,reload=false){
	var title = $(e).html();
	$.ajax({
		type: 'post',
		url: post_url,
		data:data,
		beforeSend: function () {
			$(e).attr('disabled',"true");
			$(e).html('<span class="spinner-border spinner-border-sm mr-1" role="status" aria-hidden="true"></span>Loading...');
		},
		complete: function () {
			$(e).removeAttr("disabled");
			$(e).html(title);
		  //ajax请求完成，不管成功失败
		},
		success: function (res) {
			if(res.code){
				balert('成功提示',res.message,'success');
				//刷新当前页面
				if(reload){
					setTimeout('window.location.reload()',1000);
				}
			}else{
				balert('失败提示',res.message,'danger');
				if(res.message=='请登录后再操作！'){
					window.location.href = res.data.jumpUrl;
				}
			}
		}
	});
}




