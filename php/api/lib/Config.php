<?php

namespace lib;

class Config
{
    /**
     * @var string 配置文件路径
     */
    private static $confing_file_path = __DIR__ . '/../config/config.php';

    /**
     * @var array 配置参数
     */
    private static $config = [];




    /**
     * 加载配置文件（PHP格式）
     * @access public
     * @param  string $file  配置文件名
     * @param  string $name  配置名（如设置即表示二级配置）
     * @param  string $range 作用域
     * @return mixed
     */
    public static function load()
    {
        if (is_file(self::$confing_file_path)) {
            return self::set(include self::$confing_file_path);
        } else {
            return self::$config;
        }
    }


    /**
     * 设置配置参数 name 为数组则为批量设置
     * @access public
     * @param  string|array $name  配置参数名（支持二级配置 . 号分割）
     * @param  mixed        $value 配置值
     * @return mixed
     */
    public static function set($name, $value = null)
    {
        // 数组则表示批量设置
        if (is_array($name)) {
            self::$config = array_merge(self::$config, $name);
            return self::$config;
        }

        // 为空直接返回已有配置
        return self::$config;
    }

    /**
     * 检测配置是否存在
     * @access public
     * @param  string $name 配置参数名（支持二级配置 . 号分割）
     * @return bool
     */
    public static function has($name)
    {
        return isset(self::$config[$name]);
    }

    /**
     * 获取配置参数 为空则获取所有配置
     * @access public
     * @param  string $name 配置参数名（支持二级配置 . 号分割）
     * @return mixed
     */
    public static function get($name = null)
    {
        // 如果配置为空，则调用load函数加载配置文件
        if (empty(self::$config)) {
            self::load();
        }

        // 无参数时获取所有
        if (empty($name) && isset(self::$config)) {
            return self::$config;
        }
        return self::has($name) ? self::$config[$name] : '';
    }


}
