<?php
/**
 * HTTP响应头
 *
 * @package chinacn\score
 * <AUTHOR>
 */
namespace lib;

class HTTPResponseHeader
{
    /**
     * 设置响应头状态码
     *
     * @param int $iResponseCode
     * @param bool $bTerminal 是否立即终止脚本（默认：false）
     */
    public static function set($iResponseCode = 200, $bTerminal = false)
    {
        self::_set($iResponseCode);

        if ($bTerminal)
            exit;
    }

    /**
     * 301跳转
     *
     * @param string $sURL 目标URL
     * @param bool $bTerminal 是否立即终止脚本（默认：false）
     */
    public static function set301($sURL, $bTerminal = false)
    {
        header('Location: ' . $sURL);
        self::set(301, $bTerminal);
    }

    /**
     * 302跳转
     *
     * @param string $sURL 目标URL
     * @param bool $bTerminal 是否立即终止脚本（默认：false）
     */
    public static function set302($sURL, $bTerminal = false)
    {
        header('Location: ' . $sURL);
        self::set(302, $bTerminal);
    }

    /**
     * 输出ETag
     */
    public static function setETag($sETag)
    {
        header('ETag: "' . $sETag . '"');
    }

    /**
     * 输出最后修改时间
     */
    public static function setLastModified($iLastModified)
    {
        if ($iLastModified > 0) {
            header('Last-Modified: ' . gmdate('D, d M Y H:i:s', $iLastModified) . ' GMT');
        }
    }

    /**
     * 输出和缓存控制相关的头信息
     */
    public static function setCacheControl($iExpires)
    {
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + $iExpires) . ' GMT');
        header('Cache-Control: max-age=' . $iExpires);
    }

    /**
     * inner set
     * @param int $iResponseCode
     */
    protected static function _set($iResponseCode = 200)
    {
        if (function_exists('http_response_code')) {
            http_response_code($iResponseCode);
            return;
        }

        switch ($iResponseCode) {
            case 100:
                $sDescription = 'Continue';
                break;
            case 101:
                $sDescription = 'Switching Protocols';
                break;
            case 200:
                $sDescription = 'OK';
                break;
            case 201:
                $sDescription = 'Created';
                break;
            case 202:
                $sDescription = 'Accepted';
                break;
            case 203:
                $sDescription = 'Non-Authoritative Information';
                break;
            case 204:
                $sDescription = 'No Content';
                break;
            case 205:
                $sDescription = 'Reset Content';
                break;
            case 206:
                $sDescription = 'Partial Content';
                break;
            case 300:
                $sDescription = 'Multiple Choices';
                break;
            case 301:
                $sDescription = 'Moved Permanently';
                break;
            case 302:
                $sDescription = 'Moved Temporarily';
                break;
            case 303:
                $sDescription = 'See Other';
                break;
            case 304:
                $sDescription = 'Not Modified';
                break;
            case 305:
                $sDescription = 'Use Proxy';
                break;
            case 400:
                $sDescription = 'Bad Request';
                break;
            case 401:
                $sDescription = 'Unauthorized';
                break;
            case 402:
                $sDescription = 'Payment Required';
                break;
            case 403:
                $sDescription = 'Forbidden';
                break;
            case 404:
                $sDescription = 'Not Found';
                break;
            case 405:
                $sDescription = 'Method Not Allowed';
                break;
            case 406:
                $sDescription = 'Not Acceptable';
                break;
            case 407:
                $sDescription = 'Proxy Authentication Required';
                break;
            case 408:
                $sDescription = 'Request Time-out';
                break;
            case 409:
                $sDescription = 'Conflict';
                break;
            case 410:
                $sDescription = 'Gone';
                break;
            case 411:
                $sDescription = 'Length Required';
                break;
            case 412:
                $sDescription = 'Precondition Failed';
                break;
            case 413:
                $sDescription = 'Request Entity Too Large';
                break;
            case 414:
                $sDescription = 'Request-URI Too Large';
                break;
            case 415:
                $sDescription = 'Unsupported Media Type';
                break;
            case 500:
                $sDescription = 'Internal Server Error';
                break;
            case 501:
                $sDescription = 'Not Implemented';
                break;
            case 502:
                $sDescription = 'Bad Gateway';
                break;
            case 503:
                $sDescription = 'Service Unavailable';
                break;
            case 504:
                $sDescription = 'Gateway Time-out';
                break;
            case 505:
                $sDescription = 'HTTP Version not supported';
                break;
            default:
                exit('Unknown http status code "' . htmlentities($iResponseCode) . '"');
        }

        $sProtocol = (isset($_SERVER['SERVER_PROTOCOL']) ? $_SERVER['SERVER_PROTOCOL'] : 'HTTP/1.0');

        header($sProtocol . ' ' . $iResponseCode . ' ' . $sDescription);
    }
}
