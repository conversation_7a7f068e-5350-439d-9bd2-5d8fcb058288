<?php
namespace lib;

class Request
{
    /**
     * @return array|false|string
     */
    public static function getJsonBody()
    {
        $body = '';
        $res = strpos($_SERVER['CONTENT_TYPE'], 'application/json');
        //Log::console_log(__FUNCTION__,'接受到的头信息：'.$_SERVER['CONTENT_TYPE']);
        //Log::console_log(__FUNCTION__,'匹配结果：'.json_encode($res));
        if ($res !== false) {
            $body = file_get_contents('php://input');
            $body_json = json_decode($body, true);
            \lbase\fpm\Server::set_req_body($body_json);
        }
        return $body;
    }


    //获取key 并过滤有害信息
    public static function getValue($key)
    {
        $request = array_diff_assoc($_REQUEST, $_COOKIE);
        if (isset($request[$key])) {
            if (is_string($request[$key])) {
                if (!json_decode($request[$key])) {
                    $request[$key] = str_replace("\"", "'", $request[$key]);
                    $request[$key] = addslashes($request[$key]);
                    return trim($request[$key]);
                }
            }
            return $request[$key];
        }
        return null;
    }


    //统一返回接口参数
    public static function returnApi($rtn)
    {
        header('Content-Type:application/json');
        echo json_encode($rtn);

        \lbase\fpm\Server::on_request_end($rtn);
        \lbase\metric\Registry::instance()->flush_to_metricregistryserver();

        die;
    }
}


?>

