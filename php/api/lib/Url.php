<?php
/**
 * 处理需要使用的URL
 */

namespace lib;

class Url
{

    //获取访问的域名 以及端口
    public static function _getHomeUrl()
    {
        // 接入机 nginx 转发没设置 HTTP_X_FORWARDED_PROTO，所以这里判断不出 https，直接为空就能用，所以不用改接入机了
        return '';

        $http_type = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https')) ? 'https://' : 'http://';
        return $http_type . $_SERVER['HTTP_HOST'];
    }


    //ajax生成url
    public static function getAjaxUrl($act)
    {
        $homeUrl = self::_getHomeUrl();
        $ret = $homeUrl . '/index.php?op=ajax&act=' . $act;
        return $ret;
    }

    //生成非ajax请求的url
    public static function getUrl($op, $parm = array())
    {
        $query_str = '';
        if (is_array($parm) && !empty($parm)) {
            $query_str = '&' . http_build_query($parm);
        }
        return self::_getHomeUrl() . '/index.php?op=' . $op . $query_str;
    }


}
?>

