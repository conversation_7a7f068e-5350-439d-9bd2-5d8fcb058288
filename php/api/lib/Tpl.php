<?php
namespace lib;

/**
 * 获取模板文件
 */
class Tpl
{
    public $_tpl = '';
    public $_args = array();
    public $_rtntpl = false;

    public function __construct($args)
    {
        if (is_array($args)) {
            $this->_args = $args;
        } else if ($args === true) {
            $this->_rtntpl = true;
        }
    }

    public static function get($args)
    {
        $tpl = new Tpl($args);
        if (!$tpl->getTpl()) {
            return $tpl->_tpl;
        }
    }

    public function getTpl()
    {
        $this->getTplFile();
        if ($this->_rtntpl === true)
            return false;

        foreach ($this->_args as $_k => $_v) {
            $$_k = $_v;
        }
        if (!is_file($this->_tpl)) {
            echo $this->_tpl;
            //header('Location:'.(SYS_LANG=='cn'?EN_SYSTEM_DOMAIN:EN_SYSTEM_DOMAIN));
            die("the templates is not exist");
        }
        #$this->_tpl = ROOT_PATH.'/'.SYS_LANG.'.html';
        require_once($this->_tpl);
    }

    const TEMPLATE_PATH = __DIR__ . "/../template";

    public function getTplFile()
    {
        if (defined("TEMPLATES_FILE")) {
            $this->_tpl = self::TEMPLATE_PATH . '/' . TEMPLATES_FILE;
        } else {
            $this->_tpl = pathinfo($_SERVER['REQUEST_URI']);
            $this->_tpl = self::TEMPLATE_PATH . '/' . $this->_tpl['filename'] . '.html';
        }
        return true;
    }
}

?>

