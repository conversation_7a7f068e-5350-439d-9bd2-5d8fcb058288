<?php

namespace lib;

/**
 * url注册表
 */
class UrlRegistryTable
{
    //url数组定义   [类,方法,是否需要登录,是否为cli模式]
    const register_url_list = [
        //API接口------------------------------------------------------------------------------------------------------------
        //'init_group_cards_one'  =>['\api\ApiRummyAct','initGroupCards',false,false],                //Rummy AI grou牌组
        'init_group_cards' => ['\api\ApiRummyAct', 'iniGroupCardsBat', false, false],
        //Rummy AI 批量grou牌组
        'draw_card' => ['\api\ApiRummyAct', 'drawCard', false, false],
        //Rummy AI 摸明 摸暗 判定
        'discard_card' => ['\api\ApiRummyAct', 'discardCard', false, false],
        //Rummy AI 需要打掉的牌
        'drop_card' => ['\api\ApiRummyAct', 'dropCard', false, false],
        //Rummy AI 是否需要drop
        'deal_cards_v2' => ['\api\ApiRummyAct', 'dealCardsByProfit', false, false],
        //Rummy AI 盈利率模式 发牌
        'deal_cards_v2_bat' => ['\api\ApiRummyAct', 'dealCardsByProfitBat', false, false],
        //Rummy AI 盈利率模式 批量发牌
        'match_all_ai' => ['\api\ApiRummyAct', 'matchAllAi', false, false],
        //Rummy AI 盈利率模式 是否匹配全ai
        'dark_card' => ['\api\ApiRummyAct', 'DrawDarkCardByProfit', false, false],
        //Rummy AI 盈利率 当前用户摸暗牌逻辑
        'jack_pool_name' => ['\api\ApiRummyAct', 'getJackPoolNameByProfit', false, false],
        //Rummy AI 获取奖池名称

        'auto_sort_one' => ['\api\ApiRummyAct', 'autoSortCards', false, false],
        //Rummy AI AutoSort
        ####################################################################################################################
        'tp_deal_cards' => ['\api\ApiTeenPattyPrAct', 'dealCards', false, false],
        //TeenPatti AI 发牌
        'tp_opt' => ['\api\ApiTeenPattyPrAct', 'aiOpt', false, false],
        //TeenPatti AI 操作
        'tp_bet_sequence' => ['\api\ApiTeenPattyPrAct', 'betSequence', false, false],
        //TeenPatti AI 玩家顺位预估
        'tp_change_card' => ['\api\ApiTeenPattyPrAct', 'changeCards', false, false],
        //TeenPatti AI 中途换牌

        //非百人场
        'init_header_nickname' => ['\api\ApiRummyAct', 'changeNameAndHeader', false, false],
        //非百人场 初始化ai的头像和昵称
        ####################################################################################################################
        'ab_deal_cards' => ['\api\ApiAbAct', 'dealCards', false, false],
        //AB AI 发牌
        'ab_ai_init' => ['\api\ApiAbAct', 'initAiTableInfo', false, false],
        //AB AI 信息初始化
        'ab_bet' => ['\api\ApiAbAct', 'betAmount', false, false],
        //AB AI 下注
        'ab_balance' => ['\api\ApiAbAct', 'balanceAmountList', false, false],
        //AB AI 账户初始化 重置
        ####################################################################################################################
        'abc_deal_cards' => ['\api\ApiAbClassicAct', 'dealCards', false, false],
        //ABfast AI 发牌
        'abc_ai_init' => ['\api\ApiAbClassicAct', 'initAiTableInfo', false, false],
        //ABfast AI 信息初始化
        'abc_bet' => ['\api\ApiAbClassicAct', 'betAmount', false, false],
        //ABfast AI 下注
        'abc_balance' => ['\api\ApiAbClassicAct', 'balanceAmountList', false, false],
        //ABfast AI 账户初始化 重置
        ####################################################################################################################
        'rb_deal_cards' => ['\api\ApiRedBlackAct', 'dealCards', false, false],
        //红黑 AI 发牌
        'rb_ai_init' => ['\api\ApiRedBlackAct', 'initAiTableInfo', false, false],
        //红黑 AI 信息初始化
        'rb_bet' => ['\api\ApiRedBlackAct', 'betAmount', false, false],
        //红黑 AI 下注
        'rb_balance' => ['\api\ApiRedBlackAct', 'balanceAmountList', false, false],
        //红黑 AI 账户初始化 重置
        ####################################################################################################################
        'rbf_deal_cards' => ['\api\ApiRedBlackFastAct', 'dealCards', false, false],
        //快速红黑 AI 发牌
        'rbf_ai_init' => ['\api\ApiRedBlackFastAct', 'initAiTableInfo', false, false],
        //快速红黑 AI 信息初始化
        'rbf_bet' => ['\api\ApiRedBlackFastAct', 'betAmount', false, false],
        //快速红黑 AI 下注
        'rbf_balance' => ['\api\ApiRedBlackFastAct', 'balanceAmountList', false, false],
        //快速红黑 AI 账户初始化 重置
        ####################################################################################################################
        'ld_deal_cards' => ['\api\ApiLuckyDiceAct', 'dealCards', false, false],
        //六面 AI 发牌
        'ld_ai_init' => ['\api\ApiLuckyDiceAct', 'initAiTableInfo', false, false],
        //六面 AI 信息初始化
        'ld_bet' => ['\api\ApiLuckyDiceAct', 'betAmount', false, false],
        //六面 AI 下注
        'ld_balance' => ['\api\ApiLuckyDiceAct', 'balanceAmountList', false, false],
        //六面 AI 账户初始化 重置
        ####################################################################################################################
        'lh_deal_cards' => ['\api\ApiLongHuAct', 'dealCards', false, false],
        //龙虎 AI 发牌
        'lh_ai_init' => ['\api\ApiLongHuAct', 'initAiTableInfo', false, false],
        //龙虎 AI 信息初始化
        'lh_bet' => ['\api\ApiLongHuAct', 'betAmount', false, false],
        //龙虎 AI 下注
        'lh_balance' => ['\api\ApiLongHuAct', 'balanceAmountList', false, false],
        //龙虎 AI 账户初始化 重置
        'lh_pool_cards_cnt' => ['\api\ApiLongHuAct', 'cardPoolCntAndUsedCnt', false, false],
        //龙虎 AI 获取牌池剩余数量 以及 牌池已使用数量
        ####################################################################################################################
        'wf_deal_cards' => ['\api\ApiWheelAct', 'dealCards', false, false],
        //幸运转盘 AI 发牌
        'wf_ai_init' => ['\api\ApiWheelAct', 'initAiTableInfo', false, false],
        //幸运转盘 AI 信息初始化
        'wf_bet' => ['\api\ApiWheelAct', 'betAmount', false, false],
        //幸运转盘 AI 下注
        'wf_balance' => ['\api\ApiWheelAct', 'balanceAmountList', false, false],
        //幸运转盘 AI 账户初始化 重置
        ####################################################################################################################
        'l7_deal_cards' => ['\api\Api7up7downAct', 'dealCards', false, false],
        //7上7下 AI 发牌
        'l7_ai_init' => ['\api\Api7up7downAct', 'initAiTableInfo', false, false],
        //7上7下 AI 信息初始化
        'l7_bet' => ['\api\Api7up7downAct', 'betAmount', false, false],
        //7上7下 AI 下注
        'l7_balance' => ['\api\Api7up7downAct', 'balanceAmountList', false, false],
        //7上7下 AI 账户初始化 重置
        //------------------------------------------------------------------------------------------------------------------
        ####################################################################################################################
        'lt_deal_cards' => ['\api\ApiLuckyLotoAct', 'dealCards', false, false],
        //lucky Loto AI 发牌
        'lt_ai_init' => ['\api\ApiLuckyLotoAct', 'initAiTableInfo', false, false],
        //lucky Loto AI 信息初始化
        'lt_bet' => ['\api\ApiLuckyLotoAct', 'betAmount', false, false],
        //lucky Loto AI 下注
        'lt_balance' => ['\api\ApiLuckyLotoAct', 'balanceAmountList', false, false],
        //lucky Loto AI 账户初始化 重置
        ####################################################################################################################
        'spx_deal_cards' => ['\api\ApiSpacexAct', 'dealCards', false, false],
        //space x AI 发牌
        'spx_ai_init' => ['\api\ApiSpacexAct', 'initAiTableInfo', false, false],
        //space x AI 信息初始化
        'spx_bet' => ['\api\ApiSpacexAct', 'betAmount', false, false],
        //space x AI 下注
        'spx_leave' => ['\api\ApiSpacexAct', 'leaveSpacex', false, false],
        //space x AI 离开
        'spx_balance' => ['\api\ApiSpacexAct', 'balanceAmountList', false, false],
        //space x AI 账户初始化 重置
        ####################################################################################################################

        'bb_deal_cards' => ['\api\ApiBenzBmwAct', 'dealCards', false, false],
        //奔驰宝马 AI 发牌
        'bb_ai_init' => ['\api\ApiBenzBmwAct', 'initAiTableInfo', false, false],
        //奔驰宝马 AI 信息初始化
        'bb_bet' => ['\api\ApiBenzBmwAct', 'betAmount', false, false],
        //奔驰宝马 AI 下注
        'bb_balance' => ['\api\ApiBenzBmwAct', 'balanceAmountList', false, false],
        //奔驰宝马 AI 账户初始化 重置


        //后台使用url---------------------------------------------------------------------------------------------------------
        ####################################################################################################################
        'tpw_deal_cards' => ['\api\ApiTpWarAct', 'dealCards', false, false],
        //tp War AI 发牌
        'tpw_ai_init' => ['\api\ApiTpWarAct', 'initAiTableInfo', false, false],
        //tp War AI 信息初始化
        'tpw_bet' => ['\api\ApiTpWarAct', 'betAmount', false, false],
        //tp War AI 下注
        'tpw_balance' => ['\api\ApiTpWarAct', 'balanceAmountList', false, false],
        //tp War AI 账户初始化 重置

        ####################################################################################################################
        'slots_client_show_info' => ['\api\ApiSlotsAct', 'clientShowInfo', false, false],
        //客户端展示信息
        'slots_deal_cards' => ['\api\ApiSlotsAct', 'dealCards', false, false],
        //slots AI 发牌

        'login' => ['\score\LoginAct', 'login', false, false],
        //管理后台登录
        'logout' => ['\score\LoginAct', 'logout', true, false],
        //管理后台登录
        'ajax' => ['\score\AjaxAct', 'run', false, false],
        //统一ajax请求入口
        #####
        'demo-rummy-dealcards' => ['\score\DemoRummyAct', 'dealCards', true, false],
        //[demo 第一视角] Rummy 模拟发牌
        'demo-rummy-draw-dis-card' => ['\score\DemoRummyAct', 'drawAndDisCard', true, false],
        //[demo 第一视角] Rummy 模拟 摸牌 打牌
        'demo-teenpatty-dealcards' => ['\score\DemoTeenPattyAct', 'dealCards', true, false],
        //[demo 第一视角] teenpatti 发牌并排序展示
        ####
        'comparison-card-code' => ['\score\CardCodeComparisonTableAct', 'comparisonTable', true, false],
        //AI [牌码对照表]
        'comparison-rummy-check-group' => ['\score\CardCodeComparisonTableAct', 'checkRummyGroup', true, false],
        //AI [牌码对照表 验证Rummy摆牌规则]
        'comparison-teenpatty-check-group-score' => ['\score\CardCodeComparisonTableAct', 'checkTeenPattyGroupScore', true, false],
        //AI [牌码对照表 验证Rummy摆牌规则]
        ####

        ####


        ####
        'pr-z-config' => ['\score\ConfigPrAct', 'prZConfigList', true, false],
        //[盈利率] [真人相关配置列表]
        'pr-ai-config' => ['\score\ConfigPrAct', 'prAiConfigList', true, false],
        //[盈利率] [ai相关配置列表]
        'pr-profit-log' => ['\score\ConfigPrAct', 'getJackpotProfitLog', true, false],
        //[盈利率] [奖池初始化之前盈利情况log查看]

        'pr-tp-ai-config' => ['\score\ConfigTpPrAct', 'prAiConfigList', true, false],
        //[盈利率] [tp ai相关配置列表]

        'pr-tp-ak47-ai-config' => ['\score\ConfigTpAK47PrAct', 'prAiConfigList', true, false],
        //[盈利率] [tp-ak47 ai相关配置列表]

        'pr-tp-joker-ai-config' => ['\score\ConfigTpJokerPrAct', 'prAiConfigList', true, false],
        //[盈利率] [tp-joker ai相关配置列表]

        'pr-tp-profit-log' => ['\score\ConfigTpPrAct', 'getJackpotProfitLog', true, false],
        //[盈利率] [奖池初始化之前盈利情况log查看]
        'pr-hp-ai-config' => ['\score\ConfigHundredPeopleAct', 'prAiConfigList', true, false],
        //[盈利率] [百人场配置]
        ###
        'overall-profit' => ['\score\OverallProfitAct', 'overallProfitList', true, false],
        //[大盘控] [盈利情况]
        ###

        ###

        // 玩法数据模拟
        'get_data_simulation' => ['\score\DataSimulationAct', 'main', true, false],


    ];


    /**
     * 获取url注册表配置
     */
    public static function get($key)
    {
        $val = self::register_url_list[$key] ?? false;
        if ($val === false) {
            return false;
        }
        $geturlRegistry['class'] = $val[0];
        $geturlRegistry['function'] = $val[1];
        $geturlRegistry['checkLogin'] = $val[2];
        $geturlRegistry['cli'] = $val[3];
        $geturlRegistry['verifyToken'] = false;
        return $geturlRegistry;
    }


    /**
     * 获取当前执行页面key
     */
    public static function getOp()
    {
        $op = Request::getValue('op');
        if (!empty($op)) {
            return strtolower($op);
        } else {
            return false;
        }
    }
}
