<?php

namespace lib;

class Log
{

    public static function console_log($fun, $msg, $name = 'other')
    {
        //        if(!$name){
//            $name = 'other';
//        }
//        //rummy tp log屏蔽
//        if($name!='other'){
//            \SeasLog::setLogger('ai_api_new/'.$name);
//            \SeasLog::info($fun.' | '.$msg);
//        }
        // bakhmut: 不知道为啥上面把打日志注掉了
        //\SeasLog::info($fun . ' | ' . $msg);
        \lbase\Log::info($fun, ([
            'msg' => $msg,
        ]));
    }

    // 有些调用 console_log 的地方，fun 参数传的不对，所以加了这个函数，把那些调用点纠正一下
    public static function console_log_room_id($fun, $room_id, $msg)
    {
        //\SeasLog::info($fun . ' | ' . $room_id . ' ' . $msg);
        \lbase\Log::info($fun, ([
            'room_id' => $room_id,
            'msg' => $msg,
        ]));
    }

    public static function console_object_log($fun, $msg)
    {
        //\SeasLog::info($fun.' | '.$msg);
        // bakhmut: 不知道为啥上面把打日志注掉了
        //\SeasLog::info($fun . ' | ' . $msg);
        \lbase\Log::info($fun, ([
            'msg' => $msg,
        ]));
    }

    // 发现没人调用这个函数
    public static function err_log($fun, $msg)
    {
        //\SeasLog::error($fun . ' | ' . $msg);
        \lbase\Log::error($fun, ([
            'msg' => $msg,
        ]));
    }

}
