<?php

/**
 * redis操作类
 * 说明，任何为false的串，存在redis中都是空串。
 * 只有在key不存在时，才会返回false。
 * 这点可用于防止缓存穿透
 *
 */
namespace lib;

class Xredis
{
    private \Redis $redis;


    public function __construct($config, int $dbId = 0)
    {
    }

    /**
     * 得到实例化的对象.
     * 为每个数据库建立一个连接
     * 如果连接超时，将会重新建立一个连接
     * @param array $config
     * @param int $dbId
     * @return Xredis
     */
    public static function getInstance($config, int $dbId = 0): Xredis
    {
        return new self($config, $dbId);
    }

    private function __clone()
    {
    }


    /*****************hash表操作函数*******************/

    /**
     * 得到hash表中一个字段的值
     * @param string $key 缓存key
     * @param string  $field 字段
     * @return string|false
     */
    public function hGet($key, $field)
    {
        $out = null;
        \lbase\GetRedis::Master()->Pipeline((new \lbase\redis\KeyHash($key))->HGET($field, $out), name: "api_hGet");
        return $out ?? false;

        return $this->redis->hGet($key, $field);
    }

    /**
     * 为hash表设定一个字段的值
     * @param string $key 缓存key
     * @param string  $field 字段
     * @param string $value 值。
     * @return bool 
     */
    public function hSet($key, $field, $value)
    {
        $out = null;
        \lbase\GetRedis::Master()->Pipeline((new \lbase\redis\KeyHash($key))->HSET($field, $value, $out), name: "api_hSet");
        return $out ?? false;

        return $this->redis->hSet($key, $field, $value);
    }

    /**
     * 删除hash表中指定字段 ,支持批量删除
     * @param string $key 缓存key
     * @param string  $field 字段
     * @return int
     */
    public function hdel($key, $field)
    {
        $fieldArr = explode(',', $field);
        $fieldArr = array_map('trim', $fieldArr);
        $out = null;
        \lbase\GetRedis::Master()->Pipeline((new \lbase\redis\KeyHash($key))->HDELm($fieldArr, $out), name: "api_hdel");
        return $out ?? 0;


        $fieldArr = explode(',', $field);
        $delNum = 0;

        foreach ($fieldArr as $row) {
            $row = trim($row);
            $delNum += $this->redis->hDel($key, $row);
        }

        return $delNum;
    }

    /**
     * 为hash表多个字段设定值。
     * @param string $key
     * @param array $value
     * @return array|bool
     */
    public function hMset($key, $value)
    {
        if (!is_array($value)) {
            return false;
        }

        $err = \lbase\GetRedis::Master()->Pipeline((new \lbase\redis\KeyHash($key))->HMSET($value), name: "api_hMset");
        if ($err) {
            return false;
        }
        return true;


        return $this->redis->hMset($key, $value);
    }

    /**
     * 为hash表多个字段设定值。
     * @param string $key
     * @param array|string $value string以','号分隔字段
     * @return array|bool
     */
    public function hMget($key, $field)
    {
        if (!is_array($field)) {
            $field = explode(',', $field);
        }

        $out = null;
        \lbase\GetRedis::Master()->Pipeline((new \lbase\redis\KeyHash($key))->HMGET($field, $out), name: "api_hMget");
        return $out ?? false;


        return $this->redis->hMget($key, $field);
    }

    /**
     * 返回所有hash表的所有字段
     * @param string $key
     * @return array|bool
     */
    public function hKeys($key)
    {
        $out = null;
        \lbase\GetRedis::Master()->Pipeline((new \lbase\redis\KeyHash($key))->HKEYS($out), name: "api_hKeys");
        return $out ?? false;



        return $this->redis->hKeys($key);
    }


    /**
     * 返回所有hash表的字段值，为一个关联数组
     * @param string $key
     * @return array|bool
     */
    public function hGetAll($key)
    {
        $out = null;
        \lbase\GetRedis::Master()->Pipeline((new \lbase\redis\KeyHash($key))->HGETALL($out), name: "api_hGetAll");
        return $out ?? false;

        return $this->redis->hGetAll($key);
    }


    /**
     * 返回队列指定区间的元素
     * @param mixed $key
     * @param mixed $start
     * @param mixed $end
     */
    public function lRange($key, $start, $end)
    {
        $out = null;
        \lbase\GetRedis::Master()->Pipeline((new \lbase\redis\KeyList($key))->LRANGE($start, $end, $out), name: "api_lRange");
        return $out ?? false;


        return $this->redis->lrange($key, $start, $end);
    }

    /*************redis字符串操作命令*****************/

    /**
     * 设置一个key
     * @param mixed $key
     * @param mixed $value
     */
    public function set($key, $value)
    {
        $err = \lbase\GetRedis::Master()->Pipeline((new \lbase\redis\KeyString($key))->SET($value), name: "api_set");
        if ($err) {
            return false;
        }
        return true;

        return $this->redis->set($key, $value);
    }

    /**
     * 得到一个key
     * @param mixed $key
     */
    public function get($key)
    {
        $out = null;
        \lbase\GetRedis::Master()->Pipeline((new \lbase\redis\KeyString($key))->GET($out), name: "api_get");
        return $out ?? false;

        return $this->redis->get($key);
    }


    /*************redis　无序集合操作命令*****************/

    /**
     * 返回集合中所有元素
     * @param mixed $key
     */
    public function sMembers($key)
    {
        $out = null;
        \lbase\GetRedis::Master()->Pipeline((new \lbase\redis\KeySet($key))->SMEMBERS($out), name: "api_sMembers");
        return $out ?? false;

        return $this->redis->sMembers($key);
    }


    /**
     * 删除指定key
     * @param mixed $key
     */
    public function del($key)
    {
        $out = null;
        \lbase\GetRedis::Master()->Pipeline((new \lbase\redis\KeyBase($key))->DEL($out), name: "api_del");
        return $out ?? false;

        return $this->redis->del($key);
    }

}
