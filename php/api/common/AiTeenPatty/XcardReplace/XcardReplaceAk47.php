<?php
/**
 * @todo   TeenPatty ai ak47玩法替换癞子
 * <AUTHOR>
 * @deprecated
 */
namespace common\AiTeenPatty\XcardReplace;

use common\AiTeenPatty\AiTeenPattyPrDealCadWeight;
use common\AiTeenPatty\AiTeenPattyXCardReplace;
use common\Common;

class XcardReplaceAk47 extends AiTeenPattyXCardReplace
{

    //读取癞子概率配置表
    private $xcardReplaceRateConfig;

    //癞子替换配置xml路径
    private $configXmlPath = __DIR__ . '/ak47XCardRate.xml';

    //ak47都是癞子
    private $allXcardList = [];

    //换癞子的个数
    private $replaceNum = 0;

    public function __construct($cls)
    {
        parent::__construct($cls);
        $this->getXcardReplaceRateConfig();
        $this->allXcardList = get_all_xcard_by_cls(Common::GAME_TYPE_TEEN_PATTI_AK47);

    }

    //获取本次换牌癞子数量
    public function replaceXCard($dealCards, $pool)
    {
        $this->initVal();
        $this->dealCards = sort_teen_patty_cards_asc($dealCards);
        $this->notXcardDealCards = $this->dealCards;
        $this->overReplaceDealCards = $this->dealCards;
        $this->pool = $pool;

        // 先获取原始发牌的牌型和牌力值，下面会根据牌力值来获取更换癞子的权重
        $this->setGroupNameAndScore();

        $replaceIndexList = [];
        //如果发的牌中已经有了癞子 则不执行癞子替换概率
        foreach ($dealCards as $key => $card) {
            if (in_array($card, $this->allXcardList)) {
                $replaceIndexList[] = $key;
            }
        }

        //执行随机癞子
        if (!$replaceIndexList) {
            $this->replaceNum = $this->getReplaceNumByScore();
            if ($this->replaceNum > 0) {
                $this->getReplaceCarderIndexList();
            } else {
                return;
            }
        } else {
            // 如果发的牌中已经有了癞子，不执行癞子替换
            return;
            $this->replaceCarderIndexList = $replaceIndexList;
        }

        //查找剩余牌池中所有的癞子
        $this->xCardList = $this->getXcardListInPool();

        //执行替牌逻辑
        $this->replaceDo();
    }


    //豹子 只有ak47
    protected function replaceBaozi(): array
    {
        $replaceCarderIndex = [];
        for ($i = 0; $i < $this->replaceNum; $i++) {
            $replaceCarderIndex[] = random_int(0, 2);
        }
        return $replaceCarderIndex;
    }


    // 根据分值获取替换癞子的数量
    private function getReplaceNumByScore()
    {
        // 根据分值反查 TP_CARD_SCORE 得到索引，然后去 dealCardWeight.xml 里面二分查找得到发牌索引
        // 然后用发牌索引去查 ak47XCardRate.xml 得到个数数组，从里面随机选一个
        $obj = new AiTeenPattyPrDealCadWeight();
        $index = $obj->getIndexByCardScore($this->groupScore);
        if (!isset($this->xcardReplaceRateConfig[$index])) {
            return 0;
        } else {
            $xcardReplaceWeight = $this->xcardReplaceRateConfig[$index];
            unset($xcardReplaceWeight['groupName']);
            $xcardReplaceWeight = array_values($xcardReplaceWeight);
            return get_rand($xcardReplaceWeight);
        }
    }


    //获取牌池中所有的癞子
    private function getXcardListInPool(): array
    {
        return array_intersect($this->allXcardList, $this->pool);
    }


    //读取癞子替换概率配置表
    private function getXcardReplaceRateConfig()
    {
        static $result = null;
        if ($result === null) {
            $xmlObj = simplexml_load_file($this->configXmlPath);
            $result = json_decode(json_encode($xmlObj), 1)['row'];
        }

        $this->xcardReplaceRateConfig = $result;
    }
}
