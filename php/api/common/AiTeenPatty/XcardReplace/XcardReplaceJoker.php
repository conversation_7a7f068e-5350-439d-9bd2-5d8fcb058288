<?php
/**
 * @todo   TeenPatty ai joker玩法替换癞子
 * <AUTHOR>
 * @deprecated
 */
namespace common\AiTeenPatty\XcardReplace;

use common\Common;
use common\AiTeenPatty\AiTeenPattyXCardReplace;

class XcardReplaceJoker extends AiTeenPattyXCardReplace
{

    //癞子不占用牌池 癞子标识为大王 必然有一个癞子
    //判定是否要换牌
    public function replaceXCard($dealCards, $pool)
    {
        $this->initVal();
        $this->dealCards = sort_teen_patty_cards_asc($dealCards);
        $this->notXcardDealCards = $this->dealCards;
        $this->overReplaceDealCards = $this->dealCards;
        $this->pool = $pool;
        // 意思好像是换个癞子进去后要基本保持旧的牌型基本不变至少不降低
        $this->getReplaceCarderIndexList();
        $this->setAllXcardList();
        $this->replaceDo();
    }

    //设置所有癞子牌
    private function setAllXcardList()
    {
        $this->xCardList = get_all_xcard_by_cls(Common::GAME_TYPE_TEEN_PATTI_JOKER);
    }

}
