<?php
/**
 * @todo teenPatty发牌接口
 */

namespace common\AiTeenPatty;

use llogic\common\Struct;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;

class AiTeenPattyPrControlDealCardV2
{
    private $branchName = "未指定";
    // 记录特殊的用户情况，用于修饰流程分支
    private $uidBranchMap = [];

    public $capitalNames = [];

    //----------------------------------------
    //AI 性格属性生存周期 秒
    private $aiDispositionExpiresTime = 86400;
    //牌池
    private $pool;
    //房间号
    private $roomId;
    //房间类型
    private $roomType;
    //解析之后的房间信息
    private $roomInfo;
    //玩家列表
    private $playerList;
    //----------------------------------------

    //----------------------------------------
    //TP pr配置
    private $prConfig = [];
    //-----------------------------------------

    //-----------------------------------------
    //获取大盘控结果
    public $isChangeCard = false;
    //最终换牌概率
    public $changeRate = 0;

    private $rateMaxCardKill = 0;
    private $isMaxCardKill = false;
    //-----------------------------------------

    //-----------------------------------------
    //玩家拿牌顺序
    private $sortPlayerList = [];
    //AI顺序
    private $sortAiList = [];

    //控制类型玩家列表
    private $upPlayerPrList = [];
    //正常玩家类型
    private $normalPlayerPrList = [];
    //抑制玩家类型列表
    private $downPlayerPrList = [];

    //所有玩家类型列表
    private $allPlayerPrList = [];
    //pr低于0.3的玩家列表
    private $slowerPlayerPrList = [];
    //pr信息列表
    private $prInfoList = [];
    //透杀玩家列表
    private $discloseKillerPlayerList = [];
    //-----------------------------------------


    //----------------------------------------
    //用户发到的手牌数组
    private $dealCardsList = [];
    //排序之后的牌组
    private $dealCardsListSortByScore = [];
    //----------------------------------------

    //----------------------------------------
    //本场次玩家发牌类型  down  up  normal
    private $dealCardPlanType = 'normal';

    // 抑制模式下第一大牌和第二大牌，让第二大牌肯定小于第一大牌，这个没问题
    private $downDealCardConfig = [
        2 => [1 => ['min' => 40004, 'max' => 960000000000], 2 => ['min' => 3282, 'max' => 0]],
        3 => [1 => ['min' => 200003, 'max' => 960000000000], 2 => ['min' => 100002, 'max' => 0]],
        4 => [1 => ['min' => 260003, 'max' => 960000000000], 2 => ['min' => 180002, 'max' => 0]],
        5 => [1 => ['min' => 84200000, 'max' => 960000000000], 2 => ['min' => 240002, 'max' => 0]],
    ];

    // 扶植状态下覆盖第一大牌，可能把第一大牌拉低一点
    // bakhmut: 第一大牌给需要被扶植且 pr 最低的真人，扶植！
    // 所以这个扶植就出现偏差了，被扶植的人可能拿的不是最大的牌，这样可能扶植失败呀
    private $upDealCardConfig = [
        2 => [1 => ['min' => 40003, 'max' => 960000000000]],
        3 => [1 => ['min' => 240002, 'max' => 960000000000]],
        4 => [1 => ['min' => 54200000, 'max' => 960000000000]],
        5 => [1 => ['min' => 324300000, 'max' => 960000000000]],
    ];

    // 所有玩家的初始顺位
    private $estimatedList = [];
    //玩家手牌最大的分数 有wscore优先使用wscore
    private $playerMaxScore = 0;
    //玩家是否拿到了三条或者同花顺
    private $playerHasSanTiaoOrTongHuaShun = false;
    //玩家是否拿到了同花或者顺子
    private $playerHasTonghuOrShunZi = false;
    //分配之后的信息
    private $userDealCardsList = [];
    //本次请求底注
    private $base = 0;
    //本次请求货币类型
    private $currency = 0;
    //本次请求玩法
    private $cls = 0;
    //本场次性格分配情况
    private $dispositionConfig = [];
    //tp牌型配置
    private $initAvgSequence;
    //有新充buff玩家参与的局不执行大盘控
    // bakhmut: 这个功能没实现啊！没人写这个字段，恒定 false
    private $notExecuteSysKiller = false;
    //玩家是否tp透杀状态
    private $playerIsDiscloseKiller = false;
    //大盘今日实际盈利金额
    // bakhmut: 这是个布尔值，不是金额，为真表示大盘有缺口
    private $sysRealWinAmount = 0;

    public function __construct($playerList, $roomType, $roomId)
    {
        $this->roomId = $roomId;
        $this->roomType = $roomType;
        $this->roomInfo = parse_room_type($roomType);
        $this->base = $this->roomInfo['base'];
        $this->currency = $this->roomInfo['currency'];
        $this->cls = $this->roomInfo['cls'];
        $this->playerList = $playerList;

        // TeenPatty 只发一次牌，只使用一副牌，且没有大小王
        $this->pool = init_card_pool(1, false);

        // 人均胜率对应顺位
        $obj = new AiTeenPattyPrCardWinRate($this->cls, count($this->playerList));
        $this->initAvgSequence = $obj->getSequenceByWinRate($obj->getAvgWinRateByPlayerCnt());

        // 获取 tp 的 pr 配置
        $this->getPrConfig();

        // 获取 ai 性格动态配置参数
        $this->getDispositionConfig();

        // 设置玩家的 pr 信息
        $this->setPlayerControlInfo();

        // 获取大盘控结果
        $this->isChangeCard = $this->getChangeCardRes();

        // 根据玩家状态重新生成发牌顺序
        $this->sortPlayerList();

        // 根据人数生成发牌
        // 按牌力值倒序排列 dealCardsListSortByScore 
        $this->getDealCardList();

        // 执行发牌处理
        // 执行极大杀判定
        // 分配牌组到用户身上
        // 处理心里门槛系数
        $this->dealCardRun();
    }




    //获取发到的牌
    public function getDealCards()
    {
        // 各家发相同的牌测试用
        // 如果相同牌型相同点数 主动开牌的一方输
        if (0) {
            $tmp = [];
            $last = null;
            foreach ($this->userDealCardsList as $key => $val) {
                if ($last === null) {
                    $last = $val;
                }
                $tmp[$key] = $last;
            }
            return $tmp;
        }


        $cardInfoList = '';
        foreach ($this->userDealCardsList as $key => $val) {
            $onCardInfo = '';
            foreach ($val['ucards'][0]['cards'] as $v) {
                $info = get_card_info($v);
                $onCardInfo .= $info['text_type'] . $info['num'] . ' ';
            }
            $cardInfoList .= $val['utype'] . '=' . $val['uid'] . '[' . $onCardInfo . ']---|---';
        }
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '用户拿到的牌：' . $cardInfoList);
        return $this->userDealCardsList;
    }

    //#AI性格配置---------------------------------------------------------------------------------
    //获取本场次的性格分配情况
    private function getDispositionConfig()
    {
        $dispositionConfig = RedisOpt::getTpAiDispositionRateConfigOne($this->currency, $this->roomType);
        $this->dispositionConfig = $dispositionConfig;
    }

    //获取随机性格
    private function getRandDispositionConfig()
    {
        $randRate = [];
        if (empty($this->dispositionConfig)) {
            $disposition = Common::getTpAiDispositionList();
            foreach ($disposition as $k => $v) {
                $randRate[$k] = 10000;
            }
        } else {
            foreach ($this->dispositionConfig as $k => $v) {
                $randRate[$k] = $v * 10000;
            }
        }
        return get_rand($randRate);
    }
    //#AI性格配置---------------------------------------------------------------------------------------



    //#AI用户pr处理---------------------------------------------------------------------------------
    //获取TP的PR配置
    private function getPrConfig()
    {
        $prConfig = RedisOpt::getTpPrConfigList();
        if ($prConfig) {
            $this->prConfig = $prConfig;
        }
    }

    //根据用户id获取用户是否有过充值
    private function getPlayerIsRecharge(int $playerIndex): bool
    {
        $rechargeCnt = RedisOpt::getPlayerRechargeCnt($playerIndex);
        if ($rechargeCnt > 0) {
            return true;
        } else {
            return false;
        }
    }

    //根据用户的pr配置获取用户的控制状态
    private function getPlayerControlType($playerIndex, array $prInfo, $isRechargeBuff): string
    {
        //抑制概率 得出抑制结果
        if ($prInfo['pr'] >= $prInfo['prConfig']['u_begin']) {
            //需要抑制
            // abs((pr - u_begin) / (u_limit - u_begin)) * 0.333
            $downRate = (0.333 * ($prInfo['pr'] - $prInfo['prConfig']['u_begin'])) / ($prInfo['prConfig']['u_limit'] - $prInfo['prConfig']['u_begin']);
            $downRate = abs($downRate);
            $downRate = round($downRate, 3);

            // 限制概率最大值为 0.333
            $downRate = min(0.333, $downRate);

            // 已知概率，判断事件是否发送
            $res = (rand() / (getrandmax() + 1)) < $downRate;

            \lbase\Log::info('TP.getPlayerControlType.抑制检测', [
                "room_id" => $this->roomId,
                "uid" => $playerIndex,
                "prInfo" => $prInfo,
                "downRate" => $downRate,
                "res" => $res,
            ]);

            if ($res) {
                return Common::TP_PLAYER_CONTROL_TYPE_DOWN;
            }
        }

        //有tp透杀的用户  只有  抑制和正常2中状态
        $isTpDiscloseKiller = RedisOpt::getUserTagIsTpDiscloseKiller($playerIndex, $this->cls);
        if ($isTpDiscloseKiller) {
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, $playerIndex . '$isTpDiscloseKiller：' . json_encode($isTpDiscloseKiller) . '没有抑制 走正常状态 没有扶植');
            return Common::TP_PLAYER_CONTROL_TYPE_NORMAL;
        }

        $experimental = $playerIndex % 2 == 1;
        $experimental = 1;

        $a = \llogic\user\UserTagRepo::SystemReadUserBoolTags($playerIndex, [
            \llogic\user\UserTagName::USER_TAG_WOOL_WHITE,
            \llogic\user\UserTagName::USER_TAG_SUSPECTED_WOOL_WHITE,
            \llogic\user\UserTagName::USER_TAG_SAME_DEVICE_WOOL_WHITE,
            \llogic\user\UserTagName::USER_TAG_UNKNOWN_WOOL_WHITE,

            \llogic\user\UserTagName::USER_TAG_WOOL_PARTY,
            \llogic\user\UserTagName::USER_TAG_SUSPECTED_WOOL_PARTY,
            \llogic\user\UserTagName::USER_TAG_SAME_DEVICE_WOOL,
            \llogic\user\UserTagName::USER_TAG_UNKNOWN_WOOL,

            \llogic\user\UserTagName::USER_TAG_PRE_WITHDRAW_CONTROL,
            \llogic\user\UserTagName::USER_TAG_TP_HIGH_KILL,
            \llogic\user\UserTagName::USER_TAG_TP_AK47_HIGH_KILL,
            \llogic\user\UserTagName::USER_TAG_TP_JOKER_HIGH_KILL,
            \llogic\user\UserTagName::USER_TAG_RUMMY_HIGH_KILL,
            \llogic\user\UserTagName::USER_TAG_HIGH_WITHDRAW_CONTROL,
            \llogic\user\UserTagName::USER_TAG_HIGH_BASE_CONTROL,
        ]);
        // php -r "echo '0' || '0';" 
        $isWoolParty = boolval($a[\llogic\user\UserTagName::USER_TAG_WOOL_PARTY] && !$a[\llogic\user\UserTagName::USER_TAG_WOOL_WHITE]);
        $isWoolParty = $isWoolParty || boolval($a[\llogic\user\UserTagName::USER_TAG_SAME_DEVICE_WOOL] && !$a[\llogic\user\UserTagName::USER_TAG_SAME_DEVICE_WOOL_WHITE]);
        //$isWoolParty = $isWoolParty || boolval($a[\llogic\user\UserTagName::USER_TAG_UNKNOWN_WOOL] && !$a[\llogic\user\UserTagName::USER_TAG_UNKNOWN_WOOL_WHITE]);
        $isSuspectedWoolParty = boolval($a[\llogic\user\UserTagName::USER_TAG_SUSPECTED_WOOL_PARTY] && !$a[\llogic\user\UserTagName::USER_TAG_SUSPECTED_WOOL_WHITE]);
        $isPreWithdrawControl = boolval($a[\llogic\user\UserTagName::USER_TAG_PRE_WITHDRAW_CONTROL]);
        $isTPHighKill = boolval($a[\llogic\user\UserTagName::USER_TAG_TP_HIGH_KILL]);
        $isTPAK47HighKill = boolval($a[\llogic\user\UserTagName::USER_TAG_TP_AK47_HIGH_KILL]);
        $isTPJokerHighKill = boolval($a[\llogic\user\UserTagName::USER_TAG_TP_JOKER_HIGH_KILL]);
        $isRummyHighKill = boolval($a[\llogic\user\UserTagName::USER_TAG_RUMMY_HIGH_KILL]);
        $isHighWithdrawControl = boolval($a[\llogic\user\UserTagName::USER_TAG_HIGH_WITHDRAW_CONTROL]);
        $isHighBaseControl = boolval($a[\llogic\user\UserTagName::USER_TAG_HIGH_BASE_CONTROL]);

        // 羊毛党高概率抑制
        if ($isWoolParty && $experimental) {
            $woolKillRate = 0.0;

            if ((rand() / (getrandmax() + 1)) < $woolKillRate) {
                return Common::TP_PLAYER_CONTROL_TYPE_DOWN;
            }

            // 不扶植
            return Common::TP_PLAYER_CONTROL_TYPE_NORMAL;
        }

        // 疑似羊毛低概率抑制
        if ($isSuspectedWoolParty && $experimental) {
            $woolKillRate = 0.33;

            if ((rand() / (getrandmax() + 1)) < $woolKillRate) {
                return Common::TP_PLAYER_CONTROL_TYPE_DOWN;
            }

            // 不扶植
            return Common::TP_PLAYER_CONTROL_TYPE_NORMAL;
        }

        // 预提现控制低概率抑制
        if ($isPreWithdrawControl && $experimental) {
            $woolKillRate = 0.33;

            if ((rand() / (getrandmax() + 1)) < $woolKillRate) {
                return Common::TP_PLAYER_CONTROL_TYPE_DOWN;
            }

            // 不扶植
            return Common::TP_PLAYER_CONTROL_TYPE_NORMAL;
        }

        // TP高频杀
        if ($isTPHighKill && $experimental) {
            $woolKillRate = 0.33;

            if ((rand() / (getrandmax() + 1)) < $woolKillRate) {
                return Common::TP_PLAYER_CONTROL_TYPE_DOWN;
            }

            // 不扶植
            return Common::TP_PLAYER_CONTROL_TYPE_NORMAL;
        }

        // TP AK47高频杀
        if ($isTPAK47HighKill && $experimental) {
            $woolKillRate = 0.33;

            if ((rand() / (getrandmax() + 1)) < $woolKillRate) {
                return Common::TP_PLAYER_CONTROL_TYPE_DOWN;
            }

            // 不扶植
            return Common::TP_PLAYER_CONTROL_TYPE_NORMAL;
        }

        // TP Joker高频杀
        if ($isTPJokerHighKill && $experimental) {
            $woolKillRate = 0.33;

            if ((rand() / (getrandmax() + 1)) < $woolKillRate) {
                return Common::TP_PLAYER_CONTROL_TYPE_DOWN;
            }

            // 不扶植
            return Common::TP_PLAYER_CONTROL_TYPE_NORMAL;
        }

        // 高W%控
        if ($isHighWithdrawControl && $experimental) {
            $woolKillRate = 0.33;

            if ((rand() / (getrandmax() + 1)) < $woolKillRate) {
                return Common::TP_PLAYER_CONTROL_TYPE_DOWN;
            }

            // 不扶植
            return Common::TP_PLAYER_CONTROL_TYPE_NORMAL;
        }

        // 高分场控
        if ($isHighBaseControl && $experimental) {
            $woolKillRate = 0.0;

            if ((rand() / (getrandmax() + 1)) < $woolKillRate) {
                return Common::TP_PLAYER_CONTROL_TYPE_DOWN;
            }

            // 不扶植
            return Common::TP_PLAYER_CONTROL_TYPE_NORMAL;
        }

        //新充buff
        if ($prInfo['pr'] < $prInfo['prConfig']['u_begin'] && $isRechargeBuff) {
            /*
            * a、玩家((小计输钱流水-小计赢钱流水)-max(总计赢钱流水-总计输钱流水+小计输钱流水-小计赢钱流水,0))/新充buff额度>0.2时，按照以下概率进行扶植
            扶植概率=max（3%，当时TP PR计算出的实际扶植率）
            * */
            $upBuffRes = Common::getIsUpBuff(false, $this->isChangeCard, $this->changeRate, Common::USER_TAG_CHARGE_BUFF, Common::ROOM_UNION_TYPE_BUFF, $playerIndex, 'notBankruptcyProtect', '', $this->roomInfo);
            if (isset($upBuffRes['isUpBuff']) && $upBuffRes['isUpBuff']) {
                //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, $playerIndex . '((小计输钱流水-小计赢钱流水)-max(总计赢钱流水-总计输钱流水+小计输钱流水-小计赢钱流水,0))/新充buff额度>0.2 符合条件');
                //需要扶植
                // php -r "echo count(['a'=>null]);"
                $upType = null;
                if ($prInfo['pr'] <= $prInfo['prConfig']['d_begin']) {
                    $upRate = (0.333 * ($prInfo['prConfig']['d_begin'] - $prInfo['pr'])) / ($prInfo['prConfig']['d_begin'] - $prInfo['prConfig']['d_limit']);
                    $upRate = abs($upRate);
                    $upRate = min(0.333, $upRate);
                } else {
                    $upRate = 0;
                }

                $minUpRateForRechargeBuff = 0.1;
                if ($isRechargeBuff && $upRate < $minUpRateForRechargeBuff) {
                    $upType = '新充扶植';
                    //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, $playerIndex . '在新充扶植状态 有充值buff在 原始概率' . $upRate . '最低扶植概率' . $minUpRateForRechargeBuff);
                    $upRate = $minUpRateForRechargeBuff;
                }
                $res = (rand() / (getrandmax() + 1)) < $upRate;
                //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, $playerIndex . '在新充扶植状态');
                //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, $playerIndex . '新充扶植随机概率：' . $upRate);
                //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, $playerIndex . '新充扶植随机结果：' . json_encode($res));
                if ($res) {
                    $this->uidBranchMap[$playerIndex] = $upType;
                    return Common::TP_PLAYER_CONTROL_TYPE_UP;
                } else {
                    return Common::TP_PLAYER_CONTROL_TYPE_NORMAL;
                }
            } else {
                //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, $playerIndex . '((小计输钱流水-小计赢钱流水)-max(总计赢钱流水-总计输钱流水+小计输钱流水-小计赢钱流水,0))/新充buff额度>0.2 not符合条件');
            }
        }

        //扶植概率 得出扶植结果
        if ($prInfo['pr'] <= $prInfo['prConfig']['d_begin']) {
            //需要扶植
            // abs((d_begin - pr) / (d_limit - d_begin)) * 0.333
            $upRate = (0.333 * ($prInfo['prConfig']['d_begin'] - $prInfo['pr'])) / ($prInfo['prConfig']['d_begin'] - $prInfo['prConfig']['d_limit']);
            $upRate = abs($upRate);

            // 限制概率最大值为 0.333
            $upRate = min(0.333, $upRate);

            $res = (rand() / (getrandmax() + 1)) < $upRate;

            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, $playerIndex . '在扶植状态');
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, $playerIndex . '扶植随机概率：' . $upRate);
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, $playerIndex . '扶植随机结果：' . json_encode($res));
            if ($res) {
                return Common::TP_PLAYER_CONTROL_TYPE_UP;
            }
        }

        //扶植和抑制没有结果 则按照正常计算
        return Common::TP_PLAYER_CONTROL_TYPE_NORMAL;
    }



    //设置用户的发牌控制
    private function setPlayerControlInfo()
    {

        foreach ($this->playerList as $val) {
            //玩家初始顺位列表
            $this->estimatedList[$val['uid']] = $this->initAvgSequence;
            //只有真人执行以下pr逻辑
            if ($val['utype'] != Common::PLAYER_TYPE_PEOPLE) {
                $this->sortAiList[] = $val['uid'];
                continue;
            }
            //获取用户是否有充值buff
            $isRechargeBuff = RedisOpt::getUserTagIsRechargeBuffer($val['uid']);

            // bakhmut: 这段代码拿到 prInfo
            //获取玩家是否充值
            $isRecharge = $this->getPlayerIsRecharge((int) $val['uid']);
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, $val['uid'] . '玩家是否充值：' . json_encode($isRecharge));
            $prObj = new \common\AiCommon\AiPlayerPr();
            $prObj->setPrConfig($this->prConfig);
            if ($isRecharge) {
                //获取单个tp的pr
                $prInfo = $prObj->getPlayerPrByCls((int) $val['uid'], $this->cls, (int) $val['user_wallet']);
            } else {
                $prInfo = $prObj->getPlayerAllPr((int) $val['uid'], $this->cls, (int) $val['user_wallet']);
            }
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, $val['uid'] . '玩家pr信息：' . json_encode($prInfo));

            $controlType = $this->getPlayerControlType($val['uid'], $prInfo, $isRechargeBuff);
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, $val['uid'] . '玩家pr控制类型：' . json_encode($controlType));
            $this->prInfoList[$val['uid']] = $prInfo;

            if ($controlType == Common::TP_PLAYER_CONTROL_TYPE_NORMAL) {
                //正常玩家类型
                $this->normalPlayerPrList[$val['uid']] = $prInfo['pr'];
            }
            if ($controlType == Common::TP_PLAYER_CONTROL_TYPE_DOWN) {
                //抑制玩家类型
                $this->downPlayerPrList[$val['uid']] = $prInfo['pr'];
            }
            if ($controlType == Common::TP_PLAYER_CONTROL_TYPE_UP) {
                //扶植玩家类型
                $this->upPlayerPrList[$val['uid']] = $prInfo['pr'];
            }
            if ($prInfo['pr'] <= 0.3) {
                //pr低于0.3的玩家列表
                $this->slowerPlayerPrList[$val['uid']] = $prInfo['pr'];
            }
            $this->allPlayerPrList[$val['uid']] = $prInfo['pr'];
        }
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '扶植玩家列表：' . json_encode($this->upPlayerPrList));
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '抑制玩家列表：' . json_encode($this->downPlayerPrList));
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '正常玩家列表：' . json_encode($this->normalPlayerPrList));
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '低于0.3玩家列表：' . json_encode($this->slowerPlayerPrList));
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, 'AI列表：' . json_encode($this->sortAiList));
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '所有真人玩家列表：' . json_encode($this->allPlayerPrList));

    }

    //根据用户的控制状态和pr 调整用户的发牌顺序
    private function sortPlayerList()
    {
        if ($this->isChangeCard && !$this->notExecuteSysKiller) {
            $this->dealCardPlanType = 'down';
            $this->sortPlayerListBySysKiller();
        } else {
            $this->sortPlayerListByControlType();
        }
    }

    //执行大盘控的时候用户发牌顺序
    private function sortPlayerListBySysKiller()
    {
        //1、 把第二大给场上pr最高的玩家
        //2、 把第一大优先给pr低于0.3的玩家 如果不存在这样的玩家，则随机给Ai
        //3、 其他选项则随机

        // bakhmut: 如果没有真人则随机顺序
        if (!$this->allPlayerPrList) {
            //如果没有真人玩家
            shuffle($this->sortAiList);
            $this->sortPlayerList = $this->sortAiList;
            $this->branchName = "大盘控但没有真人";
            return;
        }

        //有真人玩家
        // bakhmut: 第二大牌给 pr 最高的真人
        arsort($this->allPlayerPrList);
        $secondPlayerIndex = array_key_first($this->allPlayerPrList);
        unset($this->allPlayerPrList[$secondPlayerIndex]);

        // bakhmut: 第一大牌给谁？
        if ($this->slowerPlayerPrList && 0) {
            //有pr低于3.0的玩家
            arsort($this->slowerPlayerPrList);
            $firstPlayerIndex = array_key_last($this->slowerPlayerPrList);
            unset($this->allPlayerPrList[$firstPlayerIndex]);

            if ($this->sortAiList) {
                $this->branchName = "大盘控但扶植有机器人";
            } else {
                $this->branchName = "大盘控但全真人";
            }
        } elseif ($this->sortAiList) {
            //没有低于3.0的玩家  有ai
            shuffle($this->sortAiList);
            $firstPlayerIndex = array_shift($this->sortAiList);

            $this->branchName = "大盘控成功";
        } else {
            //没有低于3.0的玩家  也没有ai
            // bakhmut: 第一大牌给 pr 最低的真人
            $firstPlayerIndex = array_key_last($this->allPlayerPrList);
            unset($this->allPlayerPrList[$secondPlayerIndex]);

            $this->branchName = "大盘控但全真人";
        }

        //如果还有剩余的真是玩家  则获取所有的id
        // bakhmut: 确定两个关键角色后，剩下的随机打乱
        $allPlayerIndexList = [];
        if ($this->allPlayerPrList) {
            $allPlayerIndexList = array_keys($this->allPlayerPrList);
        }
        $sortPlayerList = array_merge($allPlayerIndexList, $this->sortAiList);
        shuffle($sortPlayerList);

        // bakhmut: 组装最终的排位
        array_unshift($sortPlayerList, $secondPlayerIndex);
        array_unshift($sortPlayerList, $firstPlayerIndex);
        $this->sortPlayerList = array_unique($sortPlayerList);
    }

    //非大盘控的时候用户发牌顺序
    private function sortPlayerListByControlType()
    {
        if (!empty($this->downPlayerPrList)) {
            //有需要抑制的玩家
            $this->dealCardPlanType = 'down';
            $this->sortPlayerListByControlTypeDown();
        } elseif (!empty($this->upPlayerPrList)) {
            //有需要扶植的玩家
            $this->dealCardPlanType = 'up';
            $this->sortPlayerListByControlTypeUp();
        } else {
            //没有抑制 也没有需要扶植的玩家
            $this->sortPlayerListByControlTypeNormal();
        }
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '玩家控牌类型：' . json_encode($this->dealCardPlanType));
    }

    //非大盘控的时候 抑制发牌用户顺序
    //1、如果存在需要抑制的玩家 把第二大给场上pr最高的需要抑制的玩家
    //2、第一大优先给pr最低的需要扶植的玩家 如果不存在这样的玩家 则随机给Ai
    //3、抑制生效时 第一大和第二大牌 不得低于一下牌型
    //  ------------------------------------------------------
    //  |       人数      |    第一大牌      |    第二大牌      |
    //  -------------------------------------------------------
    //  |       2人      |       对子       |    AX*  - 第一大 |
    //  -------------------------------------------------------
    //  |       3人      |       对Q        |    对6 - 第一大  |
    //  -------------------------------------------------------
    //  |       4人      |       同花       |    对J - 第一大   |
    //  -------------------------------------------------------
    //  |       5人      |     A以上同花     |    对A -  第一大  |
    //  -------------------------------------------------------
    private function sortPlayerListByControlTypeDown()
    {
        //第二大的牌
        // bakhmut: 第二大牌给 pr 最高的真人，抑制！
        arsort($this->downPlayerPrList);
        $secondPlayerIndex = array_key_first($this->downPlayerPrList);
        unset($this->allPlayerPrList[$secondPlayerIndex]);

        //第一大的牌
        if ($this->upPlayerPrList) {
            // bakhmut: (1) 需要被扶植且 pr 最低的真人
            arsort($this->upPlayerPrList);
            $firstPlayerIndex = array_key_last($this->upPlayerPrList);
            unset($this->allPlayerPrList[$firstPlayerIndex]);

            if ($this->sortAiList) {
                $this->branchName = "抑制局但扶植有机器人";
            } else {
                $this->branchName = "抑制局但扶植全真人";
            }
            $this->branchName .= $this->uidBranchMap[$firstPlayerIndex] ?? '';
        } elseif ($this->sortAiList) {
            //没有需要扶植的用户
            // bakhmut: (2) 某个机器人
            shuffle($this->sortAiList);
            $firstPlayerIndex = array_shift($this->sortAiList);

            $this->branchName = "抑制局成功";
        } else {
            //没有需要扶植的玩家  也没有ai
            // bakhmut: (3) pr 最低的真人
            $firstPlayerIndex = array_key_last($this->allPlayerPrList);
            unset($this->allPlayerPrList[$firstPlayerIndex]);

            $this->branchName = "抑制局但全真人";
        }

        //如果还有剩余的真是玩家  则获取所有的id
        // bakhmut: 确定两个关键角色后，剩下的随机打乱
        $allPlayerIndexList = [];
        if ($this->allPlayerPrList) {
            $allPlayerIndexList = array_keys($this->allPlayerPrList);
        }
        $sortPlayerList = array_merge($allPlayerIndexList, $this->sortAiList);
        shuffle($sortPlayerList);

        // bakhmut: 组装最终的排位
        array_unshift($sortPlayerList, $secondPlayerIndex);
        array_unshift($sortPlayerList, $firstPlayerIndex);
        $this->sortPlayerList = array_unique($sortPlayerList);
    }

    //非大盘控的时候  扶植发牌用户顺序
    //1、如果存在需要扶植的玩家，把第一大牌给场上 pr最小的需要扶植的玩家
    //2、其余的玩家执行随机
    //3、扶植生效时 第一大的玩家的牌不得低于一下牌型
    //  ------------------------------------
    //  |       人数      |    第一大牌      |
    //  ------------------------------------
    //  |       2人      |       对子       |
    //  ------------------------------------
    //  |       3人      |       对Q        |
    //  ------------------------------------
    //  |       4人      |       同花       |
    //  ------------------------------------
    //  |       5人      |     A以上同花     |
    //  ------------------------------------
    private function sortPlayerListByControlTypeUp()
    {
        //第二大的牌
        // bakhmut: 第一大牌给需要被扶植且 pr 最低的真人，扶植！
        arsort($this->upPlayerPrList);
        $firstPlayerIndex = array_key_first($this->upPlayerPrList);
        unset($this->allPlayerPrList[$firstPlayerIndex]);

        //如果还有剩余的真是玩家  则获取所有的id
        // bakhmut: 确定这个关键角色后，剩下的随机打乱
        $allPlayerIndexList = [];
        if ($this->allPlayerPrList) {
            $allPlayerIndexList = array_keys($this->allPlayerPrList);
        }
        $sortPlayerList = array_merge($allPlayerIndexList, $this->sortAiList);
        shuffle($sortPlayerList);

        // bakhmut: 组装最终的排位
        array_unshift($sortPlayerList, $firstPlayerIndex);
        $this->sortPlayerList = array_unique($sortPlayerList);

        if ($this->sortAiList) {
            $this->branchName = "扶植局有机器人";
        } else {
            $this->branchName = "扶植局全真人";
        }
        $this->branchName .= $this->uidBranchMap[$firstPlayerIndex] ?? '';
    }


    //没有需要大盘控  扶植 抑制的玩家   则随机发牌
    private function sortPlayerListByControlTypeNormal()
    {
        $allPlayerIndexList = [];
        if ($this->allPlayerPrList) {
            $allPlayerIndexList = array_keys($this->allPlayerPrList);
        }
        $sortPlayerList = array_merge($allPlayerIndexList, $this->sortAiList);
        shuffle($sortPlayerList);
        $this->sortPlayerList = array_unique($sortPlayerList);

        if ($this->sortAiList) {
            $this->branchName = "随机局有机器人";
        } else {
            $this->branchName = "随机局全真人";
        }
    }
    //#AI用户pr处理---------------------------------------------------------------------------------



    //#按照基础发牌系数 执行随机发牌---------------------------------------------------
    //获取发牌 以及 牌的分数
    private function getUserDealCardsList()
    {
        $cnt = count($this->playerList);
        for ($i = 0; $i < $cnt; $i++) {
            //实例化发牌类
            $obj = new AiTeenPattyDealCardByGroupName($this->pool);
            $dealCards = $obj->getDealCards(Common::ZJ_GROUP_NAME_WEIGHT, $this->cls, Common::USER_TAG_NORMAL_BUFF);
            $this->pool = $obj->getCardPool();
            $this->dealCardsList[$i] = $dealCards;
        }
    }

    /**
     * 按照分数排序
     * @return void
     */
    public function sortDealCardsListByScore()
    {
        $sortList = [];
        /*
            jingzhao：代码走到这里时，如果是ak47 / joker 这种衍生玩法，对应的癞子牌已经替换好了
                      接下来会拿替换完癞子的牌重新生成一下牌组和牌力值（癞子会替换牌力值最高的牌型）
                      $this->dealCardsList就是替换完癞子以后的新手牌
                      调用完AiTeenPattyHandCardsScore这个类，就能拿到新手牌的的新牌组和牌力值
        */
        foreach ($this->dealCardsList as $key => $val) {

            if (0x01) {
                $sortList[$key]['cards'] = $val;
                \llogic\game\tpcard\TpScoreAlgo::TpApiSetupCardScoreEtc($this->cls, $sortList[$key], $sortList[$key]['cards'], 'cards_score');
            } else {
                $AiTeenPattyHandCardsScoreObj = new AiTeenPattyHandCardsScore($val[0], $val[1], $val[2], $this->cls);
                $sortList[$key]['cards'] = $val;
                $sortList[$key]['pcards'] = \llogic\game\tpcard\TpCardAlgo::PrettyFormatCards($sortList[$key]['cards']);
                //替换完癞子的手牌
                $sortList[$key]['cards_score'] = $AiTeenPattyHandCardsScoreObj->getHandCardsScore();
                $sortList[$key]['group_name'] = $AiTeenPattyHandCardsScoreObj->getGroupName();
            }

            $scoreList[] = $sortList[$key]['wscore'] ?? $sortList[$key]['cards_score'];
        }
        //按照分数进行 降序排序
        array_multisort($scoreList, SORT_DESC, $sortList);
        $this->dealCardsListSortByScore = $sortList;

        //据发牌控制类型执行替牌逻辑
        //jingzhao: 下面会判断 是否需要扶(up) / 是否需要抑制(down)
        $playerCnt = count($this->playerList);
        if ($this->dealCardPlanType == 'up') {
            // 对 TPJoker TPAK47 试验不做这个处理看看效果
            if (!in_array($this->cls, [Struct::GAME_TYPE_TEEN_PATTI_JOKER, Struct::GAME_TYPE_TEEN_PATTI_AK47])) {
                //覆盖第一大的牌
                $scoreMin = $this->upDealCardConfig[$playerCnt][1]['min'];
                $scoreMax = $this->upDealCardConfig[$playerCnt][1]['max'];
                $sortList[0] = $this->getDealCardByScoreRegion($scoreMin, $scoreMax, true);
            }
        }
        if ($this->dealCardPlanType == 'down') {
            if (!in_array($this->cls, [Struct::GAME_TYPE_TEEN_PATTI_JOKER, Struct::GAME_TYPE_TEEN_PATTI_AK47])) {
                //覆盖第一大的牌
                $scoreMin = $this->downDealCardConfig[$playerCnt][1]['min'];
                $scoreMax = $this->downDealCardConfig[$playerCnt][1]['max'];
                $sortList[0] = $this->getDealCardByScoreRegion($scoreMin, $scoreMax, true);
                //覆盖第二大的牌
                $scoreMin = $this->downDealCardConfig[$playerCnt][2]['min'];
                $scoreMax = $sortList[0]['cards_score'];
                $sortList[1] = $this->getDealCardByScoreRegion($scoreMin, $scoreMax, false);
            }
        }

        // 最终排序的手牌
        $this->dealCardsListSortByScore = $sortList;
    }


    //根据区间值获取要发的牌
    private function getDealCardByScoreRegion($minScore, $maxScore, $isInclude)
    {
        $obj = new AiTeenPattyDealCardByGroupName($this->pool);
        $obj->minScore = $minScore;
        $obj->maxScore = $maxScore;
        $obj->isInclude = boolval($isInclude);
        $cardsInfo['cards'] = $obj->getDealCards(Common::ZJ_GROUP_NAME_SCORE_REGION, $this->cls);
        $this->pool = $obj->getCardPool();

        if (0x01) {
            \llogic\game\tpcard\TpScoreAlgo::TpApiSetupCardScoreEtc($this->cls, $cardsInfo, $cardsInfo['cards'], 'cards_score');

            // 看看发的牌是否符合分数区间
            if ($cardsInfo['cards_score'] < $minScore || $cardsInfo['cards_score'] > $maxScore + 1) {
                \lbase\Log::error("getDealCardByScoreRegion-Failed", [
                    'cls' => $this->cls,
                    'playerList' => $this->playerList,
                    'minScore' => $minScore,
                    'maxScore' => $maxScore,
                    'isInclude' => $isInclude,
                    'cardsInfo' => $cardsInfo,
                ]);
            }
        } else {
            $cardsInfo['pcards'] = \llogic\game\tpcard\TpCardAlgo::PrettyFormatCards($cardsInfo['cards']);
            $AiTeenPattyHandCardsScoreObj = new AiTeenPattyHandCardsScore($cardsInfo['cards'][0], $cardsInfo['cards'][1], $cardsInfo['cards'][2], $this->cls);
            $cardsInfo['cards_score'] = $AiTeenPattyHandCardsScoreObj->getHandCardsScore();
            $cardsInfo['group_name'] = $AiTeenPattyHandCardsScoreObj->getGroupName();
        }


        return $cardsInfo;
    }


    // 根据人数执行发牌
    private function getDealCardList()
    {
        // 按照概率触发发出所有的牌组
        $this->getUserDealCardsList();
        // 按照分数排序
        $this->sortDealCardsListByScore();
    }
    //---------------------------------------------------------------------------------------------


    // 执行发牌
    private function dealCardRun()
    {
        // 分配牌组到用户身上
        $this->assignCardsToUser();
        // 处理心里门槛系数
        $this->cardBaseWaveRate();
    }




    // 极大杀判定执行
    private function maxCardKiller()
    {
        //再没有大盘杀 没有被扶植 也没有被抑制的时候 （pr进入抑制阶段的人 或者 有tp透杀标签的人） 将这幅牌还给场上随机一个AI  无Ai不执行
        //发牌第一大的玩家id
        $playerTypeList = array_column($this->playerList, 'utype', 'uid');
        $firstPlayerIndexKey = array_key_first($this->sortPlayerList);
        $firstPlayerIndex = $this->sortPlayerList[$firstPlayerIndexKey];
        $firstPlayerType = $playerTypeList[$firstPlayerIndex];

        $firstPlayerCardKey = array_key_first($this->dealCardsListSortByScore);
        $firstPlayerCardScore = $this->dealCardsListSortByScore[$firstPlayerCardKey]['cards_score'];

        if (!$this->isChangeCard && !empty($this->sortAiList) && $firstPlayerType == Common::PLAYER_TYPE_PEOPLE && $firstPlayerCardScore >= 640002) {
            //第一个玩家是否有透杀标签
            $firstPlayerDiscloseKiiler = RedisOpt::getUserTagIsTpDiscloseKiller($firstPlayerIndex, $this->cls);

            if ($firstPlayerDiscloseKiiler || $this->prInfoList[$firstPlayerIndex]['pr'] >= $this->prInfoList[$firstPlayerIndex]['prConfig']['u_begin']) {
                //执行概率判定
                if ($firstPlayerDiscloseKiiler) {
                    $rate = 1 - 0.5 / max($this->prInfoList[$firstPlayerIndex]['pr'], 1);
                } else {
                    $rate = 1 - 0.8 / max($this->prInfoList[$firstPlayerIndex]['pr'], 1);
                }

                $this->rateMaxCardKill = $rate;

                $res = (rand() / (getrandmax() + 1)) < $rate;
                if ($res) {
                    //判断玩家的pr是否
                    $aiList = $this->sortAiList;
                    shuffle($aiList);
                    $aiPlayerIndex = array_shift($aiList);

                    //交换第一个真人和ai的发牌位置
                    $aiPlayerIndexKey = array_search($aiPlayerIndex, $this->sortPlayerList);

                    $this->sortPlayerList[$firstPlayerIndexKey] = $aiPlayerIndex;
                    $this->sortPlayerList[$aiPlayerIndexKey] = $firstPlayerIndex;

                    $this->isMaxCardKill = true;

                }
            }
        }
    }

    // 发牌到用户身上
    private function assignCardsToUser()
    {
        // 分配牌的时候重新初始化信息
        $this->playerHasSanTiaoOrTongHuaShun = false;
        $this->playerHasTonghuOrShunZi = false;

        // 获取用户类型
        $playerTypeList = array_column($this->playerList, 'utype', 'uid');

        // 执行极大杀判定
        $this->maxCardKiller();

        // 此时才能确定最终发牌顺序 sortPlayerList dealCardsListSortByScore 有大到小

        $rtnArr = [];
        foreach ($this->sortPlayerList as $playerIndex) {
            $info = [];
            $info['uid'] = $playerIndex;
            $info['utype'] = $playerTypeList[$playerIndex];
            $cardInfo = array_shift($this->dealCardsListSortByScore);
            $info['ucards'][] = $cardInfo;
            $rtnArr[$playerIndex] = $info;


            // 对于真人玩家提取一些标记，用于处理心里门槛系数
            if ($info['utype'] == Common::PLAYER_TYPE_PEOPLE) {
                //获取玩家是否tp透杀 只要有tp透杀的玩家参与 则执行标签判定
                $playerIsDiscloseKiller = RedisOpt::getUserTagIsTpDiscloseKiller($playerIndex, $this->cls);
                if ($playerIsDiscloseKiller) {
                    $this->discloseKillerPlayerList[$playerIndex] = $playerIndex;

                    $this->playerIsDiscloseKiller = true;
                }

                // 如果真人拿到了豹子或者同花顺
                $card_score = $cardInfo['wscore'] ?? $cardInfo['cards_score'];
                if ($card_score > 1200000000) {
                    $this->playerHasSanTiaoOrTongHuaShun = true;
                }

                // 如果真人拿到了顺子或者同花
                if ($card_score <= 1200000000 && $card_score >= 640002) {
                    $this->playerHasTonghuOrShunZi = true;
                }

                //20220222
                //当用户拿到了aa以上的最大牌 且大盘有缺口  则全场ai会执行透杀用户时 调整自己心里的加倍次数
                if ($card_score >= 640002 && $this->sysRealWinAmount > 0) {
                    $this->discloseKillerPlayerList[$playerIndex] = $playerIndex;
                    //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '用户拿到了aa以上的大牌' . $this->playerMaxScore . ' 且大盘有缺口：' . $this->sysRealWinAmount . '调整心里加倍次数');
                    $this->playerIsDiscloseKiller = true;
                }

                // 如果是真人玩家拿到的最大分值
                $this->playerMaxScore = max($this->playerMaxScore, $card_score);
            }
        }

        $this->userDealCardsList = $rtnArr;
    }



    //注1：当有玩家拿到三条\同花顺时，AI不会再发生换牌行为，同时全场所有Ai会将自己的所有牌型的心里门槛倍数^0.5，并且会弃牌
    //注2：当有玩家拿到顺子\同花时，换牌行为结束后，所有比全场最大的玩家牌小的（含等于）的AI会将自己当前的心里门槛倍数^0.55，并且会弃牌，比AI大的牌则不会
    private function cardBaseWaveRate()
    {
        foreach ($this->userDealCardsList as $key => $val) {
            $notDropRate = bcdiv(random_int(85, 90), 100, 2);

            //随机得到用户的性格属性
            // bakhmut: 读取这个机器人绑定的性格，如果没有绑定，则随机绑定一个
            $disposition = RedisOpt::getTpAiAssignDisposition($key);
            if (!$disposition) {
                $disposition = $this->getRandDispositionConfig();
                $expiresTime = time() + $this->aiDispositionExpiresTime;
                RedisOpt::setTpAiAssignDisposition($key, $disposition, $expiresTime);
            }

            //玩家的初始顺位
            $info['estimated'] = $this->initAvgSequence;

            //被透杀的玩家列表
            $info['disclose_killer_player_list'] = $this->discloseKillerPlayerList;

            // 初始心里加倍上限次数：心里加倍上限【闷牌和看牌总和】
            $info['raise_cnt_limit'] = Common::getTpAiRaiseLimitCntByBase($this->base);

            /*
            手动打一个“TP透杀状态”
            有这个标签的玩家参加的局，在场Ai会提前知道自己是不是比这个玩家牌大
            如果比这些玩家中最大的还大，则本次心里加倍上限在原有的基础上加 随机1-2次
            如果比这些玩家中最大的还小，则本次心里加倍上限在原有的基础上减 随机1-2次
            */

            // 最终心里加倍上限次数
            $card_score = $this->userDealCardsList[$key]['ucards'][0]['wscore'] ?? $this->userDealCardsList[$key]['ucards'][0]['cards_score'];
            if ($this->playerIsDiscloseKiller) {
                if ($card_score > $this->playerMaxScore) {
                    $info['raise_cnt_limit'] = $info['raise_cnt_limit'] + random_int(1, 2);
                }
                if ($card_score < $this->playerMaxScore) {
                    $info['raise_cnt_limit'] = max(($info['raise_cnt_limit'] - random_int(1, 3)), 1);
                }
            }

            // 顺位表里面剔除自己，这里是初始顺位，根据人数推算而来
            $estimatedList = $this->estimatedList;
            unset($estimatedList[$key]);
            $info['estimated_list'] = $estimatedList;

            if ($this->playerHasSanTiaoOrTongHuaShun) {
                $info['not_drop_rate'] = 1;
                $info['disposition'] = $disposition;
                $this->userDealCardsList[$key]['ucards'][0]['card_base_wave_rate'] = json_encode($info);
            } else {
                if ($this->playerHasTonghuOrShunZi && $card_score <= $this->playerMaxScore) {
                    $info['not_drop_rate'] = 1;
                    $info['disposition'] = $disposition;
                    $this->userDealCardsList[$key]['ucards'][0]['card_base_wave_rate'] = json_encode($info);
                } else {
                    $info['not_drop_rate'] = $notDropRate;
                    $info['disposition'] = $disposition;
                    $this->userDealCardsList[$key]['ucards'][0]['card_base_wave_rate'] = json_encode($info);
                }
            }
        }
    }

    //注1：当有玩家拿到三条\同花顺时，AI不会再发生换牌行为，同时全场所有Ai会将自己的所有牌型的心里门槛倍数^0.5，并且会弃牌
    private function getChangeCardRes(): bool
    {
        //执行换牌逻辑
        $changeRate = $this->getChangeCardRate();
        $this->changeRate = $changeRate;
        $isChangeCard = (rand() / (getrandmax() + 1)) < $changeRate;

        return $isChangeCard;
    }



    //获取换牌概率
    private function getChangeCardRate()
    {
        $changeRate = 0;
        // 换牌逻辑动态配置参数
        $dynamicInfo = RedisOpt::getPrTpDymaicOptConfigOne($this->roomInfo['currency'], $this->roomType);
        if (!$dynamicInfo) {
            return 0;
        }

        foreach ($this->playerList as $val) {
            // bakhmut: 跳过机器人
            if ($val['utype'] != Common::PLAYER_TYPE_PEOPLE) {
                continue;
            }

            $debug = [];
            // 用户杀率类型，和有效充值次数有关
            $capitalType = Common::getSysKillerTypeByPlayerIndex($val['uid'], $debug);

            $debug["capitalName"] = Struct::get_capitcal_sortable_name($capitalType);
            $this->capitalNames[$val['uid']] = $debug;

            // 例如 TP 的 0.01 房间是 10_tp_jack_pool
            $jackPortName = Common::getPoolNameByClsAndBase($this->cls, $this->base);

            //获取大盘缺口金额
            // roomInfo 例如 cls: "5" currency: "1" max: "5" base:"10"
            // capitalType 例如 4
            $overallLossAmountObj = new \common\AiCommon\AiOverallWinRateV2($jackPortName, $this->roomInfo, $capitalType);

            //换牌初始概率
            $initChangeRate = $dynamicInfo['deal_rate'] ?? 0;
            $waveRate = $overallLossAmountObj->getTpSysKillerWaveRate($dynamicInfo);
            // waveRate = max(0, 缺口率) * intervene_rate
            if ($waveRate > 0) {
                $this->sysRealWinAmount = 1;
            }
            //换牌概率上限
            //Tp基础杀率=min(配置的杀率上限,原有杀率+房间控系数*当日实时缺口额度/max(当日实时盈利目标,5000,房间底注*1000倍))
            // tmpChangeRate = min(sys_killer_limit_rate, deal_rate + max(0, 缺口率) * intervene_rate)
            // sys_killer_limit_rate 房间控上限
            // deal_rate 房间控初始
            // intervene_rate 房间控系数
            // 最后取所有真人的 tmpChangeRate 的最小值作为大盘控的概率
            $tmpChangeRate = min($dynamicInfo['sys_killer_limit_rate'], ($initChangeRate + $waveRate));
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '换牌逻辑最终概率：' . $changeRate);
            if ($changeRate == 0) {
                $changeRate = $tmpChangeRate;
            } else {
                //$changeRate = min($changeRate, $tmpChangeRate);
                $changeRate = max($changeRate, $tmpChangeRate);
            }
        }

        return $changeRate;
    }

    public function calcBranchName(): string
    {
        return $this->branchName;
    }

    public function GetDealCardDebugInfo(): array
    {
        $uids = array_keys($this->userDealCardsList);
        $prs = array_map(fn($uid) => $this->prInfoList[$uid]['pr'] ?? null, $uids);
        $hands = array_map(fn($uid) => $this->userDealCardsList[$uid]['ucards'][0]['pcards'], $uids);
        $wscores = array_map(fn($uid) => $this->userDealCardsList[$uid]['ucards'][0]['wscore'] ?? $this->userDealCardsList[$uid]['ucards'][0]['cards_score'], $uids);
        $capitals = array_map(fn($uid) => $this->capitalNames[$uid] ?? null, $uids);

        // 因为在扶植时 uidShouldWin 可能不是最大的牌，所以可能输，这里单独再记录一个最大牌的玩家
        $max_wscore = max($wscores);
        $uid_of_max_wscore = $uids[array_search($max_wscore, $wscores, true)];
        $marks = [];
        if ($uid_of_max_wscore != $uids[0]) {
            $marks[] = '首位不最大';
        }

        return [
            'branch' => $this->calcBranchName(),
            'uidShouldWin' => $uids[0],
            'uid_of_max_wscore' => $uid_of_max_wscore,
            'marks' => $marks,
            'uids' => $uids,
            'prs' => $prs,
            'hands' => $hands,
            'wscores' => $wscores,
            'capitals' => $capitals,
            'changeRate' => $this->changeRate,
            'isChangeCard' => $this->isChangeCard,
            'rateMaxCardKill' => $this->rateMaxCardKill,
            'isMaxCardKill' => $this->isMaxCardKill,
        ];
    }

}
