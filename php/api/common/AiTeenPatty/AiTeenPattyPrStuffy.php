<?php
/**
 * @todo   TeenPatty ai逻辑 闷牌阶段的操作
 * <AUTHOR>
 */
namespace common\AiTeenPatty;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;

class AiTeenPattyPrStuffy
{
    //玩家是否drop
    private $isDrop = false;
    //玩家是否加注
    //是否主动发起比牌
    private $isInitiativeCheck = false;
    //是否被动的接受比牌
    private $isPassiveCheck = false;
    //是否是抬轿局
    private $cryUp = false;

    //初始化信息
    //当前操作玩家id
    private $playerIndex = 0;
    //当前房间id
    private $roomId = 0;
    //当前游戏底注
    private $base = 0;
    //当前游戏进行到的轮数
    private $nowRound = 0;
    //桌子上所有玩家信息
    private $playerInfoList = [];
    //本次操作玩家信息
    private $playerTableInfo = [];
    //本次操作的玩家是否是 本场最后一个操作的玩家
    private $isLastOpt = false;
    //本场操作的玩家是否 第一个
    private $isFirstOpt = false;
    //当前闷牌玩家数量
    private $stuffyCardPlayerCnt = 0;
    //当前看牌的玩家数量
    private $seeCardPlayerCnt = 0;
    //累计比牌次数
    private $checkCnt = 0;
    //累计加注次数
    private $raiseCnt = 0;
    //第四个人看牌轮数
    private $fourthplayerSeeCardRound = 0;
    //玩家的闷牌概率
    private $stuffyRate = 0;

    //ai 性格
    private $disposition = '';

    //ai的心里加倍次数上限
    private $aiRaiseCntLimit = 1;

    //ai 对应的性格的配置
    private $dispositionConfig = [];

    //初始化数据
    public function __construct($playerInfoList, $playerIndex, $rtype, $circle, $roomId, $cryUp)
    {
        $this->cryUp = $cryUp;
        //当前操作的玩家id
        $this->playerIndex = $playerIndex;
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '当前操作玩家id：' . $this->playerIndex);
        $this->roomId = $roomId;
        $roomInfo = parse_room_type($rtype);
        $this->base = $roomInfo['base'];
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '当前游戏底注：' . $this->base);
        //当前游戏进行到的轮数
        $this->nowRound = $circle;
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '当前游戏轮数：' . $this->nowRound);
        //桌子上所有玩家信息
        $this->playerInfoList = $playerInfoList;
        //玩家是否是第一个操作的ai
        $this->isFirstOpt = $this->setIsFirstOrLastOptPlayer(true);
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[当前操作玩家] 第一个操作的ai：' . json_encode($this->isFirstOpt));
        //计算是否是本场最后一个操作的
        $this->isLastOpt = $this->setIsFirstOrLastOptPlayer(false);
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[当前操作玩家] 最后一个操作的ai：' . json_encode($this->isLastOpt));

        //统计玩家的其他操作
        $this->setPlayerOptStatCnt();
        //计算闷牌人数和看牌人数
        $this->setStuffyOrSeePlayerCnt();

        //获取ai的性格
        $postBackInfo = json_decode($this->playerTableInfo['card_base_wave_rate'], 1);
        $this->disposition = $postBackInfo['disposition'] ?? Common::ZJ_DISPOSITION_STEADY;
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[当前操作玩家] ai 性格：' . $this->disposition);

        //获取ai的心里加倍次数
        $this->aiRaiseCntLimit = $postBackInfo['raise_cnt_limit'] ?? 1;
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[当前操作玩家] ai 心里加倍次数上限：' . $this->aiRaiseCntLimit);

        //获取ai对应的性格配置
        $this->getAiDispositionConfig();
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[当前操作玩家] ai 性格配置：' . json_encode($this->dispositionConfig));

        //计算闷牌概率
        $stuffyRate = $this->getStuffyRate();
        $maxStuffyRate = $this->getMaxStuffyRate();
        $this->stuffyRate = min($stuffyRate, $maxStuffyRate);
    }

    //获取看牌结果
    public function getIsSee()
    {
        if ($this->playerTableInfo['see']) {
            $isSee = true;
        } else {
            /*1、闷牌阶段，轮到玩家行动时
            当前已加倍次数=加倍心理上限时，无论加倍概率如何，该用户不会在加倍
            当前已加倍次数>加倍心理上限时，无论加倍概率如何，该用户会立刻看牌*/
            if ($this->raiseCnt > $this->aiRaiseCntLimit) {
                //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[是否看牌] 加注判定：已加倍次数' . $this->raiseCnt . '>心里加倍次数上限' . $this->aiRaiseCntLimit . '结果$this->isSee：' . json_encode(true));
                return true;
            }
            //抬轿局闷牌概率*2
            if ($this->cryUp) {
                $this->stuffyRate = $this->stuffyRate * 2;
            }
            $rand[1] = bcmul($this->stuffyRate, 10000, 0);
            $rand[0] = 10000 - $rand[1];
            $isSee = !get_rand($rand);
        }
        return $isSee;
    }

    //是否加注
    public function getIsRaise()
    {
        //1、闷牌阶段，轮到玩家行动时
        //当前已加倍次数=加倍心理上限时，无论加倍概率如何，该用户不会在加倍
        if ($this->raiseCnt >= $this->aiRaiseCntLimit) {
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[计算加注概率] 加注判定：已加倍次数' . $this->raiseCnt . '>=心里加倍次数上限' . $this->aiRaiseCntLimit . '结果$this->isRaise：' . json_encode(false));
            return false;
        }
        $raiseRate = $this->stuffyRaise();
        //抬叫局 加倍概率*2
        if ($this->cryUp) {
            $raiseRate = $raiseRate * 2;
        }
        $rand[1] = bcmul($raiseRate, 10000, 0);

        if ($rand[1] >= 10000) {
            $isRaise = true;
        } elseif ($rand[1] <= 0) {
            $isRaise = false;
        } else {
            $rand[0] = 10000 - $rand[1];
            $isRaise = (bool) get_rand($rand);
        }
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[计算加注概率] 加注判定：' . json_encode($rand) . '结果：' . json_encode($isRaise));
        return $isRaise;
    }

    //是否drop
    public function getIsDrop()
    {
        return $this->isDrop;
    }

    //是否接受比牌
    public function getIsPassiveCheck()
    {
        return $this->isPassiveCheck;
    }

    //是否主动发起比牌
    public function getIsInitiativeCheck()
    {
        return $this->isInitiativeCheck;
    }

    //获取本次操作的ai性格
    private function getAiDispositionConfig()
    {
        $dispositionConfigList = RedisOpt::getTpAiDispositionConfig();
        $this->dispositionConfig = $dispositionConfigList[$this->disposition] ?? [];
    }

    //获取是否是第一个操作的玩家 或者是 最后一个操作的玩家
    private function setIsFirstOrLastOptPlayer($isFirst = true)
    {
        $playerList = $this->playerInfoList;
        if ($isFirst) {
            $playerIndex = array_shift($playerList);
        } else {
            $playerIndex = array_pop($playerList);
        }
        return $playerIndex['uid'] == $this->playerIndex;
    }

    //执行其他玩家行为统计操作
    private function setPlayerOptStatCnt()
    {
        $playerSeeCardRoundList = [];
        foreach ($this->playerInfoList as $val) {
            $playerSeeCardRoundList[] = $val['first_seen_circle'];
            //本次操作的玩家信息
            if ($val['uid'] == $this->playerIndex) {
                $this->playerTableInfo = $val;
            }
            //成功比牌次数
            $this->checkCnt += $val['accept_compare'];
            //总的加倍次数
            $this->raiseCnt += $val['wager_count'];
        }
        //计算第四个人第几轮看的牌
        sort($playerSeeCardRoundList);
        $this->fourthplayerSeeCardRound = $playerSeeCardRoundList[3] ?? 0;
    }

    //执行闷牌人数 和 看牌人数
    private function setStuffyOrSeePlayerCnt()
    {
        foreach ($this->playerInfoList as $val) {
            //如果是第一轮  只看 已经行动过的人的数据
            if ($val['uid'] == $this->playerIndex && $this->nowRound == 0) {
                break;
            }
            //已经离场的不计算
            if (!$val['is_playing']) {
                continue;
            }
            //只计算不包含自己的
            if ($val['uid'] != $this->playerIndex) {
                if ($val['see']) {
                    $this->seeCardPlayerCnt += 1;
                } else {
                    $this->stuffyCardPlayerCnt += 1;
                }
            }
        }
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[闷牌人数] ：' . $this->stuffyCardPlayerCnt);
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[看牌人数] ：' . $this->seeCardPlayerCnt);
    }


    //计算最大闷牌概率
    private function getMaxStuffyRate()
    {
        $dd2 = $this->dispositionConfig['dd2'] ?? 0.15;
        return 0.95 - $dd2 * $this->seeCardPlayerCnt;
    }

    //计算闷牌概率
    private function getStuffyRate()
    {
        $stuffyRate = 0;
        $initStuffyRate = $this->dispositionConfig['initStuffyRate'] ?? 0;
        //第一轮 第一个操作的玩家  闷牌概率等于 场次配置的初始概率
        if ($this->isFirstOpt && $this->nowRound == 0) {
            //SEE_RATE[$this->base]['stuffyRate']
            $stuffyRate = $initStuffyRate;
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[计算闷牌概率] 第一轮 第一个操作的玩家使用初始概率：闷牌概率等于 场次配置的初始概率');
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[计算闷牌概率] 第一轮 第一个操作的玩家使用初始概率：' . $stuffyRate);
            return $stuffyRate;
        }
        //如果是最后一个ai 最后一个操作且看牌人数超过4个 且没有比过牌 则每轮以 95% 50% 10% 0%的概率加倍闷
        if ($this->isLastOpt && $this->checkCnt <= 0 && $this->seeCardPlayerCnt >= 4 && $this->fourthplayerSeeCardRound > 0) {
            //计算当前轮数和第四个看牌轮数差值
            $round = $this->nowRound - $this->fourthplayerSeeCardRound;
            $stuffyRate = LAST_AI_RAISE_RATE[$round] ?? 0;
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[计算闷牌概率] 最后一个ai 最后一个操作且看牌人数超过4个 且没有比过牌：' . $stuffyRate);
            return $stuffyRate;
        }
        //接下来的用户闷牌概率，由本轮中已行动的玩家中，场上看牌人数和闷牌人数的差概率决定

        if ($this->stuffyCardPlayerCnt > $this->seeCardPlayerCnt) {
            //如果闷牌人数 大于 看牌人数 ： 闷牌概率 = 初始闷牌概率 * （1+（闷牌人数-看牌人数）* 0.4）；
            $aa2 = $this->dispositionConfig['aa2'] ?? 0.4;
            $stuffyRate = $initStuffyRate * (1 + ($this->stuffyCardPlayerCnt - $this->seeCardPlayerCnt) * $aa2);
            $stuffyRate = min($stuffyRate, 0.95);
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[计算闷牌概率] 闷牌人数 大于 看牌人数：闷牌概率 = 初始闷牌概率 * （1+（闷牌人数-看牌人数）* ' . $aa2 . '）');
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[计算闷牌概率] 闷牌人数 大于 看牌人数：' . $initStuffyRate . '*(1+(' . $this->stuffyCardPlayerCnt . '-' . $this->seeCardPlayerCnt . ')*' . $aa2 . ')=' . $stuffyRate);
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[计算闷牌概率] 闷牌人数 大于 看牌人数：' . $stuffyRate);
        }
        if ($this->stuffyCardPlayerCnt == $this->seeCardPlayerCnt) {
            //如果闷牌人数 等于 看牌人数 ： 闷牌概率 = 初始闷牌概率
            //闷牌人数=看牌活人时：闷牌概率=初始闷牌概率/1.2^看牌人数
            $bb1 = $this->dispositionConfig['bb1'] ?? 1;
            $stuffyRate = bcdiv($initStuffyRate, bcmul(intval(pow($bb1, $this->seeCardPlayerCnt) * 100), 0.01, 2), 2);
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[计算闷牌概率] 闷牌人数 等于 看牌人数：闷牌概率=初始闷牌概率/' . $bb1 . '^看牌人数');
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[计算闷牌概率] 闷牌人数 等于 看牌人数：' . $stuffyRate);
        }
        if ($this->stuffyCardPlayerCnt < $this->seeCardPlayerCnt) {
            //如果闷牌人数 小于 看牌人数 ： 闷牌概率 = 初始闷牌概率 /2^ （看牌人数-闷牌人数）；
            $cc1 = $this->dispositionConfig['cc1'] ?? 1;
            $stuffyRate = $initStuffyRate / bcmul(intval((pow(2, ($this->seeCardPlayerCnt - $this->stuffyCardPlayerCnt)) * 100)), 0.01, 2);
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[计算闷牌概率] 闷牌人数 小于 看牌人数：初始闷牌概率 /' . $cc1 . '^ （看牌人数-闷牌人数）');
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[计算闷牌概率] 闷牌人数 小于 看牌人数：' . $initStuffyRate . ' /(2^ （' . $this->seeCardPlayerCnt . '-' . $this->stuffyCardPlayerCnt . '）)');
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[计算闷牌概率] 闷牌人数 小于 看牌人数：' . $stuffyRate);
        }
        return $stuffyRate;
    }

    //闷牌跟注情况
    private function stuffyRaise()
    {
        if ($this->checkCnt > 0) {
            //如果已经发生过比牌行为
            if ($this->stuffyRate == 0) {
                $raiseRate = 0;
            } else {
                $raiseRate = bcdiv($this->stuffyRate, 4, 2);
                //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[计算闷牌加注概率] 已经成功比过牌 加注概率：' . $this->stuffyRate . '/4=' . $raiseRate);
            }
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[计算闷牌加注概率] 已经成功比过牌 加注概率：' . $raiseRate);
            return $raiseRate;
        }
        //如果是最后一个ai 最后一个操作且看牌人数超过4个 且没有比过牌 则每轮以 95% 50% 10% 0%的概率加倍闷
        if ($this->isLastOpt && $this->seeCardPlayerCnt >= 4 && $this->fourthplayerSeeCardRound > 0) {
            //LAST_AI_RAISE_RATE
            //计算当前轮数和第四个看牌轮数差值
            $round = $this->nowRound - $this->fourthplayerSeeCardRound;
            $raiseRate = LAST_AI_RAISE_RATE[$round] ?? 0;
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[计算闷牌加注概率] 最后一个ai 最后一个操作且看牌人数超过4个 且没有比过牌 加注概率：' . $raiseRate);
            return $raiseRate;
        }
        //加倍概率衰减： 公式 = 初始加倍概率 * 衰减系数^已加倍次数
        $initRaiseRate = $this->dispositionConfig['initRaiseRate'] ?? 0;
        $raiseDecayRate = $this->dispositionConfig['raiseDecayRate'] ?? 0;
        $lowestRaiseRate = $this->dispositionConfig['lowestRaiseRate'] ?? 0;
        $raiseRate = max($lowestRaiseRate, bcmul($initRaiseRate, bcmul(intval(pow($raiseDecayRate, $this->raiseCnt) * 100), 0.01, 2), 2));
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[计算闷牌加注概率] 初始加倍概率' . $initRaiseRate . ' * 衰减系数' . $raiseDecayRate . '^已加倍次数' . $this->raiseCnt . '：' . $raiseRate);
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[计算闷牌加注概率]，加倍概率衰减 加注概率：' . $raiseRate);
        return $raiseRate;
    }


}
