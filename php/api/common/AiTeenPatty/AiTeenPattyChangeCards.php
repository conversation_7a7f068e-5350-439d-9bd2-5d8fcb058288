<?php
/**
 * @todo   换牌逻辑
 */
namespace common\AiTeenPatty;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;

class AiTeenPattyChangeCards
{

    //全场所有玩家信息
    private $playerInfoList;

    //房间号
    private $roomId;

    //房间信息
    private $roomType;

    //房间信息
    private $roomInfo;

    //最大牌分数
    private $maxCardScore = 0;

    //最大牌是否是ai
    private $maxCardUtype = Common::PLAYER_TYPE_AI;
    /**
     * @var mixed
     */
    private $cls;
    /**
     * @var mixed
     */
    private $base;

    //牌池
    private $pool;

    //当前操作玩家
    private $playerIndex;

    //所有ai
    private $aiPlayerInfoList = [];

    //是否有缺口
    private $sysIsNotWinAmount = false;

    //初始杀率
    private $initChangeRate = 0;

    //用户钱包 计算pr使用
    private $userWalletAmount = 0;

    public function __construct($playerInfoList, $roomType, $roomId, $playerIndex)
    {
        $this->playerIndex = $playerIndex;
        $this->playerInfoList = $playerInfoList;
        $this->roomId = $roomId;
        $this->roomType = $roomType;
        $this->roomInfo = parse_room_type($roomType);
        $this->cls = $this->roomInfo['cls'];
        $this->base = $this->roomInfo['base'];
        //初始化牌池
        $this->pool = init_card_pool(1, false);
    }



    //获取换牌结果
    public function getChangeCardsRes()
    {
        //当前玩家是新手不触发
        $isRookieBuff = RedisOpt::getUserTagIsRookieBuffer($this->playerIndex);
        if ($isRookieBuff) {
            //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId . '-----新手buff 不执行换牌:', 'changeCards');
            return [];
        }

        $this->setMaxCardScore();
        if ($this->maxCardUtype == Common::PLAYER_TYPE_AI) {
            //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId . '-----最大牌是AI 不执行换牌:', 'changeCards');
            return [];
        }
        //3set以上牌型 不执行逻辑
        if ($this->maxCardScore >= 90000000000) {
            //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId . '-----333set以上不执行换牌:' . $this->maxCardScore, 'changeCards');
            return [];
        }
        //没有ai则不执行
        if (empty($this->aiPlayerInfoList)) {
            //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId . '-----没有AI 不执行换牌:', 'changeCards');
            return [];
        }

        //获取当前玩家的tp透杀标签
        $playerIsDiscloseKiller = RedisOpt::getUserTagIsTpDiscloseKiller($this->playerIndex, $this->cls);
        //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId . '-----' . $this->playerIndex . ':tp透杀标签' . json_encode($playerIsDiscloseKiller), 'changeCards');

        //获取分流水最大的波动概率
        $sysKillerRate = $this->setSysKillerRate();
        //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId . '-----$sysKillerRate:' . $sysKillerRate, 'changeCards');
        if (!$this->sysIsNotWinAmount && !$playerIsDiscloseKiller) {
            //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId . '-----无透杀  无波动概率 不执行换牌', 'changeCards');
            return [];
        }
        //获取用户的pr
        $obj = new \common\AiCommon\AiPlayerPr();
        $prInfo = $obj->getPlayerPrByCls($this->playerIndex, $this->cls, $this->userWalletAmount);
        $pr = $prInfo['pr'] ?? 1;
        //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId . '-----用户pr信息:' . json_encode($pr), 'changeCards');
        //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId . '-----用户pr信息 初始杀率:' . json_encode($this->initChangeRate), 'changeCards');
        //大户杀概率=1*min(0.66,(配置的各场次大户杀初始概率+(当时大盘杀概率*TP PR*1.5)))
        //$rate = 1*min(0.66,(0.5+$sysKillerRate));
        $rate = 1 * min(0.66, ($this->initChangeRate + ($sysKillerRate * $pr * 1.5)));
        //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId . '-----最终概率:' . $rate, 'changeCards');
        $rand[1] = intval($rate * 1000);
        $rand[0] = 1000 - $rand[1];
        $res = get_rand($rand);
        //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId . '-----随机换牌结果:' . json_encode($res), 'changeCards');
        if ($res) {
            //执行换牌逻辑
            $changeInfo = $this->getChangeCards();
            //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId . '-----换牌结果:' . json_encode($changeInfo), 'changeCards');
            return $changeInfo;
        } else {
            return [];
        }
    }


    //执行发牌逻辑
    private function getChangeCards(): array
    {
        $obj = new AiTeenPattyDealCardByGroupName($this->pool);
        $obj->minScore = $this->maxCardScore;
        $cards = $obj->getDealCards(Common::ZJ_GROUP_NAME_SCORE_REGION, $this->cls);

        $cardsInfo = [];
        if (0x01) {
            \llogic\game\tpcard\TpScoreAlgo::TpApiSetupCardScoreEtc($this->cls, $cardsInfo, $cards, 'card_score');
        } else {
            $AiTeenPattyHandCardsScoreObj = new AiTeenPattyHandCardsScore($cards[0], $cards[1], $cards[2], $this->cls);
            $cardsInfo['card_score'] = $AiTeenPattyHandCardsScoreObj->getHandCardsScore();
            $cardsInfo['group_name'] = $AiTeenPattyHandCardsScoreObj->getGroupName();
        }

        $cardsInfo['hand_card'] = $cards;
        //执行id随机
        $aiPlayerInfoList = $this->aiPlayerInfoList;
        shuffle($aiPlayerInfoList);
        $aiPlayerInfo = array_pop($aiPlayerInfoList);
        $cardsInfo['uid'] = $aiPlayerInfo['uid'];
        //执行参数修改
        $card_base_wave_rate = json_decode($aiPlayerInfo['card_base_wave_rate'], 1);
        $card_base_wave_rate['not_drop_rate'] = 1 / count($this->playerInfoList);

        // 获取玩家真实手牌的顺位
        // 搜索代码发现，没有人读这个字段
        $card_base_wave_rate['estimated'] = \llogic\game\tpcard\TpScoreAlgo::ScoreToIndex0($cardsInfo['card_score']);

        $cardsInfo['card_base_wave_rate'] = json_encode($card_base_wave_rate);
        return $cardsInfo;
    }


    //找出全场最大的牌
    private function setMaxCardScore()
    {
        foreach ($this->playerInfoList as $info) {
            //去除牌池
            $this->pool = del_b_arr_from_a_arr($this->pool, $info['hand_card']);
            //设置所有的ai id列表 且在玩的
            if ($info['type'] == Common::PLAYER_TYPE_AI && $info['is_playing']) {
                $this->aiPlayerInfoList[] = $info;
            }

            // server层传过来的 可能是wscore也可能是card_rank
            $card_score = $info['card_score'];

            // 获取最大的牌
            if ($card_score >= $this->maxCardScore) {
                $this->maxCardScore = $card_score;
                $this->maxCardUtype = $info['type'];
            }

            if ($info['uid'] == $this->playerIndex) {
                $this->userWalletAmount = isset($info['user_wallet']) ? $info['user_wallet'] : 0;
            }
        }
    }


    //获取波动概率 取全场最大
    private function setSysKillerRate()
    {
        $sysKillerRate = 0;
        //获取奖池名称
        $dynamicInfo = RedisOpt::getPrTpDymaicOptConfigOne($this->roomInfo['currency'], $this->roomType);
        Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '换牌逻辑动态配置参数：' . json_encode($dynamicInfo), 'changeCards');
        if (!$dynamicInfo) {
            return 0;
        }
        foreach ($this->playerInfoList as $val) {
            if ($val['type'] != Common::PLAYER_TYPE_PEOPLE) {
                continue;
            }
            $capitalType = Common::getSysKillerTypeByPlayerIndex($val['uid']);
            //获取大盘缺口金额
            $jackPortName = Common::getPoolNameByClsAndBase($this->cls, $this->base);
            $overallLossAmountObj = new \common\AiCommon\AiOverallWinRateV2($jackPortName, $this->roomInfo, $capitalType);
            //换牌初始概率
            $initChangeRate = $dynamicInfo['deal_rate'] ?? 0;
            $waveRate = $overallLossAmountObj->getTpSysKillerWaveRate($dynamicInfo);
            if ($waveRate > 0) {
                $this->sysIsNotWinAmount = true;
            }
            //换牌概率上限
            //Tp基础杀率=min(配置的杀率上限,原有杀率+房间控系数*当日实时缺口额度/max(当日实时盈利目标,5000,房间底注*1000倍))
            $tmpChangeRate = min($dynamicInfo['sys_killer_limit_rate'], ($initChangeRate + $waveRate));
            Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '换牌逻辑最终概率：' . $sysKillerRate, 'changeCards');
            if ($sysKillerRate == 0) {
                $sysKillerRate = $tmpChangeRate;
                $this->initChangeRate = $dynamicInfo['change_card_init_rate'] ?? 0;
            } else {
                if ($tmpChangeRate > $sysKillerRate) {
                    $sysKillerRate = $tmpChangeRate;
                    $this->initChangeRate = $dynamicInfo['change_card_init_rate'] ?? 0;
                }
            }
        }
        return $sysKillerRate;
    }

}
