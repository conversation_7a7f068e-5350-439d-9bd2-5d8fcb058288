<?php
/**
 * @todo   TeenPatty 动态发牌权重
 * <AUTHOR>
 */
namespace common\AiTeenPatty;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;

class AiTeenPattyPrDealCadWeight
{

    //顺位表类型  每个索引类型 对应一个顺位表
    private $dealCardWeightFieldNameList = [
        Common::GAME_TYPE_TEEN_PATTI => [
            Common::USER_TAG_ROOKIE => 'tpRookieWeight',
            Common::USER_TAG_NORMAL_BUFF => 'tpWeight',
        ],
        Common::GAME_TYPE_TEEN_PATTI_JOKER => [
            Common::USER_TAG_ROOKIE => 'jokerRookieWeight',
            Common::USER_TAG_NORMAL_BUFF => 'jokerWeight',
        ],
        Common::GAME_TYPE_TEEN_PATTI_AK47 => [
            Common::USER_TAG_ROOKIE => 'ak47RookieWeight',
            Common::USER_TAG_NORMAL_BUFF => 'ak47Weight',
        ],
    ];

    //基础顺位
    private $baseSeqFieldNameList = [
        Common::GAME_TYPE_TEEN_PATTI => 'tpSeq',
        Common::GAME_TYPE_TEEN_PATTI_JOKER => 'jokerSeq',
        Common::GAME_TYPE_TEEN_PATTI_AK47 => 'ak47Seq',
    ];

    //顺位胜率配置表路径
    private $dealCardWeightXmlPath = __DIR__ . '/dealCardWeight.xml';

    //读取到的发牌权重配置表
    private $dealCardWeightTable = [];

    //发牌区间
    private $minIndex;
    private $maxIndex;

    public function __construct()
    {
        $this->dealCardWeightTable = $this->setDealCardWeightTable();
    }

    //获取所有的发牌权重列表
    public function getDealCardWeightTable(): array
    {
        return $this->dealCardWeightTable;
    }

    //获取发牌区间的最小索引
    public function getMinIndex()
    {
        return $this->minIndex;
    }
    //获取发牌区间的最大索引
    public function getMaxIndex()
    {
        return $this->maxIndex;
    }

    //根据类型获取顺位表  解析excel 生成的 xml
//    索引    tp发牌权重	joker发牌权重    ak47发牌权重
//    0         1		    1	            1
//    1         1		    1	            1
    private function setDealCardWeightTable(): array
    {
        // 禁止引用外部xml实体
        // 使用静态变量来存储结果
        static $result = null;

        // 检查是否已经有缓存的结果
        if ($result !== null) {
            return $result;
        }

        // 禁止引用外部xml实体
        $xmlObj = simplexml_load_file($this->dealCardWeightXmlPath);
        $result = json_decode(json_encode($xmlObj), 1)['row'];
        return $result;
    }

    //根据游戏返回发牌权重
    public function getDealCardWeightByGameTypeAndBuffType($gameType, $buffType): array
    {
        $field = $this->getDealCardWeightFieldNameByCls($gameType, $buffType);

        static $resultMap = [];
        $result = $resultMap[$field] ?? null;
        if ($result === null) {
            $result = array_column($this->dealCardWeightTable, $field);
            $resultMap[$field] = $result;
        }

        return $result;
    }


    //获取一个按照权重发牌的索引
    public function getDealCardWeightIndexByGameTypeAndBuffType($gameType, $buffType): int
    {
        $dealCardWeight = $this->getDealCardWeightByGameTypeAndBuffType($gameType, $buffType);
        return (int) get_rand($dealCardWeight);
    }

    // 根据 dealCardWeight.xml 权重表索引得到 TP_CARD_LIST 中的区间
    public function getDealCardRegionByIndex($dealCardIndex)
    {
        if ($dealCardIndex == -1) {
            $this->minIndex = 0;
            $this->maxIndex = count($this->dealCardWeightTable) - 1;
        } else {

            //此时发牌的
            $this->minIndex = $dealCardIndex > 1 ? $this->dealCardWeightTable[$dealCardIndex - 1][$this->getBaseSeqFieldNameByCls()] : 0;
            $this->maxIndex = $this->dealCardWeightTable[$dealCardIndex][$this->getBaseSeqFieldNameByCls()];
        }
    }

    public function getDealCardIndexDebugInfo($dealCardIndex)
    {
        return $this->dealCardWeightTable[$dealCardIndex][$this->getBaseSeqFieldNameByCls()] ?? null;
    }

    /**
     * 根据手牌分值获取发牌索引
     * @deprecated 分数反查不支持癞子
     */
    public function getIndexByCardScore($score): int
    {
        //根据分值获取基础顺位
        $sequence = \llogic\game\tpcard\TpScoreAlgo::ScoreToIndex1($score);

        if ($sequence == -1) {
            return -1;
        }

        //根据顺位获取最相近的
        $baseSeqList = array_column($this->dealCardWeightTable, $this->getBaseSeqFieldNameByCls());
        $index = binarySearch($baseSeqList, $sequence, true, 'asc')[0];
        if ($sequence > $baseSeqList[$index] && $sequence <= $baseSeqList[$index + 1]) {
            return (int) ($index + 1);
        } else {
            return (int) $index;
        }
    }


    /**
     * 根据一个分值范围，返回一个 dealCardWeight.xml 的发牌索引
     * @deprecated 分数反查不支持癞子
     */
    public function getIndexByScoreRegion($minScore, $maxScore, $gameType, $buffType): int
    {
        // 根据分数反查得到 TP_CARD_SCORE 的索引，然后根据索引去 dealCardWeight.xml 里面做二分查找，得到 dealCardWeight.xml 的索引
        $maxIndex = $this->getIndexByCardScore($minScore);
        $minIndex = $this->getIndexByCardScore($maxScore);
        if ($minIndex == $maxIndex) {
            return $minIndex;
        }
        //获取次区间的发牌权重
        $allDealCardWeightList = $this->getDealCardWeightByGameTypeAndBuffType($gameType, $buffType);
        //截取索引区间的权重
        $dealCardWeightList = array_slice($allDealCardWeightList, $minIndex, ($maxIndex - $minIndex), true);
        return (int) get_rand($dealCardWeightList);
    }



    //获取发牌权重
    public function getDealCardWeightFieldNameByCls($cls, $buffType): string
    {
        $list = $this->dealCardWeightFieldNameList[$cls] ?? $this->dealCardWeightFieldNameList[Common::GAME_TYPE_TEEN_PATTI];
        return $list[$buffType] ?? $list[Common::USER_TAG_NORMAL_BUFF];
    }

    //获取基础顺位
    public function getBaseSeqFieldNameByCls($cls = ""): string
    {
        if (!$cls) {
            return "baseSeq"; //基础顺位 不会因游戏不同而更改
        } else {
            return $this->baseSeqFieldNameList[$cls] ?? "baseSeq";
        }
    }

}
