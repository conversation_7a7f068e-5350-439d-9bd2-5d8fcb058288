<?php
/**
 * @todo teenPatty新手保护发牌接口
 */

namespace common\AiTeenPatty;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;

class AiTeenPattyPrRookieBuffDealCardV2
{

    //AI 性格属性生存周期 秒
    private $aiDispositionExpiresTime = 86400;
    //牌池
    private $pool;
    //房间号
    private $roomId;
    //房间类型
    private $roomType;
    //解析之后的房间信息
    private $roomInfo;
    //----------------------------------------
    //新手拿到第一大牌的个数配置
    // 随机 [min, max] 个的 0 然后 push 一个 1，最后补 0 到 50 个
    private $firstCardConfig = [
        ['min' => 0, 'max' => 1],
        ['min' => 0, 'max' => 2],
        ['min' => 1, 'max' => 3],
        ['min' => 1, 'max' => 3],
        ['min' => 1, 'max' => 4],

        ['min' => 1, 'max' => 4],
        ['min' => 1, 'max' => 4],
        ['min' => 1, 'max' => 4],
        ['min' => 1, 'max' => 4],
        ['min' => 1, 'max' => 4],

        ['min' => 2, 'max' => 4],
        ['min' => 2, 'max' => 4],
        ['min' => 2, 'max' => 4],
        ['min' => 2, 'max' => 4],
        ['min' => 2, 'max' => 4],

        ['min' => 2, 'max' => 5],
        ['min' => 2, 'max' => 5],
        ['min' => 2, 'max' => 5],
        ['min' => 2, 'max' => 5],
        ['min' => 2, 'max' => 5],

        ['min' => 2, 'max' => 5],
        ['min' => 2, 'max' => 5],
        ['min' => 2, 'max' => 5],
        ['min' => 2, 'max' => 5],
        ['min' => 2, 'max' => 5],

        ['min' => 2, 'max' => 5],
        ['min' => 2, 'max' => 5],
        ['min' => 2, 'max' => 5],
        ['min' => 2, 'max' => 5],
        ['min' => 2, 'max' => 5],

        ['min' => 2, 'max' => 5],
        ['min' => 2, 'max' => 5],
        ['min' => 2, 'max' => 5],
        ['min' => 2, 'max' => 5],
        ['min' => 2, 'max' => 5],

        ['min' => 2, 'max' => 5],
        ['min' => 2, 'max' => 5],
        ['min' => 2, 'max' => 5],
        ['min' => 2, 'max' => 5],
        ['min' => 2, 'max' => 5],

        ['min' => 2, 'max' => 5],
        ['min' => 2, 'max' => 5],
        ['min' => 2, 'max' => 5],
        ['min' => 2, 'max' => 5],
    ];
    //基础发牌权重
    private $baseDealCardsWeight = [];

    //新手发牌权重
    private $rookieDealCardsWeight = [];

    //---------------------------------------
    //新手玩家id
    private $playerIndex = 0;
    //新手发牌顺序
    private $rookieDealCardList = [];
    //获取新手这次是否发送全场最大的牌
    private $isMaxDealCard = false;
    //----------------------------------------


    //----------------------------------------
    //用户发到的手牌数组
    private $dealCardsList = [];
    //排序之后的牌组
    private $dealCardsListSortByScore = [];


    // isCryUp 抬叫就是机器人不断跟注，给玩家超大牌，玩家不跟注就输了，跟注就赢了
    // secondScoreCardRes 第二大牌，这个看起来是“特大牌”的意思，此时真人用 3，机器人用 2
    // isMaxDealCard 新手大牌，此时真人用 1
    private $rookieDealCardConfig = [
        2 => [1 => ['min' => 40004, 'max' => 960000000000], 2 => ['min' => 140002, 'max' => 0], 3 => ['min' => 53200000, 'max' => 960000000000]],
        3 => [1 => ['min' => 200003, 'max' => 960000000000], 2 => ['min' => 200002, 'max' => 0], 3 => ['min' => 133200000, 'max' => 960000000000]],
        4 => [1 => ['min' => 260003, 'max' => 960000000000], 2 => ['min' => 640002, 'max' => 0], 3 => ['min' => 900000000, 'max' => 960000000000]],
        5 => [1 => ['min' => 84200000, 'max' => 960000000000], 2 => ['min' => 83200000, 'max' => 0], 3 => ['min' => 1800000000, 'max' => 960000000000]],
    ];

    // isCryUp 抬叫就是机器人不断跟注，给玩家最大牌，玩家不跟注就输了，跟注就赢了
    // 3 给真人，2 给机器人，1 没有用
    private $rookieDealCardCryUpConfig = [
        2 => [1 => ['min' => 53200000, 'max' => 960000000000], 2 => ['min' => 640013, 'max' => 0], 3 => ['min' => 900000000, 'max' => 960000000000]],
        3 => [1 => ['min' => 53200000, 'max' => 960000000000], 2 => ['min' => 640013, 'max' => 0], 3 => ['min' => 900000000, 'max' => 960000000000]],
        4 => [1 => ['min' => 123200000, 'max' => 960000000000], 2 => ['min' => 120800000, 'max' => 0], 3 => ['min' => 2100000000, 'max' => 960000000000]],
        5 => [1 => ['min' => 9000000000, 'max' => 960000000000], 2 => ['min' => 60000000000, 'max' => 0], 3 => ['min' => 36000000000, 'max' => 960000000000]],
    ];

    //tp新手发牌时 在tp0.5及以下的场 随到指定最大牌时，有x%的概率让ai也拿到第二大的牌（反向杀一次ai） 不同的场次概率不同， 比如0.01=》30% 0.05=》20% 0.1=》15% 0.5=》10%
    private $aiSecondScoreCardRateConfig = [
        10 => 0.18,
        50 => 0.15,
        100 => 0.12,
        500 => 0.1,
    ];
    //----------------------------------------



    //所有的ai 和真人 玩家列表
    private $playerList = [];
    //ai列表
    private $aiPlayerIndexList = [];
    //所有玩家的初始顺位
    private $estimatedList = [];
    //玩家手牌最大的分数 有wscore优先使用wscore
    private $playerMaxScore = 0;
    //玩家手牌最大的分值的用户id
    private $playerMaxScorePlayerIndex = 0;
    //玩家是否拿到了三条或者同花顺
    private $playerHasSanTiaoOrTongHuaShun = false;
    //玩家是否拿到了同花或者顺子
    private $playerHasTonghuOrShunZi = false;
    //分配之后的信息
    private $userDealCardsList = [];
    //本次请求底注
    private $base = 0;
    //本次请求货币类型
    private $currency = 0;
    //本次请求玩法
    private $cls = 0;
    //本场次性格分配情况
    private $dispositionConfig = [];
    //tp牌型配置
    private $initAvgSequence;
    //是否抬叫局
    private $isCryUp = false;
    //玩家是否tp透杀状态
    private $playerIsDiscloseKiller = false;

    private $secondScoreCardRate = 0;
    private $secondScoreCardRes = false;


    public function __construct($playerList, $roomType, $roomId, $isCryUp)
    {
        $this->roomId = $roomId;
        $this->roomType = $roomType;
        $this->roomInfo = parse_room_type($roomType);
        $this->base = $this->roomInfo['base'];
        $this->currency = $this->roomInfo['currency'];
        $this->cls = $this->roomInfo['cls'];
        $this->playerList = $playerList;

        //抬叫局替换发牌区间
        $this->isCryUp = $isCryUp;

        //人均胜率对应顺位
        $obj = new AiTeenPattyPrCardWinRate($this->cls, count($this->playerList));
        $this->initAvgSequence = $obj->getSequenceByWinRate($obj->getAvgWinRateByPlayerCnt());

        //teenPatty只发一次牌 只使用一副牌 且没有大小王
        $this->pool = init_card_pool(1, false);

        //获取ai性格动态配置参数
        $this->getDispositionConfig();

        //充玩家列表里找出真实玩家id
        $this->getRookiePlayerIndex();

        // 获取新手的发牌列表
        $this->getPlayerFirstCardLis();

        // 获取新手这次是否发最大的牌
        $this->getPlayerIsMaxDealCard();

        // 根据玩家数量执行相应数量的发牌
        // 按牌力值倒序排列 dealCardsListSortByScore 
        $this->getDealCardList();

        // 执行分配牌组的处理
        // 分配牌组到用户身上
        // 处理心里门槛系数
        $this->dealCardRun();

        // 更新玩家的新手发牌顺序
        $this->setPlayerFirstCardList();
    }

    //获取发到的牌
    public function getDealCards(): array
    {
        foreach ($this->userDealCardsList as $key => &$val) {
            // "3983435":{"uid":3983435,"utype":0,"ucards":[{"cards":[37,22,39],"cards_score":1800000000,"group_name":"ZJ_GROUP_NAME_SHUNZI","card_base_wave_rate":"{\"estimated\":\"5660\",\"raise_cnt_limit\":4,\"estimated_list\":{\"9712742\":\"5660\",\"2877857\":\"5660\",\"2372424\":\"5660\",\"4785833\":\"5660\"},\"not_drop_rate\":1,\"disposition\":\"STEADY\"}"}]},
            // val 里面有 ucards，ucards 是个数组，但是有且只有一个元素，这个元素的字段有 cards cards_score group_name card_base_wave_rate
            // 为了方便人眼查看牌是啥，和 cards 并列放一个 pcards
            // 这个添加 pcards 字段的逻辑挪到较为原始的位置了
        }
        return $this->userDealCardsList;
    }

    //获取当前剩余牌池
    public function getCardPool(): array
    {
        return $this->pool;
    }

    //获取当前抬叫局是否生效
    public function getCryUp(): bool
    {
        return $this->isCryUp;
    }


    // 该私有函数实际上是初始化内部字段用的
    //获取新手玩家id
    //新手玩家只能匹配ai  全场只有一个真人玩家
    private function getRookiePlayerIndex()
    {
        foreach ($this->playerList as $val) {
            if ($val['utype'] == Common::PLAYER_TYPE_PEOPLE) {
                $this->playerIndex = $val['uid'];
            } else {
                $this->aiPlayerIndexList[] = $val['uid'];
            }
            //玩家初始顺位列表
            $this->estimatedList[$val['uid']] = $this->initAvgSequence;
        }
    }

    //重新设置玩家的剩余新手发牌顺序
    private function setPlayerFirstCardList(): bool
    {
        return RedisOpt::setUserTagTpRookieDealCardList($this->playerIndex, $this->rookieDealCardList);
    }

    //初始化AI的拿到最大牌的排序列表
    private function initPlayerFirstCardList(): array
    {
        $rookieDealCardList = [];
        foreach ($this->firstCardConfig as $key => $val) {

            $lo = $val['min'];
            $hi = $val['max'];

            $number = random_int($lo, $hi);
            for ($i = 1; $i < $number; $i++) {
                array_push($rookieDealCardList, 0);
            }
            array_push($rookieDealCardList, 1);
        }
        //预设50个发牌组
        $cnt = 50 - count($rookieDealCardList);
        if ($cnt >= 1) {
            for ($i = 1; $i <= $cnt; $i++) {
                array_push($rookieDealCardList, 0);
            }
        }
        RedisOpt::setUserTagTpRookieDealCardList($this->playerIndex, $rookieDealCardList);
        return $rookieDealCardList;
    }

    //获取用户剩余的新手发牌情况
    // 就是一个 1 0 数组，1 表示给新手发全场最大的牌
    private function getPlayerFirstCardLis()
    {
        $rookieDealCardList = RedisOpt::getUserTagTpRooikeDealCardList($this->playerIndex);
        if (!$rookieDealCardList) {
            $rookieDealCardList = $this->initPlayerFirstCardList();
        }
        $this->rookieDealCardList = $rookieDealCardList;
    }

    //获取用户这次是否发全场最大的牌
    private function getPlayerIsMaxDealCard()
    {
        // 用户本场是否必拿最大牌
        $isMax = array_shift($this->rookieDealCardList);
        if ($isMax) {
            $this->isMaxDealCard = true;
        } else {
            $this->isMaxDealCard = false;
        }
    }
    //#AI基础发牌权重---------------------------------------------------------------------------------

    //获取本场次的性格分配情况
    private function getDispositionConfig()
    {
        // 500_tp_jack_pool {"STEADY":"45","STEADY2":"5","CUNNING":"30","EXCITED":"20"}
        $dispositionConfig = RedisOpt::getTpAiDispositionRateConfigOne($this->currency, $this->roomType);
        $this->dispositionConfig = $dispositionConfig;
    }

    //获取随机性格
    private function getRandDispositionConfig()
    {
        $randRate = [];
        if (empty($this->dispositionConfig)) {
            $disposition = Common::getTpAiDispositionList();
            foreach ($disposition as $k => $v) {
                $randRate[$k] = 10000;
            }
        } else {
            foreach ($this->dispositionConfig as $k => $v) {
                $randRate[$k] = $v * 10000;
            }
        }
        return get_rand($randRate);
    }

    // 根据人数执行发牌
    private function getDealCardList()
    {
        // 按照概率触发发出所有的牌组
        $this->getUserDealCardsList();
        // 按照分数排序
        $this->sortDealCardsListByScore();
    }
    //---------------------------------------------------------------------------------------------

    // 执行发牌
    private function dealCardRun()
    {
        // 分配牌组到用户身上
        $this->assignCardsToUser();
        // 处理心里门槛系数
        $this->cardBaseWaveRate();
    }

    // 发牌到用户身上
    private function assignCardsToUser()
    {
        // 分配牌的时候重新初始化信息
        $this->playerHasSanTiaoOrTongHuaShun = false;
        $this->playerHasTonghuOrShunZi = false;

        // 获取用户类型
        $playerTypeList = array_column($this->playerList, 'utype', 'uid');
        $playerIndexList = array_column($this->playerList, 'uid');

        $maxCardInfo = [];
        $rtnArr = [];
        $secondScoreCardRes = false;
        $playerCnt = count($this->playerList);
        // 执行乱序发牌
        shuffle($playerIndexList);

        // 如果玩家处于末位，又想给玩家发最大的牌，那么有可能达不到预期效果，所以这种情况就把玩家挪到首位
        if ($this->isMaxDealCard && end($playerIndexList) == $this->playerIndex) {
            array_unshift($playerIndexList, array_pop($playerIndexList));
        }

        foreach ($playerIndexList as $playerIndex) {
            $info = [];
            $info['uid'] = $playerIndex;
            $info['utype'] = $playerTypeList[$playerIndex];
            $secondScoreCardRes = false;
            if ($playerIndex == $this->playerIndex && $this->isMaxDealCard) {
                // 第二大的牌随机结果
                $secondScoreCardRate = $this->aiSecondScoreCardRateConfig[$this->base] ?? 0;
                $secondScoreCardRateX = RedisOpt::getUserTagTpRookieSecondCardRateX($this->playerIndex);
                $secondScoreCardRateX = max(1, $secondScoreCardRateX);
                $secondScoreCardRate = $secondScoreCardRateX * $secondScoreCardRate;
                $secondScoreCardRateRand[1] = 100 * $secondScoreCardRate;
                $secondScoreCardRateRand[0] = 100 - $secondScoreCardRateRand[1];
                $secondScoreCardRes = get_rand($secondScoreCardRateRand);


                // 先取走全场最大的牌
                $cardInfo = array_shift($this->dealCardsListSortByScore);
                // 据发牌控制类型执行替牌逻辑
                // 抬叫发牌
                if ($this->isCryUp) {
                    // 覆盖第一大的牌
                    $scoreMin = $this->rookieDealCardCryUpConfig[$playerCnt][3]['min'];
                    $scoreMax = $this->rookieDealCardCryUpConfig[$playerCnt][3]['max'];
                } elseif ($secondScoreCardRes) {
                    // 覆盖第一大的牌
                    $scoreMin = $this->rookieDealCardConfig[$playerCnt][3]['min'];
                    $scoreMax = $this->rookieDealCardConfig[$playerCnt][3]['max'];

                    $this->secondScoreCardRate = $secondScoreCardRate;
                    $this->secondScoreCardRes = true;
                } else {
                    // 覆盖第一大的牌
                    $scoreMin = $this->rookieDealCardConfig[$playerCnt][1]['min'];
                    $scoreMax = $this->rookieDealCardConfig[$playerCnt][1]['max'];
                }


                if (!in_array($this->cls, [\llogic\common\Struct::GAME_TYPE_TEEN_PATTI_JOKER, \llogic\common\Struct::GAME_TYPE_TEEN_PATTI_AK47])) {
                    $cardInfo = $this->getDealCardByScoreRegion($scoreMin, $scoreMax, true, $cardInfo['cards']);
                } else {
                    $cardInfo = $this->makeBigDealCard($scoreMin, $scoreMax);
                }

                $maxCardInfo = $cardInfo;
                $info['ucards'][] = $cardInfo;

            } elseif ($playerIndex == $this->playerIndex && !$this->isMaxDealCard) {
                // 优先使用最小的牌
                $cardInfo = array_pop($this->dealCardsListSortByScore);
                // 重新随机换一下牌

                // 实例化发牌类
                $obj = new AiTeenPattyDealCardByGroupName($this->pool);
                // 发到的牌
                $dealCards = $obj->getDealCards(Common::ZJ_GROUP_NAME_WEIGHT, $this->cls, Common::USER_TAG_ROOKIE);
                $this->pool = $obj->getCardPool();

                if (0x01) {
                    $cardInfo['cards'] = $dealCards;
                    \llogic\game\tpcard\TpScoreAlgo::TpApiSetupCardScoreEtc($this->cls, $cardInfo, $cardInfo['cards'], 'cards_score');
                } else {
                    $AiTeenPattyHandCardsScoreObj = new AiTeenPattyHandCardsScore($dealCards[0], $dealCards[1], $dealCards[2], $this->cls);
                    $cardInfo['cards'] = $dealCards;
                    $cardInfo['pcards'] = \llogic\game\tpcard\TpCardAlgo::PrettyFormatCards($cardInfo['cards']);
                    $cardInfo['cards_score'] = $AiTeenPattyHandCardsScoreObj->getHandCardsScore();
                    $cardInfo['group_name'] = $AiTeenPattyHandCardsScoreObj->getGroupName();
                }

                $info['ucards'][] = $cardInfo;

            } else {
                // 优先使用最小的牌
                $info['ucards'][] = $cardInfo = array_pop($this->dealCardsListSortByScore);
            }

            // 记录谁拿到了最大的手牌
            $card_score = $cardInfo['wscore'] ?? $cardInfo['cards_score'];
            if ($card_score >= $this->playerMaxScore) {
                $this->playerMaxScore = $card_score;
                $this->playerMaxScorePlayerIndex = $playerIndex;
            }

            $rtnArr[$playerIndex] = $info;

            // 对于真人玩家提取一些标记，用于处理心里门槛系数
            if ($info['utype'] == Common::PLAYER_TYPE_PEOPLE) {
                if (!$this->playerIsDiscloseKiller) {
                    //获取玩家是否tp透杀 只要有tp透杀的玩家参与 则执行标签判定
                    $playerIsDiscloseKiller = RedisOpt::getUserTagIsTpDiscloseKiller($playerIndex, $this->cls);
                    if ($playerIsDiscloseKiller) {
                        $this->playerIsDiscloseKiller = true;
                    }
                }

                // 如果真人拿到了豹子或者同花顺
                if ($cardInfo['cards_score'] > 1200000000) {
                    $this->playerHasSanTiaoOrTongHuaShun = true;
                }
                // 如果真人拿到了顺子或者同花
                if ($cardInfo['cards_score'] <= 1200000000 && $cardInfo['cards_score'] >= 640002) {
                    $this->playerHasTonghuOrShunZi = true;
                }
            }
        }


        // 如果本次用户不应该拿到最大牌，但是随机拿到了最大牌，则延迟一轮后面的发牌顺序
        if (!$this->isMaxDealCard && $this->playerIndex == $this->playerMaxScorePlayerIndex) {
            array_unshift($this->rookieDealCardList, 0);
            // 此时不执行抬叫操作
            $this->isCryUp = false;
        }

        // 如果玩家正常扶植到了最大牌 且 ai要拿第二大牌 执行换牌
        if ($this->isMaxDealCard && $this->aiPlayerIndexList) {
            if ($this->isCryUp) {
                // 抬叫覆盖第二大牌

                RedisOpt::clearUserTagTpRookieSecondCardRateX($this->playerIndex); // 抬叫局随中了，清空概率倍数

                if (!in_array($this->cls, [\llogic\common\Struct::GAME_TYPE_TEEN_PATTI_JOKER, \llogic\common\Struct::GAME_TYPE_TEEN_PATTI_AK47])) {

                    $aiPlayerIndexList = $this->aiPlayerIndexList;
                    shuffle($aiPlayerIndexList);
                    $aiPlayerIndex = $aiPlayerIndexList[0];

                    // 覆盖第二大的牌
                    $scoreMin = $this->rookieDealCardCryUpConfig[$playerCnt][2]['min'];
                    $scoreMax = $maxCardInfo['cards_score'];
                    $cardInfo = $this->getDealCardByScoreRegion($scoreMin, $scoreMax, false, $rtnArr[$aiPlayerIndex]['ucards'][0]);
                    $rtnArr[$aiPlayerIndex]['ucards'][0] = $cardInfo;

                    // 覆盖第三大的牌
                    if (isset($aiPlayerIndexList[1])) {
                        $aiPlayerIndex = $aiPlayerIndexList[1];
                        $cardInfo = $this->getDealCardByScoreRegion($scoreMin, $scoreMax, false, $rtnArr[$aiPlayerIndex]['ucards'][0]);
                        $rtnArr[$aiPlayerIndex]['ucards'][0] = $cardInfo;
                    }

                }
            } elseif ($secondScoreCardRes) {
                RedisOpt::clearUserTagTpRookieSecondCardRateX($this->playerIndex); // 第二大牌随中了，清空概率倍数

                if (!in_array($this->cls, [\llogic\common\Struct::GAME_TYPE_TEEN_PATTI_JOKER, \llogic\common\Struct::GAME_TYPE_TEEN_PATTI_AK47])) {

                    $aiPlayerIndexList = $this->aiPlayerIndexList;
                    shuffle($aiPlayerIndexList);
                    $aiPlayerIndex = $aiPlayerIndexList[0];

                    // 覆盖第二大的牌
                    $scoreMin = $this->rookieDealCardConfig[$playerCnt][2]['min'];
                    $scoreMax = $maxCardInfo['cards_score'];
                    $cardInfo = $this->getDealCardByScoreRegion($scoreMin, $scoreMax, false, $rtnArr[$aiPlayerIndex]['ucards'][0]);
                    $rtnArr[$aiPlayerIndex]['ucards'][0] = $cardInfo;
                }
            } else {
                // 第二大牌没有随中，增加概率倍数
                RedisOpt::setUserTagTpRookieSecondCardRateX($this->playerIndex, 1);
            }
        }

        $this->userDealCardsList = $rtnArr;
    }


    // 根据区间值获取要发的牌
    private function getDealCardByScoreRegion($minScore, $maxScore, $isInclude, $dealCard = []): array
    {
        $obj = new AiTeenPattyDealCardByGroupName($this->pool);
        $obj->minScore = $minScore;
        $obj->maxScore = $maxScore;
        $obj->isInclude = boolval($isInclude);
        $cardsInfo['cards'] = $obj->getDealCards(Common::ZJ_GROUP_NAME_SCORE_REGION, $this->cls, Common::USER_TAG_ROOKIE);
        $this->pool = $obj->getCardPool();

        if (0x01) {
            \llogic\game\tpcard\TpScoreAlgo::TpApiSetupCardScoreEtc($this->cls, $cardsInfo, $cardsInfo['cards'], 'cards_score');
        } else {
            $cardsInfo['pcards'] = \llogic\game\tpcard\TpCardAlgo::PrettyFormatCards($cardsInfo['cards']);
            $AiTeenPattyHandCardsScoreObj = new AiTeenPattyHandCardsScore($cardsInfo['cards'][0], $cardsInfo['cards'][1], $cardsInfo['cards'][2], $this->cls);
            $cardsInfo['cards_score'] = $AiTeenPattyHandCardsScoreObj->getHandCardsScore();
            $cardsInfo['group_name'] = $AiTeenPattyHandCardsScoreObj->getGroupName();
        }

        return $cardsInfo;
    }


    //注1：当有玩家拿到三条\同花顺时，AI不会再发生换牌行为，同时全场所有Ai会将自己的所有牌型的心里门槛倍数^0.5，并且会弃牌
    //注2：当有玩家拿到顺子\同花时，换牌行为结束后，所有比全场最大的玩家牌小的（含等于）的AI会将自己当前的心里门槛倍数^0.55，并且会弃牌，比AI大的牌则不会
    private function cardBaseWaveRate()
    {
        foreach ($this->userDealCardsList as $key => $val) {
            $notDropRate = bcdiv(random_int(70, 75), 100, 2);
            //随机得到用户的性格属性
            $disposition = RedisOpt::getTpAiAssignDisposition($key);
            if (!$disposition) {
                $disposition = $this->getRandDispositionConfig();
                $expiresTime = time() + $this->aiDispositionExpiresTime;
                RedisOpt::setTpAiAssignDisposition($key, $disposition, $expiresTime);
            }
            //玩家的初始顺位
            $info['estimated'] = $this->initAvgSequence;

            // 初始心里加倍上限次数：心里加倍上限【闷牌和看牌总和】
            $info['raise_cnt_limit'] = Common::getTpAiRaiseLimitCntByBase($this->base);

            /*
            手动打一个“TP透杀状态”
            有这个标签的玩家参加的局，在场Ai会提前知道自己是不是比这个玩家牌大
            如果比这些玩家中最大的还大，则本次心里加倍上限在原有的基础上加 随机1-2次
            如果比这些玩家中最大的还小，则本次心里加倍上限在原有的基础上减 随机1-2次
            */

            // 最终心里加倍上限次数
            $card_score = $this->userDealCardsList[$key]['ucards'][0]['wscore'] ?? $this->userDealCardsList[$key]['ucards'][0]['cards_score'];
            if ($this->playerIsDiscloseKiller) {
                if ($card_score > $this->playerMaxScore) {
                    $info['raise_cnt_limit'] = $info['raise_cnt_limit'] + random_int(1, 2);
                }
                if ($card_score < $this->playerMaxScore) {
                    $info['raise_cnt_limit'] = max(($info['raise_cnt_limit'] - random_int(1, 2)), 1);
                }
            }

            // 顺位表里面剔除自己，这里是初始顺位，根据人数推算而来
            $estimatedList = $this->estimatedList;
            unset($estimatedList[$key]);
            $info['estimated_list'] = $estimatedList;

            if ($this->playerHasSanTiaoOrTongHuaShun) {
                $info['not_drop_rate'] = 1;
                $info['disposition'] = $disposition;
                $this->userDealCardsList[$key]['ucards'][0]['card_base_wave_rate'] = json_encode($info);
            } else {
                if ($this->playerHasTonghuOrShunZi && $card_score <= $this->playerMaxScore) {
                    $info['not_drop_rate'] = 1;
                    $info['disposition'] = $disposition;
                    $this->userDealCardsList[$key]['ucards'][0]['card_base_wave_rate'] = json_encode($info);
                } else {
                    $info['not_drop_rate'] = $notDropRate;
                    $info['disposition'] = $disposition;
                    $this->userDealCardsList[$key]['ucards'][0]['card_base_wave_rate'] = json_encode($info);
                }
            }
        }
    }


    //获取发牌 以及 牌的分数
    private function getUserDealCardsList()
    {
        $cnt = count($this->playerList);
        for ($i = 0; $i < $cnt; $i++) {
            //实例化发牌类
            $obj = new AiTeenPattyDealCardByGroupName($this->pool);
            $dealCards = $obj->getDealCards(Common::ZJ_GROUP_NAME_WEIGHT, $this->cls, Common::USER_TAG_NORMAL_BUFF);
            $this->pool = $obj->getCardPool();
            $this->dealCardsList[$i] = $dealCards;
        }
    }


    //根据牌组分值 从小到大排序
    public function sortDealCardsListByScore()
    {
        $sortList = [];
        foreach ($this->dealCardsList as $key => $val) {
            if (0x01) {
                $sortList[$key]['cards'] = $val;
                \llogic\game\tpcard\TpScoreAlgo::TpApiSetupCardScoreEtc($this->cls, $sortList[$key], $sortList[$key]['cards'], 'cards_score');
            } else {
                // 这个根据牌组获取分数支持癞子牌
                $AiTeenPattyHandCardsScoreObj = new AiTeenPattyHandCardsScore($val[0], $val[1], $val[2], $this->cls);
                $sortList[$key]['cards'] = $val;
                $sortList[$key]['pcards'] = \llogic\game\tpcard\TpCardAlgo::PrettyFormatCards($sortList[$key]['cards']);
                $sortList[$key]['cards_score'] = $AiTeenPattyHandCardsScoreObj->getHandCardsScore();
                $sortList[$key]['group_name'] = $AiTeenPattyHandCardsScoreObj->getGroupName();
            }

            $scoreList[] = $sortList[$key]['wscore'] ?? $sortList[$key]['cards_score'];
        }
        //按照分数进行 降序排序
        array_multisort($scoreList, SORT_DESC, $sortList);
        $this->dealCardsListSortByScore = $sortList;

        // 最终排序的手牌
        $this->dealCardsListSortByScore = $sortList;
    }


    // 新加一个大牌到排序列表里面
    private function makeBigDealCard($scoreMin, $scoreMax)
    {
        // 新造一个大牌，加进去，重新排序，然后取最大的，这样避免新造的不是最大
        $cardInfo = $this->getDealCardByScoreRegion($scoreMin, $scoreMax, true);
        $this->dealCardsListSortByScore[] = $cardInfo;
        $scoreList = array_map(fn($val) => $val['wscore'] ?? $val['cards_score'], $this->dealCardsListSortByScore);
        array_multisort($scoreList, SORT_DESC, $this->dealCardsListSortByScore);

        \lbase\Log::info('makeBigDealCard-AfterSort', [
            'cls' => $this->cls,
            'scoreMin' => $scoreMin,
            'scoreMax' => $scoreMax,
            'cardInfo' => $cardInfo,
            'scoreList' => $scoreList,
            'dealCardsListSortByScore' => $this->dealCardsListSortByScore,
        ]);


        $big = array_shift($this->dealCardsListSortByScore);

        \lbase\Log::info('makeBigDealCard-AfterShift', [
            'dealCardsListSortByScore' => $this->dealCardsListSortByScore,
            'big' => $big,
        ]);

        return $big;

    }

    // 获取流程分支名用于统计
    public function calcBranchName(): string
    {
        $branchName = "新手小牌";
        if ($this->isMaxDealCard) {
            if ($this->isCryUp) {
                $branchName = "新手抬叫";
            } else if ($this->secondScoreCardRes) {
                $branchName = "新手第二大牌";
            } else {
                $branchName = "新手大牌";
            }
        } else if ($this->playerIndex == $this->playerMaxScorePlayerIndex) {
            $branchName = "新手意外大牌";
        }
        return $branchName;
    }

    public function GetDealCardDebugInfo(): array
    {
        $uids = array_keys($this->userDealCardsList);
        $hands = array_map(fn($uid) => $this->userDealCardsList[$uid]['ucards'][0]['pcards'], $uids);
        $wscores = array_map(fn($uid) => $this->userDealCardsList[$uid]['ucards'][0]['wscore'] ?? $this->userDealCardsList[$uid]['ucards'][0]['cards_score'], $uids);
        $uidShouldWin = $this->isMaxDealCard ? $this->playerIndex : $this->playerMaxScorePlayerIndex;

        // 因为在扶植时 uidShouldWin 可能不是最大的牌，所以可能输，这里单独再记录一个最大牌的玩家
        $max_wscore = max($wscores);
        $uid_of_max_wscore = $uids[array_search($max_wscore, $wscores, true)];
        $marks = [];
        if ($uid_of_max_wscore != $uidShouldWin) {
            $marks[] = '首位不最大';
        }

        return [
            'branch' => $this->calcBranchName(),
            'uidShouldWin' => $uidShouldWin,
            'uidReal' => $this->playerIndex,
            'uid_of_max_wscore' => $uid_of_max_wscore,
            'marks' => $marks,
            'uids' => $uids,
            'hands' => $hands,
            'wscores' => $wscores,
            'secondScoreCardRate' => $this->secondScoreCardRate,
        ];
    }
}
