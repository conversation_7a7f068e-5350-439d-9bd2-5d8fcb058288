<?php
/**
 * @todo   TeenPatty ai逻辑 预估顺位
 * <AUTHOR>
 * @deprecated
 */
namespace common\AiTeenPatty;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;

class AiTeenPattyPrBetSequence
{

    //初始化信息
    //当前操作玩家id
    private $ulist = [];

    //ulist 玩家id的
    private $uInfoList = [];

    //当前操作
    private $opt;

    //回传参数
    private $postBackInfoList = [];

    //当前房间id
    private $roomId = 0;

    //本场所有的加倍次数
    private $raiseCnt = 0;

    //玩家id
    private $playerIndex = 0;


    //是否是全场最大牌
    private $isMaxCard = false;

    //初始化数据
    public function __construct($playerIndex, $ulist, $opt, $roomId)
    {
        //解析房间信息
        $this->roomId = $roomId;
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '$playerIndex' . $playerIndex);
        $this->playerIndex = $playerIndex;
        $this->opt = $opt;
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '$opt' . Common::getTpPlayerOptText($this->opt));
        //当前桌子玩家列表
        $this->ulist = $ulist;
        $AiMaxCardRank = 0;
        $maxCardRank = 0;
        //全场总的加注次数
        foreach ($this->ulist as $val) {
            // server层传过来的 可能是wscore也可能是card_rank
            $card_score = $val['card_score'];

            //获取是否是全场最大
            if ($val['type'] == Common::PLAYER_TYPE_AI) {
                $AiMaxCardRank = max($AiMaxCardRank, $card_score);
            }
            $maxCardRank = max($maxCardRank, $card_score);
            $this->uInfoList[$val['uid']] = $val;
            //总的加倍次数
            $this->raiseCnt += $val['wager_count'];
        }
        if ($AiMaxCardRank == $maxCardRank) {
            $this->isMaxCard = true;
        }
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '总加注次数：' . $this->raiseCnt);

        //获取预估胜率
        $this->updateEstimatedList();
    }

    // 获取顺位预估
    // 发牌时会根据人数推算初始顺位，机器人看牌后会根据自己的牌更新其他人的顺位预估
    private function updateEstimatedList()
    {
        // 没有看牌，不执行顺位评估
        if (!$this->uInfoList[$this->playerIndex]['see']) {
            return;
        }

        foreach ($this->ulist as $key => $val) {
            if ($val['type'] != Common::PLAYER_TYPE_AI) {
                continue;
            }
            if ($val['uid'] == $this->playerIndex) {
                continue;
            }

            $postBackInfo = json_decode($val['card_base_wave_rate'], 1);

            //获取ai的性格
            $disposition = $postBackInfo['disposition'] ?? Common::ZJ_DISPOSITION_STEADY;

            //获取ai对应的性格配置
            $dispositionConfig = $this->getAiDispositionConfig($disposition);

            //获取tp透杀玩家列表
            $discloseKillerPlayerList = $postBackInfo['disclose_killer_player_list'] ?? [];
            $isDiscloseKiller = isset($discloseKillerPlayerList[$this->playerIndex]);

            // 变更玩家预估顺位
            // 变更前
            $beforeEstimated = $postBackInfo['estimated_list'][$this->playerIndex];
            // 变更后
            $afterEstimated = $this->getPlayerGroupBaseNum($this->raiseCnt, $dispositionConfig, $beforeEstimated, $isDiscloseKiller);

            $postBackInfo['estimated_list'][$this->playerIndex] = $afterEstimated;

            $this->postBackInfoList[$val['uid']] = json_encode($postBackInfo);
        }
    }

    //获取灰产信息
    public function getPostBackInfo()
    {
        return $this->postBackInfoList;
    }

    //获取本次操作的ai性格
    private function getAiDispositionConfig($disposition)
    {
        $dispositionConfigList = RedisOpt::getTpAiDispositionConfig();
        return $dispositionConfigList[$disposition] ?? [];
    }


    /**
     * 变更玩家预估顺位
     * 第一次看牌后，其他玩家和AI的每次跟注和加注，都会改变顺位：普通跟注，顺位缩减90%，加倍跟注，顺位缩减80%
     */
    private function getPlayerGroupBaseNum($raiseCnt, $dispositionConfig, $estimated, $isDiscloseKiller)
    {
        $rate = 1;

        // 平跟
        if ($this->opt == Common::TP_PLAYER_OPT_FOLLOW) {
            //
            $followDynamic = $dispositionConfig['followDynamic'] ?? 0.9;
            $followDynamicPow = $dispositionConfig['followDynamicPow'] ?? 1.7;

            // $rate = $followDynamic*(1-pow($raiseCnt,$followDynamicPow)*0.01);
            // 90%*跟注参数^max(1,(2^(本场已加倍次数^0.81)-1.7))  20220531修改
            $rate = $followDynamic * pow($followDynamic, (max(1, pow(2, pow($raiseCnt, $followDynamicPow)) - 1.7)));
        }

        // 加注
        if ($this->opt == Common::TP_PLAYER_OPT_RAISE) {
            $raiseCnt = $raiseCnt > 0 ? $raiseCnt - 1 : $raiseCnt;
            $raiseDynamic = $dispositionConfig['raiseDynamic'] ?? 0.8;
            $followDynamic = $dispositionConfig['followDynamic'] ?? 0.9;
            $raiseDynamicPow = $dispositionConfig['raiseDynamicPow'] ?? 1.5;

            //$rate = $raiseDynamic * (1 - pow($raiseCnt, $raiseDynamicPow) * 0.01);
            //90%*跟注参数^max(1,(2^(本场已加倍次数^0.81)-1.7))  20220531修改
            $rate = $raiseDynamic * pow($followDynamic, (max(1, pow(2, pow($raiseCnt, $raiseDynamicPow)) - 1.7)));
        }

        // 被透杀
        if ($isDiscloseKiller && !$this->isMaxCard) {
            $estimatedRes = floor($estimated * ($rate * $rate));
        } else {
            $estimatedRes = floor($estimated * $rate);
        }

        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[当前操作玩家] 顺位变更 $this->>playerIndex:' . $this->playerIndex . '原始顺位:' . $estimated . '变更后的顺位：' . $estimatedRes);
        return $estimatedRes;
    }
}
