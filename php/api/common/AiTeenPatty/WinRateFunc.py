import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import curve_fit

# 数据
data = [
    [0.9998, 0.9998],
    [0.9996, 0.9996],
    [0.9994, 0.9994],
    [0.9992, 0.9993],
    [0.999, 0.9991],
    [0.9989, 0.9989],
    [0.9987, 0.9987],
    [0.9985, 0.9985],
    [0.9983, 0.9983],
    [0.9981, 0.9981],
    [0.998, 0.9979],
    [0.9978, 0.9978],
    [0.9976, 0.9976],
    [0.9974, 0.9974],
    [0.9972, 0.9972],
    [0.9971, 0.997],
    [0.9969, 0.9968],
    [0.9967, 0.9966],
    [0.9965, 0.9965],
    [0.9963, 0.9963],
    [0.9962, 0.9961],
    [0.996, 0.9959],
    [0.9958, 0.9957],
    [0.9956, 0.9955],
    [0.9954, 0.9953],
    [0.9927, 0.9914],
    [0.99, 0.9875],
    [0.9873, 0.9836],
    [0.9846, 0.9797],
    [0.9819, 0.9758],
    [0.9791, 0.9719],
    [0.9764, 0.9679],
    [0.9737, 0.964],
    [0.971, 0.9601],
    [0.9683, 0.9562],
    [0.9656, 0.9523],
    [0.9629, 0.9484],
    [0.9627, 0.9481],
    [0.9625, 0.9478],
    [0.9623, 0.9475],
    [0.9621, 0.9472],
    [0.9619, 0.947],
    [0.9618, 0.9467],
    [0.9616, 0.9464],
    [0.9614, 0.9461],
    [0.9612, 0.9458],
    [0.961, 0.9456],
    [0.9609, 0.9453],
    [0.9607, 0.945],
    [0.9605, 0.9447],
    [0.9603, 0.9444],
    [0.9601, 0.9442],
    [0.96, 0.9439],
    [0.9598, 0.9436],
    [0.9596, 0.9433],
    [0.9594, 0.9431],
    [0.9592, 0.9428],
    [0.959, 0.9425],
    [0.9589, 0.9422],
    [0.9587, 0.9419],
    [0.9585, 0.9417],
    [0.9583, 0.9414],
    [0.9581, 0.9411],
    [0.958, 0.9408],
    [0.9578, 0.9405],
    [0.9576, 0.9403],
    [0.9574, 0.94],
    [0.9572, 0.9397],
    [0.9571, 0.9394],
    [0.9569, 0.9391],
    [0.9567, 0.9389],
    [0.9565, 0.9386],
    [0.9563, 0.9383],
    [0.9562, 0.938],
    [0.956, 0.9377],
    [0.9558, 0.9375],
    [0.9556, 0.9372],
    [0.9554, 0.9369],
    [0.9552, 0.9366],
    [0.9551, 0.9363],
    [0.9549, 0.9361],
    [0.9547, 0.9358],
    [0.9545, 0.9355],
    [0.9543, 0.9352],
    [0.9542, 0.9349],
    [0.954, 0.9347],
    [0.9538, 0.9344],
    [0.9536, 0.9341],
    [0.9534, 0.9338],
    [0.9533, 0.9335],
    [0.9531, 0.9333],
    [0.9529, 0.933],
    [0.9527, 0.9327],
    [0.9525, 0.9324],
    [0.9524, 0.9321],
    [0.9522, 0.9319],
    [0.952, 0.9316],
    [0.9518, 0.9313],
    [0.9516, 0.931],
    [0.9514, 0.9307],
    [0.9513, 0.9305],
    [0.9415, 0.9164],
    [0.9335, 0.9049],
    [0.9272, 0.8958],
    [0.9223, 0.8888],
    [0.9187, 0.8835],
    [0.9162, 0.8799],
    [0.9145, 0.8776],
    [0.9136, 0.8763],
    [0.9133, 0.8758],
    [0.9122, 0.8742],
    [0.9111, 0.8726],
    [0.91, 0.871],
    [0.9089, 0.8694],
    [0.9078, 0.8678],
    [0.9067, 0.8663],
    [0.9057, 0.8647],
    [0.9046, 0.8631],
    [0.9035, 0.8615],
    [0.9024, 0.8599],
    [0.9013, 0.8583],
    [0.9002, 0.8567],
    [0.8991, 0.8552],
    [0.8981, 0.8536],
    [0.897, 0.852],
    [0.8959, 0.8504],
    [0.8948, 0.8488],
    [0.8937, 0.8472],
    [0.8926, 0.8457],
    [0.8915, 0.8441],
    [0.8905, 0.8425],
    [0.8894, 0.8409],
    [0.8883, 0.8393],
    [0.8872, 0.8377],
    [0.8861, 0.8361],
    [0.885, 0.8346],
    [0.8839, 0.833],
    [0.8829, 0.8314],
    [0.8818, 0.8298],
    [0.8807, 0.8282],
    [0.8796, 0.8266],
    [0.8785, 0.8251],
    [0.8774, 0.8235],
    [0.8763, 0.8219],
    [0.8752, 0.8203],
    [0.8742, 0.8187],
    [0.8731, 0.8171],
    [0.872, 0.8155],
    [0.8709, 0.814],
    [0.8698, 0.8124],
    [0.8687, 0.8108],
    [0.8676, 0.8092],
    [0.8666, 0.8076],
    [0.8655, 0.806],
    [0.8644, 0.8045],
    [0.8633, 0.8029],
    [0.8622, 0.8013],
    [0.8611, 0.7997],
    [0.86, 0.7986],
    [0.859, 0.7975],
    [0.8579, 0.7963],
    [0.8568, 0.7952],
    [0.8557, 0.7941],
    [0.8546, 0.793],
    [0.8535, 0.7919],
    [0.8524, 0.7908],
    [0.8514, 0.7896],
    [0.8503, 0.7885],
    [0.8492, 0.7874],
    [0.8481, 0.7863],
    [0.847, 0.7852],
    [0.8459, 0.784],
    [0.8448, 0.7829],
    [0.8438, 0.7818],
    [0.8427, 0.7807],
    [0.8416, 0.7796],
    [0.8405, 0.7785],
    [0.8394, 0.7773],
    [0.8383, 0.7762],
    [0.8372, 0.7751],
    [0.8362, 0.774],
    [0.8351, 0.7729],
    [0.834, 0.7717],
    [0.8329, 0.7706],
    [0.8318, 0.7695],
    [0.8307, 0.7684],
    [0.8296, 0.7673],
    [0.8286, 0.7661],
    [0.8275, 0.765],
    [0.8264, 0.7639],
    [0.8253, 0.7628],
    [0.8242, 0.7617],
    [0.8231, 0.7606],
    [0.822, 0.7594],
    [0.821, 0.7583],
    [0.8199, 0.7572],
    [0.8188, 0.7561],
    [0.8177, 0.755],
    [0.8166, 0.7538],
    [0.8155, 0.7527],
    [0.8144, 0.7516],
    [0.8133, 0.7505],
    [0.8123, 0.7494],
    [0.8112, 0.7483],
    [0.8101, 0.7471],
    [0.809, 0.746],
    [0.8079, 0.7449],
    [0.8068, 0.7438],
    [0.8057, 0.7427],
    [0.8047, 0.7415],
    [0.8036, 0.7404],
    [0.8025, 0.7393],
    [0.8014, 0.7382],
    [0.8003, 0.7371],
    [0.7992, 0.7359],
    [0.7981, 0.7348],
    [0.7971, 0.7337],
    [0.796, 0.7326],
    [0.7949, 0.7315],
    [0.7938, 0.7304],
    [0.7927, 0.7292],
    [0.7916, 0.7281],
    [0.7905, 0.727],
    [0.7895, 0.7259],
    [0.7884, 0.7248],
    [0.7873, 0.7236],
    [0.7862, 0.7225],
    [0.7851, 0.7214],
    [0.784, 0.7203],
    [0.7829, 0.7192],
    [0.7819, 0.7181],
    [0.7808, 0.7169],
    [0.7797, 0.7158],
    [0.7786, 0.7147],
    [0.7775, 0.7136],
    [0.7764, 0.7125],
    [0.7753, 0.7113],
    [0.7743, 0.7102],
    [0.7732, 0.7091],
    [0.7721, 0.708],
    [0.771, 0.7069],
    [0.7699, 0.7058],
    [0.7688, 0.7046],
    [0.7677, 0.7035],
    [0.7667, 0.7024],
    [0.7656, 0.7013],
    [0.7645, 0.7002],
    [0.7634, 0.699],
    [0.7623, 0.6979],
    [0.7612, 0.6968],
    [0.7601, 0.6957],
    [0.759, 0.6946],
    [0.758, 0.6934],
    [0.7569, 0.6923],
    [0.7558, 0.6912],
    [0.7547, 0.6901],
    [0.7536, 0.689],
    [0.7525, 0.6879],
    [0.7514, 0.6867],
    [0.7504, 0.6856],
    [0.7493, 0.6845],
    [0.7482, 0.6834],
    [0.7471, 0.6823],
    [0.746, 0.6811],
    [0.7449, 0.68],
    [0.7438, 0.6789],
    [0.7167, 0.6509],
    [0.6895, 0.623],
    [0.6651, 0.5978],
    [0.6434, 0.5754],
    [0.6244, 0.5559],
    [0.6081, 0.5391],
    [0.5945, 0.5251],
    [0.5837, 0.5139],
    [0.5755, 0.5055],
    [0.5701, 0.5],
    [0.5457, 0.4748],
    [0.5212, 0.4496],
    [0.4995, 0.4273],
    [0.4805, 0.4077],
    [0.4642, 0.3909],
    [0.4506, 0.3769],
    [0.4398, 0.3657],
    [0.4316, 0.3573],
    [0.4262, 0.3518],
    [0.4235, 0.349],
    [0.4018, 0.3311],
    [0.38, 0.3132],
    [0.361, 0.2975],
    [0.3448, 0.2841],
    [0.3312, 0.2729],
    [0.3203, 0.264],
    [0.3122, 0.2572],
    [0.3067, 0.2528],
    [0.304, 0.2505],
    [0.285, 0.2349],
    [0.266, 0.2192],
    [0.2497, 0.2058],
    [0.2362, 0.1946],
    [0.2253, 0.1857],
    [0.2171, 0.179],
    [0.2117, 0.1745],
    [0.209, 0.1722],
    [0.1927, 0.1588],
    [0.1764, 0.1454],
    [0.1629, 0.1342],
    [0.152, 0.1253],
    [0.1438, 0.1186],
    [0.1384, 0.1141],
    [0.1357, 0.1118],
    [0.1221, 0.1007],
    [0.1086, 0.0895],
    [0.0977, 0.0805],
    [0.0895, 0.0738],
    [0.0841, 0.0693],
    [0.0814, 0.0671],
    [0.0705, 0.0582],
    [0.0597, 0.0492],
    [0.0515, 0.0425],
    [0.0461, 0.038],
    [0.0434, 0.0358],
    [0.0352, 0.0291],
    [0.0271, 0.0224],
    [0.0217, 0.0179],
    [0.019, 0.0157],
    [0.0135, 0.0112],
    [0.0081, 0.0067],
    [0.0054, 0.0045],
    [0.0027, 0.0022],
    [-0, 0],
]

# 提取数据
x_data, y_data = zip(*data)

# 绘制数据点，点小一些
plt.scatter(x_data, y_data, label="Data", color="blue", s=1)


# 定义要拟合的函数（例如多项式或指数函数）
def fit_function(x, a, b, c):
    return a * x * x + b * x + c


def fit_function2(x, a, b, c):
    return a * np.exp(b * x) + c


def fit_function3(x, a, b, c, d):
    return a * x * x + b * x + c + d * x * x * x


def fit_function4(x, a, b, c, d):
    return np.where(x < 0.5, a * x + b, c * x + d)


# 拟合数据
params, covariance = curve_fit(fit_function, x_data, y_data)

# 绘制拟合曲线
x_fit = np.linspace(min(x_data), max(x_data), 100)
y_fit = fit_function(x_fit, *params)
print("params:", params)
plt.plot(x_fit, y_fit, label="Fit", color="red")


# 绘制 y = x
plt.plot(x_fit, x_fit, label="y=x", color="green")

# 绘制 y = x 和数据集的差距
y_diff = [x - y for y, x in zip(y_data, x_data)]
plt.scatter(x_data, y_diff, label="Diff", color="black", s=1)


WinRateMiddleX = 0.5
WinRateMiddleY = 0.4


def down_win_rate(rate: float) -> float:
    if rate < WinRateMiddleX:
        return rate * WinRateMiddleY / WinRateMiddleX
    return WinRateMiddleY + (rate - WinRateMiddleX) * (1 - WinRateMiddleY) / (
        1 - WinRateMiddleX
    )


def up_win_rate(rate: float) -> float:
    if rate < WinRateMiddleY:
        return rate * WinRateMiddleX / WinRateMiddleY
    return WinRateMiddleX + (rate - WinRateMiddleY) * (1 - WinRateMiddleX) / (
        1 - WinRateMiddleY
    )


# 绘制这俩函数
x_fit = np.linspace(0, 1, 100)
y_fit = [down_win_rate(x) for x in x_fit]
plt.plot(x_fit, y_fit, label="DownWinRate", color="purple")

y_fit = [up_win_rate(x) for x in x_fit]
# plt.plot(x_fit, y_fit, label="UpWinRate", color="orange")


# 添加图例和标题
plt.legend()
plt.title("Data and Exponential Fit")
plt.xlabel("X")
plt.ylabel("Y")

# 显示图形
plt.show()
