<?php
/**
 * @todo   TeenPatty 获取牌组的分数
 * <AUTHOR>
 *
 */
namespace common\AiTeenPatty;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;

/**
 * 计算手牌牌力值
 * @deprecated TeenPatti 系列都改用新代码了，好像还有 TpWar 用这里
 */
class AiTeenPattyHandCardsScore
{
    // TeenPatty 固定牌牌数3张
    //所有花色
    private $handCardsColor = [];
    //所有点数
    private $handCardsNum = [];
    //pr操作需要的牌组分数
    private $prHandCardsScore = 0;
    //牌组名称
    private $groupName;
    //牌组得分
    private $handCardsScore;

    //所有癞子牌
    private $allXCardList = [];

    public function __construct($card1, $card2, $card3, $cls)
    {
        $this->allXCardList = get_all_xcard_by_cls($cls);
        $handCards = [1 => $card1, 2 => $card2, 3 => $card3];
        //执行癞子替换最高牌型
        $this->replaceCard($handCards);
        if (!$this->groupName) {
            $this->getHandCardGroupNameRun();
        }
        //执行癞子替换最高牌型
        $this->getHandCardsScoreRun();
        $this->getPrHandCardsScoreRun();
    }

    //获取牌型类别
    public function getGroupName(): string
    {
        return $this->groupName;
    }

    //获取手牌分数
    public function getHandCardsScore(): int
    {
        return $this->handCardsScore;
    }

    //获取pr手牌分数
    public function getPrHandCardsScore(): int
    {
        return $this->prHandCardsScore;
    }


    /**
     * 执行癞子替换最高牌型
     * @param mixed $cards：数组下标从1开始，固定有三个元素代表三张牌，每一个元素为卡牌的编号
     * @return void
     */
    private function replaceCard($cards)
    {
        // 癞子牌列表，数组元素为癞子的卡牌编号
        $xCardList = [];
        // 非癞子牌的点数(1-13)
        $normalCardPointList = [];
        // 非癞子牌的花色(方块为fk，红桃为ht，黑桃为hht，梅花为mh)
        $normalCardColorList = [];

        // 遍历三张牌，区分是普通牌还是癞子牌，分别记录
        foreach ($cards as $val) {
            if (in_array($val, $this->allXCardList)) {
                $xCardList[] = $val;
            } else {
                $cardInfo = get_card_info($val);
                $normalCardPointList[] = $cardInfo['num'];
                $normalCardColorList[] = $cardInfo['type'];
            }
        }

        // 计算三张手牌里有几张癞子，不同的癞子数可能有不同的情况
        $xCardCnt = count($xCardList);

        // 没有癞子 说明不用替换 保持原样就行
        if ($xCardCnt == 0) {
            $this->handCardsColor = $normalCardColorList;
            $this->handCardsNum = $normalCardPointList;
            return;
        }

        // 三张全是癞子，替换成牌力值最大的牌组，为豹子AAA
        if ($xCardCnt == 3) {
            $this->groupName = Common::ZJ_GROUP_NAME_BAOZI;
            // 固定为AAA
            $this->handCardsNum = [1, 1, 1];
            return;
        }

        // 两张癞子，取非癞子的那张牌组合成豹子
        if ($xCardCnt == 2) {
            $this->groupName = Common::ZJ_GROUP_NAME_BAOZI;
            $point = array_pop($normalCardPointList);
            // 固定为非癞子牌的豹子
            $this->handCardsNum = [$point, $point, $point];
            return;
        }

        // 一张癞子，执行判定，对应多种情况
        if ($xCardCnt == 1) {
            sort($normalCardPointList);
            $subPoint = abs(($normalCardPointList[0] - $normalCardPointList[1]));
            $colorCnt = count(array_flip($normalCardColorList));
            /*
                老代码 这里有问题 
                问题1：如果发的牌是[2,13,66]，对应的是2,K,癞子,他下面的判断会把这种牌型当作顺子
                问题2: 如果发的牌是[2,5,66]，对应的是同花2同花5，理论上来讲癞子应该会替换成同花的最大单排A，组成同花2,5,A，但是下面的代码会把癞子替换成对子
            */
            if (in_array($subPoint, [1, 2, 11, 12])) {
                if ($colorCnt == 1) {
                    // 同花
                    $this->groupName = Common::ZJ_GROUP_NAME_TONGHUA_SHUNZI;
                } else {
                    // 顺子
                    $this->groupName = Common::ZJ_GROUP_NAME_SHUNZI;
                }
                // 同花顺
                if ($subPoint == 1 || $subPoint == 12) {
                    if ($normalCardPointList[0] == 2) {
                        $point = $normalCardPointList[0] - 1;
                    } else {
                        $point = $normalCardPointList[1] + 1;
                    }
                } else {
                    $point = $normalCardPointList[1] - 1;
                }
                $this->handCardsNum = $normalCardPointList;
                $this->handCardsNum[] = $point % 13;
                return;
            }
            if ($subPoint == 0) {
                // 对子
                $this->groupName = Common::ZJ_GROUP_NAME_BAOZI;
                $point = array_pop($normalCardPointList);
                $this->handCardsNum = [$point, $point, $point];
                return;
            }
            // 其余判定为散牌，取除癞子以外的最大单牌，组成对子
            $this->groupName = Common::ZJ_GROUP_NAME_DUIZI;
            if ($normalCardPointList[0] == 1) {
                $point = 1;
            } else {
                $point = $normalCardPointList[1];
            }
            $this->handCardsNum = $normalCardPointList;
            $this->handCardsNum[] = $point;
        }
    }




    //获取牌力值
    private function getHandCardGroupNameRun()
    {
        //优先判定高分值牌型
        //执行豹子判定
        $isBaozi = $this->getIsBaozi();
        if ($isBaozi) {
            $this->groupName = Common::ZJ_GROUP_NAME_BAOZI;
            return;
        }
        //执行同花判定
        $isTonghua = $this->getIsTonghua();
        //执行顺子判定
        $isShunzi = $this->getIsShunzi();
        //同花顺
        if ($isTonghua && $isShunzi) {
            $this->groupName = Common::ZJ_GROUP_NAME_TONGHUA_SHUNZI;
            return;
        }
        //同花
        if ($isTonghua && !$isShunzi) {
            $this->groupName = Common::ZJ_GROUP_NAME_TONGHUA;
            return;
        }
        //顺子
        if (!$isTonghua && $isShunzi) {
            $this->groupName = Common::ZJ_GROUP_NAME_SHUNZI;
            return;
        }
        //对子判定
        $isDuizi = $this->getIsDuizi();
        if ($isDuizi) {
            $this->groupName = Common::ZJ_GROUP_NAME_DUIZI;
            return;
        }
        //指定到最后判定为散牌
        $this->groupName = Common::ZJ_GROUP_NAME_SANPAI;
    }

    //计算牌力总分数
    private function getHandCardsScoreRun()
    {
        if (!isset(BASE_ZJ_TYPE_POWER_SCORE[$this->groupName])) {
            $this->handCardsScore = 0;
            return;
        }
        //牌型分值
        $groupScore = 0;
        //散牌分值
        $sanPaiScore = 0;
        $handCardsNumArr = $this->handCardsNum;
        if ($this->groupName == Common::ZJ_GROUP_NAME_DUIZI) {
            //对子特殊判定  22A < 332
            $numCntArr = array_count_values($handCardsNumArr);
            $cntNumArr = array_flip($numCntArr);
            $groupScore = BASE_ZJ_DIANSHU_SCORE[$cntNumArr[2]] * 2;
            $sanPaiScore = BASE_ZJ_DIANSHU_SCORE[$cntNumArr[1]];
        } elseif ($this->groupName == Common::ZJ_GROUP_NAME_SANPAI || $this->groupName == Common::ZJ_GROUP_NAME_TONGHUA) {
            //如果是散牌和同花  按照权重计算分
            $maxNum = in_array(1, $handCardsNumArr) ? 1 : max($handCardsNumArr);
            //如果有1存在 则计算为最大值
            $maxKey = array_search($maxNum, $handCardsNumArr);
            unset($handCardsNumArr[$maxKey]);
            $groupScore += BASE_ZJ_DIANSHU_SCORE[$maxNum] * 100;
            $groupScore += BASE_ZJ_DIANSHU_SCORE[max($handCardsNumArr)] * 10;
            $groupScore += BASE_ZJ_DIANSHU_SCORE[min($handCardsNumArr)];
        } else {
            //以上牌型都不存在
            foreach ($handCardsNumArr as $val) {
                $groupScore += BASE_ZJ_DIANSHU_SCORE[$val];
            }
        }
        //执行牌型加权计算
        $this->handCardsScore = BASE_ZJ_TYPE_POWER_SCORE[$this->groupName] * $groupScore + $sanPaiScore;
    }

    //计算牌力第一判断分数
    private function getPrHandCardsScoreRun()
    {
        if (!isset(BASE_ZJ_TYPE_POWER_SCORE[$this->groupName])) {
            $this->prHandCardsScore = 0;
            return;
        }
        //牌型分值
        $groupScore = 0;
        $handCardsNumArr = $this->handCardsNum;
        if ($this->groupName == Common::ZJ_GROUP_NAME_DUIZI) {
            //对子特殊判定  22A < 332
            $numCntArr = array_count_values($handCardsNumArr);
            $cntNumArr = array_flip($numCntArr);
            $groupScore = BASE_ZJ_DIANSHU_SCORE[$cntNumArr[2]] * 2;
        } elseif ($this->groupName == Common::ZJ_GROUP_NAME_TONGHUA) {
            //如果是同花  按照权重计算分
            $maxNum = in_array(1, $handCardsNumArr) ? 1 : max($handCardsNumArr);
            $groupScore += BASE_ZJ_DIANSHU_SCORE[$maxNum] * 100;
        } elseif ($this->groupName == Common::ZJ_GROUP_NAME_SANPAI) {
            //如果是散牌  按照权重计算分
            $maxNum = in_array(1, $handCardsNumArr) ? 1 : max($handCardsNumArr);
            //如果有1存在 则计算为最大值
            $maxKey = array_search($maxNum, $handCardsNumArr);
            unset($handCardsNumArr[$maxKey]);
            $groupScore += BASE_ZJ_DIANSHU_SCORE[$maxNum] * 100;
            $groupScore += BASE_ZJ_DIANSHU_SCORE[max($handCardsNumArr)] * 10;
        } elseif ($this->groupName == Common::ZJ_GROUP_NAME_SHUNZI) {
            //以上牌型都不存在
            foreach ($handCardsNumArr as $val) {
                $groupScore += BASE_ZJ_DIANSHU_SCORE[$val];
            }
        } else {
            $groupScore = 100;
        }
        //执行牌型加权计算
        $this->prHandCardsScore = BASE_ZJ_TYPE_POWER_SCORE[$this->groupName] * $groupScore;
    }

    //同花 3个花色相同
    private function getIsTonghua(): bool
    {
        $colorArr = $this->handCardsColor;
        $res = array_flip($colorArr);
        if (count($res) == 1) {
            return true;
        } else {
            return false;
        }
    }

    //豹子规则  3个点数相同
    private function getIsBaozi(): bool
    {
        $numArr = $this->handCardsNum;
        $res = array_flip($numArr);
        if (count($res) == 1) {
            return true;
        } else {
            return false;
        }
    }

    //对子规则 2个点数相同
    private function getIsDuizi(): bool
    {
        $numArr = $this->handCardsNum;
        $res = array_flip($numArr);
        if (count($res) == 2) {
            return true;
        } else {
            return false;
        }
    }

    //识别是否为顺子
    private function getIsShunzi(): bool
    {
        sort($this->handCardsNum);
        //特殊规则
        $baseCardsArr = [[1, 2, 3], [1, 12, 13], [$this->handCardsNum[1] - 1, $this->handCardsNum[1], $this->handCardsNum[1] + 1]];
        $handNumArr = $this->handCardsNum;
        foreach ($baseCardsArr as $v) {
            if (empty(array_diff($v, $handNumArr))) {
                return true;
            }
        }
        return false;
    }




    //获取最大牌的花色
    public static function getTpWarMaxCardColor($dealCards, $groupName)
    {
        $handCardsNumColor = [];
        $handCardsNum = [];
        $colorScoreList = [];
        foreach ($dealCards as $key => $val) {
            $info = get_card_info($val);
            $handCardsNumColor[BASE_ZJ_DIANSHU_SCORE[$info['num']]] = TP_WAR_MAX_CARD_COLOR_SCORE[$info['type']];
            if ($groupName == Common::ZJ_GROUP_NAME_BAOZI) {
                $colorScoreList[] = TP_WAR_MAX_CARD_COLOR_SCORE[$info['type']];
            }
            if ($groupName == Common::ZJ_GROUP_NAME_DUIZI) {
                $handCardsNum[$key] = BASE_ZJ_DIANSHU_SCORE[$info['num']];
            }
        }
        if ($groupName == Common::ZJ_GROUP_NAME_DUIZI) {
            //对子 比单牌的花色
            $count = array_count_values($handCardsNum);
            $maxCardColor = $handCardsNumColor[array_flip($count)[1]];
        } else if ($groupName == Common::ZJ_GROUP_NAME_BAOZI) {
            //豹子
            sort($colorScoreList);
            $maxCardColor = array_pop($colorScoreList);
        } else {
            //散牌 同花 顺子 同花顺 比第一大牌的花色
            ksort($handCardsNumColor);
            $maxCardColor = array_pop($handCardsNumColor);
        }
        return $maxCardColor;
    }
}
