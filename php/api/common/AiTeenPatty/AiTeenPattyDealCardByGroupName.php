<?php
/**
 * @todo tp类玩法 根据指定条件发牌
 */
namespace common\AiTeenPatty;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;

class AiTeenPattyDealCardByGroupName
{
    //牌池
    private $pool;
    //发到的牌
    private $userDealCards = [];

    //玩法
    private $cls;
    //=========================

    //按照权重发牌 Common::USER_TAG_ROOKIE
    private $buffType;

    //=========================
    //按照索引区间发牌
    public $minIndex = 0;
    public $maxIndex = 22099;


    //=========================
    //区间发牌最小值
    public $minScore = 432;
    //区间发牌最大分值
    public $maxScore = 960000000000;
    //区间发牌是否包含边界值
    public $isInclude = true;

    public function __construct($pool)
    {
        $this->pool = $pool;
    }

    //获取发到的牌
    public function getDealCards(string $groupName, int $cls, $buffType = Common::USER_TAG_NORMAL_BUFF): array
    {
        $this->buffType = $buffType;
        $this->cls = $cls;
        //每次发牌前重置发牌数据
        $this->userDealCards = [];
        $this->dealCardByGroupNameRun($groupName);
        return $this->userDealCards;
    }

    //获取当前剩余牌池
    public function getCardPool(): array
    {
        return $this->pool;
    }

    //根据规则生成执行的牌组
    private function dealCardByGroupNameRun($groupName)
    {
        switch ($groupName) {
            //==========================这里只针对tp基础牌型发牌==========================
            case Common::ZJ_GROUP_NAME_BAOZI:
            case Common::ZJ_GROUP_NAME_TONGHUA_SHUNZI:
            case Common::ZJ_GROUP_NAME_SHUNZI:
            case Common::ZJ_GROUP_NAME_TONGHUA:
            case Common::ZJ_GROUP_NAME_DUIZI:
            case Common::ZJ_GROUP_NAME_SANPAI:
                return $this->dealCardByGroupName($groupName);

                $this->getRandCardByBaseGroup($this->getBaseGroupCardListByGroupName($groupName));
                break;
            //==========================================================================
            case Common::ZJ_GROUP_NAME_INDEX_REGION:
                \lbase\Log::error('TpScore-ZJ_GROUP_NAME_INDEX_REGION-不应该有代码调用这里', [
                    'minIndex' => $this->minIndex,
                    'maxIndex' => $this->maxIndex,
                    'cls' => $this->cls,
                ]);

                $this->getRandCardByBaseGroup($this->getRandCardByIndexRegion());
                break;
            case Common::ZJ_GROUP_NAME_SCORE_REGION:
                return $this->dealCardByScoreRegion();

                $this->getRandCardByBaseGroup($this->getRandCardByScoreRegion());
                break;
            case Common::ZJ_GROUP_NAME_WEIGHT:
                return $this->dealCardByWeightTable();

                $this->getRandCardByBaseGroup($this->getRandCardByWeight());
                break;
            default:
                return $this->dealCardRandom();

                //随机牌
                $this->getRandCardByNum();
                break;
        }
    }

    /**
     * 根据分组名称获取发牌牌组
     * @param string $groupName
     * @return array
     * @deprecated 不支持癞子
     */
    private function getBaseGroupCardListByGroupName(string $groupName): array
    {
        $start = GROUP_INDEX_START_END_LIST[$groupName][0];
        $end = GROUP_INDEX_START_END_LIST[$groupName][1];
        $len = $end - $start;
        return array_slice(TP_CARD_LIST, $start, $len);
    }

    /**
     * 通过发牌枚举值区间执行发牌
     * @return array
     * @deprecated 不支持癞子
     */
    private function getRandCardByIndexRegion(): array
    {
        $start = max(0, $this->minIndex);
        $end = min(22099, $this->maxIndex);
        $len = max(1, ($end - $start));
        return array_slice(TP_CARD_LIST, $this->minIndex, $len);
    }

    /**
     * 按照分值区间执行发牌，返回多个 cards 即多副手牌
     * @return array
     * @deprecated 分数反查不支持癞子
     */
    private function getRandCardByScoreRegion(): array
    {
        // 根据一个分值范围，返回一个 dealCardWeight.xml 的发牌索引
        $dealCardWeightObj = new AiTeenPattyPrDealCadWeight();
        $dealCardIndex = $dealCardWeightObj->getIndexByScoreRegion($this->minScore, $this->maxScore, $this->cls, $this->buffType);

        // 根据 dealCardWeight.xml 权重表索引得到 TP_CARD_LIST 中的区间，这个区间内是花色差异
        $dealCardWeightObj->getDealCardRegionByIndex($dealCardIndex);

        // 从 TP_CARD_LIST 截取一个区间的发牌
        $this->minIndex = $dealCardWeightObj->getMinIndex();
        $this->maxIndex = $dealCardWeightObj->getMaxIndex();
        $hands = $this->getRandCardByIndexRegion();

        // 研究一下 TPJoker TPAK47 的发牌，看看发的牌是不是符合分值区间
        if ($this->cls != \llogic\common\Struct::GAME_TYPE_TEEN_PATTI) {
            $phand2wscore = [];
            foreach ($hands as $hand) {
                $phand = "";
                $wscore = \llogic\game\tpcard\TpScoreAlgo::GetClsCardsWscore($this->cls, $hand, $phand);
                $phand2wscore[$phand] = $wscore;
            }
            // 给 1 分的容忍，因为可能有小数
            $in_score_region = array_map(fn($wscore) => $this->minScore <= $wscore && $wscore <= $this->maxScore + 1, $phand2wscore);
            $all_in_score_region = array_reduce($in_score_region, fn($carry, $item) => $carry && $item, true);
            if (!$all_in_score_region) {
                \lbase\Log::info('getRandCardByScoreRegion', [
                    'minScore' => $this->minScore,
                    'maxScore' => $this->maxScore,
                    'phand2wscore' => $phand2wscore,
                    'in_score_region' => $in_score_region,
                    'getDealCardIndexDebugInfo' => $dealCardWeightObj->getDealCardIndexDebugInfo($dealCardIndex),
                ]);
            }
        }

        return $hands;
    }

    /**
     * 通过权重执行发牌
     * @return array
     * @deprecated 不支持癞子
     */
    private function getRandCardByWeight(): array
    {
        $dealCardWeightObj = new AiTeenPattyPrDealCadWeight();
        // dealCardWeight.xml 是牌组的权重表，根据它的权重配置得到该表某一项的索引
        $dealCardIndex = $dealCardWeightObj->getDealCardWeightIndexByGameTypeAndBuffType($this->cls, $this->buffType);
        // 根据 dealCardWeight.xml 权重表索引得到 TP_CARD_LIST 中的区间
        $dealCardWeightObj->getDealCardRegionByIndex($dealCardIndex);
        $this->minIndex = $dealCardWeightObj->getMinIndex();
        $this->maxIndex = $dealCardWeightObj->getMaxIndex();
        // 获取这个区间内的所有三张牌组合
        return $this->getRandCardByIndexRegion();
    }

    private function dealCardByWeightTable()
    {
        $dealCardWeightObj = new AiTeenPattyPrDealCadWeight();
        // dealCardWeight.xml 是牌组的权重表，根据它的权重配置得到该表某一项的索引
        $dealCardIndex = $dealCardWeightObj->getDealCardWeightIndexByGameTypeAndBuffType($this->cls, $this->buffType);
        // 根据 dealCardWeight.xml 权重表索引得到 TP_CARD_LIST 中的区间
        $dealCardWeightObj->getDealCardRegionByIndex($dealCardIndex);
        $minIndex = $dealCardWeightObj->getMinIndex();
        $maxIndex = $dealCardWeightObj->getMaxIndex();

        // 顺位小的分数大
        $minScore = TP_CARD_SCORE[$maxIndex] ?? 0;
        $maxScore = TP_CARD_SCORE[$minIndex] ?? 960000000000;
        $this->dealCardByMinMaxScore($minScore, $maxScore, ['title' => "dealCardByWeightTable", 'maxRank' => $maxIndex, 'minRank' => $minIndex]);
    }

    private function dealCardByGroupName(string $groupName)
    {
        $minScore = \llogic\game\tpcard\TpScoreAlgo::GroupMinScoreMap[$groupName] ?? 0;
        $maxScore = \llogic\game\tpcard\TpScoreAlgo::GroupMaxScoreMap[$groupName] ?? 960000000000;
        $this->dealCardByMinMaxScore($minScore, $maxScore, ['title' => "dealCardByGroupName", 'groupName' => $groupName]);
    }

    private function dealCardByScoreRegion()
    {
        $this->dealCardByMinMaxScore($this->minScore, $this->maxScore, ['title' => "dealCardByScoreRegion"]);
    }

    private function dealCardRandom()
    {
        $this->dealCardByMinMaxScore(0, 960000000000, ['title' => "dealCardRandom"]);
    }

    private function dealCardByMinMaxScore(float $min_score, float $max_score, array $caller_info)
    {
        \llogic\game\tpcard\TpScoreAlgo::MinMaxScoreToPrettyListRange($min_score, $max_score, $this->cls, $pretty_ls, $first_index, $last_index);

        // 基础重试次数
        $base_cnt = $last_index - $first_index;
        $base_cnt = min($base_cnt, 20);
        $base_cnt = max($base_cnt, 3);
        // 额外重试次数
        $extra_cnt = 10;

        $bad_phands = [];
        $try_cnt = 1;
        while (true) {
            // 超过基础重试次数就扩大范围
            if ($try_cnt > $base_cnt) {
                $index = rand(0, count($pretty_ls) - 1);
            } else {
                $index = rand($first_index, $last_index - 1);
            }

            $pcards = $pretty_ls[$index] ?? '8z9xJc';
            $cards = \llogic\game\tpcard\TpCardAlgo::PrettyParseCards($pcards);

            $no_joker_cards = $cards;
            if ($this->cls == \llogic\common\Struct::GAME_TYPE_TEEN_PATTI_JOKER) {
                $no_joker_cards = array_filter($cards, fn($card) => $card != G_CARD);
            }
            $tmp_jiao_ji = array_intersect($this->pool, $no_joker_cards);
            if (count_one_dimensional_array($tmp_jiao_ji) != count_one_dimensional_array($no_joker_cards)) {
                // 牌池的牌不够发放此牌型，放弃发放，但如果是最后一次就会就忽略这个问题了
                $bad_phands[] = $pcards;
                if ($try_cnt < $base_cnt + $extra_cnt) {
                    $try_cnt++;
                    continue;
                }
            }

            $this->userDealCards = $cards;
            break;
        }

        $marks = [];
        if ($try_cnt > $base_cnt) {
            $marks[] = '重试太多';
            if (count($bad_phands) >= $base_cnt + $extra_cnt) {
                $marks[] = '扩大失败';
            } else if ($try_cnt > $base_cnt) {
                $marks[] = '扩大成功';
            }
            $marks[] = $bad_phands;
            $pool = $this->pool;
            sort($pool);
            $marks[] = \llogic\game\tpcard\TpCardAlgo::PrettyFormatCards($pool);
        }

        // 此时再更新 pool 方便前面记录修改前的 pool
        $this->pool = del_b_arr_from_a_arr($this->pool, $this->userDealCards);

        \lbase\Log::info("TpScore-dealCard", [
            'caller_info' => $caller_info,
            'min_score' => $min_score,
            'max_score' => $max_score,
            'cls' => $this->cls,
            'first_index' => $first_index,
            'last_index' => $last_index,
            'pcards' => $pcards,
            'userDealCards' => $this->userDealCards,
            'base_cnt' => $base_cnt,
            'extra_cnt' => $extra_cnt,
            'try_cnt' => $try_cnt,
            'bad_phands' => $bad_phands,
            'marks' => $marks,
        ]);
    }

    /**
     * 根据 TP_CARD_LIST 的一个子片段获取发牌，结果是设置好 userDealCards
     * @param array $baseGroup
     * @return void
     */
    private function getRandCardByBaseGroup(array $baseGroup)
    {
        // 根据不同玩法实例化不同对象
        // 以TP玩法为基础，还可以衍生出TPAK47 和 TPJoker
        $obj = AiTeenPattyXCardReplace::create($this->cls);

        //乱序数组
        shuffle($baseGroup);

        foreach ($baseGroup as $val) {
            //对比牌池中的牌 是否包含当前的牌型
            //根据玩法执行癞子牌替换
            $obj->replaceXCard($val, $this->pool);
            // 此处的牌还没有做癞子牌替换
            $notXcardDealCards = $obj->getNotXcardDealCards();
            $tmp_jiao_ji = array_intersect($this->pool, $notXcardDealCards);
            if (count_one_dimensional_array($tmp_jiao_ji) != count_one_dimensional_array($notXcardDealCards)) {
                //牌池的牌不够发放此牌型  放弃发放
                continue;
            }
            // 此处为替换某些牌为癞子牌后的牌
            $this->userDealCards = $obj->getOverReplaceDealCards();
            $this->pool = del_b_arr_from_a_arr($this->pool, $this->userDealCards);
            break;
        }
        if (!$this->userDealCards) {
            $this->getRandCardByNum();
        }
    }

    /**
     * 根据数量获取相应的牌组
     * @deprecated 不支持癞子
     */
    private function getRandCardByNum()
    {
        $baseGroup = TP_CARD_LIST;
        shuffle($baseGroup);
        $this->getRandCardByBaseGroup($baseGroup);
    }

}
