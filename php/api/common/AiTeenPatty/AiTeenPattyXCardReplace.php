<?php
/**
 * @todo   TeenPatty ai 赖子牌替换逻辑
 * <AUTHOR>
 * //@deprecated
 */
namespace common\AiTeenPatty;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;

/**
 * 给标准的发牌结果替换出癞子
 * @deprecated 发牌那里用新代码了，这个类不再使用
 */
class AiTeenPattyXCardReplace
{

    //手牌
    protected $dealCards;

    //当前牌池
    protected $pool;

    //tp玩法癞子替换类
    const GAME_TYPE_XCARD_REPLACE_CLSS_LIST = [
        Common::GAME_TYPE_TEEN_PATTI_AK47 => "XcardReplaceAk47",
        Common::GAME_TYPE_TEEN_PATTI_JOKER => "XcardReplaceJoker"
    ];
    //牌型名称
    protected $groupName;

    //分值
    protected $groupScore;

    //被替换的牌
    protected $replaceCarderIndexList;

    //要替换的癞子列表
    protected $xCardList;

    //被替换的牌
    protected $replaceCardList = [];

    //不包含癞子的发牌
    //在替换癞子的时候已经验证了牌池中是否有足够的癞子可以替换
    protected $notXcardDealCards = [];

    //替换以后的牌
    protected $overReplaceDealCards = [];

    //玩法
    protected $cls;

    public static function create($cls)
    {
        if (isset(self::GAME_TYPE_XCARD_REPLACE_CLSS_LIST[$cls])) {
            $class = '\\common\\AiTeenPatty\\XcardReplace\\' . self::GAME_TYPE_XCARD_REPLACE_CLSS_LIST[$cls];
            if (class_exists($class)) {
                return new $class($cls);
            }
        }
        return new static($cls);
    }


    public function __construct($cls)
    {
        $this->cls = $cls;
    }

    //获取当前牌池
    public function getPool()
    {
        //替换掉的牌 再放入到牌池中
        $this->pool = array_merge($this->pool, $this->replaceCardList);
        return $this->pool;
    }

    //判定是否要换牌
    public function replaceXCard($dealCards, $pool)
    {
        //普通模式下 不执行换牌逻辑
        $this->initVal();
        $this->dealCards = sort_teen_patty_cards_asc($dealCards);
        $this->notXcardDealCards = $this->dealCards;
        $this->overReplaceDealCards = $this->dealCards;
        $this->pool = $pool;
    }


    //返回用于比较不包含癞子的牌
    public function getNotXcardDealCards(): array
    {
        return $this->notXcardDealCards;
    }

    //返回被替换之后的牌
    public function getOverReplaceDealCards(): array
    {
        return $this->overReplaceDealCards;
    }

    //返回原始牌
    public function getOriginalDealCards()
    {
        return $this->dealCards;
    }

    //执行替换癞子逻辑
    protected function getReplaceCarderIndexList()
    {
        $this->setGroupNameAndScore();
        switch ($this->groupName) {
            case Common::ZJ_GROUP_NAME_TONGHUA_SHUNZI:
            case Common::ZJ_GROUP_NAME_SHUNZI:
                $replaceCarderIndexList = $this->replaceShunZi();
                break;
            case Common::ZJ_GROUP_NAME_TONGHUA:
                $replaceCarderIndexList = $this->replaceTongHua();
                break;
            case Common::ZJ_GROUP_NAME_DUIZI:
                $replaceCarderIndexList = $this->replaceDuiZi();
                break;
            case Common::ZJ_GROUP_NAME_BAOZI:
                $replaceCarderIndexList = $this->replaceBaozi();
                break;
            default:
                //散牌 只能随机替换
                $replaceCarderIndexList = $this->replaceSanPai();
        }
        $this->replaceCarderIndexList = $replaceCarderIndexList;
    }

    //替换顺子癞子
    //豹子       随便去除一张
    protected function replaceBaozi(): array
    {
        $replaceCarderIndex[] = random_int(0, 2);
        return $replaceCarderIndex;
    }

    //散牌
    protected function replaceSanPai(): array
    {
        $replaceCarderIndex[] = random_int(0, 2);
        return $replaceCarderIndex;
    }

    //替换同花或者顺子
    //同花顺 顺子
    //如果有A存在  则那个牌都可以
    //如果是 234牌型 只能去掉3
    //否则 只能取消最大 或者 最小
    protected function replaceShunZi(): array
    {
        $maxCardPoint = get_card_info($this->dealCards[2])['num'];
        if ($maxCardPoint == 1) {
            //有a存在 则随机一张
            shuffle($this->dealCards);
            $replaceCarderIndex[] = 0;
        } elseif ($maxCardPoint == 4) {
            //最大牌是4的顺子
            $replaceCarderIndex[] = 1;
        } else {
            //只能取消最大或者最小
            $replaceCarderIndex[] = random_int(0, 1) ? 2 : 0;
        }
        return $replaceCarderIndex;
    }

    //同花 同花只有A同花存在
    //        A K J  只能取消中间
    //============================
    //        A 13 10
    //        A 13 9
    //        A 13 8
    //        A 13 7
    //        A 13 6
    //        A 13 5
    //        A 13 4
    //================================
    //        以上范围  可以取消 最大 和 第二大
    protected function replaceTongHua(): array
    {
        $akjScore = 334100000;
        $ak10Score = 334000000;
        $ak4Score = 333400000;
        if ($this->groupScore == $akjScore) {
            //A K J  只能取消中间
            $replaceCarderIndex[] = 1;
        } elseif ($this->groupScore >= $ak10Score && $this->groupScore <= $ak4Score) {
            //只能取消最大或者第二大
            $replaceCarderIndex[] = random_int(1, 2);
        } else {
            //其余只能取消最大
            $replaceCarderIndex[] = 2;
        }
        return $replaceCarderIndex;

    }

    //指定对子替换
    //对子  去掉对子里一张
    protected function replaceDuiZi(): array
    {
        $card0Point = get_card_info($this->dealCards[0])['num'];
        $card1Point = get_card_info($this->dealCards[1])['num'];
        if ($card0Point == $card1Point) {
            $replaceCarderIndex[] = random_int(1, 1);
        } else {
            $replaceCarderIndex[] = random_int(1, 2);
        }
        return $replaceCarderIndex;
    }

    //获取手牌的分值和牌型
    protected function setGroupNameAndScore()
    {
        $cardsInfo = [];

        if (0x01) {
            \llogic\game\tpcard\TpScoreAlgo::TpApiSetupCardScoreEtc($this->cls, $cardsInfo, $this->dealCards, 'cards_score');
            $this->groupName = $cardsInfo['group_name'];
            $this->groupScore = $cardsInfo['cards_score'];
        } else {
            $scoreObj = new AiTeenPattyHandCardsScore($this->dealCards[0], $this->dealCards[1], $this->dealCards[2], $this->cls);
            $this->groupName = $scoreObj->getGroupName();
            $this->groupScore = $scoreObj->getHandCardsScore();
        }
    }

    //执行换牌
    protected function replaceDo()
    {
        if (!$this->replaceCarderIndexList || !$this->xCardList) {
            return;
        }
        $xCardList = $this->xCardList;
        shuffle($this->xCardList);
        foreach ($this->replaceCarderIndexList as $replaceCarderIndex) {
            //如果没有足够的癞子执行替换
            if (!$xCardList) {
                break;
            }
            //被替换掉的牌
            $this->replaceCardList[$replaceCarderIndex] = $this->dealCards[$replaceCarderIndex];
            //剔除被替换的牌
            unset($this->notXcardDealCards[$replaceCarderIndex]);
            //把固定的鬼牌癞子 放入手牌中
            //替换完成的牌
            $this->overReplaceDealCards[$replaceCarderIndex] = array_pop($xCardList);
        }
    }

    //清除初始值
    protected function initVal()
    {
        $this->replaceCarderIndexList = [];
        $this->replaceCardList = [];
        $this->groupName = "";
        $this->groupScore = 0;
    }
}
