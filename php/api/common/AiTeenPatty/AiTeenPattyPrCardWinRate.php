<?php
/**
 * @todo   TeenPatty 获取手牌的胜率
 * <AUTHOR>
 */
namespace common\AiTeenPatty;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;

class AiTeenPattyPrCardWinRate
{

    //玩法
    private $cls;

    //人数
    private $playerCnt;

    //顺位胜率配置表路径
    private $teenPattyXmlPath = __DIR__ . '/';

    //顺位表
    private $sequenceWinRateTable = [];

    //使用的顺位表配置模板
    public $teenPattyXmlTpl = [
        Common::GAME_TYPE_TEEN_PATTI => 'tpWinRate.xml',
        Common::GAME_TYPE_TEEN_PATTI_JOKER => 'jokerWinRate.xml',
        Common::GAME_TYPE_TEEN_PATTI_AK47 => 'ak47WinRate.xml',
    ];


    public function __construct($cls, $playerCnt)
    {
        $this->cls = $cls;
        $this->playerCnt = max(2, $playerCnt); //最少2个人
        $this->sequenceWinRateTable = $this->setSequenceWinRateTable();
    }

    public static function Demo1()
    {
        $obj = new AiTeenPattyPrCardWinRate(Common::GAME_TYPE_TEEN_PATTI, 2);
        $xy = [];
        $ls_len = count(TP_CARD_SCORE);
        foreach ($obj->sequenceWinRateTable as $item) {
            $sequence = $item['baseSeq'];
            $winRate = (float) $item['winRate2'];
            $rate2 = ($ls_len - 1 - $sequence) / $ls_len;
            $rate2 = round($rate2, 4);
            $xy[] = [$rate2, $winRate];
        }
        return $xy;
    }

    // 获取最顺位对应的胜率列表
    // 这个顺位是指 TP_CARD_SCORE 的索引
    public function getWinRateBySequence($sequence)
    {
        $rate2 = \llogic\game\tpcard\TpScoreAlgo::GetWinRateByRank($sequence, $this->cls, $this->playerCnt);
        // 用我的算法不查表了，支持不同玩法不同的发牌表长度
        return $rate2;


        if (!$this->sequenceWinRateTable) {
            return floor(0);
        }

        static $sequenceListByCls = [];
        $sequenceList = $sequenceListByCls[$this->cls] ?? null;
        if (!$sequenceList) {
            $sequenceList = array_column($this->sequenceWinRateTable, 'baseSeq');
            $sequenceListByCls[$this->cls] = $sequenceList;
        }

        $sequenceIndex = binarySearch($sequenceList, $sequence, true, 'asc')[0];
        $field = 'winRate' . $this->playerCnt;
        $rate = $this->sequenceWinRateTable[$sequenceIndex][$field] ?? 0;

        // 用我的算法计算一下，比较一下是否有差异
        $rate2 = \llogic\game\tpcard\TpScoreAlgo::GetWinRateByRank($sequence, $this->cls, $this->playerCnt);
        if (abs($rate2 - $rate) > 0.01 || 1) {
            $ls_len = count(\llogic\game\tpcard\TpScoreAlgo::GetSortedScoreList($this->cls));
            \lbase\Log::error('TpScore-getWinRateBySequence-计算偏差大', [
                'sequence' => $sequence,
                'cls' => $this->cls,
                'playerCnt' => $this->playerCnt,
                'rate' => $rate,
                'rate2' => $rate2,
                'ls_len' => $ls_len,
            ]);
        }

        return $rate;
    }


    // 根据玩法获取平均胜率 = 1 / 人数
    public function getAvgWinRateByPlayerCnt(): ?string
    {
        return bcdiv(1, $this->playerCnt, 3);
    }

    // 根据胜率获取顺位
    // 这个顺位是指 TP_CARD_SCORE 的索引
    // 这个实际上就可以 = (1 - winRate^(1 / (playerCnt - 1))) * count(TP_CARD_SCORE)
    public function getSequenceByWinRate($winRate)
    {
        $sequence2 = \llogic\game\tpcard\TpScoreAlgo::GetRankByWinRate($winRate, $this->cls, $this->playerCnt);
        // 用我的算法不查表了，支持不同玩法不同的发牌表长度
        return $sequence2;


        static $winRateListByClsByPlayerCnt = [];
        $winRateList = $winRateListByClsByPlayerCnt[$this->cls][$this->playerCnt] ?? null;
        if (!$winRateList) {
            $winRateList = array_column($this->sequenceWinRateTable, ('winRate' . $this->playerCnt));
            $winRateListByClsByPlayerCnt[$this->cls][$this->playerCnt] = $winRateList;
        }

        $winRateIndex = binarySearch($winRateList, $winRate, true)[0];
        $sequence = $this->sequenceWinRateTable[$winRateIndex]['baseSeq'] ?? 0;

        // 用我的算法计算一下，比较一下是否有差异
        if (abs($sequence2 - $sequence) > 100 || 1) {
            \lbase\Log::error('TpScore-getSequenceByWinRate-计算错误', [
                'winRate' => $winRate,
                'cls' => $this->cls,
                'playerCnt' => $this->playerCnt,
                'sequence' => $sequence,
                'sequence2' => $sequence2,
            ]);
        }

        return $sequence;
    }

    //根据类型获取顺位表  解析excel 生成的 xml
//    索引    顺位	    2人胜率	    2.5人胜率	3人	    3.5人	4人	    4.5人	    5人	    5.5人	    6人
//
//    0     150		    1	            2   	3	    4
//
//    1     560		    1	            2	    3	    4
    private function setSequenceWinRateTable(): array
    {
        // 使用静态变量来存储结果
        static $resultBycls = [];
        $result = $resultBycls[$this->cls] ?? null;

        // 检查是否已经有缓存的结果
        if ($result !== null) {
            return $result;
        }

        // 禁止引用外部xml实体
        $xmlObj = simplexml_load_file($this->teenPattyXmlPath . $this->teenPattyXmlTpl[$this->cls]);
        $result = json_decode(json_encode($xmlObj), 1)['row'];

        $resultBycls[$this->cls] = $result;
        return $result;
    }



}
