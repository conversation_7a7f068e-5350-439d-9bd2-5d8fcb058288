<?php
/**
 * @todo   TeenPatty ai逻辑 看牌后的逻辑
 * <AUTHOR>
 */
namespace common\AiTeenPatty;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;

class AiTeenPattyPrSee
{
    //玩家看牌的操作结果
    private $isSee = true;
    //玩家是否drop
    private $isDrop = false;
    //玩家是否加注
    private $isRaise = false;
    //是否主动发起比牌
    private $isInitiativeCheck = false;
    //是否被动的接受比牌
    private $isPassiveCheck = false;

    //初始化信息
    //当前操作玩家id
    private $playerIndex = 0;
    //玩家房间信息
    private $playerTableInfo = [];
    //玩家出牌序列
    private $playerOptNum = 0;
    // 所有比自己小的看牌的玩家的顺位差值总和
    private $allWinNumDValue = 0;
    //本次操作的玩家手牌
    private $playerHandCards = [];
    //本次操作的玩家手牌胜率
    private $playerCardWinRate = 0;

    //本次操作的玩家手牌对应的顺位
    private $playerCardWinNum = 0;
    //玩家是否可以操作比牌行为
    private $playerCanCheck = false;
    //当前房间id
    private $roomId = 0;

    //是否是抬叫局
    private $cryUp = false;
    //当前游戏进行到的轮数
    private $nowRound = 0;
    //桌子上所有玩家信息
    private $playerInfoList = [];

    //玩家初始的平均胜利
    private $playerInitAvgWinRate = 0;
    //玩家的预估顺位
    private $playerEstimatedWinNum = [];
    // 玩家的最高顺位
    // 顺位越大，牌越小，胜率越低
    private $maxEstimatedWinNum = 0;
    // 玩家的最低顺位
    private $minEstimatedWinNum = 0;

    //本场所有的加倍次数
    private $raiseCnt = 0;
    //玩家的决胜概率
    private $notDropRate = 0;
    //正在玩的玩家数量
    private $playerPlayingCnt = 0;
    //当前奖池总钱数
    private $poolAllAmount = 0;
    //当前平跟需要的钱数
    private $followAmount = 0;

    //AI 性格
    private $disposition = '';
    //ai的心里加倍次数上限
    private $aiRaiseCntLimit = 1;
    //ai 对应的性格的配置
    private $dispositionConfig = [];

    private $base;
    private $cls;

    public function __construct($playerInfoList, $playerIndex, $rtype, $circle, $roomId, $cryUp)
    {
        $this->cryUp = $cryUp;
        // 当前操作的玩家id
        $this->playerIndex = $playerIndex;
        // 解析房间信息
        $this->roomId = $roomId;
        $roomInfo = parse_room_type($rtype);
        $this->base = $roomInfo['base'];
        $this->cls = $roomInfo['cls'];

        // 当前游戏进行到的轮数
        $this->nowRound = $circle;

        // 桌子上所有玩家信息
        $this->playerInfoList = $playerInfoList;

        // 获取本次操作的玩家牌桌信息
        $this->setPlayerOptStatCnt();

        // 本次操作的玩家的手牌
        $postBackInfo = json_decode($this->playerTableInfo['card_base_wave_rate'], 1);
        // 玩家决胜概率
        $this->notDropRate = $postBackInfo['not_drop_rate'] ?? 0.75;

        // 用户顺位增加值
        $this->playerWinNumAdd = 0;


        // [当前操作玩家] ai 心里加倍次数上限
        $this->aiRaiseCntLimit = $postBackInfo['raise_cnt_limit'] ?? 1;

        // [当前操作玩家] ai 性格
        $this->disposition = $postBackInfo['disposition'] ?? Common::ZJ_DISPOSITION_STEADY;

        // [当前操作玩家] ai 性格配置
        $this->getAiDispositionConfig();

        // 本次操作的玩家的手牌
        $this->playerHandCards = $this->playerTableInfo['hand_card'];

        // 获取玩家真实手牌的顺位
        $cardScore = $this->playerTableInfo['card_rank'];

        // [当前操作玩家] 玩家手牌原始对应顺位
        $this->playerCardWinNum = \llogic\game\tpcard\TpScoreAlgo::ScoreToIndex0($cardScore);

        // 利用新代码计算一下顺位
        $pcards = "";
        $wscore = \llogic\game\tpcard\TpScoreAlgo::GetClsCardsWscore($this->cls, $this->playerHandCards, $pcards);
        $wrank = \llogic\game\tpcard\TpScoreAlgo::CalcScoreRank0($wscore, $this->cls);
        if ($wrank != $this->playerCardWinNum && $this->cls == \llogic\common\Struct::GAME_TYPE_TEEN_PATTI) {
            \lbase\Log::error("TpScore-AiTeenPattyPrSee-手牌顺位计算错误", [
                'playerIndex' => $this->playerIndex,
                'roomId' => $this->roomId,
                'playerHandCards' => $this->playerHandCards,
                'cardScore' => $cardScore,
                'pcards' => $pcards,
                'wscore' => $wscore,
                'playerCardWinNum' => $this->playerCardWinNum,
                'wrank' => $wrank,
            ]);
        }

        // 采用新代码计算的顺位
        $this->playerCardWinNum = $wrank;

        // 获取玩家当前手牌的胜率
        $this->getPlayerCardWinRate();

        // 获取预估胜率
        // playerEstimatedWinNum 剔除掉不玩的玩家
        // 计算 allWinNumDValue 所有比自己小的看牌的玩家的顺位差值总和
        $this->setPlayerEstimatedWinNum();
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[当前操作玩家] 玩家桌子信息：' . json_encode($this->playerTableInfo));
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[当前操作玩家] 玩家预估胜率列表：' . json_encode($this->playerEstimatedWinNum));


        // 获取玩家最小的顺位
        $this->getMinEstimatedWinNum();
        // 获取玩家最大的顺位
        $this->getMaxEstimatedWinNum();

        // 获取当前玩家是否可以操作比牌
        $this->playerCanCheck = $this->getPlayerCanCheck();

        // 获取玩家的平均胜率
        $this->playerInitAvgWinRate = $this->getPlayerInitAvgWinRate();

        // 执行 drop 判定
        $this->setIsDrop();

        // 上诉，执行二次 drop 审判
        $this->set2playerIsDrop();

        // 执行加注判定
        $this->setIsRaise();
    }



    //获取看牌结果
    public function getIsSee(): bool
    {
        return $this->isSee;
    }

    //是否加注
    public function getIsRaise(): bool
    {
        /*2，看牌后，轮到玩家行动时
        a、跟牌时是否加倍
        当前已加倍次数>=加倍心理上限时，无论加倍概率如何，该用户不会在加倍*/
        if ($this->raiseCnt >= $this->aiRaiseCntLimit) {
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[计算加注概率] 加注判定：已加倍次数' . $this->raiseCnt . '>=心里加倍次数上限' . $this->aiRaiseCntLimit . '结果：' . json_encode(false));
            $this->isRaise = false;
        }
        return $this->isRaise;
    }

    //是否drop
    public function getIsDrop(): bool
    {
        return $this->isDrop;
    }

    //是否接受比牌
    public function getIsPassiveCheck(): bool
    {
        return $this->isPassiveCheck;
    }

    //是否主动发起比牌
    public function getIsInitiativeCheck(): bool
    {
        return $this->isInitiativeCheck;
    }


    private function setPlayerEstimatedWinNum()
    {
        $card_base_wave_rate = json_decode($this->playerTableInfo['card_base_wave_rate'], 1);
        $this->playerEstimatedWinNum = $card_base_wave_rate['estimated_list'] ?? null;

        $playerTablePlayingList = array_column($this->playerInfoList, 'is_playing', 'uid');
        $playerTableSeeList = array_column($this->playerInfoList, 'see', 'uid');

        if (is_array($this->playerEstimatedWinNum)) {
            foreach ($this->playerEstimatedWinNum as $key => $val) {
                // 不在玩的从 playerEstimatedWinNum 里去掉
                if (empty($playerTablePlayingList[$key])) {
                    unset($this->playerEstimatedWinNum[$key]);
                }
                // 在玩的，并且已经看过牌的，并且比自己手牌顺位小的，计算比自己顺位小的差值总和
                if (!empty($playerTablePlayingList[$key]) && !empty($playerTableSeeList[$key])) {
                    if ($val < $this->playerCardWinNum) {
                        $this->allWinNumDValue = ($this->playerCardWinNum - $val) + $this->allWinNumDValue;
                    }

                }
            }
        }
    }


    //获取本次操作的ai性格
    private function getAiDispositionConfig()
    {
        $dispositionConfigList = RedisOpt::getTpAiDispositionConfig();
        $this->dispositionConfig = $dispositionConfigList[$this->disposition] ?? [];
    }


    //只剩二人时，那么AI随机弃牌概率中时，不会直接弃牌，而是会先随机是否比牌，F概率会直接比牌，随不中再弃
    //F概率=牌型胜率*奖池中能赢到的钱/本次开牌需要花费的金额
    private function set2playerIsDrop()
    {
        //$avgWinRateDynamic = isset($this->dispositionConfig['avgWinRateDynamic'])?$this->dispositionConfig['avgWinRateDynamic']:2;
        //$playerInitAvgWinRateHalf = bcmul($this->playerInitAvgWinRate,$avgWinRateDynamic,2);
        //if($this->isDrop && $this->playerPlayingCnt==2 && $this->playerCardWinRate>$playerInitAvgWinRateHalf){
        if ($this->isDrop && $this->playerPlayingCnt == 2) {
            if ($this->playerCanCheck) {
                //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[当前操作玩家] 只剩二人时，那么AI随机弃牌概率中时，不会直接弃牌，而是会先随机是否比牌，F概率会直接比牌，随不中再弃：');
                //执行二次审判
                $checkRate = $this->playerCardWinRate * $this->poolAllAmount / $this->followAmount;
                $f1 = $this->dispositionConfig['f1'] ?? 2;
                $checkRate = pow($checkRate, $f1);
                //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[当前操作玩家] F概率=(牌型胜率(' . $this->playerCardWinRate . ')*奖池中能赢到的钱(' . $this->poolAllAmount . ')/本次开牌需要花费的金额(' . $this->followAmount . '))^' . $f1 . ' = ' . $checkRate);
                $rand[1] = intval($checkRate * 10000);
                $rand[0] = 10000 - $rand[1];
                if (get_rand($rand)) {
                    //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[当前操作玩家] ：只剩二人时，改判成功 不drop 执行比牌');
                    $this->isDrop = false;
                    $this->isPassiveCheck = true;
                    $this->isInitiativeCheck = true;
                } else {
                    //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[当前操作玩家] ：只剩二人时，维持原判');
                }
            } else {
                //如果对方在闷牌 没有比牌条件时 根据自己的胜率再执行一次判定
                //牌型胜率>人均胜率时/0.97^已加倍次数时 则不执行弃牌
                $h1 = $this->dispositionConfig['h1'] ?? 1;
                //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[当前操作玩家] ：$h1');
                $PlayerCardWinRateLimit = $this->playerInitAvgWinRate / pow($h1, $this->raiseCnt);
                //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[当前操作玩家] ：$this->raiseCnt' . $this->raiseCnt);
                //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[当前操作玩家] ：playerCardWinRate' . $this->playerCardWinRate);
                //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[当前操作玩家] ：$PlayerCardWinRateLimit' . $PlayerCardWinRateLimit);
                if ($this->playerCardWinRate > $PlayerCardWinRateLimit) {
                    $this->isDrop = false;
                    $this->isPassiveCheck = true;
                    $this->isInitiativeCheck = true;
                }
            }

        }
        //否则维持原判
    }


    //是否需要弃牌
    private function setIsDrop()
    {
        //大于决胜概率时跟牌到底 不接受比牌也不主动发起比牌
        if ($this->playerCardWinRate >= $this->notDropRate) {
            $this->isDrop = false;
            $this->isInitiativeCheck = false;
            $this->isPassiveCheck = false;
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '决胜胜率：' . $this->notDropRate);
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '玩家手牌胜率：' . $this->playerCardWinRate);
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '玩家手牌胜率 大于 决胜概率 不drop 不接受比牌 也 不主动发起比牌');
            return;
        }
        //胜率高于Ai自身的不弃胜率（超过这个值不会弃牌）时，弃牌标签改为0（不同的AI会有不同的门槛，Ai生成时会在72%～85%之间随机，生成后，7天内不会改变，7天后重新随机）
        //胜率低于人均胜率的一半时，直接弃；
        //平均胜率干预参数
        $avgWinRateDynamic = $this->dispositionConfig['avgWinRateDynamic'] ?? 2;
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, ' 原有平均胜率干预参数：' . $avgWinRateDynamic);

        /*2，看牌后，轮到玩家行动时
        b、人均胜率系数
        当前已加倍次数>该值时，该玩家的人均胜率系数变为 原值*min(2.6,(1+(已加倍次数-加倍心理上限)^2))*/
        if ($this->raiseCnt > $this->aiRaiseCntLimit) {
            $avgWinRateDynamic = $avgWinRateDynamic * min(2.6, (1 + pow(($this->raiseCnt - $this->aiRaiseCntLimit), 2)));
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[平均胜率干预参数] 已加倍次数' . $this->raiseCnt . '>心里加倍次数上限' . $this->aiRaiseCntLimit . '结果$avgWinRateDynamic：' . json_encode($avgWinRateDynamic));
        }

        $playerInitAvgWinRateHalf = bcmul($this->playerInitAvgWinRate, $avgWinRateDynamic, 2);
        if ($this->playerCardWinRate < $playerInitAvgWinRateHalf) {
            $this->isDrop = true;
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, ' 人均胜率干预之后：' . $playerInitAvgWinRateHalf);
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, ' 玩家手牌胜率：' . $this->playerCardWinRate);
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, ' 胜率 低于 人均胜率干预之后，drop');
            return;
        }
        //当胜率高于人均的一半，但低于人均胜率时，会尝试比牌，如果无法比牌，会有A%的概率跟一轮，跟完后下一轮继续尝试比牌，如果无法比牌，则有B%的概率继续跟牌
        if ($this->playerCardWinRate > $playerInitAvgWinRateHalf && $this->playerCardWinRate < $this->playerInitAvgWinRate) {
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, ' 当前游戏轮数' . ($this->nowRound + 1));
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, ' 人均胜率干预之后：' . $playerInitAvgWinRateHalf);
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, ' 玩家手牌胜率：' . $this->playerCardWinRate);
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, ' 人均胜率：' . $this->playerInitAvgWinRate);
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, ' 比牌条件' . json_encode($this->playerCanCheck));
            //第三轮直接弃牌
            if ($this->nowRound >= 2) {
                $this->isDrop = true;
                //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, ' 胜率 低于 人均胜率干预之后，但低于人均胜率时 第三轮直接drop');
                return;
            }
            //如果没有比牌条件 则按照轮数公式进行随机
            if (!$this->playerCanCheck) {
                //A=90%-(当前场次已加倍次数*40%),随不中时弃牌
                if ($this->nowRound == 0) {
                    //a公式干预参数
                    $a1 = $this->dispositionConfig['a1'] ?? 0.9;
                    $a2 = $this->dispositionConfig['a2'] ?? 0.4;
                    //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, ' 胜率 低于 人均胜率干预之后，但低于人均胜率时 无比牌条件 第一轮 A=90%-(当前场次已加倍次数*40%),随不中时drop');
                    $rate = $a1 - ($this->raiseCnt * $a2);
                    //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, $a1 . '-(' . $this->raiseCnt . '*' . $a2 . ')=' . $rate);
                }
                //B=10%-(当前场次已加倍次数*4%),随不中时弃牌
                if ($this->nowRound == 1) {
                    //b公式干预参数
                    $b1 = $this->dispositionConfig['b1'] ?? 0.1;
                    $b2 = $this->dispositionConfig['b2'] ?? 0.04;
                    //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, ' 当胜率 高于 均胜率干预之后，但低于人均胜率时 无比牌条件 第二轮 B=10%-(当前场次已加倍次数*4%),随不中时drop');
                    $rate = $b1 - ($this->raiseCnt * $b2);
                    //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, $b1 . '-(' . $this->raiseCnt . '*' . $b2 . ')=' . $rate);
                }
                if ($rate <= 0) {
                    $this->isDrop = true;
                    //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '当胜率 高于 均胜率干预之后，但低于人均胜率时 无比牌条件 未随中 drop');
                    return;
                }
                //执行概率随机
                $randRes = (rand() / (getrandmax() + 1)) < $rate;
                if ($randRes) {
                    $this->isDrop = false;
                    $this->isRaise = false;
                    $this->isInitiativeCheck = false;
                    $this->isPassiveCheck = false;
                    //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '当胜率 高于 均胜率干预之后，但低于人均胜率时 无比牌条件 随中了 不drop');
                } else {
                    $this->isDrop = true;
                    //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '当胜率 高于 均胜率干预之后，但低于人均胜率时 无比牌条件 未随中 drop');
                }
            } else {
                //执行比牌操作
                //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '当胜率 高于 均胜率干预之后，但低于人均胜率时 有比牌条件 不drop 可以比牌 可以接受比牌');
                $this->isDrop = false;
                $this->isRaise = false;
                $this->isInitiativeCheck = true;
                $this->isPassiveCheck = true;
            }
            return;
        }
        //当场上有任何人的预估顺位，在自己的95%～124%，会有C的概率接受比牌，D的概率尝试比牌，比牌概率=C（跟当前倍数和接近自己的值有关），如果没随中则只跟不弃   124%以上就直接弃牌
        if ($this->playerCardWinNum <= 0 || $this->minEstimatedWinNum == 0) {
            $maxRatio = 0;
        } else {
            $maxRatio = bcdiv($this->playerCardWinNum, $this->minEstimatedWinNum, 2);
        }

        Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, $this->playerCardWinNum . '/' . $this->minEstimatedWinNum . '=' . $maxRatio);
        if ($maxRatio > 1.24) {
            Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, ' 顺位大于 1.24 drop');
            $this->isDrop = true;
            return;
        }
        if ($maxRatio >= 0.95 && $maxRatio <= 1.24) {
            Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, ' 顺位在0.95 - 1.24 之间');
            $this->isDrop = false;
            //随机被动接受比牌概率
            $c1 = $this->dispositionConfig['c1'] ?? 0.95;
            $rand[1] = bcmul($c1, 10000);
            $rand[0] = 10000 - $rand[1];
            $this->isPassiveCheck = (bool) get_rand($rand);
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, ' 被动接受比牌概率 ' . $c1);
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, ' 被动接受比牌随机结果' . json_encode($this->isPassiveCheck));
            //随机主动比牌概率
            $d3 = $this->dispositionConfig['d3'] ?? 0.15;
            $rand[1] = 0.8 - 1.6 * ($this->playerCardWinNum - $this->minEstimatedWinNum) / $this->playerCardWinNum + $this->raiseCnt * $d3;
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, ' 主动接受比牌概率 D=80%-1.6*（自己的顺位-其他人的最小顺位）/自己的顺位+当前已加倍次数*' . $d3);
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, ' 主动接受比牌概率 D=80%-1.6*（' . $this->playerCardWinNum . '-' . $this->minEstimatedWinNum . '）/' . $this->playerCardWinNum . '+' . $this->raiseCnt . '*' . $d3 . ' = ' . $rand[1]);
            $rand[1] = bcmul($rand[1], 10000, 0);
            if ($rand[1] <= 0) {
                $this->isInitiativeCheck = false;
            } else {
                $rand[0] = 10000 - $rand[1];
                $this->isInitiativeCheck = (bool) get_rand($rand);
            }
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, ' 主动接受比牌随机结果 ' . json_encode($this->isInitiativeCheck));
            return;
        }
        //当场上所有的小于自己顺位的人，小于的总值超过5%时，开始有概率弃牌（没看牌的人不参与计算总值）
        $dRate = bcdiv($this->allWinNumDValue, $this->playerCardWinNum, 2);
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '当场上所有的小于自己顺位的人，小于的总值超过5%时，开始有概率弃牌（没看牌的人不参与计算总值）');
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '所有的小于自己顺位的人，小于的总值' . $this->allWinNumDValue);
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '手牌顺位' . $this->playerCardWinNum);
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, $this->allWinNumDValue . '/' . $this->playerCardWinNum . '=' . $dRate);
        if ($dRate > 0.05) {
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '超过5%时，开始有概率弃牌');
            //弃牌概率=10%+1.5*（自己的顺位-其他人的小于值之和）/自己的顺位+本场已加倍次数*15%
            $g3 = $this->dispositionConfig['g3'] ?? 0.15;
            $dropRate = 0.1 + 1.5 * ($this->playerCardWinNum - $this->allWinNumDValue) / $this->playerCardWinNum + $this->raiseCnt * $g3;
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '弃牌概率=10%+1.5*（自己的顺位-其他人的小于值之和）/自己的顺位+本场已加倍次数*' . $g3);
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, $dropRate . '= 0.1+1.5*(' . $this->playerCardWinNum . '-' . $this->allWinNumDValue . ')/' . $this->playerCardWinNum . '+' . $this->raiseCnt . '*' . $g3);
            $rand[1] = bcmul($dropRate, 10000, 0);
            $rand[0] = 10000 - $rand[1];
            $randRes = get_rand($rand);
            if ($rand[1] <= 0 || $randRes) {
                //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '未随中  drop');
                $this->isDrop = true;
                return;
            } else {
                //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '随中了  不drop');
                $this->isDrop = false;
            }
            //此时的接受比牌概率：98%
            //随机被动接受比牌概率
            $this->isPassiveCheck = (bool) get_rand([1 => 9800, 0 => 200]);
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '此时的接受比牌概率：98%');
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '此时的接受比牌结果' . json_encode($this->isPassiveCheck));
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '此时的主动比牌概率：弃牌概率<50%时，比牌概率=1-弃牌概率，弃牌概率>50%时，比牌概率=弃牌概率');
            $initiativeCheckRate = $dropRate > 0.5 ? $dropRate : (1 - $dropRate);
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '此时的主动比牌概率' . $initiativeCheckRate);

            $rand[1] = bcmul($initiativeCheckRate, 10000, 0);
            $rand[0] = 10000 - $rand[1];
            $randRes = get_rand($rand);
            $this->isInitiativeCheck = (bool) $randRes;
            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '此时的主动比牌结果' . json_encode($this->isInitiativeCheck));
            return;
        }
        //以上条件都不符合则执行跟牌操作
        $this->isDrop = false;
        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[当前操作玩家] 以上条件都不符合  不drop');
    }

    //决策是否加注
    //当自己的牌型顺位超过场上最高一人的150%时，有E概率加注
    private function setIsRaise()
    {
        if ($this->isDrop) {
            $this->isRaise = false;
        } else {
            if ($this->playerCardWinNum <= 0) {
                // 这句写错了吧？零值是最大的牌
                $rate = 0;
            } else {
                // 我的牌越大，playerCardWinNum 就越小，rate 就越大，我就越应该加注
                $rate = bcdiv($this->maxEstimatedWinNum, $this->playerCardWinNum, 2);
            }

            // rate 太小，不加注，合理
            if ($rate < 1.1) {
                $this->isRaise = false;
                return;
            }

            //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[当前操作玩家] 当自己的牌型顺位超过场上最高一人的150%时，有E概率加注');
            //E=95%-90%*自己的顺位/其他人的最高顺位（最大不超过65%，最小不低于5%）*0.8^max((已加倍次数-1)，0)
            $e3 = $this->dispositionConfig['e3'] ?? 0.65;
            $e4 = $this->dispositionConfig['e4'] ?? 0.5;
            // 牌越大，fenmu 越小
            $fenmu = 0.9 * $this->playerCardWinNum / $this->maxEstimatedWinNum;
            if ($fenmu <= $e4) {
                $fenmu = 0.1;
            } elseif ($fenmu >= $e3) {
                $fenmu = 0.65;
            }

            // 牌越大，fenmu 越小，加注概率就越大
            $rand[1] = 0.95 - $fenmu * pow(0.8, max($this->raiseCnt - 1, 0));

            // 抬叫局，概率加倍
            if ($this->cryUp) {
                $rand[1] = $rand[1] * 2;
            }

            $rand[1] = intval($rand[1] * 10000);
            $rand[0] = 10000 - $rand[1];
            $randRes = get_rand($rand);
            if ($rand[1] <= 0 || !$randRes) {
                $this->isRaise = false;
            } else {
                $this->isRaise = true;
            }

            \lbase\Log::info('TpScore-AiTeenPattyPrSee-加注决策', [
                'roomId' => $this->roomId,
                'playerIndex' => $this->playerIndex,
                'playerCardWinNum' => $this->playerCardWinNum,
                'maxEstimatedWinNum' => $this->maxEstimatedWinNum,
                'rate' => $rate,
                //'dispositionConfig' => $this->dispositionConfig,
                'e3' => $e3,
                'e4' => $e4,
                'fenmu' => $fenmu,
                'raiseCnt' => $this->raiseCnt,
                'rand1' => $rand[1],
                'randRes' => $randRes,
                'isRaise' => $this->isRaise,
            ]);
        }
    }

    //获取玩家的最高顺位
    private function getMaxEstimatedWinNum()
    {
        //获取用户最大的预估概率
        if (empty($this->playerEstimatedWinNum)) {
            $obj = new AiTeenPattyPrCardWinRate($this->cls, count($this->playerInfoList));
            $this->maxEstimatedWinNum = $obj->getSequenceByWinRate($obj->getAvgWinRateByPlayerCnt());
        } else {
            $this->maxEstimatedWinNum = max($this->playerEstimatedWinNum);
        }
    }

    //获取玩家的最高顺位
    private function getMinEstimatedWinNum()
    {
        //获取用户最大的预估概率
        if (empty($this->playerEstimatedWinNum)) {
            $obj = new AiTeenPattyPrCardWinRate($this->cls, count($this->playerInfoList));
            $this->minEstimatedWinNum = $obj->getSequenceByWinRate($obj->getAvgWinRateByPlayerCnt());
        } else {
            $this->minEstimatedWinNum = min($this->playerEstimatedWinNum);
        }
    }

    // 执行玩家行为统计操作
    private function setPlayerOptStatCnt()
    {
        foreach ($this->playerInfoList as $key => $val) {
            // 本次操作的玩家信息
            if ($val['uid'] == $this->playerIndex) {
                $this->playerTableInfo = $val;
                $this->playerOptNum = $key;
            }
            // 计算正在玩的玩家数量
            if ($val['is_playing']) {
                $this->playerPlayingCnt += 1;
            }
            // 计算当前奖池里所有的钱？计算奖池里除了自己下注的所有钱？
            $this->poolAllAmount += $val['blind_wager_fee'];
            $this->poolAllAmount += $val['seen_wager_fee'];
            // 计算最大的上次下注的钱，就是下次平跟需要下注的钱
            $this->followAmount = max($this->followAmount, $val['last_wager_fee']);
            // 总的加倍次数，总的加注次数
            $this->raiseCnt += $val['wager_count'];
        }
    }


    //获取玩家本次是否可以执行比牌操作
    //只要上家玩家已经看牌 则可以执行比牌操作
    private function getPlayerCanCheck(): bool
    {
        $lastPlayerInfo = $this->playerOptNum == 0 ? $this->playerInfoList[count($this->playerInfoList) - 1] : $this->playerInfoList[$this->playerOptNum - 1];
        if ($lastPlayerInfo['see']) {
            return true;
        } else {
            return false;
        }
    }

    //获取玩家的平均初始胜利
    private function getPlayerInitAvgWinRate()
    {
        return bcdiv(1, count($this->playerInfoList), 4);
    }

    //获取玩家手牌对应的胜率
    private function getPlayerCardWinRate()
    {
        // 获取当前玩家人数
        // 首先减掉半个人，每一个不玩的都减掉半个人，最终至少按两个人算
        $playerCnt = count($this->playerInfoList);
        $winRatePlayerCnt = $playerCnt - ($playerCnt - $this->playerPlayingCnt) * 0.5;
        $winRatePlayerCnt = max(2, ($winRatePlayerCnt - 0.5));

        // 根据顺位获取胜率
        $obj = new AiTeenPattyPrCardWinRate($this->cls, $winRatePlayerCnt);
        // 找出与顺位接近的最后一个概率
        $this->playerCardWinRate = $obj->getWinRateBySequence($this->playerCardWinNum);

        //Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->roomId, '[当前操作玩家] 玩家手牌更改后胜率：' . json_encode($this->playerCardWinRate));
    }
}
