<?php
/**
 * ai组排 出牌模拟算法
 */

namespace common;

use llogic\common\Struct;

use lib\Log;

class Common
{
    //摸牌类型
    const TAKE_CARD_TYPE_M = 1; //摸明牌
    const TAKE_CARD_TYPE_A = 0; //摸暗牌

    //AI状态列表key
    const ROBOT_WAIT_LIST = 'robot_wait_list_'; //机器人等待中列表
    const ROBOT_PLAY_LIST = 'robot_play_list_'; //机器人游戏中列表
    const ROBOT_FROZEN_LIST = 'robot_frozen_list_'; //机器人冻结列表
    const ROBOT_ALL_LIST = 'robot_all_list_'; //机器人所有列表
    const ROBOT_REST_LIST = 'robot_rest_list_'; //机器人休息列表
    const ROBOT_DEL_LIST = 'robot_del_list_'; //机器人删除列表

    //定义真人 和 Ai类型
    const PLAYER_TYPE_AI = 1; //ai
    const PLAYER_TYPE_PEOPLE = 0; //真人

    //货币类型
    const CURRENCY_TYPE_CASH = 1; //真金
    const CURRENCY_TYPE_CHIP = 2; //筹码
    const CURRENCY_TYPE_GOLD = 10; //金币


    //游戏人数
    const MAX_PLAYER_NUM_2 = 2; //2人
    const MAX_PLAYER_NUM_4 = 4; //4人
    const MAX_PLAYER_NUM_6 = 6; //6人

    //游戏玩法定义cls
    const GAME_TYPE_RUMMY_POINTS = 1; //RummyPoints
    const GAME_TYPE_RUMMY_POOL = 2; //RummyPool
    const GAME_TYPE_RUMMY_10_CARD = 3; //Rummy10Card
    const GAME_TYPE_RUMMY_DEALS = 4; //Deals
    const GAME_TYPE_TEEN_PATTI = 5; //TeenPatti
    const GAME_TYPE_BACCARAT_AB = 8; //AndarBahar
    const GAME_TYPE_RB = 9; //RedBlack
    const GAME_TYPE_JHANDI_MUNDA = 10; //六面
    const GAME_TYPE_DRAGON_VS_TIGER = 11; //龙虎
    const GAME_TYPE_SEVEN_UP_DOWN = 12; //Up7Down
    const GAME_TYPE_ANDAR_BAHAR = 13; //经典ab
    const GAME_TYPE_RED_VS_BLACK = 14; //快速红黑
    const GAME_TYPE_LUCKY_LOTO = 15; //jackpot loto
    const GAME_TYPE_BLAST_SPACEX = 16; //space x
    const GAME_TYPE_TP_WAR = 17; //tp war
    const GAME_TYPE_RS_1_LOTTERY_FAST = 18; //一元夺宝 人满开奖
    const GAME_TYPE_RS_1_LOTTERY_TIMING = 19; //一元夺宝 定时开奖

    const GAME_TYPE_CAR_ROULETTE = 20; //奔驰宝马
    const GAME_TYPE_WHEEL_OF_FORTUNE = 21; // 幸运转盘
    const GAME_TYPE_SLOTS_GANESH = 200; // SlotsGanesh
    const GAME_TYPE_EXPLORER_SLOTS = 22; // ExplorerSlots
    const GAME_TYPE_SUPER_SIXER_SLOTS = 23; // SuperSixerSlots
    const GAME_TYPE_TEEN_PATTI_AK47 = 24; //AK47
    const GAME_TYPE_TEEN_PATTI_JOKER = 25; //Joker


    //巴西玩法
    const GAME_TYPE_TRUCO = 100; // Truco 4 5 6 7 Q J K A 2 3
    const GAME_TYPE_TRUCO_CLEAN = 101; // TrucoClean Q J K A 2 3


    const GAME_TYPE_SLOATS_LOTUS = 201; // 先随便写的
    const GAME_TYPE_SLOATS = 200; // 先随便写的




    //定义是13card 还是10card
    const GAME_TYPE_13_CARD = 13; //13card
    const GAME_TYPE_10_CARD = 10; //10card


    //定义ZJ 牌型名称
    const ZJ_GROUP_NAME_BAOZI = 'ZJ_GROUP_NAME_BAOZI'; //豹子
    const ZJ_GROUP_NAME_TONGHUA_SHUNZI = 'ZJ_GROUP_NAME_TONGHUA_SHUNZI'; //同花顺
    const ZJ_GROUP_NAME_SHUNZI = 'ZJ_GROUP_NAME_SHUNZI'; //顺子
    const ZJ_GROUP_NAME_TONGHUA = 'ZJ_GROUP_NAME_TONGHUA'; //同花
    const ZJ_GROUP_NAME_DUIZI = 'ZJ_GROUP_NAME_DUIZI'; //对子
    const ZJ_GROUP_NAME_SANPAI = 'ZJ_GROUP_NAME_SANPAI'; //散牌
    const ZJ_GROUP_NAME_RANDOM = 'ZJ_GROUP_NAME_RANDOM'; //随机牌
    const ZJ_GROUP_NAME_INDEX_REGION = 'ZJ_GROUP_NAME_INDEX_REGION'; //按照枚举索引区间发牌
    const ZJ_GROUP_NAME_SCORE_REGION = 'ZJ_GROUP_NAME_SCORE_REGION'; //按照分值区间发牌
    const ZJ_GROUP_NAME_WEIGHT = 'ZJ_GROUP_NAME_WEIGHT'; //按照权重发牌

    //定义tp 机器人的性格
    const ZJ_DISPOSITION_STEADY = 'STEADY'; //稳健类型
    const ZJ_DISPOSITION_STEADY_2 = 'STEADY2'; //稳健类型2
    const ZJ_DISPOSITION_CUNNING = 'CUNNING'; //狡诈类型
    const ZJ_DISPOSITION_EXCITED = 'EXCITED'; //冲动类型



    //定义盈利率发牌牌型
    const PROFIT_GROUP_NAME_NOLV_NOLAI = 'group_name_nolv_nolai'; //0绿0癞子
    const PROFIT_GROUP_NAME_NOLV_YESLAI = 'group_name_nolv_yeslai'; //0绿有癞子
    const PROFIT_GROUP_NAME_1LV = 'group_name_1lv'; //1绿
    const PROFIT_GROUP_NAME_2LV = 'group_name_2lv'; //2绿
    const PROFIT_GROUP_NAME_3LV = 'group_name_3lv'; //3绿
    const PROFIT_GROUP_NAME_SANPAI = 'group_name_sanpai'; //纯随机牌


    //Truco玩法牌型定义
    const TRUCO_GROUP_NAME_SANPAI = 'group_name_sanpai'; //纯随机散牌

    //Truco玩法  发牌计算胜负的标识
    const TRUCO_WIN = 1; //胜利
    const TRUCO_TIE = 2; //平
    const TRUCO_LOSE = 3; //失败

    //Truco玩法  操作枚举
    const TRUCO_ACTIVE_NONACTION = 1; //没有操作
    const TRUCO_ACTIVE_CORRER = 9; //放弃
    const TRUCO_ACTIVE_TRUCO = 5; //主动加注
    const TRUCO_ACTIVE_AUMENTAR = 13; //主动翻倍加注
    const TRUCO_PASSIVE_ACEITAR = 14; //被动同意加注

    //结果是否展示自己的牌
    const TRUCO_NOT_SHOW_CARD = 0;
    const TRUCO_SHOW_CARD = 1;

    //塞点是否使用当前的牌
    const TRUCO_MATCH_POINT_NOT_USE_CARDS = 0;
    const TRUCO_MATCH_POINT_USE_CARDS = 1;







    //定义净胜分发牌牌型
    const SCORE_GROUP_NAME_3_CHUN_SHUN = 'score_group_name_3_chun_shun'; //3纯顺 剩余牌随机


    //tp用户的控制状态
    const TP_PLAYER_CONTROL_TYPE_UP = 'up'; //用户属于扶植状态
    const TP_PLAYER_CONTROL_TYPE_DOWN = 'down'; //用户属于抑制状态
    const TP_PLAYER_CONTROL_TYPE_NORMAL = 'normal'; //用户属于正常


    //定义用户标签redis存储字段  备注  标签的值请勿重复





    const USER_TAG_PLAY_MAX_BASE = 'play_max_base'; //玩家玩过的最大场底注
    const USER_TAG_CHANGE_BASE_PROTECT_USE_CNT = 'change_base_protect_use_cnt'; //跳场发生的保护使用次数

    const USER_TAG_ROOKIE_BUFF_Z = 'rookie_buff_deal_card_z'; //新手保护真人已经发过的牌
    const USER_TAG_ROOKIE_BUFF_A = 'rookie_buff_deal_card_a'; //新手保护AI已经发过的牌
    const USER_TAG_TP_ROOKIE_DEAL_CARD_LIST = 'tp_rookie_deal_card_list'; //tp新手发牌顺序



    const USER_TAG_LAST_RUMMY_DEAL_CARD_TYPE = 'rummy_last_deal_card_type'; //rummy上一次发牌类型




    //tp新手第二大牌随机概率
    const USER_TAG_TP_ROOKIE_SECOND_CARD_RATE_X = 'tp_rookie_second_rate_X'; //tp 倒杀ai 给ai发第二大牌的概率累计值 倍数

    //################用户buff
    const USER_TAG_CHARGE_BUFF = 'charge_buff'; // 新充buff
    const USER_TAG_ROOKIE = 'rookie'; //是否是新手
    const USER_TAG_WIN_BUFF = 'win_buff'; //高手buff
    const USER_TAG_RICH_BUFF = 'rich_buff'; //小黑屋buff
    const USER_TAG_NORMAL_BUFF = 'normal_buff'; //正常状态 无任何buff
    const USER_TAG_TP_DISCLOSE_KILLER = 'tp_disclose_killer'; //tp透杀
    const USER_TAG_TP_AK47_DISCLOSE_KILLER = 'tp_ak47_disclose_killer'; //AK47透杀
    const USER_TAG_TP_JOKER_DISCLOSE_KILLER = 'tp_joker_disclose_killer'; //TPJoker透杀

    public static function buffTypeToPretty($buffType)
    {
        switch ($buffType) {
            case self::USER_TAG_CHARGE_BUFF:
                return '新充';
            case self::USER_TAG_ROOKIE:
                return '新手';
            case self::USER_TAG_WIN_BUFF:
                return '高手';
            case self::USER_TAG_RICH_BUFF:
                return '大户';
            default:
                return '普通';
        }
    }

    //是否发混
    const DEAL_CARD_NO_HUN = 1; //首牌执行规则 剔除混子牌 再按照牌型发牌
    const DEAL_CARD_YES_HUN = 2; //首牌按照执行规则执行  无特殊处理


    //buff类型
    const ROOKIE_BUFF_NOT = 0; //没有buff
    const ROOKIE_BUFF_NEW_PLAYER_PROTECT = 1; //新手保护


    //百人场房间强杀类型枚举
    const ROOM_UNION_TYPE_NORMAL = 0; //普通
    const ROOM_UNION_TYPE_WIN = 1; //高手
    const ROOM_UNION_TYPE_RICH = 2; //大户
    const ROOM_UNION_TYPE_BUFF = 3; //buff屋


    //获取房间强杀类型
    public static function getSysKillerType($type)
    {
        $list = [
            self::ROOM_UNION_TYPE_NORMAL => '普通房',
            self::ROOM_UNION_TYPE_WIN => '高手房',
            self::ROOM_UNION_TYPE_RICH => '大户房',
            self::ROOM_UNION_TYPE_BUFF => 'buff屋',
        ];
        return isset($list[$type]) ? $list[$type] : '';
    }


    //根据buff类型获取强杀类型
    public static function getSysKillerTypeByBuffer($buff)
    {
        $list = [
                //新充 新手  正常 读取正常房间的杀率
            Common::USER_TAG_CHARGE_BUFF => Common::ROOM_UNION_TYPE_BUFF,
            Common::USER_TAG_ROOKIE => Common::ROOM_UNION_TYPE_BUFF,
                //普通房
            Common::USER_TAG_NORMAL_BUFF => Common::ROOM_UNION_TYPE_NORMAL,

                //高手房 大户房  读取原有的 小黑屋 小灰屋的杀率
            Common::USER_TAG_WIN_BUFF => Common::ROOM_UNION_TYPE_WIN,
            Common::USER_TAG_RICH_BUFF => Common::ROOM_UNION_TYPE_RICH,
        ];
        if (isset($list[$buff])) {
            return $list[$buff];
        } else {
            return Common::ROOM_UNION_TYPE_NORMAL;
        }
    }


    //获取tpai类型列表
    public static function getTpAiDispositionList()
    {
        $list = [
            self::ZJ_DISPOSITION_STEADY => '稳健类型',
            self::ZJ_DISPOSITION_STEADY_2 => '稳健类型2',
            self::ZJ_DISPOSITION_CUNNING => '狡诈类型',
            self::ZJ_DISPOSITION_EXCITED => '冲动类型',
        ];
        return $list;
    }


    //tp的玩家操作枚举
    const TP_PLAYER_OPT_FOLLOW = 1; //平跟
    const TP_PLAYER_OPT_RAISE = 2; //加注

    public static function getTpPlayerOptText($opt = 0)
    {
        $list = [
            self::TP_PLAYER_OPT_FOLLOW => '平跟',
            self::TP_PLAYER_OPT_RAISE => '加注',
        ];
        if ($opt && isset($opt)) {
            return $list[$opt];
        } else {
            return '';
        }
    }

    //获取奖池名称前缀
    public static function getPoolNamePrefix()
    {
        //奖池前缀
        $list = [
            Common::GAME_TYPE_RUMMY_POINTS => '_jack_pool',
            Common::GAME_TYPE_RUMMY_10_CARD => '_jack_pool',
            Common::GAME_TYPE_RUMMY_DEALS => '_jack_pool',
            Common::GAME_TYPE_RUMMY_POOL => '_jack_pool',
            Common::GAME_TYPE_TEEN_PATTI => '_tp_jack_pool',
            Common::GAME_TYPE_TEEN_PATTI_AK47 => '_tpak47_jack_pool',
            Common::GAME_TYPE_TEEN_PATTI_JOKER => '_tpjoker_jack_pool',
            Common::GAME_TYPE_BACCARAT_AB => '_ab_jack_pool',
            Common::GAME_TYPE_ANDAR_BAHAR => '_abc_jack_pool',
            Common::GAME_TYPE_RB => '_rb_jack_pool',
            Common::GAME_TYPE_SEVEN_UP_DOWN => '_l7_jack_pool',
            Common::GAME_TYPE_JHANDI_MUNDA => '_ld_jack_pool',
            Common::GAME_TYPE_DRAGON_VS_TIGER => '_lh_jack_pool',
            Common::GAME_TYPE_RED_VS_BLACK => '_frb_jack_pool',
            Common::GAME_TYPE_LUCKY_LOTO => '_lt_jack_pool',

            Common::GAME_TYPE_BLAST_SPACEX => '_spx_jack_pool',
            Common::GAME_TYPE_CAR_ROULETTE => '_bb_jack_pool',
            Common::GAME_TYPE_TRUCO => '_truco_jack_pool',
            Common::GAME_TYPE_TRUCO_CLEAN => '_truco_clean_jack_pool',
            Common::GAME_TYPE_RS_1_LOTTERY_FAST => '_rs1lottery_fast_jack_pool',
            Common::GAME_TYPE_RS_1_LOTTERY_TIMING => '_rs1lottery_timing_jack_pool',
            Common::GAME_TYPE_TP_WAR => '_tp_war_jack_pool',

            Common::GAME_TYPE_SLOTS_GANESH => '_sloats',
            Common::GAME_TYPE_WHEEL_OF_FORTUNE => '_wf_jack_pool',
            Common::GAME_TYPE_EXPLORER_SLOTS => '_es_jack_pool',
            Common::GAME_TYPE_SUPER_SIXER_SLOTS => '_sss_jack_pool',
        ];
        return $list;
    }


    //获取百人场游戏列表
    public static function getHundredPeopleGameList()
    {
        $list = [
            Common::GAME_TYPE_BACCARAT_AB,
            Common::GAME_TYPE_RB,
            Common::GAME_TYPE_SEVEN_UP_DOWN,
            Common::GAME_TYPE_JHANDI_MUNDA,
            Common::GAME_TYPE_DRAGON_VS_TIGER,
            Common::GAME_TYPE_RED_VS_BLACK,
            Common::GAME_TYPE_ANDAR_BAHAR,
            Common::GAME_TYPE_LUCKY_LOTO,
            Common::GAME_TYPE_BLAST_SPACEX,
            Common::GAME_TYPE_CAR_ROULETTE,
            Common::GAME_TYPE_TP_WAR,

            Common::GAME_TYPE_SLOTS_GANESH,
            Common::GAME_TYPE_WHEEL_OF_FORTUNE,
            Common::GAME_TYPE_EXPLORER_SLOTS,
            Common::GAME_TYPE_SUPER_SIXER_SLOTS,
        ];
        return $list;
    }

    //根据玩法和底注获取奖池名称
    public static function getPoolNameByClsAndBase($cls, $base)
    {
        $poolBase = self::getPoolBaseByClsAndBase($cls, $base);
        return $poolBase . self::getPoolNamePrefix()[$cls];
    }


    //根据玩法获取最大牌数
    public static function getMaxCardNumByCls($cls)
    {
        $list = [
            self::GAME_TYPE_RUMMY_DEALS => 13,
            self::GAME_TYPE_RUMMY_POINTS => 13,
            self::GAME_TYPE_RUMMY_POOL => 13,
            self::GAME_TYPE_RUMMY_10_CARD => 10,
        ];
        if (isset($list[$cls])) {
            return $list[$cls];
        } else {
            return 13;
        }
    }


    //根据玩法和底注 获取奖池转化倍数
    public static function getPoolBaseByClsAndBase($cls, $base)
    {
        if ($cls != Common::GAME_TYPE_TEEN_PATTI) {
            $mul100BaseRoomTypeList = [Common::GAME_TYPE_RUMMY_POOL, Common::GAME_TYPE_RUMMY_DEALS];
            if (in_array($cls, $mul100BaseRoomTypeList)) {
                return $base / 100;
            } else {
                return $base;
            }
        } else {
            return $base;
        }
    }




    //获取盈利率牌型数组
    public static function getProfitGroupNameList()
    {
        $list = [
            self::PROFIT_GROUP_NAME_3LV => '3绿及以上',
            self::PROFIT_GROUP_NAME_2LV => '2绿',
            self::PROFIT_GROUP_NAME_1LV => '1绿',
            self::PROFIT_GROUP_NAME_NOLV_YESLAI => '0绿有癞',
            self::PROFIT_GROUP_NAME_NOLV_NOLAI => '0绿无癞',
        ];
        return $list;
    }

    //获取盈利率牌型数组
    public static function getProfitGroupNameAllList()
    {
        $list = [
            self::PROFIT_GROUP_NAME_3LV => '3绿及以上',
            self::PROFIT_GROUP_NAME_2LV => '2绿',
            self::PROFIT_GROUP_NAME_1LV => '1绿',
            self::PROFIT_GROUP_NAME_NOLV_YESLAI => '0绿有癞',
            self::PROFIT_GROUP_NAME_NOLV_NOLAI => '0绿无癞',
            self::PROFIT_GROUP_NAME_SANPAI => '随机牌',
        ];
        return $list;
    }

    //获取货币列表
    public static function getCurrencyTypeTextList($type = '')
    {
        $list = [
            self::CURRENCY_TYPE_CASH => '<div class="badge badge-danger mb-3">真金</div>',
            self::CURRENCY_TYPE_CHIP => '<div class="badge badge-success mb-3">筹码</div>',
            self::CURRENCY_TYPE_GOLD => '<div class="badge badge-warning mb-3">金币</div>',
        ];
        if ($type && isset($list[$type])) {
            return $list[$type];
        } else {
            return $list;
        }
    }


    //获取人数列表
    public static function getPlayerNumTextList($num = 0)
    {
        $list = [
            self::MAX_PLAYER_NUM_2 => '2人',
                //self::MAX_PLAYER_NUM_4  => '4人',
            self::MAX_PLAYER_NUM_6 => '6人',
        ];
        if ($num && isset($list[$num])) {
            return $list[$num];
        } else {
            return $list;
        }
    }


    //获取游戏玩法列表
    public static function getGameTypeTextList($type = '')
    {
        $list = [
            self::GAME_TYPE_RUMMY_POINTS => 'Points',
            self::GAME_TYPE_RUMMY_POOL => 'Pool',
            self::GAME_TYPE_RUMMY_10_CARD => '10Card',
            self::GAME_TYPE_RUMMY_DEALS => 'Deals',
            self::GAME_TYPE_TEEN_PATTI => 'TeenPatti',
            self::GAME_TYPE_TEEN_PATTI_AK47 => 'AK47',
            self::GAME_TYPE_TEEN_PATTI_JOKER => 'TPJoker',

            self::GAME_TYPE_BACCARAT_AB => 'Baccarat_Ab',
            self::GAME_TYPE_RB => 'Red_Black',
            self::GAME_TYPE_SEVEN_UP_DOWN => '7_Up_Down',
            self::GAME_TYPE_JHANDI_MUNDA => 'Jhandi_Munda',
            self::GAME_TYPE_DRAGON_VS_TIGER => 'Dragon_Tiger',
            self::GAME_TYPE_RED_VS_BLACK => 'Red_Black_Fast',
            self::GAME_TYPE_ANDAR_BAHAR => 'Andar_Bahar',
            self::GAME_TYPE_LUCKY_LOTO => 'Lucky_Loto',
            self::GAME_TYPE_BLAST_SPACEX => 'Space_X',
            self::GAME_TYPE_CAR_ROULETTE => 'Benz_Bmw',

            self::GAME_TYPE_TRUCO => 'Truco',
            self::GAME_TYPE_TRUCO_CLEAN => 'Truco_Clean',

            self::GAME_TYPE_RS_1_LOTTERY_TIMING => 'Rs.1.Lottery_Timing',
            self::GAME_TYPE_RS_1_LOTTERY_FAST => 'Rs.1.Lottery_Fast',
            self::GAME_TYPE_TP_WAR => 'Tp_War',

            self::GAME_TYPE_SLOTS_GANESH => 'SlotsGanesh',
            self::GAME_TYPE_WHEEL_OF_FORTUNE => 'WheelOfFortune',
            self::GAME_TYPE_EXPLORER_SLOTS => 'ExplorerSlots',
            self::GAME_TYPE_SUPER_SIXER_SLOTS => 'SuperSixerSlots',
        ];
        if ($type && isset($list[$type])) {
            return $list[$type];
        } else {
            return $list;
        }
    }


    //玩家赢钱之后实际获得的百分比
    public static function getGameTypeCommissionRate($type = '')
    {
        $list = [
            self::GAME_TYPE_RUMMY_POINTS => 1,
            self::GAME_TYPE_RUMMY_POOL => 1,
            self::GAME_TYPE_RUMMY_10_CARD => 1,
            self::GAME_TYPE_RUMMY_DEALS => 1,
            self::GAME_TYPE_TEEN_PATTI => 1,
            self::GAME_TYPE_TEEN_PATTI_AK47 => 1,
            self::GAME_TYPE_TEEN_PATTI_JOKER => 1,

            self::GAME_TYPE_BACCARAT_AB => 1,
            self::GAME_TYPE_RB => 1,
            self::GAME_TYPE_SEVEN_UP_DOWN => 1,
            self::GAME_TYPE_JHANDI_MUNDA => 1,
            self::GAME_TYPE_DRAGON_VS_TIGER => 1,
            self::GAME_TYPE_RED_VS_BLACK => 1,
            self::GAME_TYPE_ANDAR_BAHAR => 1,
            self::GAME_TYPE_LUCKY_LOTO => 1,
            self::GAME_TYPE_BLAST_SPACEX => 1,

            self::GAME_TYPE_CAR_ROULETTE => 1,
            self::GAME_TYPE_TRUCO => 1,
            self::GAME_TYPE_TRUCO_CLEAN => 1,
            self::GAME_TYPE_TP_WAR => 1,

            self::GAME_TYPE_SLOTS_GANESH => 1,
            self::GAME_TYPE_WHEEL_OF_FORTUNE => 1,
            self::GAME_TYPE_EXPLORER_SLOTS => 1,
            self::GAME_TYPE_SUPER_SIXER_SLOTS => 1,
        ];
        if ($type && isset($list[$type])) {
            return $list[$type];
        } else {
            return 1;
        }
    }



    //获取状态列表对应文字
    public static function getAiStatesKeyList($states = '')
    {
        $list = [
            self::ROBOT_ALL_LIST => '所有',
            self::ROBOT_WAIT_LIST => '等待中',
            self::ROBOT_PLAY_LIST => '游戏中',
            self::ROBOT_REST_LIST => '休息中',
            self::ROBOT_DEL_LIST => '已删除',
        ];
        if ($states && isset($list[$states])) {
            return $list[$states];
        } else {
            return $list;
        }
    }


    //获取玩家身份
    public static function getRoomConfigTypeList($type = '')
    {
        $list = [
            self::PLAYER_TYPE_AI => 'AI',
            self::PLAYER_TYPE_PEOPLE => '真人',
        ];
        if ($type && isset($list[$type])) {
            return $list[$type];
        } else {
            return $list;
        }
    }


    //盈利率模式下 获取tp AI心里加倍次数上限根据底注
    public static function getTpAiRaiseLimitCntByBase($base)
    {
        $config = BASE_ZJ_AI_RAISE_CNT_CONFIG;
        if (isset($config[$base])) {
            return get_rand($config[$base]);
        } else {
            //如果不存在配置 获取一个中间默认值【1次-6次】
            return get_rand([2 => 1, 3 => 1, 4 => 1]);
        }
    }


    //盈利率模式下 tp、ak47、tpjoker 房间分组列表
    public static function getTpRoomGroupList($currency, $cls)
    {
        $allRoomList = RedisOpt::getAllRoomList();
        $newList = [];
        $cntList = [];
        $allCntList = [];
        $allList = [];
        foreach ($allRoomList as $key => $val) {
            if ($val['currency'] != $currency || $val['cls'] != $cls) {
                continue;
            }
            $poolBase = Common::getPoolBaseByClsAndBase($val['cls'], $val['base']);
            $allList[] = $poolBase;
            if (!isset($cntList[$poolBase][$val['cls']])) {
                $cntList[$poolBase][$val['cls']] = 0;
            }
            $cntList[$poolBase][$val['cls']]++;
            if (!isset($allCntList[$poolBase])) {
                $allCntList[$poolBase] = 0;
            }
            $allCntList[$poolBase]++;
            $newList[$val['cls']][$poolBase] = $val['base'];
        }
        $rtn['roomList'] = [];
        $rtn['roomCntList'] = [];
        $rtn['allCntList'] = [];
        if ($allList) {
            sort($allList);
            $data = [];
            //循环遍历所有底注
            foreach ($allList as $oneBase) {
                $poolName = Common::getPoolNameByClsAndBase($cls, $oneBase);
                $data[$poolName][$oneBase][$cls] = isset($newList[$cls][$oneBase]) ? $newList[$cls][$oneBase] : '';
            }
            $rtn['roomList'] = $data;
            $rtn['roomCntList'] = $cntList;
            $rtn['allCntList'] = $allCntList;
        }
        return $rtn;
    }




    //盈利率模式下 tp房间分组列表
    public static function getTrucoRoomGroupList($currency)
    {
        $allRoomList = RedisOpt::getAllRoomList();
        $newList = [];
        $cntList = [];
        $allCntList = [];
        $allList = [];
        foreach ($allRoomList as $key => $val) {
            if ($val['currency'] != $currency || $val['cls'] != Common::GAME_TYPE_TRUCO) {
                continue;
            }
            $poolBase = Common::getPoolBaseByClsAndBase($val['cls'], $val['base']);
            $allList[] = $poolBase;
            if (!isset($cntList[$poolBase][$val['cls']])) {
                $cntList[$poolBase][$val['cls']] = 0;
            }
            $cntList[$poolBase][$val['cls']]++;
            if (!isset($allCntList[$poolBase])) {
                $allCntList[$poolBase] = 0;
            }
            $allCntList[$poolBase]++;
            $newList[$val['cls']][$poolBase] = $val['base'];
        }
        $rtn['roomList'] = [];
        $rtn['roomCntList'] = [];
        $rtn['allCntList'] = [];
        if ($allList) {
            sort($allList);
            $data = [];
            //循环遍历所有底注
            foreach ($allList as $oneBase) {
                $poolName = Common::getPoolNameByClsAndBase(Common::GAME_TYPE_TRUCO, $oneBase);
                $data[$poolName][$oneBase][Common::GAME_TYPE_TRUCO] = isset($newList[Common::GAME_TYPE_TRUCO][$oneBase]) ? $newList[Common::GAME_TYPE_TRUCO][$oneBase] : '';
            }
            $rtn['roomList'] = $data;
            $rtn['roomCntList'] = $cntList;
            $rtn['allCntList'] = $allCntList;
        }
        return $rtn;
    }


    //盈利率模式下 tp房间分组列表
    public static function getRs1LotteryFastRoomGroupList($currency)
    {
        $allRoomList = RedisOpt::getAllRoomList();
        $newList = [];
        $cntList = [];
        $allCntList = [];
        $allList = [];
        foreach ($allRoomList as $key => $val) {
            if ($val['currency'] != $currency || $val['cls'] != Common::GAME_TYPE_RS_1_LOTTERY_FAST) {
                continue;
            }
            $poolBase = Common::getPoolBaseByClsAndBase($val['cls'], $val['base']);
            $allList[] = $poolBase;
            if (!isset($cntList[$poolBase][$val['cls']])) {
                $cntList[$poolBase][$val['cls']] = 0;
            }
            $cntList[$poolBase][$val['cls']]++;
            if (!isset($allCntList[$poolBase])) {
                $allCntList[$poolBase] = 0;
            }
            $allCntList[$poolBase]++;
            $newList[$val['cls']][$poolBase] = $val['base'];
        }
        $rtn['roomList'] = [];
        $rtn['roomCntList'] = [];
        $rtn['allCntList'] = [];
        if ($allList) {
            sort($allList);
            $data = [];
            //循环遍历所有底注
            foreach ($allList as $oneBase) {
                $poolName = Common::getPoolNameByClsAndBase(Common::GAME_TYPE_RS_1_LOTTERY_FAST, $oneBase);
                $data[$poolName][$oneBase][Common::GAME_TYPE_RS_1_LOTTERY_FAST] = isset($newList[Common::GAME_TYPE_RS_1_LOTTERY_FAST][$oneBase]) ? $newList[Common::GAME_TYPE_RS_1_LOTTERY_FAST][$oneBase] : '';
            }
            $rtn['roomList'] = $data;
            $rtn['roomCntList'] = $cntList;
            $rtn['allCntList'] = $allCntList;
        }
        return $rtn;
    }



    //盈利率模式下 tp房间分组列表
    public static function getRs1LotteryTimingRoomGroupList($currency)
    {
        $allRoomList = RedisOpt::getAllRoomList();
        $newList = [];
        $cntList = [];
        $allCntList = [];
        $allList = [];
        foreach ($allRoomList as $key => $val) {
            if ($val['currency'] != $currency || $val['cls'] != Common::GAME_TYPE_RS_1_LOTTERY_TIMING) {
                continue;
            }
            $poolBase = Common::getPoolBaseByClsAndBase($val['cls'], $val['base']);
            $allList[] = $poolBase;
            if (!isset($cntList[$poolBase][$val['cls']])) {
                $cntList[$poolBase][$val['cls']] = 0;
            }
            $cntList[$poolBase][$val['cls']]++;
            if (!isset($allCntList[$poolBase])) {
                $allCntList[$poolBase] = 0;
            }
            $allCntList[$poolBase]++;
            $newList[$val['cls']][$poolBase] = $val['base'];
        }
        $rtn['roomList'] = [];
        $rtn['roomCntList'] = [];
        $rtn['allCntList'] = [];
        if ($allList) {
            sort($allList);
            $data = [];
            //循环遍历所有底注
            foreach ($allList as $oneBase) {
                $poolName = Common::getPoolNameByClsAndBase(Common::GAME_TYPE_RS_1_LOTTERY_TIMING, $oneBase);
                $data[$poolName][$oneBase][Common::GAME_TYPE_RS_1_LOTTERY_TIMING] = isset($newList[Common::GAME_TYPE_RS_1_LOTTERY_TIMING][$oneBase]) ? $newList[Common::GAME_TYPE_RS_1_LOTTERY_TIMING][$oneBase] : '';
            }
            $rtn['roomList'] = $data;
            $rtn['roomCntList'] = $cntList;
            $rtn['allCntList'] = $allCntList;
        }
        return $rtn;
    }



    //盈利率模式下 tp房间分组列表
    public static function getTrucoCleanRoomGroupList($currency)
    {
        $allRoomList = RedisOpt::getAllRoomList();
        $newList = [];
        $cntList = [];
        $allCntList = [];
        $allList = [];
        foreach ($allRoomList as $key => $val) {
            if ($val['currency'] != $currency || $val['cls'] != Common::GAME_TYPE_TRUCO_CLEAN) {
                continue;
            }
            $poolBase = Common::getPoolBaseByClsAndBase($val['cls'], $val['base']);
            $allList[] = $poolBase;
            if (!isset($cntList[$poolBase][$val['cls']])) {
                $cntList[$poolBase][$val['cls']] = 0;
            }
            $cntList[$poolBase][$val['cls']]++;
            if (!isset($allCntList[$poolBase])) {
                $allCntList[$poolBase] = 0;
            }
            $allCntList[$poolBase]++;
            $newList[$val['cls']][$poolBase] = $val['base'];
        }
        $rtn['roomList'] = [];
        $rtn['roomCntList'] = [];
        $rtn['allCntList'] = [];
        if ($allList) {
            sort($allList);
            $data = [];
            //循环遍历所有底注
            foreach ($allList as $oneBase) {
                $poolName = Common::getPoolNameByClsAndBase(Common::GAME_TYPE_TRUCO_CLEAN, $oneBase);
                $data[$poolName][$oneBase][Common::GAME_TYPE_TRUCO_CLEAN] = isset($newList[Common::GAME_TYPE_TRUCO_CLEAN][$oneBase]) ? $newList[Common::GAME_TYPE_TRUCO_CLEAN][$oneBase] : '';
            }
            $rtn['roomList'] = $data;
            $rtn['roomCntList'] = $cntList;
            $rtn['allCntList'] = $allCntList;
        }
        return $rtn;
    }



    //盈利率模式下 百人场分组
    public static function getAbRoomGroupList($currency)
    {
        $allRoomList = RedisOpt::getAllRoomList();
        $newList = [];
        $sortArr = [];
        foreach ($allRoomList as $key => $val) {
            if ($val['currency'] != $currency || !in_array($val['cls'], Common::getHundredPeopleGameList())) {
                continue;
            }
            $val['jackportName'] = Common::getPoolNameByClsAndBase($val['cls'], $val['base']);
            $sortArr[$key] = $val['cls'];
            $val['gameName'] = Common::getGameTypeTextList($val['cls']);
            $newList[$key] = $val;
        }
        //执行排序  按照玩法排序
        array_multisort($sortArr, SORT_ASC, $newList);
        return $newList;
    }


    //盈利率模式下 房间分组列表
    public static function getRoomGroupList($currency)
    {
        $allRoomList = RedisOpt::getAllRoomList();
        $newList = [];
        $cntList = [];
        $allCntList = [];
        foreach ($allRoomList as $key => $val) {
            if ($val['currency'] != $currency || !in_array($val['cls'], [Common::GAME_TYPE_RUMMY_POINTS, Common::GAME_TYPE_RUMMY_10_CARD, Common::GAME_TYPE_RUMMY_POOL, Common::GAME_TYPE_RUMMY_DEALS,])) {
                continue;
            }
            $poolBase = Common::getPoolBaseByClsAndBase($val['cls'], $val['base']);
            $allList[] = $poolBase;
            if (!isset($cntList[$poolBase][$val['cls']])) {
                $cntList[$poolBase][$val['cls']] = 0;
            }
            $cntList[$poolBase][$val['cls']]++;
            if (!isset($allCntList[$poolBase])) {
                $allCntList[$poolBase] = 0;
            }
            $allCntList[$poolBase]++;
            $newList[$val['cls']][$poolBase] = $val['base'];
        }
        $data = [];
        if (!empty($allList)) {
            sort($allList);
            //循环遍历所有底注
            foreach ($allList as $oneBase) {
                $poolName = Common::getPoolNameByClsAndBase(Common::GAME_TYPE_RUMMY_POINTS, $oneBase);
                $data[$poolName][$oneBase][Common::GAME_TYPE_RUMMY_POINTS] = isset($newList[Common::GAME_TYPE_RUMMY_POINTS][$oneBase]) ? $newList[Common::GAME_TYPE_RUMMY_POINTS][$oneBase] : '';
                $data[$poolName][$oneBase][Common::GAME_TYPE_RUMMY_10_CARD] = isset($newList[Common::GAME_TYPE_RUMMY_10_CARD][$oneBase]) ? $newList[Common::GAME_TYPE_RUMMY_10_CARD][$oneBase] : '';
                $data[$poolName][$oneBase][Common::GAME_TYPE_RUMMY_POOL] = isset($newList[Common::GAME_TYPE_RUMMY_POOL][$oneBase]) ? $newList[Common::GAME_TYPE_RUMMY_POOL][$oneBase] : '';
                $data[$poolName][$oneBase][Common::GAME_TYPE_RUMMY_DEALS] = isset($newList[Common::GAME_TYPE_RUMMY_DEALS][$oneBase]) ? $newList[Common::GAME_TYPE_RUMMY_DEALS][$oneBase] : '';
            }
        }
        $rtn['roomList'] = $data;
        $rtn['roomCntList'] = $cntList;
        $rtn['allCntList'] = $allCntList;
        return $rtn;
    }



    //获取房间配置的玩法列表
    public static function getRoomListByType($cls = 0, $max = 0, $currency = 0)
    {
        if (!$cls && !$max && !$currency) {
            return [];
        }
        $roomList = RedisOpt::getAllRoomList();
        foreach ($roomList as $key => $val) {
            if ($cls > 0 && $val['cls'] != $cls) {
                unset($roomList[$key]);
                continue;
            }
            if ($max > 0 && $val['max'] != $max) {
                unset($roomList[$key]);
                continue;
            }
            if ($currency > 0 && $val['currency'] != $currency) {
                unset($roomList[$key]);
                continue;
            }
        }
        return $roomList;
    }


    /**
     * @param $sysPayAmount     int     系统要赔的钱，开奖时系统要赔付的钱
     * @param $sysWinAmount     int     这个命名意思反了，实际上缺口额，系统当前利润到目标利润的缺口额
     * @param $sysTargetAmount  int     系统的盈利目标
     * @param $playerBetAmount  int     用户这局总下注的钱
     * @param $buffCoefficient  float   buff调控系数
     */
    public static function getHunderGameIsSysKillerByLotteryResult($cls, $sysPayAmount, $sysWinAmount, $sysTargetAmount, $playerBetAmount, $buffCoefficient, $logName = 'other'): bool
    {
        if ($cls == Common::GAME_TYPE_BLAST_SPACEX) {
            return self::getIsSysKillerByLotteryResultSpaceX($sysPayAmount, $sysWinAmount, $sysTargetAmount, $playerBetAmount, $buffCoefficient, $logName);
        } else {
            return self::getIsSysKillerByLotteryResult($sysPayAmount, $sysWinAmount, $sysTargetAmount, $playerBetAmount, $buffCoefficient, $logName);
        }
    }

    /**
     * @param $sysPayAmount     int     系统要赔的钱，开奖时系统要赔付的钱
     * @param $sysWinAmount     int     这个命名意思反了，实际上缺口额，系统当前利润到目标利润的缺口额
     * @param $sysTargetAmount  int     系统的盈利目标
     * @param $playerBetAmount  int     用户这局总下注的钱
     * @param $buffCoefficient  float   buff调控系数
     */
    public static function getIsSysKillerByLotteryResultSpaceX($sysPayAmount, $sysWinAmount, $sysTargetAmount, $playerBetAmount, $buffCoefficient, $logName = 'other')
    {
        $sysTargetAmount = max($sysTargetAmount, 5000000);

        //没有人下注 系统也没有赔钱出去
        if ($sysPayAmount == 0 || $playerBetAmount == 0) {
            return false;
        }
        //重新随机概率
        //重随概率=如果赔钱后的目标缺口金额/当时的盈利目标金额*4*max(1，该次庄总赔钱额/该次台面下注额-1)
        //赔钱后的目标缺口金额
        if ($sysPayAmount == 0) {
            $sysPayOutWinAmount = $sysWinAmount - $playerBetAmount;
        } else {
            $sysPayOutWinAmount = $sysWinAmount + $sysPayAmount;
        }

        //Log::console_log(__FUNCTION__, '如果赔钱后的目标缺口金额：' . $sysPayOutWinAmount, $logName);
        //Log::console_log(__FUNCTION__, '当时的盈利目标金额：' . $sysTargetAmount, $logName);
        //Log::console_log(__FUNCTION__, '该次庄总赔钱额：' . $sysPayAmount, $logName);
        //Log::console_log(__FUNCTION__, '该次台面下注额：' . $playerBetAmount, $logName);
        //Log::console_log(__FUNCTION__, 'buff系数：' . $buffCoefficient, $logName);
        if ($sysPayOutWinAmount <= 0 || $sysTargetAmount <= 0) {
            return false;
        } else {
            // SpaceX 保底杀的概率 = min(0.33, 缺口额 / 目标额 * buff_coefficient * 0.33)

            $rate = min(0.33, ($sysPayOutWinAmount / $sysTargetAmount * $buffCoefficient * 0.33));
            // $rate = $sysPayOutWinAmount/$sysTargetAmount*400*max(1,$sysPayAmount/$playerBetAmount-1);
            //Log::console_log(__FUNCTION__, '得到的概率：' . $rate, $logName);
            if ($rate <= 0) {
                return false;
            } elseif ($rate > 1) {
                return true;
            } else {
                $rand[1] = intval($rate * 1000);
                $rand[0] = 1000 - $rand[1];
                $res = get_rand($rand);
                return boolval($res);
            }
        }
    }

    //根据开奖结果计算是否需要执行大盘杀

    /**
     * @param $sysPayAmount     int     系统要赔的钱，开奖时系统要赔付的钱
     * @param $sysWinAmount     int     这个命名意思反了，实际上缺口额，系统当前利润到目标利润的缺口额
     * @param $sysTargetAmount  int     系统的盈利目标
     * @param $playerBetAmount  int     用户这局总下注的钱
     * @param $buffCoefficient  float   buff调控系数
     */
    public static function getIsSysKillerByLotteryResult($sysPayAmount, $sysWinAmount, $sysTargetAmount, $playerBetAmount, $buffCoefficient, $logName = 'other')
    {
        $sysTargetAmount = max($sysTargetAmount, 5000000);

        //没有人下注 系统也没有赔钱出去
        if ($sysPayAmount == 0 || $playerBetAmount == 0) {
            return false;
        }
        //重新随机概率
        //重随概率=如果赔钱后的目标缺口金额/当时的盈利目标金额*4*max(1，该次庄总赔钱额/该次台面下注额-1)
        //赔钱后的目标缺口金额
        if ($sysPayAmount == 0) {
            $sysPayOutWinAmount = $sysWinAmount - $playerBetAmount; // 不会走到这里
        } else {
            // 扩大后的缺口额 = 缺口额 + 本次亏钱
            $sysPayOutWinAmount = $sysWinAmount + $sysPayAmount;
        }

        //Log::console_log(__FUNCTION__, '如果赔钱后的目标缺口金额：' . $sysPayOutWinAmount, $logName);
        //Log::console_log(__FUNCTION__, '当时的盈利目标金额：' . $sysTargetAmount, $logName);
        //Log::console_log(__FUNCTION__, '该次庄总赔钱额：' . $sysPayAmount, $logName);
        //Log::console_log(__FUNCTION__, '该次台面下注额：' . $playerBetAmount, $logName);
        //Log::console_log(__FUNCTION__, 'buff系数：' . $buffCoefficient, $logName);
        if ($sysPayOutWinAmount <= 0 || $sysTargetAmount <= 0) {
            return false;
        } else {
            //$rate = $sysPayOutWinAmount/$sysTargetAmount*$buffCoefficient*0.33*max(1,$sysPayAmount/$playerBetAmount-1);
            // $rate = $sysPayOutWinAmount/$sysTargetAmount*400*max(1,$sysPayAmount/$playerBetAmount-1);
            //保底概率=MAX(0.333,buff系数*0.33*如果赔钱后的目标缺口金额/MAX(当日实时盈利目标,10000))*max(1,该次庄总赔钱额/该次台面下注额-1)
            //$rate = min(0.333, $buffCoefficient * 0.15 * $sysPayOutWinAmount / max($sysTargetAmount, 10000000)) * max(1, $sysPayAmount / $playerBetAmount - 1);

            // 其他百人场保底杀的概率 = min(0.33, 缺口额 / max(100卢比,目标额) * buff_coefficient * 0.15) * max(1, 该次庄总赔钱额 / 该次台面下注额 - 1)


            $rate = min(0.33, $buffCoefficient * 0.15 * $sysPayOutWinAmount / max($sysTargetAmount, 100 * 1000)) * max(1, $sysPayAmount / $playerBetAmount - 1);
            //Log::console_log(__FUNCTION__, '得到的概率：' . $rate, $logName);

            $res = (rand() / (getrandmax() + 1)) < $rate;

            \lbase\Log::info("AB-保底杀", [
                'logName' => $logName,
                'res' => $res,
                'rate' => $rate,
                'buffCoefficient' => $buffCoefficient,
                'sysPayOutWinAmount' => $sysPayOutWinAmount,
                'sysTargetAmount' => $sysTargetAmount,
                'sysPayAmount' => $sysPayAmount,
                'playerBetAmount' => $playerBetAmount,
            ]);

            return $res;
        }
    }


    //百人根据房间下注人的buff属性计算出最低的波动杀率
    public static function getHundredSysKillerWaveRate($betList, $roomType, $killerType)
    {
        $roomInfo = parse_room_type($roomType);
        $waveRate = 0;
        $sysWinAmount = 0;
        $sysTargetAmount = 0;
        if (!is_array($betList)) {
            //Log::console_log(__FUNCTION__, '出错了：$betList:' . json_encode($betList) . '  $roomType:' . $roomType, "error2222");
        }

        $capitalNames = [];

        if ($betList && is_array($betList)) {
            foreach ($betList as $playerIndex) {
                // 用户杀率类型
                $capitalType = Common::getSysKillerTypeByPlayerIndex($playerIndex);
                $capitalNames[] = Struct::CAPITAL_SORTABLE_NAME_MAP[$capitalType] ?? '';
                // 获取大盘缺口金额
                $jackPortName = Common::getPoolNameByClsAndBase($roomInfo['cls'], $roomInfo['base']);
                $overallLossAmountObj = new \common\AiCommon\AiOverallWinRateV2($jackPortName, $roomInfo, $capitalType);
                $tmpWaveRate = $overallLossAmountObj->getSysKillerWaveRate($killerType);
                if ($waveRate == 0) {
                    $waveRate = $tmpWaveRate;
                    // 缺口额
                    $sysWinAmount = $overallLossAmountObj->getRealWinAmount();
                    // 波动目标额
                    $sysTargetAmount = $overallLossAmountObj->getDynamicTargetAmount();
                } else if ($waveRate <= $tmpWaveRate) {
                    $waveRate = $tmpWaveRate;
                    $sysWinAmount = $overallLossAmountObj->getRealWinAmount();
                    $sysTargetAmount = $overallLossAmountObj->getDynamicTargetAmount();
                }
            }
        }
        $rtn['waveRate'] = $waveRate;
        $rtn['sysWinAmount'] = $sysWinAmount; // 缺口额
        $rtn['sysTargetAmount'] = $sysTargetAmount; // 波动目标额

        if (!empty($betList)) {
            \lbase\Log::info("AB-波动杀率", [
                'roomType' => $roomType,
                'killerType' => $killerType,
                'betList' => $betList,
                'capitalNames' => $capitalNames,
                'rtn' => $rtn,
            ]);
        }

        return $rtn;
    }



    //百人根据房间下注人的buff属性计算出最低的波动杀率
    public static function getHundredLuckyLotoSysKillerWaveRate($betList, $roomType, $killerType)
    {
        if (empty($betList)) {
            $rtn['waveRate'] = 1;
            $rtn['sysTargetAmount'] = 0;
            $rtn['sysWinAmount'] = 0;
            return $rtn;
        }
        $roomInfo = parse_room_type($roomType);
        $waveRateLeft1 = [];
        $waveRateRight1 = [];
        if ($betList) {
            foreach ($betList as $playerIndex) {
                $capitalType = Common::getSysKillerTypeByPlayerIndex($playerIndex);
                //获取大盘缺口金额
                $jackPortName = Common::getPoolNameByClsAndBase($roomInfo['cls'], $roomInfo['base']);
                $overallLossAmountObj = new \common\AiCommon\AiOverallWinRateV2($jackPortName, $roomInfo, $capitalType);
                $info['waveRate'] = $overallLossAmountObj->getLuckyLotoSysKillerWaveRate();
                // 缺口额：正数有缺口，负数有盈利
                $info['sysWinAmount'] = $overallLossAmountObj->getRealWinAmount();
                $info['sysTargetAmount'] = $overallLossAmountObj->getDynamicTargetAmount();
                // waveRate 在 1 附近波动，超额就大于 1，有缺口就 小于 1
                if ($info['waveRate'] >= 1) {
                    $waveRateRight1[] = $info;
                } else {
                    $waveRateLeft1[] = $info;
                }
            }
        }
        //如果对应的小盘 有亏损时（无论是否有没有盈利） 取亏损最小的数值 （比如小盘亏5000和小盘亏1000时，大盘按亏1000算）
        if ($waveRateLeft1) {
            // 这里的 waveRate 都小于 1，取接近于 1 的
            //取波动最大的
            $waveRateList = array_column($waveRateLeft1, 'waveRate');
            array_multisort($waveRateList, SORT_DESC, $waveRateLeft1);
            $rtn = array_shift($waveRateLeft1);
        } else {
            // 这里的 waveRate 大于等于 1，取接近于 1 的
            //如果对应的小盘 只有盈利时 取盈利最小的数值 （比如小盘盈利5000和小盘盈利1000时，大盘按盈利1000算）
            //取波动最小的
            $waveRateList = array_column($waveRateRight1, 'waveRate');
            array_multisort($waveRateList, SORT_ASC, $waveRateRight1);
            $rtn = array_shift($waveRateRight1);
        }

        \lbase\Log::info('LuckyLoto-getHundredLuckyLotoSysKillerWaveRate', [
            'betList' => $betList,
            'waveRateLeft1' => $waveRateLeft1,
            'waveRateRight1' => $waveRateRight1,
            'rtn' => $rtn,
        ]);

        return $rtn;
    }



    //定义大盘控流水类型
    const CAPITAL_ROOKIE_BUFF = 1;
    const CAPITAL_NOT_BUFF_NOT_RECHARGE = 2;
    const CAPITAL_NORMAL = 3;
    const CAPITAL_RECHARGE_BUFF_1 = 4;
    const CAPITAL_RECHARGE_BUFF_2 = 5;
    const CAPITAL_RECHARGE_BUFF_3 = 6;
    const CAPITAL_RECHARGE_BUFF_4 = 7;
    const CAPITAL_RECHARGE_BUFF_5 = 8;
    const CAPITAL_RECHARGE_BUFF_6 = 9;
    const CAPITAL_RECHARGE_BUFF_7 = 10;
    const CAPITAL_RECHARGE_BUFF_7_14 = 11;
    const CAPITAL_RECHARGE_BUFF_14_21 = 12;
    const CAPITAL_RECHARGE_BUFF_21_90 = 13;
    const CAPITAL_RECHARGE_BUFF_90 = 14;


    //获取流水类型列表
    public static function getCapitalTypeTextList()
    {
        $list = [
            self::CAPITAL_ROOKIE_BUFF => '新手',
            self::CAPITAL_NOT_BUFF_NOT_RECHARGE => '非新未充',
                //self::CAPITAL_NORMAL=>'普通',
            self::CAPITAL_RECHARGE_BUFF_1 => '新充1',
            self::CAPITAL_RECHARGE_BUFF_2 => '新充2',
            self::CAPITAL_RECHARGE_BUFF_3 => '新充3',
            self::CAPITAL_RECHARGE_BUFF_4 => '新充4',
            self::CAPITAL_RECHARGE_BUFF_5 => '新充5',
            self::CAPITAL_RECHARGE_BUFF_6 => '新充6',
            self::CAPITAL_RECHARGE_BUFF_7 => '新充7',
            self::CAPITAL_RECHARGE_BUFF_7_14 => '新充8-14',
            self::CAPITAL_RECHARGE_BUFF_14_21 => '新充15-21',
            self::CAPITAL_RECHARGE_BUFF_21_90 => '新充22-90',
            self::CAPITAL_RECHARGE_BUFF_90 => '新充91+',
        ];
        return $list;
    }

    public static function getCaptitalTypeTitle($capitalType)
    {
        $list = self::getCapitalTypeTextList();
        return $list[$capitalType] ?? ('capital' . $capitalType);
    }

    //根据用户id获取当前用户使用的杀率类型
    public static function getSysKillerTypeByPlayerIndex($playerIndex, &$debug = null)
    {
        //是否是新手【redis key=user_tag_list_用户ID 字段=rookie 有为 【新手】】
        $isRookieBuff = RedisOpt::getUserTagIsRookieBuffer($playerIndex);
        if ($isRookieBuff) {
            return self::CAPITAL_ROOKIE_BUFF;
        }
        //是否是新充buff【redis key=user_tag_list_用户ID 字段=charge_buff 有为 【新充】】
        // $isRechargeBuff = RedisOpt::getUserTagIsRechargeBuffer($playerIndex);
        //用户的充值次数 【redis key=recharge_all_cnt 字段=用户ID 次数】
        $rechargeCnt = RedisOpt::getPlayerRechargeCnt($playerIndex);

        //        //没有新充  有充值
//        if(!$isRechargeBuff && $rechargeCnt>0){
//            return self::CAPITAL_NORMAL;
//        }
        //没有新充 没有新手  也没有充值
        if ($rechargeCnt <= 0) {
            return self::CAPITAL_NOT_BUFF_NOT_RECHARGE;
        }

        //有新充有充值
        //用户的续充次数 【redis key=user_tag_list_用户ID 字段=charge_buff_renew 次数 】
        $buffRenewgeCnt = RedisOpt::getUserTagChargeBuffRenew($playerIndex);
        $effectiveRechargeCnt = $rechargeCnt - $buffRenewgeCnt;

        if ($debug !== null) {
            $debug['rechargeCnt'] = $rechargeCnt;
            $debug['buffRenewgeCnt'] = $buffRenewgeCnt;
            $debug['effectiveRechargeCnt'] = $effectiveRechargeCnt;
        }

        // 发现有的 charge_buff_renew 数据有问题
        if ($effectiveRechargeCnt <= 0) {
            $effectiveRechargeCnt = $rechargeCnt;
        }

        switch ($effectiveRechargeCnt) {
            case 1:
                return self::CAPITAL_RECHARGE_BUFF_1;
                break;
            case 2:
                return self::CAPITAL_RECHARGE_BUFF_2;
                break;
            case 3:
                return self::CAPITAL_RECHARGE_BUFF_3;
                break;
            case 4:
                return self::CAPITAL_RECHARGE_BUFF_4;
                break;
            case 5:
                return self::CAPITAL_RECHARGE_BUFF_5;
                break;
            case 6:
                return self::CAPITAL_RECHARGE_BUFF_6;
                break;
            case 7:
                return self::CAPITAL_RECHARGE_BUFF_7;
                break;
            case ($effectiveRechargeCnt > 7 && $effectiveRechargeCnt <= 14):
                return self::CAPITAL_RECHARGE_BUFF_7_14;
                break;
            case ($effectiveRechargeCnt > 14 && $effectiveRechargeCnt <= 21):
                return self::CAPITAL_RECHARGE_BUFF_14_21;
                break;
            case ($effectiveRechargeCnt > 21 && $effectiveRechargeCnt <= 90):
                return self::CAPITAL_RECHARGE_BUFF_21_90;
                break;
            default:
                return self::CAPITAL_RECHARGE_BUFF_90;
                break;
        }
    }



    //获取是否执行扶植buff 以及系统杀的结果
    public static function getIsUpBuff($winPointKillerRes, $sysKillerRes, $sysKillerRate, $buffType, $killerType, $buffPlayerIndex, $balanceAmount, $logFileName, $roomInfo = [])
    {
        $rtn = [];
        $branch = '';
        $cls = $roomInfo['cls'] ?? 0;

        $experimental = $buffPlayerIndex % 2 == 1;
        $experimental = 1;

        $a = \llogic\user\UserTagRepo::SystemReadUserBoolTags($buffPlayerIndex, [
            \llogic\user\UserTagName::USER_TAG_WOOL_WHITE,
            \llogic\user\UserTagName::USER_TAG_SUSPECTED_WOOL_WHITE,
            \llogic\user\UserTagName::USER_TAG_SAME_DEVICE_WOOL_WHITE,
            \llogic\user\UserTagName::USER_TAG_UNKNOWN_WOOL_WHITE,

            \llogic\user\UserTagName::USER_TAG_WOOL_PARTY,
            \llogic\user\UserTagName::USER_TAG_SUSPECTED_WOOL_PARTY,
            \llogic\user\UserTagName::USER_TAG_SAME_DEVICE_WOOL,
            \llogic\user\UserTagName::USER_TAG_UNKNOWN_WOOL,

            \llogic\user\UserTagName::USER_TAG_PRE_WITHDRAW_CONTROL,
            \llogic\user\UserTagName::USER_TAG_TP_HIGH_KILL,
            \llogic\user\UserTagName::USER_TAG_TP_AK47_HIGH_KILL,
            \llogic\user\UserTagName::USER_TAG_TP_JOKER_HIGH_KILL,
            \llogic\user\UserTagName::USER_TAG_RUMMY_HIGH_KILL,
            \llogic\user\UserTagName::USER_TAG_HIGH_WITHDRAW_CONTROL,
            \llogic\user\UserTagName::USER_TAG_HIGH_BASE_CONTROL,

        ]);
        // php -r "echo '0' || '0';" 
        $isWoolParty = boolval($a[\llogic\user\UserTagName::USER_TAG_WOOL_PARTY] && !$a[\llogic\user\UserTagName::USER_TAG_WOOL_WHITE]);
        $isWoolParty = $isWoolParty || boolval($a[\llogic\user\UserTagName::USER_TAG_SAME_DEVICE_WOOL] && !$a[\llogic\user\UserTagName::USER_TAG_SAME_DEVICE_WOOL_WHITE]);
        //$isWoolParty = $isWoolParty || boolval($a[\llogic\user\UserTagName::USER_TAG_UNKNOWN_WOOL] && !$a[\llogic\user\UserTagName::USER_TAG_UNKNOWN_WOOL_WHITE]);
        $isSuspectedWoolParty = boolval($a[\llogic\user\UserTagName::USER_TAG_SUSPECTED_WOOL_PARTY] && !$a[\llogic\user\UserTagName::USER_TAG_SUSPECTED_WOOL_WHITE]);
        $isPreWithdrawControl = boolval($a[\llogic\user\UserTagName::USER_TAG_PRE_WITHDRAW_CONTROL]);
        $isTPHighKill = boolval($a[\llogic\user\UserTagName::USER_TAG_TP_HIGH_KILL]);
        $isTPAK47HighKill = boolval($a[\llogic\user\UserTagName::USER_TAG_TP_AK47_HIGH_KILL]);
        $isTPJokerHighKill = boolval($a[\llogic\user\UserTagName::USER_TAG_TP_JOKER_HIGH_KILL]);
        $isRummyHighKill = boolval($a[\llogic\user\UserTagName::USER_TAG_RUMMY_HIGH_KILL]);
        $isHighWithdrawControl = boolval($a[\llogic\user\UserTagName::USER_TAG_HIGH_WITHDRAW_CONTROL]);
        $isHighBaseControl = boolval($a[\llogic\user\UserTagName::USER_TAG_HIGH_BASE_CONTROL]);

        if ($isWoolParty && $experimental) {
            $branch = '羊毛党高概率抑制';
            $rtn['noUp'] = true;
            $rtn['woolKillRate'] = 0.0;

            $woolKillRate = $rtn["woolKillRate"] ?? 0;
            if ((rand() / (getrandmax() + 1)) < $woolKillRate) {
                $rtn['sysKillerRes'] = true;
            }

            \lbase\Log::info('SX.getIsUpBuff.isWoolParty', [
                'branch' => $branch,
                'rtn' => $rtn,
            ]);
        } else if ($isSuspectedWoolParty && $experimental) {
            // 对于百人场，没有新手新充就是不扶植了
            // 对于 RummyPoints 还需要加额外标记 noUp
            // 对于 TeenPatti 还需要在它的代码里面额外判断
            $branch = '疑似羊毛低概率抑制';
            $rtn['noUp'] = true;
            $rtn['woolKillRate'] = 0.33;

            $woolKillRate = $rtn["woolKillRate"] ?? 0;
            if ((rand() / (getrandmax() + 1)) < $woolKillRate) {
                $rtn['sysKillerRes'] = true;
            }

            \lbase\Log::info('SX.getIsUpBuff.isSuspectedWoolParty', [
                'branch' => $branch,
                'rtn' => $rtn,
            ]);
        } else if ($isPreWithdrawControl && $experimental) {
            $branch = '预提现控制低概率抑制';
            $rtn['noUp'] = true;
            $rtn['woolKillRate'] = 0.33;

            $woolKillRate = $rtn["woolKillRate"] ?? 0;
            if ((rand() / (getrandmax() + 1)) < $woolKillRate) {
                $rtn['sysKillerRes'] = true;
            }

            \lbase\Log::info('SX.getIsUpBuff.$isPreWithdrawControl', [
                'branch' => $branch,
                'rtn' => $rtn,
            ]);
        } else if ($isTPHighKill && $experimental && $cls == Common::GAME_TYPE_TEEN_PATTI) {
            $branch = 'TP高频杀不扶植';
            $rtn['noUp'] = true;
            $rtn['woolKillRate'] = 0.33;

            $woolKillRate = $rtn["woolKillRate"] ?? 0;
            if ((rand() / (getrandmax() + 1)) < $woolKillRate) {
                $rtn['sysKillerRes'] = true;
            }

            \lbase\Log::info('SX.getIsUpBuff.$isTPHighKill', [
                'branch' => $branch,
                'rtn' => $rtn,
            ]);
        } else if ($isTPAK47HighKill && $experimental && $cls == Common::GAME_TYPE_TEEN_PATTI_AK47) {
            $branch = 'TP AK47高频杀不扶植';
            $rtn['noUp'] = true;
            $rtn['woolKillRate'] = 0.33;

            $woolKillRate = $rtn["woolKillRate"] ?? 0;
            if ((rand() / (getrandmax() + 1)) < $woolKillRate) {
                $rtn['sysKillerRes'] = true;
            }

            \lbase\Log::info('SX.getIsUpBuff.$isTPAK47HighKill', [
                'branch' => $branch,
                'rtn' => $rtn,
            ]);
        } else if ($isTPJokerHighKill && $experimental && $cls == Common::GAME_TYPE_TEEN_PATTI_JOKER) {
            $branch = 'TP Joker高频杀不扶植';
            $rtn['noUp'] = true;
            $rtn['woolKillRate'] = 0.33;

            $woolKillRate = $rtn["woolKillRate"] ?? 0;
            if ((rand() / (getrandmax() + 1)) < $woolKillRate) {
                $rtn['sysKillerRes'] = true;
            }

            \lbase\Log::info('SX.getIsUpBuff.$isTPJokerHighKill', [
                'branch' => $branch,
                'rtn' => $rtn,
            ]);
        } else if ($isRummyHighKill && $experimental && $cls == Common::GAME_TYPE_RUMMY_POINTS) {
            $branch = 'Rummy高频杀不扶植';
            $rtn['noUp'] = true;
            $rtn['woolKillRate'] = 0.33;

            $woolKillRate = $rtn["woolKillRate"] ?? 0;
            if ((rand() / (getrandmax() + 1)) < $woolKillRate) {
                $rtn['sysKillerRes'] = true;
            }

            \lbase\Log::info('SX.getIsUpBuff.$isRummyHighKill', [
                'branch' => $branch,
                'rtn' => $rtn,
            ]);
        } else if ($isHighWithdrawControl && $experimental) {
            $branch = '高提现控抑制';
            $rtn['noUp'] = true;
            $rtn['woolKillRate'] = 0.33;

            $woolKillRate = $rtn["woolKillRate"] ?? 0;
            if ((rand() / (getrandmax() + 1)) < $woolKillRate) {
                $rtn['sysKillerRes'] = true;
            }

            \lbase\Log::info('SX.getIsUpBuff.$isHighWithdrawControl', [
                'branch' => $branch,
                'rtn' => $rtn,
            ]);
        } else if ($isHighBaseControl && $experimental) {
            $branch = '高分场控抑制';
            $rtn['noUp'] = true;
            $rtn['woolKillRate'] = 0.0;

            $woolKillRate = $rtn["woolKillRate"] ?? 0;
            if ((rand() / (getrandmax() + 1)) < $woolKillRate) {
                $rtn['sysKillerRes'] = true;
            }

            \lbase\Log::info('SX.getIsUpBuff.$isHighBaseControl', [
                'branch' => $branch,
                'rtn' => $rtn,
            ]);
        } else if ($winPointKillerRes) {
            $branch = '有复仇杀不考虑扶植';

            \lbase\Log::info('SX.getIsUpBuff.winPointKillerRes', [
                'branch' => $branch,
                'rtn' => $rtn,
            ]);
        } else if ($buffType == Common::USER_TAG_CHARGE_BUFF && $killerType == Common::ROOM_UNION_TYPE_BUFF) {
            // 有新充 BUFF 且在 BUFF 房间
            // 用户的充值次数
            $rechargeCnt = RedisOpt::getPlayerRechargeCnt($buffPlayerIndex);
            // 用户的续充次数
            $buffRenewgeCnt = RedisOpt::getUserTagChargeBuffRenew($buffPlayerIndex);

            // 获取用户的buff新充额度
            $rechargeBuffCount = RedisOpt::getUserTagRechargeBufferCount($buffPlayerIndex);
            // 获取用户的净赢流水
            $rechargeBuffProfit = RedisOpt::getUserTagRechargeBufferProfit($buffPlayerIndex);
            // 获取用户的净输流水
            $rechargeBuffLoss = RedisOpt::getUserTagRechargeBufferLoss($buffPlayerIndex);

            $ugstat = \llogic\user\UserGameStatRepoV3::SnapshotOrDefault($buffPlayerIndex);
            // 获取用户的总的净赢流水
            $allProfit = intval($ugstat->GetTotalProfit());
            // 获取用户的总的净输流水
            $allLoss = intval($ugstat->GetTotalLoss());

            // 计算用户的净输额度
            // ((小计输钱流水-小计赢钱流水) - max(总计赢钱流水-总计输钱流水+小计输钱流水-小计赢钱流水,0)) / 新充buff额度
            $lossAmount = $rechargeBuffLoss - $rechargeBuffProfit;
            $lossAmount = $lossAmount - max(($allProfit - $allLoss + $lossAmount), 0);

            if ($rechargeBuffCount == 0 || $lossAmount == 0) {
                $lossRate = 0;
            } else {
                $lossRate = bcdiv($lossAmount, $rechargeBuffCount, 3);
            }

            $limitRate1 = min(0.8, (0.55 + 0.05 * ($rechargeCnt - $buffRenewgeCnt)));
            $limitRate2 = min(0.2, (-0.1 + 0.05 * ($rechargeCnt - $buffRenewgeCnt)));

            if ($lossRate > $limitRate1) {
                $branch = '关闭大盘杀开启扶植';
                // 重置大盘杀不执行
                $rtn['sysKillerRes'] = false;
                // 永远执行扶植
                $rtn['isUpBuff'] = true;
            } else if ($lossRate > $limitRate2 && $lossRate <= $limitRate1) {
                if (!$sysKillerRes) {
                    $branch = '没有大盘杀既要被扶植也要被保底杀';
                    $rtn['isUpBuff'] = true;
                    // 既要被扶植也要被保底杀
                    $rtn['upAndDown'] = true;
                } else {
                    $branch = '有大盘杀不扶植';
                }
            } else {
                $branch = '输的太少不扶植';
            }

            \lbase\Log::info('SX.getIsUpBuff.CHARGE_BUFF', [
                'branch' => $branch,
                'buffPlayerIndex' => $buffPlayerIndex,
                'rtn' => $rtn,
                'lossRate' => $lossRate,
                'limitRate1' => $limitRate1,
                'limitRate2' => $limitRate2,
                'rechargeCnt' => $rechargeCnt,
                'buffRenewgeCnt' => $buffRenewgeCnt,
                'lossAmount' => $lossAmount,
                'rechargeBuffCount' => $rechargeBuffCount,
                'rechargeBuffProfit' => $rechargeBuffProfit,
                'rechargeBuffLoss' => $rechargeBuffLoss,
                'allProfit' => $allProfit,
                'allLoss' => $allLoss,
            ]);
        } else {
            $branch = '直接不考虑扶植';

            \lbase\Log::info('SX.getIsUpBuff.ELSE', [
                'branch' => $branch,
                'rtn' => $rtn,
            ]);
        }

        return $rtn;
    }



    //根据用户的buff类型 如果是扶植 计算赢钱重随次数
    public static function getUserRandDealCardCntByBufferType($playerIndex, $betAmount, $buff)
    {
        //新充次数
        if ($buff == Common::USER_TAG_CHARGE_BUFF) {
            return self::getRechargeBuffDealCardRandCnt($playerIndex, $betAmount);
        }

        //新手次数
        if ($buff == Common::USER_TAG_ROOKIE) {
            return self::getRookieBuffDealCardRandCnt();
        }
        return 1;
    }

    //新手buff下 发牌结果重新随机次数
    public static function getRookieBuffDealCardRandCnt()
    {
        if ((rand() / (getrandmax() + 1)) < 0.2) {
            return 2;
        }
        return 1;
    }




    //获取百人场充值buff下 发牌结果重新随机次数
    public static function getRechargeBuffDealCardRandCnt($playerIndex, $betAmount)
    {
        //Log::console_log(__FUNCTION__, $playerIndex . '：下注金额：' . $betAmount);

        //获取用户的buff新充额度
        $rechargeBuffCount = RedisOpt::getUserTagRechargeBufferCount($playerIndex);
        //Log::console_log(__FUNCTION__, $playerIndex . '：buff新充额度：' . $rechargeBuffCount);

        //下注的钱大于新充buff额度 不执行重新随机事件
        if ($betAmount > $rechargeBuffCount * 1.2) {
            //Log::console_log(__FUNCTION__, $playerIndex . '：下注的钱大于新充buff额度*1.2 不执行重新随机事件');
            return 1;
        }

        //获取用户的充值次数
        $rechargeCnt = RedisOpt::getPlayerRechargeCnt($playerIndex);
        //Log::console_log(__FUNCTION__, $playerIndex . '：用户充值次数：' . $rechargeCnt);
        //有效续充值buff次数
        $chargebuffrenew = RedisOpt::getUserTagChargeBuffRenew($playerIndex);
        //Log::console_log(__FUNCTION__, $playerIndex . '：有效续充值buff次数：' . $chargebuffrenew);
        //获取用户的净赢流水
        $rechargeBuffProfit = RedisOpt::getUserTagRechargeBufferProfit($playerIndex);
        //Log::console_log(__FUNCTION__, $playerIndex . '：净赢流水：' . $rechargeBuffProfit);
        //获取用户的净输流水
        $rechargeBuffLoss = RedisOpt::getUserTagRechargeBufferLoss($playerIndex);
        //Log::console_log(__FUNCTION__, $playerIndex . '：净输流水：' . $rechargeBuffLoss);

        //净输额度
        $lossAmount = $rechargeBuffLoss - $rechargeBuffProfit;
        if ($lossAmount == 0 || $rechargeBuffCount == 0) {
            $winRate = 0;
        } else {
            $winRate = bcdiv($lossAmount, $rechargeBuffCount, 3);
        }
        //Log::console_log(__FUNCTION__, $playerIndex . '：净输和充值buff额度比列：' . $winRate);

        //重新随机概率
        //1-max(1-0.7*0.8^（已充值次数-有效续充值buff次数）,min(=1-0.2*0.8^(已充值次数-有效续充值buff次数),(1.5+(已充值次数-有效续充值buff次数)*0.5)*本局下注金额/新充buff充值额度))+max(-0.2*0.8^(已充值次数-有效续充值buff次数),3.9*0.9^(已充值次数-有效续充值buff次数)*(小计输钱流水-小记赢钱流水)/新充buff充值额度);
        // 重随概率=1.4*0.85^(已充值次数-有效续充值buff次数)*min(0.5,(小计输钱流水-小记赢钱流水)/新充buff充值额度））
        $rechargeCnt = $rechargeCnt - $chargebuffrenew;
        $dealCardRandRate = 0.8 * pow(1, $rechargeCnt) * min(0.5, $winRate);
        //Log::console_log(__FUNCTION__, $playerIndex . '：$dealCardRandRate：' . $dealCardRandRate);

        $cnt = 0;
        if ((rand() / (getrandmax() + 1)) < $dealCardRandRate) {
            $cnt = 1;
            //Log::console_log(__FUNCTION__, $playerIndex . '：小数点概率 获得一次：' . $cnt);
        }

        //原始本事具有一次随机次数
        $cnt = $cnt + 1;
        //Log::console_log(__FUNCTION__, $playerIndex . '：原始次数+1 最终次数为：' . $cnt);

        return (int) $cnt;
    }
}
