<?php
/**
 * ai组排 出牌模拟算法
 */

namespace common;

use lib\Log;
use lib\Xredis;
use lib\Config;

class RedisOpt
{
    //获取所有玩法配置列表
    public static function getAllRoomList()
    {
        $roomList = \ldb\ConfigDB::room_type_config_get_all();
        // 这步是做啥的？好像没用啊？拷贝一份用的？
        $roomList = array_reverse($roomList, true);
        // 根据 base 从小到大排序
        $sort_arr = array_column($roomList, 'base');
        array_multisort($sort_arr, SORT_ASC, $roomList);
        return $roomList;
    }

    ####################################################################################################################


    //【净胜分】设置room 配置的方案id 以及方案执行概率
    public static function setRoomPlanInfoOne($utype, $roomCode, array $data)
    {
        $redisKey = Config::get('room_plan_key') . '_' . $utype;
        $redisDb = Config::get('room_plan_db');
        $redisConfig = Config::get('redis_config');

        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->hSet($redisKey, $roomCode, json_encode($data));
    }

    //【净胜分】获取room 方案配置列表
    public static function getRoomPlanInfoList($utype)
    {
        $redisKey = Config::get('room_plan_key') . '_' . $utype;
        $redisDb = Config::get('room_plan_db');
        $redisConfig = Config::get('redis_config');

        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $roomPlanInfoList = $xRedis->hGetAll($redisKey);
        //json格式
        return array_reverse(array_map('json_to_arr', $roomPlanInfoList), true);
    }


    ####################################################################################################################
    //【净胜分】根据计划id 获取发牌规则id
    public static function getRuleIdsByPlanId($planId, $cls = '')
    {
        if ($cls == Common::GAME_TYPE_TEEN_PATTI) {
            $redisKey = Config::get('ai_plan_key') . '_' . Common::GAME_TYPE_TEEN_PATTI;
        } else {
            $redisKey = Config::get('ai_plan_key');
        }
        $redisDb = Config::get('ai_plan_db');
        $redisConfig = Config::get('redis_config');
        $field = $planId;

        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $ruleIds = $xRedis->hGet($redisKey, $field);
        //json格式
        return json_decode($ruleIds, 1);
    }


    //【净胜分】获取发牌规则 方案列表
    public static function getPlanList($cls = '')
    {
        if ($cls == Common::GAME_TYPE_TEEN_PATTI) {
            $redisKey = Config::get('ai_plan_key') . '_' . Common::GAME_TYPE_TEEN_PATTI;
        } else {
            $redisKey = Config::get('ai_plan_key');
        }
        $redisDb = Config::get('ai_plan_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $configList = $xRedis->hGetAll($redisKey);
        return array_reverse(array_map('json_to_arr', $configList), true);
    }


    //【净胜分】设置方案信息  根据方案id
    public static function setPlanInfoByPlanId($planId, array $data, $cls = '')
    {
        if ($cls == Common::GAME_TYPE_TEEN_PATTI) {
            $redisKey = Config::get('ai_plan_key') . '_' . Common::GAME_TYPE_TEEN_PATTI;
        } else {
            $redisKey = Config::get('ai_plan_key');
        }
        $redisDb = Config::get('ai_plan_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->hSet($redisKey, $planId, json_encode($data));
    }

    ####################################################################################################################
    //【净胜分】删除rule 根据id
    public static function delRuleById($ruleId)
    {
        $redisKey = Config::get('ai_rule_key');
        $redisDb = Config::get('ai_rule_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->hdel($redisKey, $ruleId);
    }

    //【净胜分】添加rule
    public static function addRuleById($ruleId, array $data)
    {
        $redisKey = Config::get('ai_rule_key');
        $redisDb = Config::get('ai_rule_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->hSet($redisKey, $ruleId, json_encode($data));
    }

    //【净胜分】根据计划id 获取发牌规则id teenpatty需要传cls参数
    public static function getRuleList($cls = '')
    {
        if ($cls == Common::GAME_TYPE_TEEN_PATTI) {
            //获取teenpatti 牌型规则列表
            $ruleList = BASE_ZJ_TYPE_POWER_SCORE;
        } else {
            $redisKey = Config::get('ai_rule_key');
            $redisDb = Config::get('ai_plan_db');
            $redisConfig = Config::get('redis_config');
            $xRedis = Xredis::getInstance($redisConfig, $redisDb);
            $ruleList = $xRedis->hGetAll($redisKey);
            $ruleList = array_reverse(array_map('json_to_arr', $ruleList), true);
        }
        return $ruleList;
    }

    ####################################################################################################################

    //获取净胜分奖池金额
    public static function setJackpotAmountOne($roomCode, $amount)
    {
        $redisKey = Config::get('jackpot_key');
        $redisDb = Config::get('jackpot_db');
        $redisConfig = Config::get('redis_config');

        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->hSet($redisKey, $roomCode, $amount);
    }

    //获取净胜分所有奖池金额
    public static function getJackpotAmountList()
    {
        $redisKey = Config::get('jackpot_key');
        $redisDb = Config::get('jackpot_db');
        $redisConfig = Config::get('redis_config');

        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->hGetAll($redisKey);
    }

    ####################################################################################################################
    //获取用户充buff总额度
    public static function getUserTagRechargeBufferCount($playerIndex)
    {
        $wallet3 = \llogic\user\WalletRepoV3::SnapshotOrDefault($playerIndex);
        return $wallet3->rbuff_quota ?? 0;
    }





    //获取用户充buff赢钱流水
    public static function getUserTagRechargeBufferProfit($playerIndex)
    {
        $wallet3 = \llogic\user\WalletRepoV3::SnapshotOrDefault($playerIndex);
        return $wallet3->rbuff_profit ?? 0;
    }

    //获取用户充buff输钱流水
    public static function getUserTagRechargeBufferLoss($playerIndex)
    {
        $wallet3 = \llogic\user\WalletRepoV3::SnapshotOrDefault($playerIndex);
        return $wallet3->rbuff_loss ?? 0;
    }
    //有效续充值buff次数
    public static function getUserTagChargeBuffRenew($playerIndex)
    {
        $wallet3 = \llogic\user\WalletRepoV3::SnapshotOrDefault($playerIndex);
        return $wallet3->rbuff_renew_count ?? 0;
    }


    //获取用户是否是充buff
    public static function getUserTagIsRechargeBuffer($playerIndex)
    {
        return boolval(\llogic\user\UserTagRepo::SystemReadUserBoolTag($playerIndex, Common::USER_TAG_CHARGE_BUFF));
    }


    //获取用户是否是tp透杀
    public static function getUserTagIsTpDiscloseKiller($playerIndex, $cls = Common::GAME_TYPE_TEEN_PATTI)
    {
        if (0) {
            $a = \llogic\user\UserTagRepo::SystemReadUserBoolTags($playerIndex, [
                Common::USER_TAG_TP_DISCLOSE_KILLER,
                \llogic\user\UserTagName::USER_TAG_WOOL_PARTY,
                \llogic\user\UserTagName::USER_TAG_WOOL_WHITE,
            ]);
            // php -r "echo '0' || '0';" 
            return boolval($a[Common::USER_TAG_TP_DISCLOSE_KILLER] || ($a[\llogic\user\UserTagName::USER_TAG_WOOL_PARTY] && !$a[\llogic\user\UserTagName::USER_TAG_WOOL_WHITE]));
        }

        if ($cls == Common::GAME_TYPE_TEEN_PATTI_AK47) {
            $name = Common::USER_TAG_TP_AK47_DISCLOSE_KILLER;
        } else if ($cls == Common::GAME_TYPE_TEEN_PATTI_JOKER) {
            $name = Common::USER_TAG_TP_JOKER_DISCLOSE_KILLER;
        } else {
            $name = Common::USER_TAG_TP_DISCLOSE_KILLER;
        }
        return boolval(\llogic\user\UserTagRepo::SystemReadUserBoolTag($playerIndex, $name));
    }


    //获取玩家是否新手buff
    public static function getUserTagIsRookieBuffer($playerIndex)
    {
        return boolval(\llogic\user\UserTagRepo::SystemReadUserBoolTag($playerIndex, Common::USER_TAG_ROOKIE));
    }


    //获取玩家是高手buff
    public static function getUserTagIsWinBuffer($playerIndex, $cls)
    {
        return boolval(\llogic\user\UserTagRepo::SystemReadUserTmpTag($playerIndex, Common::USER_TAG_WIN_BUFF . '_' . $cls));
    }

    //获取玩家是大户buff
    public static function getUserTagIsRichBuffer($playerIndex, $cls)
    {
        return boolval(\llogic\user\UserTagRepo::SystemReadUserTmpTag($playerIndex, Common::USER_TAG_RICH_BUFF . '_' . $cls));
    }



    //设置用户上一次rummy发牌的牌型
    public static function setUserTagLastRummyDealCardType(int $playerIndex, string $rummyDealCardType): bool
    {
        return boolval(\llogic\user\UserTagRepo::SystemEditUserTmpTag($playerIndex, Common::USER_TAG_LAST_RUMMY_DEAL_CARD_TYPE, $rummyDealCardType));
    }


    //获取用户上一次rummy发牌的类型
    public static function getUserTagLastRummyDealCardType(int $playerIndex): string
    {
        return (\llogic\user\UserTagRepo::SystemReadUserTmpTag($playerIndex, Common::USER_TAG_LAST_RUMMY_DEAL_CARD_TYPE));
    }


    //根据玩家的buff类型 获取当前使用的buff
    //buff关系优先级
    //新手>高手/小黑屋>新充

    public static function getPlayerNowUseBuff($playerIndex, $cls = '')
    {
        //没有buff用户  执行正常buff判定
        if (!$playerIndex) {
            return Common::USER_TAG_NORMAL_BUFF;
        }
        //大户buff
        $isRichBuff = RedisOpt::getUserTagIsRichBuffer($playerIndex, $cls);
        if ($isRichBuff) {
            return Common::USER_TAG_RICH_BUFF;
        }

        //高手buff
        $isWinBuff = RedisOpt::getUserTagIsWinBuffer($playerIndex, $cls);
        if ($isWinBuff) {
            return Common::USER_TAG_WIN_BUFF;
        }
        //新充buff
        $isRechargeBuff = RedisOpt::getUserTagIsRechargeBuffer($playerIndex);
        if ($isRechargeBuff) {
            return Common::USER_TAG_CHARGE_BUFF;
        }
        //新手buff
        $isRookieBuff = RedisOpt::getUserTagIsRookieBuffer($playerIndex);
        if ($isRookieBuff) {
            return Common::USER_TAG_ROOKIE;
        }

        //正常玩家没有buff
        return Common::USER_TAG_NORMAL_BUFF;
    }








    //获取用户标签 参与玩过的最大场倍数
    public static function getUserTagPlayMaxBase($playerIndex)
    {
        return (\llogic\user\UserTagRepo::SystemReadUserTmpTag($playerIndex, Common::USER_TAG_PLAY_MAX_BASE));
    }


    //获取用户标签 参与玩过的最大场倍数
    public static function setUserTagPlayMaxBase($playerIndex, $maxBase)
    {
        return (\llogic\user\UserTagRepo::SystemEditUserTmpTag($playerIndex, Common::USER_TAG_PLAY_MAX_BASE, $maxBase));
    }


    //获取用户标签 新手保护真人已经发过的牌型
    public static function getUserTagRookieBuffDealCardsZ($playerIndex)
    {
        return json_decode(\llogic\user\UserTagRepo::SystemReadUserTmpTag($playerIndex, Common::USER_TAG_ROOKIE_BUFF_Z), true);
    }

    //获取用户标签 新手保护真人已经发过的牌型
    public static function setUserTagRookieBuffDealCardsZ($playerIndex, $data)
    {
        return (\llogic\user\UserTagRepo::SystemEditUserTmpTag($playerIndex, Common::USER_TAG_ROOKIE_BUFF_Z, json_encode($data)));
    }


    //获取用户标签 ai已经发过的牌型
    public static function getUserTagRookieBuffDealCardsA($playerIndex)
    {
        return json_decode(\llogic\user\UserTagRepo::SystemReadUserTmpTag($playerIndex, Common::USER_TAG_ROOKIE_BUFF_A), true);
    }

    //获取用户标签 ai已经发过的牌型
    public static function setUserTagRookieBuffDealCardsA($playerIndex, $data)
    {
        return (\llogic\user\UserTagRepo::SystemEditUserTmpTag($playerIndex, Common::USER_TAG_ROOKIE_BUFF_A, json_encode($data)));
    }

    //获取用户标签 设置跳场保护使用次数
    public static function setUserTagChangeBaseProtectUseCnt($playerIndex, $useCnt)
    {
        return (\llogic\user\UserTagRepo::SystemEditUserTmpTag($playerIndex, Common::USER_TAG_CHANGE_BASE_PROTECT_USE_CNT, $useCnt));
    }


    //获取tp新手发牌顺序
    public static function getUserTagTpRooikeDealCardList(int $playerIndex): array
    {
        $list = (\llogic\user\UserTagRepo::SystemReadUserTmpTag($playerIndex, Common::USER_TAG_TP_ROOKIE_DEAL_CARD_LIST));
        if ($list) {
            return json_decode($list, true);
        } else {
            return [];
        }
    }

    //设置tp新手发牌顺序
    public static function setUserTagTpRookieDealCardList(int $playerIndex, array $list): bool
    {
        return boolval(\llogic\user\UserTagRepo::SystemEditUserTmpTag($playerIndex, Common::USER_TAG_TP_ROOKIE_DEAL_CARD_LIST, json_encode($list)));

    }

    //获取tp新手 ai发第二大牌的概率累积值
    public static function getUserTagTpRookieSecondCardRateX(int $playerIndex): int
    {
        return intval(\llogic\user\UserTagRepo::SystemReadUserTmpTag($playerIndex, Common::USER_TAG_TP_ROOKIE_SECOND_CARD_RATE_X));
    }

    //设置p新手 ai发第二大牌的概率累积值
    public static function setUserTagTpRookieSecondCardRateX(int $playerIndex, int $rateX)
    {
        return (\llogic\user\UserTagRepo::SystemEditUserTmpTagIncrBy($playerIndex, Common::USER_TAG_TP_ROOKIE_SECOND_CARD_RATE_X, $rateX));
    }


    //设置p新手 ai发第二大牌的概率累积值
    public static function clearUserTagTpRookieSecondCardRateX(int $playerIndex): bool
    {
        return boolval(\llogic\user\UserTagRepo::SystemEditUserTmpTag($playerIndex, Common::USER_TAG_TP_ROOKIE_SECOND_CARD_RATE_X, 0));
    }



    ####################################################################################################################

    ####################################################################################################################


    //获取pr波动参数配置
    public static function getPrDymaicOptConfigOne($currency, $roomCode, $jackPoolName = '')
    {
        if (!$jackPoolName) {
            $roomInfo = parse_room_type($roomCode);
            $dynamicFieldName = Common::getPoolNameByClsAndBase($roomInfo['cls'], $roomInfo['base']);
        } else {
            $dynamicFieldName = $jackPoolName;
        }
        if ($currency == Common::CURRENCY_TYPE_CASH) {
            $redisKey = Config::get('ai_dynamic_config_key');
        } else {
            $redisKey = Config::get('ai_dynamic_config_key') . '_' . $currency;
        }
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $infoJson = $xRedis->hGet($redisKey, $dynamicFieldName);
        return json_decode($infoJson, 1);
    }

    //获取pr波动参数配置
    public static function getPrTpDymaicOptConfigOne($currency, $roomCode, $jackPoolName = '')
    {
        if (!$jackPoolName) {
            $roomInfo = parse_room_type($roomCode);
            $dynamicFieldName = Common::getPoolNameByClsAndBase($roomInfo['cls'], $roomInfo['base']);
        } else {
            $dynamicFieldName = $jackPoolName;
        }
        if ($currency == Common::CURRENCY_TYPE_CASH) {
            $redisKey = Config::get('tp_ai_dynamic_config_key');
        } else {
            $redisKey = Config::get('tp_ai_dynamic_config_key') . '_' . $currency;
        }
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $infoJson = $xRedis->hGet($redisKey, $dynamicFieldName);
        return json_decode($infoJson, 1);
    }



    //获取pr波动参数配置
    public static function getPrDymaicOptConfigList($currency)
    {
        if ($currency == Common::CURRENCY_TYPE_CASH) {
            $redisKey = Config::get('ai_dynamic_config_key');
        } else {
            $redisKey = Config::get('ai_dynamic_config_key') . '_' . $currency;
        }
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $infoJson = $xRedis->hGetAll($redisKey);
        return array_reverse(array_map('json_to_arr', $infoJson), true);
    }

    //获取pr波动参数配置
    public static function getPrTpDymaicOptConfigList($currency)
    {
        if ($currency == Common::CURRENCY_TYPE_CASH) {
            $redisKey = Config::get('tp_ai_dynamic_config_key');
        } else {
            $redisKey = Config::get('tp_ai_dynamic_config_key') . '_' . $currency;
        }
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $infoJson = $xRedis->hGetAll($redisKey);
        return array_reverse(array_map('json_to_arr', $infoJson), true);
    }

    //获取pr波动参数配置
    public static function setPrDymaicOptConfig($currency, array $data)
    {
        if ($currency == Common::CURRENCY_TYPE_CASH) {
            $redisKey = Config::get('ai_dynamic_config_key');
        } else {
            $redisKey = Config::get('ai_dynamic_config_key') . '_' . $currency;
        }
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->hMset($redisKey, $data);
    }



    //获取pr波动参数配置
    public static function setPrTpDymaicOptConfig($currency, array $data)
    {
        if ($currency == Common::CURRENCY_TYPE_CASH) {
            $redisKey = Config::get('tp_ai_dynamic_config_key');
        } else {
            $redisKey = Config::get('tp_ai_dynamic_config_key') . '_' . $currency;
        }
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->hMset($redisKey, $data);
    }

    //获取ai分配得到的性格
    public static function getTpAiAssignDisposition($playerIndex)
    {
        return \llogic\game\RobotDispositionRepo::RobotDispositionGet($playerIndex);

        $redisKey = Config::get('tp_ai_assign_disposition_key');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $aiDispositionInfo = json_decode($xRedis->hGet($redisKey, $playerIndex), 1);
        if ($aiDispositionInfo && $aiDispositionInfo['expires_time'] >= time()) {
            return $aiDispositionInfo['disposition'];
        } else {
            return '';
        }
    }

    //设置ai分配得到的性格
    public static function setTpAiAssignDisposition($playerIndex, $disposition, $expiresTime)
    {
        return \llogic\game\RobotDispositionRepo::RobotDispositionSet($playerIndex, $disposition);


        $redisKey = Config::get('tp_ai_assign_disposition_key');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $aiDispositionInfo['expires_time'] = $expiresTime;
        $aiDispositionInfo['disposition'] = $disposition;
        return $xRedis->hSet($redisKey, $playerIndex, json_encode($aiDispositionInfo));
    }

    //清除ai分配得到的性格
    public static function clearTpAiAssignDisposition()
    {
        return;

        $redisKey = Config::get('tp_ai_assign_disposition_key');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->del($redisKey);
    }


    //获取pr波动参数配置
    public static function setTpAiDispositionRateConfig($currency, array $data)
    {
        if ($currency == Common::CURRENCY_TYPE_CASH) {
            $redisKey = Config::get('tp_ai_disposition_rate_config_key');
        } else {
            $redisKey = Config::get('tp_ai_disposition_rate_config_key') . '_' . $currency;
        }
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->hMset($redisKey, $data);
    }


    //获取pr波动参数配置
    public static function getTpAiDispositionRateConfigOne($currency, $roomCode, $jackPoolName = '')
    {
        if (!$jackPoolName) {
            $roomInfo = parse_room_type($roomCode);
            $dynamicFieldName = Common::getPoolNameByClsAndBase($roomInfo['cls'], $roomInfo['base']);
        } else {
            $dynamicFieldName = $jackPoolName;
        }
        if ($currency == Common::CURRENCY_TYPE_CASH) {
            $redisKey = Config::get('tp_ai_disposition_rate_config_key');
        } else {
            $redisKey = Config::get('tp_ai_disposition_rate_config_key') . '_' . $currency;
        }
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $infoJson = $xRedis->hGet($redisKey, $dynamicFieldName);
        return json_decode($infoJson, 1);
    }


    //获取pr波动参数配置
    public static function getTpAiDispositionRateConfigList($currency): array
    {
        if ($currency == Common::CURRENCY_TYPE_CASH) {
            $redisKey = Config::get('tp_ai_disposition_rate_config_key');
        } else {
            $redisKey = Config::get('tp_ai_disposition_rate_config_key') . '_' . $currency;
        }
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $infoJson = $xRedis->hGetAll($redisKey);
        return array_reverse(array_map('json_to_arr', $infoJson), true);
    }

    //设置tp ai的性格配置
    public static function setTpAiDispositionConfig(array $data)
    {
        $redisKey = Config::get('tp_ai_disposition_config_key');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->set($redisKey, json_encode($data));
    }

    //设置tp ai的性格配置
    public static function getTpAiDispositionConfig()
    {
        $redisKey = Config::get('tp_ai_disposition_config_key');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return json_decode($xRedis->get($redisKey), 1);
    }



    //获取pr的发牌方案配置
    public static function getPrPlanInfoOne($roomCode, $uType)
    {
        $roomInfo = parse_room_type($roomCode);
        $redisKey = Config::get('profit_room_plan_key') . '_' . $uType . '_' . $roomInfo['currency'];
        $redisDb = Config::get('room_plan_db');
        $redisConfig = Config::get('redis_config');
        $field = Common::getPoolBaseByClsAndBase($roomInfo['cls'], $roomInfo['base']) . '_jack_pool';

        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $infoJson = $xRedis->hGet($redisKey, $field);
        return json_decode($infoJson, 1);
    }

    //获取pr的发牌方案配置
    public static function getPrPlanInfoList($uType, $currency, $isArr = false)
    {
        $redisKey = Config::get('profit_room_plan_key') . '_' . $uType . '_' . $currency;
        $redisDb = Config::get('room_plan_db');
        $redisConfig = Config::get('redis_config');

        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $planJsonList = $xRedis->hGetAll($redisKey);
        if ($isArr) {
            return array_reverse(array_map('json_to_arr', $planJsonList), true);
        } else {
            return $planJsonList;
        }
    }


    //获取pr配置
    public static function getPrConfigList()
    {
        $redisKey = Config::get('ai_profit_rate_config_key');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');

        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $jsonInfo = $xRedis->get($redisKey);
        return json_decode($jsonInfo, 1);
    }

    //获取pr配置
    public static function getTpPrConfigList()
    {
        $redisKey = Config::get('tp_ai_profit_rate_config_key');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');

        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $jsonInfo = $xRedis->get($redisKey);
        return json_decode($jsonInfo, 1);
    }

    //获取pr配置
    public static function setPrConfig(array $data)
    {
        $redisKey = Config::get('ai_profit_rate_config_key');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');

        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->set($redisKey, json_encode($data));
    }


    //获取pr配置
    public static function setTpPrConfig(array $data)
    {
        $redisKey = Config::get('tp_ai_profit_rate_config_key');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->set($redisKey, json_encode($data));
    }


    //获取pr 幸运配置
    public static function getPrLuckyConfig()
    {
        $redisKey = Config::get('ai_lucky_config_key');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');

        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $jsonInfo = $xRedis->get($redisKey);
        return json_decode($jsonInfo, 1);
    }


    //设置pr 幸运配置
    public static function setPrLuckyConfig(array $data)
    {
        $redisKey = Config::get('ai_lucky_config_key');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');

        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->set($redisKey, json_encode($data));
    }

    //获取pr 霉运配置
    public static function getPrUnLuckyConfig()
    {
        $redisKey = Config::get('ai_unlucky_config_key');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');

        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $jsonInfo = $xRedis->get($redisKey);
        return json_decode($jsonInfo, 1);
    }

    //设置pr 霉运配置
    public static function setPrUnLuckyConfig(array $data)
    {
        $redisKey = Config::get('ai_unlucky_config_key');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');

        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->set($redisKey, json_encode($data));
    }


    //批量设置pr的发牌方案配置
    public static function setPrPlanInfo($uType, $currency, array $data)
    {
        $redisKey = Config::get('profit_room_plan_key') . '_' . $uType . '_' . $currency;
        $redisDb = Config::get('room_plan_db');
        $redisConfig = Config::get('redis_config');

        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->hMset($redisKey, $data);
    }



    //获取盈利率 ai等级配置列表
    public static function getPrAiLevelConfigList($maxCardNum = 13)
    {
        $maxCardNum = in_array($maxCardNum, [13, 10]) ? $maxCardNum : 13;
        $redisKey = Config::get('ai_level_key');
        $redisDb = Config::get('ai_level_db');
        $redisConfig = Config::get('redis_config');
        $field = 'ai_level_' . $maxCardNum;

        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $levelConfigListJson = $xRedis->hGet($redisKey, $field);
        return json_decode($levelConfigListJson, 1);
    }

    //获取盈利率 ai等级配置列表
    public static function setPrAiLevelConfig($maxCardNum, array $data)
    {
        $maxCardNum = in_array($maxCardNum, [13, 10]) ? $maxCardNum : 13;
        $redisKey = Config::get('ai_level_key');
        $redisDb = Config::get('ai_level_db');
        $redisConfig = Config::get('redis_config');
        $field = 'ai_level_' . $maxCardNum;

        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->hSet($redisKey, $field, json_encode($data));

    }



    //获取用户暗牌摸到癞子的概率配置表
    public static function getDarkRateConfigList()
    {
        $redisKey = Config::get('dark_config_key');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return json_decode($xRedis->get($redisKey), 1);
    }

    //获取用户暗牌摸到癞子的概率配置表
    public static function setDarkRateConfig(array $data)
    {
        $redisKey = Config::get('dark_config_key');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->set($redisKey, json_encode($data));
    }



    //设置用户新手保护期间发牌牌型
    public static function setPrRookieBuffDealCardConfig(array $data)
    {
        $redisKey = Config::get('z_rookie_buff_dealcard_config_key');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->set($redisKey, json_encode($data));
    }

    //根据牌的数量获取 牌型配置表 $maxCardNum  13 或者 10
    public static function getPrRookieBuffDealCardConfig()
    {
        $redisKey = Config::get('z_rookie_buff_dealcard_config_key');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $configInfoJson = $xRedis->get($redisKey);
        $configInfo = json_decode($configInfoJson, 1);
        return $configInfo;
    }



    //根据牌的数量获取 牌型配置表 $maxCardNum  13 或者 10
    public static function getPrCardsGroupConfigByMaxCardNum($maxCardNum = 13, $isGroup = true)
    {
        if (!in_array($maxCardNum, [13, 10])) {
            $maxCardNum = 13;
        }
        $redisKey = $maxCardNum == 13 ? Config::get('z_deal_13card_rate_config_key') : Config::get('z_deal_10card_rate_config_key');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');

        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $configInfoJson = $xRedis->get($redisKey);
        $configInfo = json_decode($configInfoJson, 1);

        if ($isGroup) {
            //获取定义的牌组名称列表
            $grouNameList = Common::getProfitGroupNameList();
            $rtn = [];
            foreach ($grouNameList as $key => $val) {
                $rtn['normal'][$key] = $configInfo[$key]['normal'];
                $rtn['up'][$key] = $configInfo[$key]['up'];
                $rtn['down'][$key] = $configInfo[$key]['down'];
                //$rtn['lucky'][$key]     = $configInfo[$key]['lucky'];
                //$rtn['unlucky'][$key]   = $configInfo[$key]['unlucky'];
                $rtn['novice'][$key] = $configInfo[$key]['novice'];
                $rtn['recharge'][$key] = $configInfo[$key]['recharge'];
            }
            return $rtn;
        } else {
            return $configInfo;
        }

    }



    //设置 牌型配置表 $maxCardNum  13 或者 10
    public static function setPrCardsGroupConfigByMaxCardNum($maxCardNum, array $data)
    {
        if (!in_array($maxCardNum, [13, 10])) {
            $maxCardNum = 13;
        }
        $redisKey = $maxCardNum == 13 ? Config::get('z_deal_13card_rate_config_key') : Config::get('z_deal_10card_rate_config_key');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');

        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->set($redisKey, json_encode($data));
    }


    //盈利率 设置机器人携带bounus比例
    public static function setPrRobotBounusRate($currency, array $data)
    {
        $redisKey = Config::get('ai_bounus_rate_config_key') . '_' . $currency;
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');

        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $xRedis->hMset($redisKey, $data);
    }

    //盈利率 获取机器人携带bounus比例
    public static function getPrRobotBounusRate($currency)
    {
        $redisKey = Config::get('ai_bounus_rate_config_key') . '_' . $currency;
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');

        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->hGetAll($redisKey);
    }


    //盈利率 设置玩法预设收益比例
    public static function setPrWinRate($currency, array $data)
    {
        foreach ($data as $key => $val) {
            $data[$key] = json_encode($val);
        }
        $redisKey = Config::get('ai_win_rate_config_key') . '_' . $currency;
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $xRedis->hMset($redisKey, $data);
    }

    //盈利率 获取玩法预设收益比例
    public static function getPrWinRate($currency)
    {
        $redisKey = Config::get('ai_win_rate_config_key') . '_' . $currency;
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $list = $xRedis->hGetAll($redisKey);
        foreach ($list as $key => $val) {
            $list[$key] = json_decode($val, 1);
        }
        return $list;
    }

    //盈利率  获取单个玩法预设收益比例
    public static function getPrWinRateOneV2($jackportName, $currency, $capitalType)
    {
        $redisKey = Config::get('ai_win_rate_config_key') . '_' . $currency;
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $info = $xRedis->hGet($redisKey, $jackportName);
        if ($info) {
            $info = json_decode($info, 1);
        }
        return isset($info[$capitalType]) ? $info[$capitalType] : 0;
    }

    //盈利率 强杀干预系数
    public static function setPrRoomConfig($currency, array $data)
    {
        foreach ($data as $key => $val) {
            $data[$key] = json_encode($val);
        }
        $redisKey = Config::get('ai_room_config_key') . '_' . $currency;
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $xRedis->hMset($redisKey, $data);
    }

    //盈利率  强杀干预系数
    public static function getPrRoomConfigOne($jackportName, $currency)
    {
        $redisKey = Config::get('ai_room_config_key') . '_' . $currency;
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $info = $xRedis->hGet($redisKey, $jackportName);
        $info = json_decode($info, 1);
        if (empty($info['win_score_killer_base'])) {
            $info['win_score_killer_base'] = 1000;
        }
        if (empty($info['initial_rate'])) {
            $info['initial_rate'] = 0.08;
        }
        if (empty($info['tolerance_factor'])) {
            $info['tolerance_factor'] = 0.5;
        }
        if (empty($info['float_rate'])) {
            $info['float_rate'] = 0.25;
        }
        if (!isset($info['buff_coefficient'][0]) || empty($info['buff_coefficient'][0])) {
            $info['buff_coefficient'][0] = 1;
        }
        if (!isset($info['buff_coefficient'][1]) || empty($info['buff_coefficient'][1])) {
            $info['buff_coefficient'][1] = 1.5;
        }
        if (!isset($info['buff_coefficient'][2]) || empty($info['buff_coefficient'][2])) {
            $info['buff_coefficient'][2] = 3;
        }
        return $info;
    }


    //设置金骰子配置
    public static function setGoldDiceConfig(array $data)
    {
        $redisKey = Config::get('ai_gold_dice_config_key');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->set($redisKey, json_encode($data));
    }

    //设置金骰子配置
    public static function getGoldDiceConfig()
    {
        $redisKey = Config::get('ai_gold_dice_config_key');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $info = $xRedis->get($redisKey);
        return json_decode($info, 1);
    }



    //获取大盘营收列表
    public static function getPrRoomWalletLogList()
    {
        $redisDb = Config::get('room_wallet_log_stat_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $list = [];
        $time = time();
        for ($i = 0; $i <= 90; $i++) {
            $date = date("Ymd", strtotime('-' . $i . ' days', $time));

            if ($date < 20220630) {
                continue;
            }

            $redisKey = Config::get('room_wallet_log_stat_key_prefix') . $date;

            $info = $xRedis->hGetAll($redisKey);
            if (!$info) {
                continue;
            }
            $infoList = [];
            foreach ($info as $key => $amount) {
                // HASH ai_room_data_20211020(玩法_货币_最大人数_底注点数_杀率类型_字段名 => 金额)
                $keyArr = explode('_', $key);
                // 杀率类型
                $capitaltype = $keyArr[count($keyArr) - 2];
                // 字段名
                $type = $keyArr[count($keyArr) - 1];
                // 玩法_货币_最大人数_底注点数
                $roomInfoKey = substr($key, 0, -2);
                $roomInfo = parse_room_type($roomInfoKey);
                if ($roomInfo['currency'] != Common::CURRENCY_TYPE_CASH) {
                    continue;
                }
                // 设置房间信息
                $tmpJackportName = Common::getPoolNameByClsAndBase($roomInfo['cls'], $roomInfo['base']);
                if (in_array($roomInfo['cls'], [Common::GAME_TYPE_RUMMY_POINTS, Common::GAME_TYPE_RUMMY_POOL, Common::GAME_TYPE_RUMMY_DEALS, Common::GAME_TYPE_RUMMY_10_CARD])) {
                    $infoList[$tmpJackportName][$capitaltype]['cls'] = 'Rummy';
                    $infoList[$tmpJackportName][$capitaltype]['cls_enum'] = 1;
                } else {
                    $infoList[$tmpJackportName][$capitaltype]['cls'] = Common::getGameTypeTextList($roomInfo['cls']);
                    $infoList[$tmpJackportName][$capitaltype]['cls_enum'] = $roomInfo['cls'];
                }
                //流水相加
                if ($type == 'c') {
                    $infoList[$tmpJackportName][$capitaltype][$type] = $amount;
                } else {
                    $infoList[$tmpJackportName][$capitaltype][$type] = abs($amount);
                }
                if ($type == 'w' && $infoList[$tmpJackportName][$capitaltype]['w'] > 0) {
                    $infoList[$tmpJackportName][$capitaltype]['w'] = ($infoList[$tmpJackportName][$capitaltype]['w'] / Common::getGameTypeCommissionRate($roomInfo['cls']));
                }
                if ($type == 'w' || $type == 'l' || $type = 'tb') {
                    if (!isset($infoList[$tmpJackportName][$capitaltype]['wl'])) {
                        $infoList[$tmpJackportName][$capitaltype]['wl'] = 0;
                    }
                    $infoList[$tmpJackportName][$capitaltype]['w'] = isset($infoList[$tmpJackportName][$capitaltype]['w']) ? $infoList[$tmpJackportName][$capitaltype]['w'] : 0;
                    $infoList[$tmpJackportName][$capitaltype]['l'] = isset($infoList[$tmpJackportName][$capitaltype]['l']) ? $infoList[$tmpJackportName][$capitaltype]['l'] : 0;
                    $infoList[$tmpJackportName][$capitaltype]['wl'] = ($infoList[$tmpJackportName][$capitaltype]['w'] + $infoList[$tmpJackportName][$capitaltype]['l']);

                    //                    if($infoList[$tmpJackportName][$capitaltype]['cls'] == 'Rummy' || $infoList[$tmpJackportName][$capitaltype]['cls_enum']==Common::GAME_TYPE_TRUCO_CLEAN || $infoList[$tmpJackportName][$capitaltype]['cls_enum']==Common::GAME_TYPE_TRUCO){
//                        $infoList[$tmpJackportName][$capitaltype]['w'] = isset($infoList[$tmpJackportName][$capitaltype]['w'])?$infoList[$tmpJackportName][$capitaltype]['w']:0;
//                        $infoList[$tmpJackportName][$capitaltype]['l'] = isset($infoList[$tmpJackportName][$capitaltype]['l'])?$infoList[$tmpJackportName][$capitaltype]['l']:0;
//                        $infoList[$tmpJackportName][$capitaltype]['wl'] = ($infoList[$tmpJackportName][$capitaltype]['w']+$infoList[$tmpJackportName][$capitaltype]['l']);
//                    }else{
//                        $infoList[$tmpJackportName][$capitaltype]['wl'] = isset($infoList[$tmpJackportName][$capitaltype]['tb'])?$infoList[$tmpJackportName][$capitaltype]['tb']:0;
//                    }
                }
                $tmpJackportNameList[] = $tmpJackportName;
                $infoList[$tmpJackportName]['cls'] = Common::getGameTypeTextList($roomInfo['cls']);
                $infoList[$tmpJackportName]['cls_enum'] = $roomInfo['cls'];
                $infoList[$tmpJackportName]['base'] = explode("_", $tmpJackportName)[0] * 0.001;
            }
            foreach ($infoList as $jackportName => $statInfo) {
                if (!isset($infoList[$jackportName]['all']['c'])) {
                    $infoList[$jackportName]['all']['c'] = 0;
                }
                $infoList[$jackportName]['all']['c'] += array_sum(array_column($statInfo, 'c'));

                if (!isset($infoList[$jackportName]['all']['wl'])) {
                    $infoList[$jackportName]['all']['wl'] = 0;
                }
                $infoList[$jackportName]['all']['wl'] += array_sum(array_column($statInfo, 'wl'));
            }

            foreach ($infoList as $jackportName => $statInfo) {
                foreach ($statInfo as $capitaltype => $info) {
                    if (!isset($infoList['all'][$capitaltype]['c'])) {
                        $infoList['all'][$capitaltype]['c'] = 0;
                    }
                    $infoList['all'][$capitaltype]['c'] += (isset($info['c']) ? intval($info['c']) : 0);

                    if (!isset($infoList['all'][$capitaltype]['wl'])) {
                        $infoList['all'][$capitaltype]['wl'] = 0;
                    }
                    $infoList['all'][$capitaltype]['wl'] += (isset($info['wl']) ? intval($info['wl']) : 0);
                }
            }

            $infoList['all']['cls'] = '综合';
            $infoList['all']['cls_enum'] = 0;
            $infoList['all']['base'] = 1;
            $sort1 = array_column($infoList, 'cls_enum');
            $sort2 = array_column($infoList, 'base');

            array_multisort($sort1, SORT_ASC, $sort2, SORT_ASC, $infoList);
            $list[$date] = $infoList;
        }
        return $list;
    }



    //根据奖池名称获取 房间流水 输赢佣金 统计金额
    public static function getPrRoomWalletLogStatV2($jackportName, $currency, $capitalType)
    {
        // const AI_ROOM_DATA_RC = 'rc'; //AI总输赢
        // const AI_ROOM_DATA_RW = 'rw'; //真人总赢
        // const AI_ROOM_DATA_RL = 'rl'; //真人总输
        // const AI_ROOM_DATA_TB = 'tb'; //真人总下注
        // const AI_ROOM_DATA_RTB = 'rtb'; //新手真人总下注
        // const AI_ROOM_DATA_W = 'w'; //总赢
        // const AI_ROOM_DATA_L = 'l'; //总输
        // const AI_ROOM_DATA_C = 'c'; //总营

        //符合要求的key列表
        $rtn = [
            'w' => 0,
            'l' => 0,
            'c' => 0,
            'tb' => 0,
        ];
        $keyList = [];
        $date = date("Ymd", time());
        $redisKey = Config::get('room_wallet_log_stat_key_prefix') . $date;
        $redisDb = Config::get('room_wallet_log_stat_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);


        static $allKeyList = null;
        static $lastHrtime = 0;

        if (01) {
            $hrnow = hrtime(true);
            if ($allKeyList === null || $hrnow - $lastHrtime >= 5 * 1e9) {
                $allKeyList = $xRedis->hKeys($redisKey);
                $lastHrtime = $hrnow;
            }
        } else {

            //获取所有的key
            $allKeyList = $xRedis->hKeys($redisKey);

        }


        if (empty($allKeyList)) {
            return $rtn;
        }
        //筛选符合奖池名称的key
        foreach ($allKeyList as $val) {
            //截取字符串
            $keyArr = explode('_', $val);
            $type = $keyArr[count($keyArr) - 1];
            if (!$type) {
                continue;
            }
            //资金类型不一样 不需要
            $logCapitalType = $keyArr[count($keyArr) - 2];
            if ($logCapitalType != $capitalType) {
                continue;
            }
            // val 例如 5_1_5_1000_2_tb 表示 cls_currency_max_base_capitalType
            $roomInfo = parse_room_type($val);
            if ($roomInfo['currency'] != $currency) {
                continue;
            }
            $tmpJackportName = Common::getPoolNameByClsAndBase($roomInfo['cls'], $roomInfo['base']);
            if ($tmpJackportName != $jackportName) {
                continue;
            }
            // bakhmut: 那估计就是房间最大人数不一样，导致的需要收集多个
            $keyList[$type][] = $val;
        }
        if (empty($keyList)) {
            return $rtn;
        }
        //执行取值操作
        foreach ($keyList as $key => $val) {
            $list = $xRedis->hMget($redisKey, $val);
            if (empty($list)) {
                continue;
            }
            $rtn[$key] = array_sum($list);
        }
        return $rtn;
    }

    //根据roomType 携带货币对应的段位id 以及 数量  庄家玩法固定key
    // bakhmut: 发现无人使用
    public static function getDealerRobotBlanceAmountRegionCntList()
    {
        $redisKey = Config::get('ab_ai_blance_amount_region_id_cnt');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $key = 'dealer_game';
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return json_decode($xRedis->hGet($redisKey, $key));
    }



    //获取tp基础概率翻倍倍数列表
    public static function getTpGroupBaseXList()
    {
        $redisKey = Config::get('tp_group_base_conf');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return json_decode($xRedis->get($redisKey), 1);
    }


    //获取tp基础概率翻倍倍数列表
    public static function setTpGroupBaseXList(array $data)
    {
        $redisKey = Config::get('tp_group_base_conf');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->set($redisKey, json_encode($data));
    }


    //龙虎牌池存储
    public static function setLongHuPool(array $pool, $roomId)
    {
        $redisKey = Config::get('long_hu_pool');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->hSet($redisKey, $roomId, json_encode($pool));
    }

    //龙虎牌池获取
    public static function getLongHuPool($roomId)
    {
        $redisKey = Config::get('long_hu_pool');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $pool = $xRedis->hGet($redisKey, $roomId);
        $pool = json_decode($pool, 1);
        if (empty($pool) || count($pool) <= 3) {
            //没有牌池 初始化牌池
            $pool = init_card_pool(LONG_HU_GROUP_CARDS_CNT, false);
            RedisOpt::setLongHuPool($pool, $roomId);
        }
        return $pool;
    }

    //abClassic 牌池存储
    public static function setAbClassicPool(array $pool, $roomId)
    {
        $redisKey = Config::get('ab_classic_pool');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        return $xRedis->hSet($redisKey, $roomId, json_encode($pool));
    }

    //abClassic 获取牌池
    public static function getAbClassicPool($roomId)
    {
        $redisKey = Config::get('ab_classic_pool');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $pool = $xRedis->hGet($redisKey, $roomId);
        $pool = json_decode($pool, 1);
        return $pool;
    }

    //删除key
    public static function delAbClassicPool($roomId)
    {
        $redisKey = Config::get('ab_classic_pool');
        $redisDb = Config::get('ai_profit_rate_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);

        $xRedis->hdel($redisKey, $roomId);
    }


    //获取用户是否充过值
    public static function getPlayerRechargeCnt(int $playerIndex): int
    {
        $redisKey = Config::get('recharge_all_cnt');
        $redisDb = Config::get('recharge_all_cnt_db');
        $redisConfig = Config::get('redis_config');
        $xRedis = Xredis::getInstance($redisConfig, $redisDb);
        $cnt = $xRedis->hGet($redisKey, $playerIndex);
        return intval($cnt);
    }


}
