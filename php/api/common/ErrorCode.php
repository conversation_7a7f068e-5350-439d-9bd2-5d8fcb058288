<?php
namespace common;

class ErrorCode
{
    const ERR_CODE = [
        'API_SUCCESS' => 200,
        'API_SERVICE_ERR' => 500,
        'HAND_CARD_NUM_ERR' => 1001,
        'L_CARD_ERR' => 1002,
        'M_CARD_ERR' => 1003,
        'POST_EMPTY_ERR' => 1004,
        'P_NUM_ERR' => 1005,
        'R_NUM_ROUNDS_ERR' => 1006,
        'R_ROOM_TYPE_ERR' => 1007,
        'CARD_POOL_ERROR' => 1008,
        'CARD_MAX_CARD_ERROR' => 1009,
        'UTYPE_ERROR' => 1010,
        'JACKPOT_ERROR' => 1011,
        'ULIST_ERROR' => 1012,
        'RLSCORE_ERROR' => 1013,
        'RTYPE_ERROR' => 1014,
        'UID_ERROR' => 1015,
        'JOKER_NUM_ERROR' => 1016,
        'CIRCLE_NUM_ERROR' => 1017,
        'RAISE_LIST_ERROR' => 1018,

    ];


    //英文
    const ERR_TEXT_EN = [
        'API_SUCCESS' => 'Api Success',
        'API_SERVICE_ERR' => 'Server internal error',
        'HAND_CARD_NUM_ERR' => 'Hand card num error',
        'L_CARD_ERR' => 'Xcard empty error',
        'M_CARD_ERR' => 'Mcard empty error',
        'POST_EMPTY_ERR' => 'Post data json error',
        'P_NUM_ERR' => 'Game number of player error',
        'R_NUM_ROUNDS_ERR' => 'Game Play number of rounds error',
        'R_ROOM_TYPE_ERR' => 'Room type error',
        'CARD_POOL_ERROR' => 'Pool of card error',
        'CARD_MAX_CARD_ERROR' => 'Max num of card error',
        'UTYPE_ERROR' => 'User type error',
        'JACKPOT_ERROR' => 'Jackpot value error',
        'ULIST_ERROR' => 'User list error',
        'RLSCORE_ERROR' => 'Rlscore error',
        'RTYPE_ERROR' => 'Rtype  error',
        'UID_ERROR' => 'Uid  error',
        'JOKER_NUM_ERROR' => 'Joker num  error',
        'CIRCLE_NUM_ERROR' => 'Circle num  error',
        'RAISE_LIST_ERROR' => 'raise list error',


    ];

    //获取错误码
    private static function getErrCode($name): int
    {
        return self::ERR_CODE[$name] ?? 0;
    }

    //获取错误码对应的文字
    private static function getErrText($name): string
    {
        return self::ERR_TEXT_EN[$name] ?? $name . ' error';
    }

}
