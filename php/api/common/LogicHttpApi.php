<?php
/**
*功能：游戏服务器提供的api服务
*版本：1.0
*修改日期：2020/8/24
'说明：
*/

namespace common;

use lib\Config;
use lib\Log;

class LogicHttpApi
{
    //创建Ai
    public static function createRobotPr($addAiInfo)
    {
        $rtn['code'] = 1;
        $rtn['message'] = "创建机器人成功";
        try {
            $p['head_url_list'] = is_array($addAiInfo['head_url_list']) ? $addAiInfo['head_url_list'] : array();
            $p['name_list'] = is_array($addAiInfo['name_list']) ? $addAiInfo['name_list'] : array();
            $p['count'] = (int) $addAiInfo['count'];
            $p['sex'] = (int) $addAiInfo['sex'];
            $p['jack_pool_ai'] = $addAiInfo['jack_pool'];
            $finalUrl = \lconfig\AddrConfig::InstanceV2()->serverhttp_inner_url;
            $data['m'] = 'Platform';
            $data['f'] = 'add_robot_pr';
            $data['d'] = $p;
            // Platform add_robot_pr 的实现是调用 RobotLogic 的 init，而 init 只是简单的 return，所以这个系统是废弃的

            $res = post_helper($finalUrl, $data);
            Log::console_log(__FUNCTION__, '创建AI：' . PHP_EOL . '请求地址：' . $finalUrl . PHP_EOL . '请求参数：' . json_encode($data) . PHP_EOL . '返回结果：' . json_encode($res));
            if (!is_array($res)) {
                throw new \Exception('curl 错误：' . $res);
            }
            if ($res['d']['c'] != 200) {
                throw new \Exception('请求逻辑服API错误:' . json_encode($res));
            }
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }


    //删除Ai
    public static function delRobotPr($jackPoolName)
    {
        $rtn['code'] = 1;
        $rtn['message'] = "删除器人成功";
        try {
            $finalUrl = \lconfig\AddrConfig::InstanceV2()->serverhttp_inner_url;
            $data['m'] = 'Platform';
            $data['f'] = 'del_robot_pr';
            $data['d']['jack_pool_ai'] = $jackPoolName;
            // 因为 add_robot_pr 废弃了，这个也废弃
            $res = post_helper($finalUrl, $data);
            Log::console_log(__FUNCTION__, '删除AI：' . PHP_EOL . '请求地址：' . $finalUrl . PHP_EOL . '请求参数：' . json_encode($data) . PHP_EOL . '返回结果：' . json_encode($res));
            if (!is_array($res)) {
                throw new \Exception('curl 错误：' . $res);
            }
            if ($res['d']['c'] != 200) {
                throw new \Exception('请求逻辑服API错误:' . json_encode($res));
            }
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }









    //创建Ai
    public static function createRobot($addAiInfo)
    {
        $rtn['code'] = 1;
        $rtn['message'] = "创建机器人成功";
        try {
            $p['head_url_list'] = is_array($addAiInfo['head_url_list']) ? $addAiInfo['head_url_list'] : array();
            $p['name_list'] = is_array($addAiInfo['name_list']) ? $addAiInfo['name_list'] : array();
            $p['count'] = (int) $addAiInfo['count'];
            $p['sex'] = (int) $addAiInfo['sex'];
            $p['head_url'] = $addAiInfo['head_url'];
            $p['cls'] = (int) $addAiInfo['cls'];
            $p['max'] = (int) $addAiInfo['max'];
            $p['currency'] = (int) $addAiInfo['currency'];
            $p['base'] = (int) $addAiInfo['base'];
            $p['round'] = (int) $addAiInfo['round'];
            $p['pool'] = (int) $addAiInfo['pool'];
            $finalUrl = \lconfig\AddrConfig::InstanceV2()->serverhttp_inner_url;
            $data['m'] = 'Platform';
            $data['f'] = 'add_robot';
            $data['d'] = $p;
            // Platform add_robot 的实现是调用 RobotLogic 的 init，而 init 只是简单的 return，所以这个系统是废弃的
            $res = post_helper($finalUrl, $data);
            if (!is_array($res)) {
                throw new \Exception('curl 错误：' . $res);
            }
            if ($res['d']['c'] != 200) {
                throw new \Exception('请求逻辑服API错误:' . json_encode($res));
            }
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }



    //删除Ai
    public static function delRobot($addAiInfo)
    {
        $rtn['code'] = 1;
        $rtn['message'] = "删除器人成功";
        try {
            $finalUrl = \lconfig\AddrConfig::InstanceV2()->serverhttp_inner_url;
            $data['m'] = 'Platform';
            $data['f'] = 'del_robot';
            $data['d'] = $addAiInfo;
            // 因为 add_robot 废弃了，这个也废弃
            $res = post_helper($finalUrl, $data);
            if (!is_array($res)) {
                throw new \Exception('curl 错误：' . $res);
            }
            if ($res['d']['c'] != 200) {
                throw new \Exception('请求逻辑服API错误:' . json_encode($res));
            }
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }



    //添加 修改 世界杯活动
    public static function editWorldCupInfo($team1, $team1Score, $team2, $team2Score, $startTime, $roomId = 0, $des = '', $winOption = -1, $receiveTime = 0): array
    {
        $rtn['code'] = 1;
        $rtn['message'] = "世界杯信息更新成功";
        try {
            $finalUrl = \lconfig\AddrConfig::InstanceV2()->serverhttp_inner_url;
            $data['m'] = 'Loto';
            $data['f'] = 'game_save';
            $logoGame = [
                'id' => $roomId,
                'team' => [
                    [
                        'id' => intval($team1),
                        'score' => intval($team1Score),
                    ],
                    [
                        'id' => intval($team2),
                        'score' => intval($team2Score),
                    ],
                ],
                'options' => [0, 1, 2],
                'time_start' => intval($startTime),
                'des' => $des,
                'time_reward' => intval($receiveTime)
            ];
            if ($winOption > -1) {
                $logoGame['win_options'] = [intval($winOption)];
            } else {
                $logoGame['win_options'] = [];
            }
            $data['d'] = [
                'loto_id' => 1000,
                'loto_game' => [
                    $logoGame,
                ],
            ];
            $res = post_helper($finalUrl, $data);
            if (!is_array($res)) {
                throw new \Exception('curl 错误：' . $res);
            }
            if ($res['d']['c'] != 200) {
                throw new \Exception('请求逻辑服API错误:' . json_encode($res));
            }
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }





    //添加 修改 世界杯活动
    public static function getWorldCupInfoList(): array
    {
        $rtn['code'] = 1;
        $rtn['message'] = "成功";
        try {
            $finalUrl = \lconfig\AddrConfig::InstanceV2()->serverhttp_inner_url;
            $data['m'] = 'Loto';
            $data['f'] = 'game_list';
            $data['d'] = [
                'loto_id' => 1000,
            ];
            $res = post_helper($finalUrl, $data);
            if (!is_array($res)) {
                throw new \Exception('curl 错误：' . $res);
            }
            //			if($res['d']['c']!=200){
//				throw new \Exception('请求逻辑服API错误:'.json_encode($res));
//			}



            $rtn['data'] = $res['d'];
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }


    //删除世界杯
    public static function delWorldCupInfo($roomId): array
    {
        $rtn['code'] = 1;
        $rtn['message'] = "成功";
        try {
            $finalUrl = \lconfig\AddrConfig::InstanceV2()->serverhttp_inner_url;
            $data['m'] = 'Loto';
            $data['f'] = 'game_delete';
            $data['d'] = [
                'loto_id' => 1000,
                "loto_game_id" => [$roomId],
            ];
            $res = post_helper($finalUrl, $data);
            if (!is_array($res)) {
                throw new \Exception('curl 错误：' . $res);
            }
            if ($res['d']['c'] != 200) {
                throw new \Exception('请求逻辑服API错误:' . json_encode($res));
            }
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }


    //下注
    public static function betWorldCupInfo($roomId, $betOption, $betAmount): array
    {
        $rtn['code'] = 1;
        $rtn['message'] = "成功";
        try {
            $finalUrl = \lconfig\AddrConfig::InstanceV2()->serverhttp_inner_url;
            $data['m'] = 'Loto';
            $data['f'] = 'game_bet';
            $data['d'] = [
                'loto_id' => 1000,
                "loto_game_id" => $roomId,
                'bet_option' => intval($betOption),
                'bet_fee' => intval($betAmount),

            ];
            $res = post_helper($finalUrl, $data);
            if (!is_array($res)) {
                throw new \Exception('curl 错误：' . $res);
            }
            if ($res['d']['c'] != 200) {
                throw new \Exception('请求逻辑服API错误:' . json_encode($res));
            }
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }


}
?>

