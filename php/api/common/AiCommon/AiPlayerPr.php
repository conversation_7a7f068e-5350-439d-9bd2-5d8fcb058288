<?php
/**
 * @todo pr读取
 */

namespace common\AiCommon;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;

class AiPlayerPr
{
    //pr配置
    private $prConfig = [];

    /** 概率计算倍数 */
    private $rateBase = 10000;

    /** 概率计算保留小数 */
    private $rateNum = 4;


    public function __construct()
    {

    }

    //设置pr配置
    public function setPrConfig(array $prConfig)
    {
        $this->prConfig = $prConfig;
    }

    //根据用户id获取用户是否为充值用户
    public function getPlayerIsRecharge(int $playerIndex): bool
    {
        $rechargeCnt = RedisOpt::getPlayerRechargeCnt($playerIndex);
        if ($rechargeCnt > 0) {
            return true;
        } else {
            return false;
        }
    }


    //根据用户id获取用户的总pr
    /**
     * 计算给定玩家的总PR，根据用户ID
     * 
     * IF 净收益=0
     *   PR=1
     * ELSE 根据判断是否为充值用户计算PR
     *    IF 不是充值用户
     *      PR参数 = min（第一档流水值 , max（10000 , 数钱流水）)
     *      PR = 1 + （赢钱流水 - 输钱流水） / PR参数 
     *    ELSE 是充值用户
     *      PR参数 = min(max(10000, 输钱流水), max(10000, 充值总额 * mround(该玩法的输钱流水/总输钱流水+0.01,0.02)))
     *      PR = 1 + （赢钱流水 - 输钱流水） / PR参数 
     *      
     *      
     */
    public function getPlayerAllPr(int $playerIndex, int $cls, int $depositeAmount): array
    {
        $isRecharge = $this->getPlayerIsRecharge($playerIndex);

        $ugstat = \llogic\user\UserGameStatRepoV3::SnapshotOrDefault($playerIndex);
        // 获取用户的总的净赢流水
        $winAmount = intval($ugstat->GetTotalProfit());
        // 获取用户的总的净输流水
        $lossAmount = intval($ugstat->GetTotalLoss());

        $prParam = 0;
        //计算用户的净收益
        $profit = $winAmount - $lossAmount;
        //净收益为0，PR值为1
        if ($profit == 0) {
            $pr = 1;
        } else {
            //根据是否为充值用户，使用不同公式计算PR值
            if (!$isRecharge) {
                //默认取第一档的流水值
                $minAmount = isset($this->prConfig[0]) ? $this->prConfig[0]['p'] * 1000 : 15000;
                $prParam = min($minAmount, max(10000, $lossAmount));
                $pr = 1 + bcdiv(($winAmount - $lossAmount), $prParam, 4);
                Log::console_log(__FUNCTION__, 'get_player_pr $playerIndex ' . $playerIndex . ' $cls ' . $cls . ' $winAmount ' . $winAmount . ' $lossAmount ' . $lossAmount . ' $minAmount ' . $minAmount . ' $prParam ' . $prParam);
            } else {
                //                $pr = 1 + bcdiv(($winAmount-$lossAmount),max(10000,$lossAmount),4);
                //PR=1+（在TP所有房间的净赢钱流水-在TP所有房间的净输钱流水）/max(10，(充值总额+赠送总金额+bonus获取总金额)*mround(该玩法的输钱流水/总输钱流水+0.01,0.02))))
                //用户的输钱流水
                $lossAmountAll = intval($ugstat->GetTotalLoss());
                $prParam = min(max(10000, $lossAmount), max(10000, $depositeAmount * (round(($lossAmount / max(10000, $lossAmountAll)) * 50 + 0.5) / 50)));
                $pr = 1 + bcdiv(($winAmount - $lossAmount), $prParam, 4);
                Log::console_log(__FUNCTION__, 'get_player_pr $playerIndex ' . $playerIndex . ' $cls ' . $cls . ' $depositeAmount ' . $depositeAmount . ' $winAmount ' . $winAmount . ' $lossAmount ' . $lossAmount . ' $lossAmountAll ' . $lossAmountAll . ' $prParam ' . $prParam);
            }
        }
        Log::console_log(__FUNCTION__, 'get_player_pr $pr ' . $pr);
        //计算用户使用的pr档位
        $prConfig = $this->getPlayerPrConfig($playerIndex, min($lossAmount, $prParam));
        //组装用户的pr相关数据
        $prInfo['winAmount'] = $winAmount;
        $prInfo['lossAmount'] = $lossAmount;
        $prInfo['pr'] = $pr;
        $prInfo['prConfig'] = $prConfig;
        return $prInfo;
    }



    //根据用户id获取玩家的tp的pr
    public function getPlayerPrByCls(int $playerIndex, int $cls, int $depositeAmount): array
    {
        $clsList = Common::getGameTypeTextList();
        if (!isset($clsList[$cls])) {
            $prInfo['winAmount'] = 0;
            $prInfo['lossAmount'] = 0;
            $prInfo['pr'] = 1;
            $prInfo['prConfig'] = [];
            return $prInfo;
        }

        //用户的赢钱流水
        //用户的输钱流水

        $ugstat = \llogic\user\UserGameStatRepoV3::SnapshotOrDefault($playerIndex);
        $winAmount = intval(($ugstat->GetGameProfit($cls)));
        $lossAmount = intval(($ugstat->GetGameLoss($cls)));
        // 获取用户的总的净输流水
        $lossAmountAll = intval($ugstat->GetTotalLoss());

        // depositeAmount 是历史总充值，加上系统赠送的
        // 输钱流水减去赢钱流水，是净输的钱
        // 净输的钱哪里来，只能从充值的钱里面来
        // 所以净输钱肯定是小于等于总充值的

        // depositeAmount 是 CardLogic 调用 RoomLogic 的 get_user_balance_wallet 获取的
        // 查 user_wallet 表，14 是系统赠送的钱

        $prParam = 0;
        //计算用户的
        $profit = $winAmount - $lossAmount;

        //            $pr = 1 + bcdiv(($winAmount-$lossAmount),max(10000,$lossAmount),4);
        //方案修改：
        //PR=1+（在TP所有房间的净赢钱流水-在TP所有房间的净输钱流水）/max(10，max((充值总额+赠送总金额+bonus获取总金额)*(round(该玩法的输钱流水/max(10,总输钱流水)) *50+0.01) / 50)))


        // revisedLossAmount = max(10000, lossAmount)
        // clsLossRatioToAll = (lossAmount / max(10000, lossAmountAll))
        // clsLossRatioToAllRevised = round(clsLossRatioToAll * 50 + 0.5) / 50  // 相当于加了 0.02 这是何意？
        // revisedDepositeAmount = max(10000, depositeAmount * clsLossRatioToAllRevised)
        // prParam = min(revisedLossAmount, revisedDepositeAmount)
        $revisedLossAmount = max(10000, $lossAmount);
        $clsLossRatioToAll = $lossAmount / max(10000, $lossAmountAll);
        $clsLossRatioToAllRevised = round($clsLossRatioToAll * 50 + 0.5) / 50;
        $revisedDepositeAmount = max(10000, $depositeAmount * $clsLossRatioToAllRevised);
        $prParam = min($revisedLossAmount, $revisedDepositeAmount);

        // profit = winAmount - lossAmount
        // pr = 1 + (profit / prParam)
        $pr = 1 + bcdiv($profit, $prParam, 4);

        //计算用户使用的pr档位
        $p = min($lossAmount, $prParam);
        $prConfig = $this->getPlayerPrConfig($playerIndex, $p);

        //组装用户的pr相关数据
        $prInfo['winAmount'] = $winAmount;
        $prInfo['lossAmount'] = $lossAmount;
        $prInfo['pr'] = $pr;
        $prInfo['prConfig'] = $prConfig;

        \lbase\Log::debug("getPlayerPrByCls", [
            'playerIndex' => $playerIndex,
            'cls' => $cls,
            'pr' => $pr,
            'winAmount' => $winAmount,
            'lossAmount' => $lossAmount,
            'lossAmountAll' => $lossAmountAll,
            'clsLossRatioToAll' => $clsLossRatioToAll,
            'clsLossRatioToAllRevised' => $clsLossRatioToAllRevised,
            'revisedLossAmount' => $revisedLossAmount,
            'depositeAmount' => $depositeAmount,
            'revisedDepositeAmount' => $revisedDepositeAmount,
            'profit' => $profit,
            'prParam' => $prParam,
            'p' => $p,
            'prConfig' => $prConfig,
        ]);

        return $prInfo;
    }


    //-------------获取并计算用户当前使用的各种配置--------------------------------------------------------------------------
    //获取当前PR配置表
    private function getPlayerPrConfig(int $playerIndex, int $lossAmount): array
    {
        $lossAmount = bcmul($lossAmount, 0.001, 2);
        $prConfigOne = [
            'p' => 50000,
            'd_limit' => 0.005,
            'd_begin' => 0.2,
            'u_begin' => 5,
            'u_limit' => 10,
            'ed' => 1,
            'eu' => 1,
        ];
        if (!$this->prConfig) {
            return $prConfigOne;
        }
        //如果么有充值 默认第一档
        $isRecharge = $this->getPlayerIsRecharge($playerIndex);
        if (!$isRecharge) {
            return $this->prConfig[1];
        }

        //总配置数量
        $cnt = count($this->prConfig);
        //获取当前适用的配置
        $prConfigKey = -1;
        foreach ($this->prConfig as $key => $val) {
            if ($lossAmount <= $val['p']) {
                $prConfigKey = $key;
                break;
            }
        }
        Log::console_log(__FUNCTION__, 'get_player_pr $playerIndex ' . $playerIndex . ' $lossAmount ' . $lossAmount . ' $prConfigKey ' . $prConfigKey);
        //如果没有符合要求的配置区间 则取最后一条数据
        if ($prConfigKey == -1) {
            $prConfigKey = $cnt;
        }
        //如果配置是第一个 或者最后一个直接去表中的配置不用计算
        if ($prConfigKey == 1 || $prConfigKey == $cnt) {
            $prConfigOne = [
                'd_limit' => $this->prConfig[$prConfigKey]['d_limit'],
                'd_begin' => $this->prConfig[$prConfigKey]['d_begin'],
                'u_begin' => $this->prConfig[$prConfigKey]['u_begin'],
                'u_limit' => $this->prConfig[$prConfigKey]['u_limit'],
            ];

        } else {
            //其他的按照2档位之前的数值计算
            //p2为当前当
            $eu2 = $this->prConfig[$prConfigKey]['eu'];
            $ed2 = $this->prConfig[$prConfigKey]['ed'];

            $p2 = $this->prConfig[$prConfigKey]['p'];
            $p1 = $this->prConfig[$prConfigKey - 1]['p'];
            $p = $lossAmount;

            $d_begin_2 = $this->prConfig[$prConfigKey]['d_begin'];
            $d_begin_1 = $this->prConfig[$prConfigKey - 1]['d_begin'];

            $d_limit_2 = $this->prConfig[$prConfigKey]['d_limit'];
            $d_limit_1 = $this->prConfig[$prConfigKey - 1]['d_limit'];

            $u_begin_2 = $this->prConfig[$prConfigKey]['u_begin'];
            $u_begin_1 = $this->prConfig[$prConfigKey - 1]['u_begin'];

            $u_limit_2 = $this->prConfig[$prConfigKey]['u_limit'];
            $u_limit_1 = $this->prConfig[$prConfigKey - 1]['u_limit'];
            $prConfigOne = [
                'd_begin' => $this->getProfitRate($d_begin_2, $d_begin_1, $p, $p1, $p2, $ed2, true),
                'd_limit' => $this->getProfitRate($d_limit_2, $d_limit_1, $p, $p1, $p2, $ed2, true),
                'u_begin' => $this->getProfitRate($u_begin_1, $u_begin_2, $p, $p1, $p2, $eu2, false),
                'u_limit' => $this->getProfitRate($u_limit_1, $u_limit_2, $p, $p1, $p2, $eu2, false),
            ];
        }
        return $prConfigOne;
    }



    /** 统PR一计算公式 */
    //限制起点公式	u_begin = u_begin_1-(u_begin_1-u_begin_2)*（P-P1）^eu2/(P2-P1+1)^eu2
    //限制极值公式	u_limit = u_limit_1-(u_limit_1-u_limit_2)*（P-P1）^eu2/(P2-P1+1)^eu2
    //扶植起点公式	d_begin = d_begin_2-(d_begin_2-d_begin_1)*（P-P1）^ed2/(P2-P1+1)^ed2
    //扶植极值公式	d_limit = d_limit_2-(d_limit_2-d_limit_1)*（P-P1）^ed2/(P2-P1+1)^ed2
    private function getProfitRate($d1, $d2, $p, $p1, $p2, $eu, $isAdd): string
    {
        // fz = (d1 - d2) * (p - p1)
        // fm = (p2 - p1)
        // powRes = (fz / fm) ^ eu
        // isAdd: res = d2 + powRes
        // isSub: res = d1 - powRes
        // 指数处理的有问题，不符合注释，(d1 - d2) 不应该做指数处理，如果指数为 1 则还好

        $fz = bcmul(bcsub($d1, $d2, $this->rateNum), bcsub($p, $p1, $this->rateNum));
        $fm = bcadd(bcsub($p2, $p1, $this->rateNum), 0, $this->rateNum);
        if ($fm <= 0) {
            $fm = 1;
        }
        $powRes = (int) (pow(bcdiv($fz, $fm, $this->rateNum), $eu) * $this->rateBase);
        $powRes = bcdiv($powRes, $this->rateBase, $this->rateNum);
        if ($isAdd) {
            $res = bcadd($d2, $powRes, $this->rateNum);
            //$msg = $d2.'+('.$d1.'-'.$d2.')*('.$p.'-'.$p1.')^'.$eu.'/('.$p2.'-'.$p1.'+1)^'.$eu.'='.$res;
        } else {
            $res = bcsub($d1, $powRes, $this->rateNum);
            //$msg = $d1.'-('.$d1.'-'.$d2.')*('.$p.'-'.$p1.')^'.$eu.'/('.$p2.'-'.$p1.'+1)^'.$eu.'='.$res;
        }
        return $res;
    }

}
