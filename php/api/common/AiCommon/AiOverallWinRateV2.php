<?php
/**
 * @todo 盈利率发牌接口  执行预设盈利比例筛选计算
 */

namespace common\AiCommon;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;

class AiOverallWinRateV2
{
    //奖池名称
    private $jackPortName;

    //奖池类型
    private $capitalType;

    //当前是今天的第几分钟
    private $todayMinutes = 0;


    //预设收益比例
    private $winRate = 0;

    //时间对目标金额波动概率
    private $timeRate = 0;

    //赢钱之后实际到账比列
    private $commissionRate = 0.9;

    //盈利金额
    // bakhmut: 上面的注释错了，这个是今日系统利润距离动态目标的缺口金额
    private $todayWinAmount = 0;

    //原始目标金额
    private $todayOldTargetAmount = 0;

    //今日波动目标金额
    private $todayDynamicTargetAmount = 0;

    //今日系统回收金额
    // bakhmut: 系统的盈亏
    private $todaySystemAmount = 0;

    //流水金额
    private $todayLogAmount = 0;

    //今日流水营收统计情况
    private $todayRoomStatInfo = [];

    //获取房间配置
    private $roomConfig = [];

    //强杀大盘控系数
    private $roomInfo = [];

    private $currency;

    public function __construct($jackPortName, $roomInfo, $capitalType)
    {
        $this->capitalType = $capitalType;

        $this->roomInfo = $roomInfo;
        //房间类型
        $this->currency = $roomInfo['currency'] ?? Common::CURRENCY_TYPE_CASH;

        $this->commissionRate = isset($roomInfo['cls']) ? Common::getGameTypeCommissionRate($roomInfo['cls']) : 0.9;

        //获取奖池名称
        $this->jackPortName = $jackPortName;

        $this->timeRate = $this->getWinTimeRate();

        //获取大盘与收益比例
        $this->getWinRate();

        //获取房间配置
        $this->setRoomConfig();

        //获取今日的房间流水统计
        $this->todayRoomStatInfo = $this->getTodayRoomStatInfo();

        //获取今日盈利金额
        // bakhmut: 系统的盈亏
        $this->todaySystemAmount = $this->getTodaySystemAmount();

        //今日流水金额
        // bakhmut: 今日真人输赢流水总额
        $this->todayLogAmount = $this->getTodayLogAmount();

        //今日原始目标金额
        // bakhmut: 奖池今日实时原始目标额 = 今日真人输赢流水总额 * 用户杀率类型关联的盈利率
        $this->todayOldTargetAmount = $this->getTodayOldTargetAmount();

        //今日波动目标金额
        $this->todayDynamicTargetAmount = $this->getTodayDynamicTargetAmount();

        //获取今日盈利金额
        // bakhmut: 上面的注释错了，这个是今日系统利润距离动态目标的缺口金额
        $this->todayWinAmount = $this->getTodayWinAmount();
    }


    //获取流水金额
    public function getLogAmount()
    {
        return $this->todayLogAmount;
    }

    //获取系统回收金额
    public function getSystemAmount(): int
    {
        return $this->todaySystemAmount;
    }

    //获取今日收益钱数
    public function getWinAmount()
    {
        return max($this->todayWinAmount, 0);
    }

    //获取今日真是收益钱数
    public function getRealWinAmount()
    {
        return $this->todayDynamicTargetAmount - $this->todaySystemAmount;
    }

    //获取原始目标
    public function getOldTargetAmount()
    {
        return $this->todayOldTargetAmount;
    }

    //获取营收目标金额
    public function getDynamicTargetAmount()
    {
        return $this->todayDynamicTargetAmount;
    }

    //获取现在是今天的第几分钟
    public function getTodayMinutes(): int
    {
        return $this->todayMinutes;
    }

    //获取分钟波动概率
    public function getTimeRate(): string
    {
        return $this->timeRate;
    }

    //获取房间配置
    public function getRoomConfig(): array
    {
        return $this->roomConfig ?? SYS_KILLER_CONFIG;
    }



    //获取盈利之后的目标连续函数百分比
    private function getWinTimeRate(): string
    {
        //本游戏目标X%的情况(X=今日分钟数*0.96/(300+今日分钟数)+0.2）
        $dateInfo = getdate();
        $hours = $dateInfo['hours'];
        $minutes = $dateInfo['minutes'];
        //计算当前是第几分钟
        $todayMinutes = $hours * 60 + $minutes + 1;
        $this->todayMinutes = $todayMinutes;
        return bcadd($todayMinutes * 0.96 / (300 + $todayMinutes), 0.2, 4);
    }

    //今日流水营收情况
    private function getTodayRoomStatInfo(): array
    {
        return RedisOpt::getPrRoomWalletLogStatV2($this->jackPortName, $this->currency, $this->capitalType);
    }

    //获取预设收益比例
    private function getWinRate()
    {
        $this->winRate = RedisOpt::getPrWinRateOneV2($this->jackPortName, $this->currency, $this->capitalType);
        if (!$this->winRate) {
            //Log::console_log(__FUNCTION__, 'getWinRate：' . json_encode($this->jackPortName) . '----' . json_encode($this->capitalType) . '----' . $this->currency, 'common');
            $this->winRate = 0;

        }
    }

    // 获取目标盈利率
    public function getTargetProfitRate()
    {
        return $this->winRate;
    }

    //获取房间配置
    private function setRoomConfig()
    {
        $this->roomConfig = RedisOpt::getPrRoomConfigOne($this->jackPortName, $this->currency);
    }

    //获取今日流水金额
    private function getTodayLogAmount()
    {
        //        //如果是rummy一类的计算使用 输钱流水+赢钱流水之和
//        if(in_array($this->roomInfo['cls'],[Common::GAME_TYPE_RUMMY_POINTS,Common::GAME_TYPE_RUMMY_DEALS,Common::GAME_TYPE_RUMMY_POOL,Common::GAME_TYPE_RUMMY_10_CARD])){
//            //f否则取 下注额
//            //如果今日的流水为0  则选取昨日的流水的
//            $this->todayRoomStatInfo['w'] = isset($this->todayRoomStatInfo['w'])?$this->todayRoomStatInfo['w']:0;
//            $this->todayRoomStatInfo['l'] = isset($this->todayRoomStatInfo['l'])?$this->todayRoomStatInfo['l']:0;
//            $w = $this->todayRoomStatInfo['w']<=0?0:($this->todayRoomStatInfo['w']/$this->commissionRate);
//            $l = $this->todayRoomStatInfo['l']<=0?0:$this->todayRoomStatInfo['l'];
//            return ($l+$w);
//        }else{
//            return isset($this->todayRoomStatInfo['tb'])?$this->todayRoomStatInfo['tb']:0;
//        }

        // const AI_ROOM_DATA_RC = 'rc'; //AI总输赢
        // const AI_ROOM_DATA_RW = 'rw'; //真人总赢
        // const AI_ROOM_DATA_RL = 'rl'; //真人总输
        // const AI_ROOM_DATA_TB = 'tb'; //真人总下注
        // const AI_ROOM_DATA_RTB = 'rtb'; //新手真人总下注
        // const AI_ROOM_DATA_W = 'w'; //总赢
        // const AI_ROOM_DATA_L = 'l'; //总输
        // const AI_ROOM_DATA_C = 'c'; //总营

        $this->todayRoomStatInfo['w'] = $this->todayRoomStatInfo['w'] ?? 0;
        $this->todayRoomStatInfo['l'] = $this->todayRoomStatInfo['l'] ?? 0;
        $w = $this->todayRoomStatInfo['w'] <= 0 ? 0 : ($this->todayRoomStatInfo['w'] / $this->commissionRate);
        $l = max($this->todayRoomStatInfo['l'], 0);
        // 今日真人输赢流水总额
        return ($l + $w);

    }


    //获取今日系统回收金额
    private function getTodaySystemAmount()
    {
        $this->todayRoomStatInfo['c'] = $this->todayRoomStatInfo['c'] ?? 0;
        return $this->todayRoomStatInfo['c'];
    }


    //获取今日原始目标金额
    private function getTodayOldTargetAmount()
    {
        $todayLogAmount = $this->getTodayLogAmount();

        //Log::console_log(__FUNCTION__, 'getTodayOldTargetAmount：' . json_encode($todayLogAmount) . '----' . json_encode($this->winRate), 'common');
        return $todayLogAmount * $this->winRate;
    }

    //获取今日波动目标金额
    private function getTodayDynamicTargetAmount()
    {
        // todaySystemAmount 是今天系统赚的钱
        // todayOldTargetAmount 是今日真人输赢流水总额 * 用户杀率类型关联的盈利率，这个值在当天是不断增长的
        // 今天系统赚的钱还不错，动态目标就定少点
        if ($this->todaySystemAmount > $this->todayOldTargetAmount) {
            return intval($this->todayOldTargetAmount * $this->timeRate);
        } else {
            return $this->todayOldTargetAmount;
        }
    }

    //获取今日赢钱金额
    private function getTodayWinAmount()
    {
        $amount = $this->todayDynamicTargetAmount - $this->todaySystemAmount;
        if ($amount < 0) {
            return 0;
        } else {
            return $amount;
        }
    }


    //获取系统杀率
    public function getSysKillerWaveRate($killConfigType): float
    {
        $todayWinAmount = $this->todayDynamicTargetAmount - $this->todaySystemAmount;
        //如果是负数 则说明挣钱的状态 不执行波动概率
        if ($todayWinAmount <= 0) {
            $rate = 0;
        } else {
            //$rate = $this->roomConfig['buff_coefficient'][$killConfigType] * $this->roomConfig['intervene_rate'] * ($todayWinAmount / max($this->todayDynamicTargetAmount, 5000000));
            $rate = $this->roomConfig['buff_coefficient'][$killConfigType] * $this->roomConfig['intervene_rate'] * ($todayWinAmount / max($this->todayDynamicTargetAmount, 50000));
        }
        return intval($rate * 1000) * 0.001;
    }

    //获取tp的杀率
    // 缺口率 = (动态目标 - 奖池余额) / max(动态目标, ₹1000, 底注的 1000 倍)
    // waveRate = max(0, 缺口率) * intervene_rate
    public function getTpSysKillerWaveRate($dymaicInfo): float
    {
        $todayWinAmount = $this->todayDynamicTargetAmount - $this->todaySystemAmount;
        //Tp基础杀率=max(配置的杀率上限,原有杀率+房间杀率系数*当日实时缺口额度/max(当日实时盈利目标,5000))
        if ($todayWinAmount <= 0) {
            $rate = 0;
        } else {
            //$rate = $dymaicInfo['intervene_rate'] * ($todayWinAmount / max($this->todayDynamicTargetAmount, 1000000, ($this->roomInfo['base'] * 1000)));
            $rate = $dymaicInfo['intervene_rate'] * ($todayWinAmount / max($this->todayDynamicTargetAmount, 10000, ($this->roomInfo['base'] * 10)));
        }
        return intval($rate * 1000) * 0.001;
    }

    public function getTeenPattiJackpotControlProb($dynamicInfo, &$waveRate, &$controlProb)
    {
        if (empty($dynamicInfo)) {
            $waveRate = 0;
            $controlProb = 0;
            return;
        }
        // waveRate = max(0, 缺口率) * intervene_rate
        $waveRate = $this->getTpSysKillerWaveRate($dynamicInfo);
        // 房间控初始
        $initChangeRate = $dynamicInfo['deal_rate'] ?? 0;
        //换牌概率上限
        //Tp基础杀率=min(配置的杀率上限,原有杀率+房间控系数*当日实时缺口额度/max(当日实时盈利目标,5000,房间底注*1000倍))
        // controlProb = min(sys_killer_limit_rate, deal_rate + max(0, 缺口率) * intervene_rate)
        // sys_killer_limit_rate 房间控上限
        // deal_rate 房间控初始
        // intervene_rate 房间控系数
        // 最后取所有真人的 controlProb 的最小值作为大盘控的概率
        $controlProb = min($dynamicInfo['sys_killer_limit_rate'], ($initChangeRate + $waveRate));
    }


    //获取rummy的杀率
    public function getRummySysKillerWaveRate($dymaicInfo)
    {
        $todayWinAmount = $this->todayDynamicTargetAmount - $this->todaySystemAmount;
        //Tp基础杀率=max(配置的杀率上限,原有杀率+房间杀率系数*当日实时缺口额度/max(当日实时盈利目标,5000))
        // 这个地方好像有问题：这个 todayWinAmount 是今日系统利润距离动态目标的缺口金额，这个 Rummy 杀率计算写反了吧？上面的 TP 杀率计算是对的！没有缺口应该返回零，缺口越大应该杀率越高，这个地方写反了吧？
        if ($todayWinAmount <= 0) {
            $info = '$todayWinAmount <= 0';
            $rate = 1;
        } else {
            $info = '$todayWinAmount > 0';
            //$rate = 1 - $dymaicInfo['intervene_rate'] * ($todayWinAmount / max($this->todayDynamicTargetAmount, 5000000, $this->roomInfo['base'] * 1000));
            $rate = 1 - $dymaicInfo['intervene_rate'] * ($todayWinAmount / max($this->todayDynamicTargetAmount, 10000, $this->roomInfo['base'] * 10));
        }
        $rate = intval($rate * 1000) * 0.001;
        // 验证各种情况下的杀率
        \lbase\Log::debug('rummy_sys_killer_wave_rate', [
            'todayDynamicTargetAmount' => $this->todayDynamicTargetAmount,
            'todaySystemAmount' => $this->todaySystemAmount,
            'todayWinAmount' => $todayWinAmount,
            'rate' => $rate,
            'info' => $info,
            'res_rate' => min(1, $rate),
            'max' => max($this->todayDynamicTargetAmount, 5000000, $this->roomInfo['base'] * 1000),
            'base' => $this->roomInfo['base'] * 1000
        ]);
        return min(1, $rate);
    }


    //获取rummy ai的暗牌扶植概率
    public function getRummyADarkSysKillerWaveRate($aControlRate, $aControlCoefficient)
    {
        $todayWinAmount = $this->todayDynamicTargetAmount - $this->todaySystemAmount;
        //rummy ai的暗牌扶植概率=max(0.333，,默认喂牌率+ai扶植系数*（当日实时缺口额度/max(当日实时盈利目标,1000))
        //$rate = $aControlRate + $aControlCoefficient * ($todayWinAmount / max($this->todayDynamicTargetAmount, $this->roomInfo['base'] * 1000));
        $rate = $aControlRate + $aControlCoefficient * ($todayWinAmount / max($this->todayDynamicTargetAmount, $this->roomInfo['base'] * 10));
        $rate = max(0.333, $rate);
        $rate = intval($rate * 1000) * 0.001;
        return min(1, $rate);
    }

    //获取rummy 真人的暗牌抑制概率
    public function getRummyZDarkSysKillerWaveRate($zControlCoefficient)
    {
        $todayWinAmount = $this->todayDynamicTargetAmount - $this->todaySystemAmount;
        //真人的暗牌抑制概率=max(0.333，,默认喂牌率+ai扶植系数*（当日实时缺口额度/max(当日实时盈利目标,1000))
        //$rate = $zControlCoefficient * ($todayWinAmount / max($this->todayDynamicTargetAmount, $this->roomInfo['base'] * 1000));
        $rate = $zControlCoefficient * ($todayWinAmount / max($this->todayDynamicTargetAmount, $this->roomInfo['base'] * 10));
        $rate = max(0.333, $rate);
        $rate = intval($rate * 1000) * 0.001;
        return min(1, $rate);
    }



    //获取lucky loto 扶植或者强杀的概率
    public function getLuckyLotoSysKillerWaveRate()
    {
        // 缺口额 = 目标额 - 实际额
        $gapAmount = $this->todayDynamicTargetAmount - $this->todaySystemAmount;
        // 缺口率 = 缺口额 / max(5000 卢比, 目标额)
        //$gapRate = $gapAmount / max($this->todayDynamicTargetAmount, 5000000);
        $gapRate = $gapAmount / max($this->todayDynamicTargetAmount, 50000);
        // 房间控系数
        $intervene_rate = $this->roomConfig['intervene_rate'];
        // 换算新的波动杀率
        // waveRate 在 1 附近波动，超额就大于 1，有缺口就 小于 1
        $waveRate = max(0.25, (1 - $intervene_rate * $gapRate));
        $waveRate = round($waveRate, 3);

        \lbase\Log::info('LuckyLoto-getLuckyLotoSysKillerWaveRate', [
            '新充分类' => Common::getCaptitalTypeTitle($this->capitalType),
            '目标额' => round($this->todayDynamicTargetAmount / 1000, 2),
            '实际额' => round($this->todaySystemAmount / 1000, 2),
            '缺口额' => round($gapAmount / 1000, 2),
            '缺口率' => round($gapRate, 3),
            '房间控系数' => round($intervene_rate, 3),
            '波动杀率' => round($waveRate, 3),
        ]);

        return $waveRate;
    }

}
