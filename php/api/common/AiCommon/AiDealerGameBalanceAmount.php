<?php
/**
* @todo 庄家下注类游戏 ai携带金额公共类
* 此逻辑为ai携带金额
* 三、金额模拟
1、机器人进场时，携带金额按照以下配置生成（最小单位0.1）
*
*/

namespace common\AiCommon;


class AiDealerGameBalanceAmount
{
    //AI列表
    private $aiList = [];

    //账户金额配置
    /**
     * 携带卢比区间权重配置
     * @var array{
     *  amount_region: array<int, array{min: int, max: int}>,
     *  weight: array<int, int>
     * }
     */
    private $balanceAmountConfig = [];

    // 设置下注的最小金额(min_amount)和最大金额(max_amount)
    private $tableConfig = [];

    //数据结构
    /*private $aiList =
    [
    100001=>[
    'balance_region_id'=>1,
    'balance_amount'=>100000,
    ],
    100002=>[
    'balance_region_id'=>1,
    'balance_amount'=>100000,
    ],
    ];
    */

    public function __construct($aiList)
    {
    }

    //设置账户金额配置
    public function setBalanceAmountConfig(array $balanceAmountConfig)
    {
        $this->balanceAmountConfig = $balanceAmountConfig;
    }

    //设置桌子配置
    public function setTableConfig(array $tableConfig)
    {
        $this->tableConfig = $tableConfig;
    }

    //设置ai列表
    public function setAiList($aiList)
    {
        $this->aiList = $aiList;
    }

    //获取ai账户金额
    public function getAiListBalance(): array
    {
        if (!is_array($this->aiList) || empty($this->aiList)) {
            return [];
        }
        foreach ($this->aiList as $playerIndex => $balanceInfo) {
            $this->aiList[$playerIndex]['cd'] = $this->getPlayGameCD();

            // 如果这个机器人之前没有初始化过
            if (empty($balanceInfo) || $balanceInfo['balance_region_id'] == 0) {
                // 随机富裕阶层，随机携带金额
                $this->aiList[$playerIndex] = $this->initBalanceAmount();
                // 随机头像昵称
                $headerAndNickname = $this->changeNicknameAndHeaderUrl($playerIndex);
                $this->aiList[$playerIndex]['head'] = $headerAndNickname['head'];
                $this->aiList[$playerIndex]['name'] = $headerAndNickname['name'];
                continue;
            }

            // 如果机器人的携带金额不在合理范围，则在 balance_region_id 关联的范围内重新随机
            // 由此可见 balance_region_id 的设计目的是表示机器人属于哪个富裕阶层
            $tableConfigMinAmount = ($this->tableConfig['min_amount'] ?? 100) * 1000;
            if ($balanceInfo['balance_amount'] < $tableConfigMinAmount || $balanceInfo['balance_amount'] >= $tableConfigMinAmount * 500) {
                $this->aiList[$playerIndex]['balance_amount'] = $this->resetBalanceAmount($balanceInfo['balance_region_id'], $balanceInfo['balance_amount']);
                // 随机头像昵称
                $headerAndNickname = $this->changeNicknameAndHeaderUrl($playerIndex);
                $this->aiList[$playerIndex]['head'] = $headerAndNickname['head'];
                $this->aiList[$playerIndex]['name'] = $headerAndNickname['name'];

            }
        }
        return $this->aiList;
    }



    //获取ai账户金额
    public function getAiListChangeNameAndHeader(): array
    {
        if (!is_array($this->aiList) || empty($this->aiList)) {
            return [];
        }
        foreach ($this->aiList as $playerIndex => $balanceInfo) {
            //执行充值操作
            $this->aiList[$playerIndex]['head'] = $this->changeHeaderUrlV2();
            $this->aiList[$playerIndex]['name'] = $this->changeNicknameV2();
        }
        return $this->aiList;
    }

    //更换AI的头像和昵称
    private function changeNicknameAndHeaderUrl($robotId): array
    {
        //变更方案   每个AI拥有10个头像和10个昵称   序列id 区间为 ($robotId-1)*10+1 ， ($robotId-1)*10+1
        $robotRealId = $robotId % 10000;
        $min = ($robotRealId - 1) * 10 + 1;
        $max = ($robotRealId - 1) * 10 + 10;
        $nickNameId = random_int($min, $max);
        $headerUrlId = random_int($min, $max);
        $rtn['name'] = $this->changeNickname($nickNameId);
        $rtn['head'] = $this->changeHeaderUrl($headerUrlId);
        return $rtn;
    }


    //处理AI的头像跟昵称
    private function changeNickname($id): string
    {
        //获取一个头像
        return ROBOT_NICKNAME[$id] ?? '';
    }


    //变更rummy tp ai头像
    private function changeHeaderUrlV2(): string
    {
        // return ROBOT_ALL_HEADER_URL['ossPath'] . mt_rand(ROBOT_ALL_HEADER_URL['start'], (ROBOT_ALL_HEADER_URL['end'] - 1)) . ROBOT_ALL_HEADER_URL['suffix'];
        return strval(mt_rand(ROBOT_ALL_HEADER_URL['start'], (ROBOT_ALL_HEADER_URL['end'])));
    }

    //变更rummy tp ai头像
    private function changeNicknameV2(): string
    {
        return ROBOT_NICKNAME[(mt_rand(0, (count(ROBOT_NICKNAME) - 1)))];
    }

    //变更AI的头像
    private function changeHeaderUrl($id): string
    {
        // if ($id >= ROBOT_ALL_HEADER_URL['start'] && $id <= ROBOT_ALL_HEADER_URL['end']) {
        // return ROBOT_ALL_HEADER_URL['ossPath'] . $id . ROBOT_ALL_HEADER_URL['suffix'];
        // return strval($id);
        // } else {
        //     return '';
        // }

        //record by Jingzhao:修复百人场调用此函数获取机器人头像 获取不到的问题
        //原因是 老逻辑是拿着id去判断 现在无法走这个逻辑了 需要更改
        return strval(mt_rand(ROBOT_ALL_HEADER_URL['start'], (ROBOT_ALL_HEADER_URL['end'])));
    }

    //更新AI参与游戏的CD时间
    private function getPlayGameCD(): int
    {
        return random_int(0, 1800) + 600;
    }

    //处理金额初始化
    private function initBalanceAmount(): array
    {
        $rtn = [
            'balance_id' => 0,
            'balance_amount' => 0,
        ];
        //获取随机金额id
        $weight = $this->balanceAmountConfig['weight'] ?? [];
        if (!$weight) {
            return $rtn;
        }
        $configId = get_rand($weight);

        //获取随机金额范围
        $amountRegion = $this->balanceAmountConfig['amount_region'] ?? [];
        if (!$amountRegion || !isset($amountRegion[$configId])) {
            return $rtn;
        }
        $amount = random_int($amountRegion[$configId]['min'], $amountRegion[$configId]['max']);
        return [
            'balance_region_id' => $configId,
            'balance_amount' => $amount * 1000
        ];
    }



    //处理重置金额
    private function resetBalanceAmount($regionId, $balanceAmount)
    {
        //如果身上的金币数量小于进入房间所需要的最小金额 执行充值行为
        //获取随机金额范围
        //【0-（最大金额-最小金额）】随机+最小金额
        //获取随机金额范围
        $amountRegion = $this->balanceAmountConfig['amount_region'] ?? [];
        if (!$amountRegion || !isset($amountRegion[$regionId])) {
            return $balanceAmount;
        }
        $amount = random_int(0, $amountRegion[$regionId]['max'] - $amountRegion[$regionId]['min']) + $amountRegion[$regionId]['min'];
        return $amount * 1000;
    }
}
