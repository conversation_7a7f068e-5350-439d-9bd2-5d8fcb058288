<?php
/**
 * 此逻辑为ai下注
 */

namespace common\AiCommon;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;

class AiDealerGameBetV2
{

    //设置下注的筹码面值列表
    private $chipFaceValueList = [];

    //房间类型信息
    private $roomInfo = [];

    //AI列表
    private $aiList = [];

    //AI下注配置
    private $betConfig = [];

    //下注区域
    private $betOption = [];

    //设置可变选项权重
    private $betOptionChangeWeight = [];

    //当前游戏进行到了第几秒
    private $curSecond;

    //当前游戏下注的最大时间
    private $betMaxSecond;

    //当前ai的类型
    private $aiType = 'other';

    //互斥选项
    private $mutexList = [];

    //补充ai数量
    private $inventedAiCnt = -100;


    //luckyLoto 开奖奖池金额
    private $luckyLotoJackPotAmount = 0;
    //luckyLoto 上次开奖时间戳
    private $luckyLotoLastOpenJackPotTime = 0;

    public function __construct()
    {

    }
    //luckyLoto 上次开奖时间戳
    public function setLuckyLotoLastOpenJackPotTime($luckyLotoLastOpenJackPotTime)
    {
        $this->luckyLotoLastOpenJackPotTime = $luckyLotoLastOpenJackPotTime;
    }

    //luckyLoto 开奖奖池金额
    public function setLuckyLotoJackPotAmount($luckyLotoJackPotAmount)
    {
        $this->luckyLotoJackPotAmount = $luckyLotoJackPotAmount;
    }

    //构建虚拟ai的数量
    public function setInventedAiCnt($inventedAiCnt)
    {
        $this->inventedAiCnt = -intval($inventedAiCnt);
    }

    //设置当前下注轮数
    public function setRoomInfo($roomType)
    {
        $roomInfo = parse_room_type($roomType);
        $this->roomInfo = $roomInfo;
    }

    //设置下注可变选项权重
    public function setBetOptionChangeWeight($betOptionChangeWeight)
    {
        $this->betOptionChangeWeight = $betOptionChangeWeight;
    }

    //设置机器人下注区域
    public function setMutexList($mutexList)
    {
        $this->mutexList = $mutexList;
    }

    //设置下注区域
    public function setBetOption(array $betOption)
    {
        $this->betOption = $betOption;
    }

    //设置下注配置
    public function setBetConfig(array $betConfig)
    {
        $this->betConfig = $betConfig;
    }

    //设置当前游戏是第几秒
    public function setCurSecond(int $second)
    {
        $this->curSecond = $second;
    }

    //设置当前下注的ai列表
    public function setAiList(array $aiList)
    {
        $this->aiList = $aiList;
    }

    //设置当前下注筹码面值列表
    public function setChipFaceValueList(array $chipFaceValueList)
    {
        $this->chipFaceValueList = $chipFaceValueList;
    }
    //设置下注的最大秒数
    public function setBetMaxSecond(int $maxSecond)
    {
        $this->betMaxSecond = $maxSecond;
    }

    //设置下注的ai类型
    public function setAiType($type)
    {
        if (in_array($type, ['other', 'rank'])) {
            $this->aiType = $type;
        } else {
            $this->aiType = 'other';
        }
    }


    //补充虚拟ai
    private function addAiList()
    {
        if ($this->aiType != 'other') {
            return;
        }
        for ($i = -1; $i >= $this->inventedAiCnt; $i--) {
            $this->aiList[$i]['bet_option'] = [];
            $this->aiList[$i]['all_bet_amount'] = 0;
            $this->aiList[$i]['over_bet_amount'] = 0;
        }
    }


    //获取可以随机的下注区域
    private function getBetOption(): array
    {
        /*
            $mutexList示例 ↓

            'other' => [
                [
                    'option' => [0 => 462, 2 => 462],
                    'select_option_cnt' => 1
                ],
                [
                    'option' => [1 => 50],
                    'select_option_cnt' => 1
                ]
            ],

            'other'为 $this->aiType
        */
        $optionList = [];
        $betOptionList = $this->mutexList[$this->aiType] ?? [];
        foreach ($betOptionList as $val) {
            for ($i = 1; $i <= $val['select_option_cnt']; $i++) {
                if (!isset($val['option']) || !$val['option']) {
                    continue;
                }
                $option = get_rand($val['option']);
                if ($option < 0) {
                    continue;
                }
                $optionList[$option] = $val['option'][$option];
                unset($val['option'][$option]);
            }
        }
        return $optionList;
    }

    //获取下注列表
    public function getBetList(): array
    {
        //补充虚拟ai
        if ($this->aiType == 'other') {
            $this->addAiList();
        }

        //获取当前ai的数量
        $aiCnt = count($this->aiList);
        $betConfigIndex = 0;
        $list = [];
        if ($aiCnt <= 0) {
            return $list;
        }

        // 规划每一秒的下注计划
        for ($i = 1; $i <= $this->betMaxSecond; $i++) {
            if ($i >= $this->betConfig[$betConfigIndex]['min_second'] && $i <= $this->betConfig[$betConfigIndex]['max_second']) {
            } else {
                $betConfigIndex++;
            }
            $betConfig = $this->betConfig[$betConfigIndex];
            //            Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__,'betConfig_tttt：$betConfigIndex'.$betConfigIndex.'$this->betMaxSecond'.$this->betMaxSecond.'$i'.$i);
//            Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__,'betConfig_tttt：'.json_encode($this->betConfig));
            if (empty($this->aiList) || !is_array($this->aiList)) {
                continue;
            }

            // 规划这一秒各个机器人的下注计划
            foreach ($this->aiList as $aiPlayerIndex => $info) {
                //判断这个ai本次是否下注(有概率的 不是每个ai都一定会下注)
                // 有秒内下注概率的，参考 LONG_HU_BET_WEIGHT
                $isBet = $this->getIsBet($betConfig);
                if (!$isBet) {
                    continue;
                }

                //判断是否为 后补充的虚拟ai 如果是的话 赋值
                if ($aiPlayerIndex < 0) {
                    $betInfo['bet_option'] = $this->getBetOption();
                    $betInfo['all_bet_amount'] = random_int(35000, 2000000);
                    $betInfo['over_bet_amount'] = 0;
                } else {
                    $betInfo = $this->aiList[$aiPlayerIndex];
                }

                // 按下注区域权重表随机得到一个下注区域
                $betOption = get_rand($betInfo['bet_option']);

                //获取本次下注金额
                //all_bet_amount = $user_data['ai_bet_fee']     本轮下注总金额 也可以理解成是一个刻度线 如果超过刻度线 代表后续不再下注
                //over_bet_amount = $user_data['bet_fee']       本轮累计下注金额 初始为0 每一次循环都会累加 直到超过all_bet_amount 不再累加
                if ($this->aiType == 'rank') {
                    $allBetAmount = $this->getBetAmountRank($betConfig, $betInfo['all_bet_amount'], $betInfo['over_bet_amount'], $betOption);
                } else {
                    $allBetAmount = $this->getBetAmountOther($betConfig, $betInfo['all_bet_amount'], $betInfo['over_bet_amount'], $betOption);
                }

                //根据下注金额 匹配相邻最近的筹码 和 筹码个数
                $chipInfo = $this->getNearChipAndCntRank($aiPlayerIndex, $allBetAmount['surplusAmount'], $allBetAmount['betAmount']);
                if (!$chipInfo || $chipInfo['cnt'] <= 0) {
                    continue;
                }
                $chipInfo['bet_option'] = $betOption;

                $list[$i][$aiPlayerIndex] = $chipInfo;

                //修改本轮累计下注金额
                $this->aiList[$aiPlayerIndex]['over_bet_amount'] = $this->aiList[$aiPlayerIndex]['over_bet_amount'] + $chipInfo['chip_face_value'] * $chipInfo['cnt'];
            }
        }
        /*
            $list的最里层的返回参数：
                chip_face_value ：下注的筹码
                cnt：下注的筹码数量
                bet_option：下注的奖池
        */
        return $list;
    }



    //获取下注列表
    public function getBetListLuckyLoto(): array
    {
        //人数增幅比例=距离上次意思开奖间隔时间（s）*0.00002
        $addBetOption0PlayerCntRate = (time() - $this->luckyLotoLastOpenJackPotTime) * 0.00002;
        //钱数增幅=MAX(当前显示的奖池金额*0.0000001-0.1,0）
        $addBetAmountRate = max($this->luckyLotoJackPotAmount * 0.0000000001 - 0.1, 0);
        if ($this->aiType == 'other') {
            $this->addAiList();
        }
        //获取当前ai的数量
        $aiCnt = count($this->aiList);
        $betConfigIndex = 0;
        $list = [];
        if ($aiCnt <= 0) {
            return $list;
        }
        //获取每秒下注的ai
        for ($i = 1; $i <= $this->betMaxSecond; $i++) {
            if ($i >= $this->betConfig[$betConfigIndex]['min_second'] && $i <= $this->betConfig[$betConfigIndex]['max_second']) {
            } else {
                $betConfigIndex++;
            }
            $betConfig = $this->betConfig[$betConfigIndex];
            //            Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__,'betConfig_tttt：$betConfigIndex'.$betConfigIndex.'$this->betMaxSecond'.$this->betMaxSecond.'$i'.$i);
//            Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__,'betConfig_tttt：'.json_encode($this->betConfig));
            if (empty($this->aiList) || !is_array($this->aiList)) {
                continue;
            }

            //下注0区域的人数
            $betOption0PlayerCnt = 0;
            $notBetOption0PlayerList = $this->aiList;
            foreach ($this->aiList as $aiPlayerIndex => $info) {
                $isBet = $this->getIsBet($betConfig);
                if (!$isBet) {
                    continue;
                }
                if ($aiPlayerIndex < 0) {
                    $betInfo['bet_option'] = $this->getBetOption();
                    $betInfo['all_bet_amount'] = random_int(35000, 2000000);
                    $betInfo['over_bet_amount'] = 0;
                } else {
                    $betInfo = $this->aiList[$aiPlayerIndex];
                }
                $betOption = get_rand($betInfo['bet_option']);
                //获取本次下注金额
                $allBetAmount = $this->getBetAmountOther($betConfig, $betInfo['all_bet_amount'], $betInfo['over_bet_amount'], $betOption);
                if ($betOption == 0) {
                    unset($notBetOption0PlayerList[$aiPlayerIndex]);
                    $betOption0PlayerCnt++;
                    $allBetAmount['betAmount'] += ($allBetAmount['betAmount'] * $addBetAmountRate);
                }
                $chipInfo = $this->getNearChipAndCntRank($aiPlayerIndex, $allBetAmount['surplusAmount'], $allBetAmount['betAmount']);
                if (!$chipInfo || $chipInfo['cnt'] <= 0) {
                    continue;
                }
                $chipInfo['bet_option'] = $betOption;
                $list['other'][$i][$aiPlayerIndex] = $chipInfo;
                $this->aiList[$aiPlayerIndex]['over_bet_amount'] = $this->aiList[$aiPlayerIndex]['over_bet_amount'] + $chipInfo['chip_face_value'] * $chipInfo['cnt'];
            }

            //补充人数
            $addPlayerCnt = intval($betOption0PlayerCnt * $addBetOption0PlayerCntRate);
            $addPlayerCnt = min($addPlayerCnt, count($notBetOption0PlayerList));
            if ($addPlayerCnt >= 1) {
                //补齐下注

                $randPlayerIndexList = array_rand($notBetOption0PlayerList, $addPlayerCnt);
                if (!is_array($randPlayerIndexList)) {
                    $randPlayerIndexList = [$randPlayerIndexList];
                }
                foreach ($randPlayerIndexList as $aiPlayerIndex) {
                    $betInfo = $this->aiList[$aiPlayerIndex];
                    $betOption = 0;
                    $allBetAmount = $this->getBetAmountOther($betConfig, $betInfo['all_bet_amount'], $betInfo['over_bet_amount'], $betOption);
                    $allBetAmount['betAmount'] += ($allBetAmount['betAmount'] * $addBetAmountRate);
                    $chipInfo = $this->getNearChipAndCntRank($aiPlayerIndex, $allBetAmount['surplusAmount'], $allBetAmount['betAmount']);
                    if (!$chipInfo || $chipInfo['cnt'] <= 0) {
                        continue;
                    }
                    $chipInfo['bet_option'] = $betOption;
                    $list['rank'][$i][$aiPlayerIndex] = $chipInfo;
                    $this->aiList[$aiPlayerIndex]['over_bet_amount'] = $this->aiList[$aiPlayerIndex]['over_bet_amount'] + $chipInfo['chip_face_value'] * $chipInfo['cnt'];
                }
            }
        }
        return $list;
    }



    //获取下注列表
    public function getBetListV3(): array
    {
        if ($this->aiType == 'other') {
            $this->addAiList();
        }
        //获取当前ai的数量
        $aiCnt = count($this->aiList);
        $betConfigIndex = 0;
        $list = [];
        if ($aiCnt <= 0) {
            return $list;
        }
        //获取每秒下注的ai
        for ($i = 1; $i <= $this->betMaxSecond; $i++) {
            if ($i >= $this->betConfig[$betConfigIndex]['min_second'] && $i <= $this->betConfig[$betConfigIndex]['max_second']) {
            } else {
                $betConfigIndex++;
            }
            $betConfig = $this->betConfig[$betConfigIndex];
            if (empty($this->aiList) || !is_array($this->aiList)) {
                continue;
            }
            foreach ($this->aiList as $aiPlayerIndex => $info) {
                $isBet = $this->getIsBet($betConfig);
                if (!$isBet) {
                    continue;
                }
                if ($aiPlayerIndex < 0) {
                    $betInfo['bet_option'] = $this->getBetOption();
                    $betInfo['all_bet_amount'] = random_int(35000, 2000000);
                    $betInfo['over_bet_amount'] = 0;
                } else {
                    $betInfo = $this->aiList[$aiPlayerIndex];
                }
                $betOption = get_rand($betInfo['bet_option']);


                //获取本次下注金额
                if ($this->aiType == 'rank') {
                    $allBetAmount = $this->getBetAmountRank($betConfig, $betInfo['all_bet_amount'], $betInfo['over_bet_amount'], $betOption);
                } else {
                    $allBetAmount = $this->getBetAmountOther($betConfig, $betInfo['all_bet_amount'], $betInfo['over_bet_amount'], $betOption);
                }
                if ($allBetAmount['betAmount'] > $allBetAmount['surplusAmount']) {
                    continue;
                }
                if (random_int(1, 10) > 2) {
                    $chipInfo = $this->getNearChipAndCntRank($aiPlayerIndex, $allBetAmount['surplusAmount'], $allBetAmount['betAmount']);

                } else {
                    $chipInfo['chip_face_value'] = min((intval($allBetAmount['betAmount'] * 0.001) * 1000), 20000000);
                }

                $chipInfo['cnt'] = 1;
                if ($aiPlayerIndex > 0 && isset($chipInfo['chip_face_value']) && $chipInfo['chip_face_value'] > 0) {
                    $this->aiList[$aiPlayerIndex]['over_bet_amount'] = $this->aiList[$aiPlayerIndex]['over_bet_amount'] + $chipInfo['chip_face_value'] * $chipInfo['cnt'];
                }
                $chipInfo['bet_option'] = $betOption;
                $list[$i][$aiPlayerIndex] = $chipInfo;
                unset($this->aiList[$aiPlayerIndex]);
            }
        }
        return $list;
    }




    //获取下注列表
    public function getBetListV2(): array
    {
        //获取当前ai的数量
        $aiCnt = count($this->aiList);
        $list = [];
        $round1BetInfo = [];
        if ($aiCnt <= 0) {
            return $list;
        }
        $betAiPlayerIndex = [];
        //获取每秒下注的ai
        for ($r = 1; $r <= 2; $r++) {
            if ($r == 2) {
                $this->aiList = $betAiPlayerIndex[1];
            }
            for ($i = 1; $i <= $this->betMaxSecond; $i++) {
                $betConfigIndex = ($r - 1) * 10 + $i;
                $betConfig = $this->betConfig[$betConfigIndex - 1];
                if (empty($this->aiList) || !is_array($this->aiList)) {
                    continue;
                }
                foreach ($this->aiList as $aiPlayerIndex => $info) {
                    $isBet = $this->getIsBet($betConfig);
                    if (!$isBet) {
                        continue;
                    }
                    //一轮只能下注一次
                    if (isset($betAiPlayerIndex[$r][$aiPlayerIndex])) {
                        continue;
                    }
                    $betInfo = $this->aiList[$aiPlayerIndex];
                    //如果存在上次的下注区域   则
                    $lastBetOption = 100;
                    if (isset($round1BetInfo[$aiPlayerIndex]) && !empty($round1BetInfo[$aiPlayerIndex])) {
                        $lastBetOption = $round1BetInfo[$aiPlayerIndex]['bet_option'];
                    }
                    $betOptionRand = $this->betOptionChangeWeight[$this->aiType][$lastBetOption] ?? $this->betOptionChangeWeight[$this->aiType][-1];
                    $betOption = get_rand($betOptionRand);
                    //没有随机到下注
                    if ($betOption < 0) {
                        continue;
                    }
                    //如果随机到的结果和上次相反 且余额不足总的需要下注的金额的一半 则不下注
                    if ($lastBetOption != 100 && $betOption != $lastBetOption && $betInfo['over_bet_amount'] > $betInfo['all_bet_amount'] * 0.5) {
                        continue;
                    }
                    //获取本次下注金额
                    if ($this->aiType == 'rank') {
                        $allBetAmount = $this->getBetAmountRank($betConfig, $betInfo['all_bet_amount'], $betInfo['over_bet_amount'], $betOption);
                    } else {
                        $allBetAmount = $this->getBetAmountOther($betConfig, $betInfo['all_bet_amount'], $betInfo['over_bet_amount'], $betOption);
                    }
                    $chipInfo = $this->getNearChipAndCntRank($aiPlayerIndex, $allBetAmount['surplusAmount'], $allBetAmount['betAmount']);
                    if (!$chipInfo || $chipInfo['cnt'] <= 0) {
                        continue;
                    }
                    $chipInfo['bet_option'] = $betOption;
                    $list[$betConfigIndex][$aiPlayerIndex] = $chipInfo;
                    if ($aiPlayerIndex > 0) {
                        $this->aiList[$aiPlayerIndex]['bet_option'] = $betOption;
                        $this->aiList[$aiPlayerIndex]['over_bet_amount'] = $this->aiList[$aiPlayerIndex]['over_bet_amount'] + $chipInfo['chip_face_value'] * $chipInfo['cnt'];
                    }
                    $betAiPlayerIndex[$r][$aiPlayerIndex] = $this->aiList[$aiPlayerIndex];
                }
            }
        }
        return $list;
    }

    //获取本次是否下注
    private function getIsBet($betConfig): bool
    {
        //        if(empty($betConfig)){
//            Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__,'betConfig_ssss'.json_encode($betConfig));
//        }
        $betRate = $this->aiType == 'rank' ? $betConfig['rank_bet_rate'] : $betConfig['other_bet_rate'];
        $rand[1] = $betRate * 100;
        $rand[0] = 100 - $rand[1];
        return (bool) get_rand($rand);
    }


    //获取本轮下注金额
    private function getBetAmountOther($curSecondConfig, $allBetAmount, $overBetAmount, $betOption): array
    {
        //如果本轮累计下注金额 > 本轮下注总金额 代表不再下注了 直接return  
        if ($overBetAmount >= $allBetAmount) {
            return ['surplusAmount' => 0, 'betAmount' => 0];
        }

        // 根据区域权重表随机得到一个区域
        $regionId = get_rand($curSecondConfig['bet_amount_region_weight']);
        // 区域定义了下注率范围
        if (!isset($curSecondConfig['bet_amount_region'][$regionId]['min'])) {
            $min = 0;
        } else {
            $min = $curSecondConfig['bet_amount_region'][$regionId]['min'] * 10000;
        }
        $max = $curSecondConfig['bet_amount_region'][$regionId]['max'] * 10000;
        // 根据下注率范围得到下注率
        $betRate = random_int($min, $max) * 0.0001;
        // 剩余下注额
        $rtn['surplusAmount'] = $allBetAmount - $overBetAmount;
        // 下注位置决定的下注率系数
        if (!$this->betOption) {
            $betAmountX = 1;
        } else {
            $betAmountX = $this->betOption[$betOption]['bet_amount_x'] ?? 1;
        }
        // 计算本轮下注金额
        $randBetAmount = intval($allBetAmount * $betRate * $betAmountX);
        // 不能超过剩余下注额
        $rtn['betAmount'] = min($rtn['surplusAmount'], $randBetAmount);

        return $rtn;
    }

    //获取本轮下注金额
    private function getBetAmountRank($curSecondConfig, $allBetAmount, $overBetAmount, $betOption): array
    {
        //如果本轮累计下注金额 > 本轮下注总金额 代表不再下注了 直接return  
        if ($overBetAmount >= $allBetAmount) {
            return ['surplusAmount' => 0, 'betAmount' => 0];
        }

        //计算本轮下注金额
        $betRate = random_int($curSecondConfig['min_bet_amount_rate'] * 100, $curSecondConfig['max_bet_amount_rate'] * 100) * 0.01;
        $rtn['surplusAmount'] = $allBetAmount - $overBetAmount;
        if (!$this->betOption) {
            $betAmountX = 1;
        } else {
            $betAmountX = $this->betOption[$betOption]['bet_amount_x'] ?? 1;
        }
        $randBetAmount = intval($allBetAmount * $betRate * $betAmountX);
        $rtn['betAmount'] = min($rtn['surplusAmount'], $randBetAmount);
        return $rtn;
    }


    //找出距离最近的筹码 并返回下注个数
    private function getNearChipAndCntRank($playerIndex, $surplusAmount, $amount): array
    {
        $chipInfo['chip_face_value'] = 0;
        $chipInfo['cnt'] = 0;
        $chip = min($this->chipFaceValueList) * 1000;
        if ($chip > $surplusAmount || $amount < 10) {
            return $chipInfo;
        }
        foreach ($this->chipFaceValueList as $key => $val) {
            if ($amount < $val * 1000) {
                break;
            }
            if ($playerIndex < 0 && isset($this->chipFaceValueList[$key + 1])) {
                //模拟的ai下注
                $chip = $this->chipFaceValueList[$key + 1] * 1000;
            } else {
                $chip = $val * 1000;
            }
        }
        if ($chip <= 0) {
            return $chipInfo;
        }
        $cnt = max(1, intval($amount / $chip));
        $chipInfo['chip_face_value'] = $chip;
        $chipInfo['cnt'] = $cnt;
        return $chipInfo;
    }
}
