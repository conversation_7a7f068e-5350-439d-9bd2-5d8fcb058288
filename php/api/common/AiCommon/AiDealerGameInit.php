<?php
/**
 * @todo 坐庄类游戏 进入牌桌初始化
 */

namespace common\AiCommon;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;

class AiDealerGameInit
{

    //AI列表
    private $aiList = [];

    //下注区域
    private $betOptionList = [];

    //下注概率
    private $betRate = [];

    //下注金额比例
    private $betAmountRate = [];

    //桌子配置
    private $tableConfig = [];

    //获取是other  还是 rank
    private $aiType = 'other';


    public function __construct()
    {

    }
    //设置机器人下注区域
    public function setBetOptionList($mutexList)
    {
        $this->betOptionList = $mutexList;
    }

    //设置机器人下注概率
    public function setBetRate($betRate)
    {
        $this->betRate = $betRate;
    }

    //设置机器人下注金额比例
    public function setBetAmountRate($betAmountRate)
    {
        $this->betAmountRate = $betAmountRate;
    }

    //设置需要初始化的ai列表
    public function setAiList($aiList)
    {
        $this->aiList = $aiList;
    }

    //设置下注的类型
    public function setAiType($type)
    {
        $this->aiType = $type;
    }

    //设置桌子配置
    public function setTableConfig($tableConfig)
    {
        $this->tableConfig = $tableConfig;
    }

    //获取ai初始化信息
    public function getAiInitList(): array
    {
        if (!is_array($this->aiList) || empty($this->aiList)) {
            return [];
        }
        $list = [];
        foreach ($this->aiList as $playerIndex => $balanceAmount) {
            $info = [];
            if ($balanceAmount <= 0) {
                continue;
            }
            // 获取本轮是否下注
            if (!$this->getIsBet()) {
                continue;
            }
            // 获取下注区域权重表
            $info['bet_option'] = $this->getBetOption();
            // 本轮要下注的总金额
            // 根据余额计算和玩法配置计算本局要下注的总金额
            $info['all_bet_amount'] = $this->getBetAmount($balanceAmount);
            $list[$playerIndex] = $info;
        }

        return $list;
    }



    // 获取下注区域权重表
    private function getBetOption(): array
    {
        // rank 和 other 配置了不同的风格，参考 LONG_HU_MUTEX_LIST
        $optionList = [];
        $betOptionList = $this->betOptionList[$this->aiType] ?? [];
        // 遍历数组，每项有个权重表(option)，有个随机几次(select_option_cnt)
        foreach ($betOptionList as $val) {
            for ($i = 1; $i <= $val['select_option_cnt']; $i++) {
                $option = get_rand($val['option']);
                if ($option < 0) {
                    continue;
                }
                $optionList[$option] = $val['option'][$option];
                unset($val['option'][$option]);
            }
        }
        // 返回权重表
        return $optionList;
    }

    // 获取本轮是否下注
    private function getIsBet()
    {
        // rank 和 other 配置了不同的下注概率，参考 LONG_HU_BET_RATE
        $betRate = $this->betRate[$this->aiType] ?? [];
        $rand[1] = $betRate * 100;
        $rand[0] = 100 - $rand[1];
        return get_rand($rand);
    }


    // 获取本轮要下注的总金额
    // 根据余额计算和玩法配置计算本局要下注的总金额
    private function getBetAmount($balanceAmount)
    {
        // rank 和 other 配置了不同的比例范围，参考 LONG_HU_BET_AMOUNT_RATE
        $betAmountRate = $this->betAmountRate[$this->aiType] ?? [];
        $betAmountRate = random_int($betAmountRate['min'] * 1000, $betAmountRate['max'] * 1000) * 0.001;
        $betAmount = $betAmountRate * $balanceAmount;
        // 不同的玩法有不同的下注上限，参考 LONG_HU_TABLE
        $tableConfigMaxAmount = ($this->tableConfig['max_amount'] ?? 20000) * 1000;
        return min($betAmount, $tableConfigMaxAmount);
    }
}
