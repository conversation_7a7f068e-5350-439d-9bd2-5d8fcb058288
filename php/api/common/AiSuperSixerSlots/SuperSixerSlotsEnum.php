<?php
declare(strict_types=1);
namespace common\AiSuperSixerSlots;

class SuperSixerSlotsEnum
{
    /* <注意，此配置文件里所有金额单位都是卢比，和此玩法有关存储在redis里的金额都是千分位，注意区分！！！！！！！！>*/

    // 2
    const LUCKY_DRAW_TWO = 1;
    // 4
    const LUCKY_DRAW_FOUR = 2;
    // 6
    const LUCKY_DRAW_SIX = 3;
    // 中奖奖杯
    const LUCKY_DRAW_CUP = 4;
    // 清除奖励
    const LUCKY_DRAW_CLEAR = 5;
    // 未中奖
    const LUCKY_DRAW_NOT_WIN = 6;

    // 最大能领取的奖励倍数
    const MAX_REWARD_MULTIPLE = 500;

    /**
     * 普通情况下 抽奖权重配置
     * @var array
     */
    const LUCKY_DRAW_NORMAL_WEIGHT = [
        self::LUCKY_DRAW_NOT_WIN => 650,
        self::LUCKY_DRAW_TWO => 200,
        self::LUCKY_DRAW_FOUR => 77,
        self::LUCKY_DRAW_SIX => 23,
        self::LUCKY_DRAW_CLEAR => 82,
        self::LUCKY_DRAW_CUP => 20,
    ];

    /**
     * 复仇杀状态下 抽奖权重配置
     * @var array
     */
    const LUCKY_DRAW_WIN_POINT_KILLER_WEIGHT = [
        self::LUCKY_DRAW_NOT_WIN => 865,
        self::LUCKY_DRAW_TWO => 200,
        self::LUCKY_DRAW_FOUR => 77,
        self::LUCKY_DRAW_SIX => 23,
        self::LUCKY_DRAW_CLEAR => 100,
        self::LUCKY_DRAW_CUP => 20,
    ];

    /**
     * 奖励梯度配置 key当前解锁的奖励等级 val对应的奖励倍率
     * @var array
     */
    const REWARD_CONFIG = [
        self::LUCKY_DRAW_TWO => [
            '1' => 1,
            '2' => 2,
            '3' => 4,
            '4' => 7,
            '5' => 10
        ],
        self::LUCKY_DRAW_FOUR => [
            '1' => 3,
            '2' => 8,
            '3' => 16,
            '4' => 29,
            '5' => 50
        ],
        self::LUCKY_DRAW_SIX => [
            '1' => 10,
            '2' => 40,
            '3' => 100,
            '4' => 200,
            '5' => 350
        ],
    ];

    /**
     * 下注档位配置
     * balance : 根据余额找出对应的默认spin档位
     * @var array
     */
    const BET_LEVEL_CONFIG = [
        "2" => [
            'balance' => ['min' => 0, 'max' => 250],
        ],
        "5" => [
            'balance' => ['min' => 250, 'max' => 500],
        ],
        "10" => [
            'balance' => ['min' => 500, 'max' => 1500],
        ],
        "20" => [
            'balance' => ['min' => 1500, 'max' => 5000],
        ],
        "50" => [
            'balance' => ['min' => 5000, 'max' => 15000],
        ],
        "150" => [
            'balance' => ['min' => 15000, 'max' => 50000],
        ],
        "500" => [
            'balance' => ['min' => 50000, 'max' => 200000],
        ],
        "1000" => [
            'balance' => ['min' => 200000, 'max' => 600000],
        ],
        "2000" => [
            'balance' => ['min' => 600000, 'max' => 900000],
        ],
        "3000" => [
            'balance' => ['min' => 900000, 'max' => 0],
        ],
    ];

    const REWARD_TYPE_NULL = 0;
    const REWARD_TYPE_BIG_WIN = 1;
    const REWARD_TYPE_SUPER_WIN = 2;
    const REWARD_TYPE_MEGA_WIN = 3;
    const REWARD_TYPE_EPIC_WIN = 4;


    /**
     * 获取slots用户临时数据
     * @param int $uid
     * @param array $slots_user
     * @return void
     */
    public static function getSlotsUserInfo(int $uid, array &$slots_user)
    {
        \lbase\GetRedis::Master()->Pipeline(\wm\games\SuperSixerSlots\Keys::KeyUserInfo($uid)->GET($slots_user));
        if (empty($slots_user)) {
            self::initSlotsUserInfo($uid, $slots_user);
        }
    }

    /**
     * 初始化slots用户临时数据
     * @param int $uid
     * @param array $slots_user
     * @return void
     */
    public static function initSlotsUserInfo(int $uid, array &$slots_user)
    {
        // uid
        $slots_user['uid'] = $uid;
        // 当轮游戏的spin金额
        $slots_user['cur_bet_fee'] = 0;
        // 当前解锁到的奖励档位
        $slots_user['cur_rewards_level'] = [
            self::LUCKY_DRAW_TWO => 0,
            self::LUCKY_DRAW_FOUR => 0,
            self::LUCKY_DRAW_SIX => 0,
        ];
        // 当轮是否已领取过超级大奖
        $slots_user['is_top_prize'] = 0;
        // 额外获得的奖励
        $slots_user['extra_reward'] = 0;
    }


    /**
     * 根据用户携带金额获取bet档位
     * @param int $total_fee
     * @return int
     */
    public static function getUserBetFee(int $total_fee)
    {
        $minBetLevel = null;

        foreach (\common\AiSuperSixerSlots\SuperSixerSlotsEnum::BET_LEVEL_CONFIG as $betLevel => $config) {
            $minBalance = \llogic\common\LogicUtil::moneyChangeValueB2S($config['balance']['min']);
            $maxBalance = \llogic\common\LogicUtil::moneyChangeValueB2S($config['balance']['max']);

            // 检查用户金额是否在该档位的范围内
            if ($total_fee >= $minBalance && ($maxBalance === 0 || $total_fee < $maxBalance)) {
                return \llogic\common\LogicUtil::moneyChangeValueB2S($betLevel);
            }

            // 记录最小档位
            if ($minBetLevel === null || $minBetLevel > $minBalance) {
                $minBetLevel = $betLevel;
            }
        }

        // 如果没有找到匹配的档位返回最小档位 作为保底
        return \llogic\common\LogicUtil::moneyChangeValueB2S($minBetLevel);
    }


    /**
     * 计算可领取奖励
     * @param array $slots_user
     * @return float|int
     */
    public static function calculateClaimableReward(array $slots_user)
    {
        $config = \common\AiSuperSixerSlots\SuperSixerSlotsEnum::REWARD_CONFIG;
        $bet_fee = $slots_user['cur_bet_fee'];

        $multiple = 0;
        foreach ($config as $card => $rewards) {
            $level = $slots_user['cur_rewards_level'][$card];
            if (empty($level)) {
                continue;
            }

            if ($level >= count($rewards)) {
                $multiple += end($rewards);
            } else {
                $multiple += $rewards[$level];
            }
        }

        return $bet_fee * $multiple;
    }


    /**
     * 处理抽奖结果
     * @param int $card
     * @param int $bet_fee
     * @param array $slots_user
     * @return void
     */
    public static function handleSpinRes(int $card, int $bet_fee, array &$slots_user)
    {
        switch ($card) {
            // 普通奖
            case \common\AiSuperSixerSlots\SuperSixerSlotsEnum::LUCKY_DRAW_TWO:
            case \common\AiSuperSixerSlots\SuperSixerSlotsEnum::LUCKY_DRAW_FOUR:
            case \common\AiSuperSixerSlots\SuperSixerSlotsEnum::LUCKY_DRAW_SIX:
                self::updateRewardHighLight($card, $bet_fee, $slots_user);
                break;
            // 奖杯
            case \common\AiSuperSixerSlots\SuperSixerSlotsEnum::LUCKY_DRAW_CUP:
                self::updateRewardsForCup($bet_fee, $slots_user);
                break;
            // 清除
            case \common\AiSuperSixerSlots\SuperSixerSlotsEnum::LUCKY_DRAW_CLEAR:
                self::clearAccumulatedRewards($slots_user);
                break;
            // 未中奖
            default:
                self::processNoWin($bet_fee, $slots_user);
                break;
        }
    }


    /**
     * 普通奖
     * 更新对应奖励列的高亮状态和累计奖励
     * @param int $card
     * @param int $bet_fee
     * @param array $slots_user
     * @return void
     */
    public static function updateRewardHighLight(int $card, int $bet_fee, array &$slots_user)
    {
        $config = \common\AiSuperSixerSlots\SuperSixerSlotsEnum::REWARD_CONFIG;
        $multiple = 0;

        // 已解锁了该条奖励列的所有格子，额外给一份最高档位奖励
        $level = $slots_user['cur_rewards_level'][$card];
        if ($level == count($config[$card])) {
            $multiple = end($config[$card]);
            goto end;
        }

        // 解锁新格子，晋升奖励档位
        $slots_user['cur_rewards_level'][$card]++;

        // 判断是否满足超级大奖的条件
        if (self::allRewardsUnlocked($slots_user) && empty($slots_user['is_top_prize'])) {
            $slots_user['is_top_prize'] = 1;
            $multiple = \common\AiSuperSixerSlots\SuperSixerSlotsEnum::MAX_REWARD_MULTIPLE;
        }

        end:
        $slots_user['cur_bet_fee'] = $bet_fee;
        $slots_user['extra_reward'] = ($slots_user['extra_reward'] ?? 0) + $multiple * $bet_fee;
    }


    /**
     * 奖杯
     * 更新对应列奖励
     * @param int $bet_fee
     * @param array $slots_user
     * @return void
     */
    public static function updateRewardsForCup(int $bet_fee, array &$slots_user)
    {
        $config = \common\AiSuperSixerSlots\SuperSixerSlotsEnum::REWARD_CONFIG;
        $multiple = 0;

        foreach ($config as $card => $rewards) {
            $level = $slots_user['cur_rewards_level'][$card];

            // 已解锁了该条奖励列的所有格子，额外给一份最高档位奖励
            if ($level == count($rewards)) {
                $multiple += end($rewards);
            } else {
                $slots_user['cur_rewards_level'][$card]++;
            }
        }

        // 判断是否满足超级大奖的条件
        if (self::allRewardsUnlocked($slots_user) && empty($slots_user['is_top_prize'])) {
            $slots_user['is_top_prize'] = 1;
            $multiple = \common\AiSuperSixerSlots\SuperSixerSlotsEnum::MAX_REWARD_MULTIPLE;
        }

        $slots_user['cur_bet_fee'] = $bet_fee;
        $slots_user['extra_reward'] = ($slots_user['extra_reward'] ?? 0) + $multiple * $bet_fee;
    }


    /**
     * 清除
     * 清除当前积累的所有已解锁奖励
     * @param array $slots_user
     * @return void
     */
    public static function clearAccumulatedRewards(array &$slots_user)
    {
        $config = \common\AiSuperSixerSlots\SuperSixerSlotsEnum::REWARD_CONFIG;
        $slots_user['cur_rewards_level'] = array_fill_keys(array_keys($config), 0);
        $slots_user['is_top_prize'] = 0;
        $slots_user['cur_bet_fee'] = 0;
        $slots_user['extra_reward'] = 0;
    }


    /**
     * 未中奖
     * 记录spin金额
     * @param int $bet_fee
     * @param array $slots_user
     * @return void
     */
    public static function processNoWin(int $bet_fee, array &$slots_user)
    {
        $slots_user['cur_bet_fee'] = $bet_fee;
        $slots_user['extra_reward'] = 0;
    }


    /**
     * 检查是否所有奖励全部解锁
     * @param array $slots_user
     * @return bool
     */
    public static function allRewardsUnlocked(array $slots_user)
    {
        $config = \common\AiSuperSixerSlots\SuperSixerSlotsEnum::REWARD_CONFIG;
        foreach ($config as $card => $rewards) {
            if ($slots_user['cur_rewards_level'][$card] < count($rewards)) {
                return false;
            }
        }
        return true;
    }
}


