<?php
/**
 * SuperSixerSlots数据模拟
 * 当积攒奖励达到一定条件后就收奖，收奖后重置
 * 收奖倍数是参数可传的变量
 */
namespace common\AiSuperSixerSlots;

use common\Common;

class SuperSixerSlotsDealCardReport extends SuperSixerSlotsDealCard
{
    // 获得Top Prize的次数
    private static $top_price_count = 0;

    // 获得超额奖的次数
    private static $extra_reward_count = 0;


    /*
        20卢比的底注模拟10W次
        只要当前积攒的奖励达到一定条件，就收奖，收奖后重置所有进度

        收奖条件：
            >= 2倍收奖                ：变量传2
            >= 5倍收奖                ：变量传5
            >= 10倍收奖               ：变量传10
            >= 20倍收奖               ：变量传20
            >= 50倍收奖               ：变量传50
            >= 100倍收奖              ：变量传100
            >= 300倍收奖              ：变量传300
            获得Top Prize后收奖       ：变量传10000
            获得1次超额奖后收奖       ：变量传10001
            获得2次超额奖后收奖       ：变量传10002
            获得3次超额奖后收奖       ：变量传10003

        统计列：
            中奖总金额 =(超级大奖 + 超额奖 + 主动领奖),
            超级大奖,
            超额奖,
            主动领奖,
            是否中奖,
            2中线,
            4中线,
            6中线,
            Wild中线,
            kong中线,
            Reset Cashpot中线
    */

    //php hp php/api/common/AiSuperSixerSlots/SuperSixerSlotsDealCardReport.php Main 10000
    public static function Main(int $multiple = 20, int $bet_fee = 0, int $num = 0, int $is_backend = 0)
    {
        if (empty($num)) {
            $num = 100000;
        }

        if (empty($bet_fee)) {
            $bet_fee = 20;
        }

        $uid = 123456;
        $slots_user = [];
        \common\AiSuperSixerSlots\SuperSixerSlotsEnum::getSlotsUserInfo($uid, $slots_user);

        for ($i = 0; $i < $num; $i++) {
            $rows[] = self::randRow($multiple, $bet_fee, $slots_user);
        }

        // 设定标题行
        $title = '中奖总金额 =(超级大奖 + 超额奖 + 主动领奖),超级大奖,超额奖,主动领奖,是否中奖,2中线,4中线,6中线,Wild中线,kong中线,Reset Cashpot中线';
        $title = explode(',', $title);

        if ($is_backend) {
            // 来源于API后台，导出文件给浏览器
            put_csv($rows, $title, "SuperSixer");

        } else {
            // 来源于命令行执行，导出文件给本地存储文件
            self::export($rows, $$title);
        }
        return;

        return $rows;
    }

    private static function randRow(int $multiple, int $bet_fee, array &$slots_user)
    {
        $obj = self::newObj($bet_fee, $slots_user);
        $row = [];
        self::ProcessInitRows($row);
        $obj->rowCollect($multiple, $row, $slots_user);
        return $row;
    }

    private static function newObj(int $bet_fee, array $slots_user): SuperSixerSlotsDealCardReport
    {
        $betAmount = $bet_fee * 1000;
        $obj = new self($betAmount, $slots_user);
        return $obj;
    }

    public function __construct($betAmount, $slots_user)
    {
        $this->allRaiseAmount = $betAmount;
        $this->slots_user = $slots_user;
        $this->betFee = $betAmount;
    }

    private function rowCollect($multiple, &$row, &$slots_user)
    {
        $bet_fee = $this->betFee;

        $dealCardInfo = $this->dealCardOnce();

        // 本次spin中奖结果
        $card = reset($dealCardInfo['dealCard']) ?? \common\AiSuperSixerSlots\SuperSixerSlotsEnum::LUCKY_DRAW_NOT_WIN;

        // 计算中奖线卡牌种类数量
        $this->ProcessWinCard($card, $row);

        // 处理本次spin带来的用户临时数据变化
        \common\AiSuperSixerSlots\SuperSixerSlotsEnum::handleSpinRes($card, $bet_fee, $slots_user);

        // 是否有额外获得的奖励，需要区分是 超级大奖 还是 超额奖
        $extra_reward = $slots_user['extra_reward'] ?? 0;
        $max_reward = \common\AiSuperSixerSlots\SuperSixerSlotsEnum::MAX_REWARD_MULTIPLE * $bet_fee;

        if ($extra_reward >= $max_reward) {
            // 超级大奖
            $extra_reward = \llogic\common\LogicUtil::moneyChangeValueS2B($extra_reward);
            $row['total_rewards'] = ($row['total_rewards'] ?? 0) + $extra_reward;
            $row['total_top_prize'] = ($row['total_top_prize'] ?? 0) + $extra_reward;
            $slots_user['extra_reward'] = 0;
            self::$top_price_count++;

        } elseif ($extra_reward) {
            // 超额奖
            $extra_reward = \llogic\common\LogicUtil::moneyChangeValueS2B($extra_reward);
            $row['total_rewards'] = ($row['total_rewards'] ?? 0) + $extra_reward;
            $row['extra_rewards'] = ($row['extra_rewards'] ?? 0) + $extra_reward;
            $slots_user['extra_reward'] = 0;
            self::$extra_reward_count++;
        }

        // 看是否满足里面的条件，满足就领奖
        self::receive($bet_fee, $multiple, $slots_user, $row);
    }


    /**
     * 初始化统计项
     * @param mixed $row
     * @return void
     */
    private static function ProcessInitRows(&$row)
    {
        // 中奖总金额
        $row['total_rewards'] = 0;
        // 超级大奖
        $row['total_top_prize'] = 0;
        // 超额奖
        $row['extra_rewards'] = 0;
        // 主动领奖
        $row['receive_rewards'] = 0;
        // 是否中奖
        $row['is_win'] = 0;
        // 2
        $row['two'] = 0;
        // 4
        $row['four'] = 0;
        // 6
        $row['six'] = 0;
        // 奖杯
        $row['cup'] = 0;
        // 没中奖
        $row['no_win'] = 0;
        // 清除所有
        $row['clear'] = 0;
    }

    private function ProcessWinCard($card, &$row)
    {
        if ($card == \common\AiSuperSixerSlots\SuperSixerSlotsEnum::LUCKY_DRAW_TWO) {
            $row['two'] = 1;
            $row['is_win'] = 1;
        } else if ($card == \common\AiSuperSixerSlots\SuperSixerSlotsEnum::LUCKY_DRAW_FOUR) {
            $row['four'] = 1;
            $row['is_win'] = 1;
        } else if ($card == \common\AiSuperSixerSlots\SuperSixerSlotsEnum::LUCKY_DRAW_SIX) {
            $row['six'] = 1;
            $row['is_win'] = 1;
        } else if ($card == \common\AiSuperSixerSlots\SuperSixerSlotsEnum::LUCKY_DRAW_CUP) {
            $row['cup'] = 1;
            $row['is_win'] = 1;
        } else if ($card == \common\AiSuperSixerSlots\SuperSixerSlotsEnum::LUCKY_DRAW_CLEAR) {
            $row['clear'] = 1;
        } else if ($card == \common\AiSuperSixerSlots\SuperSixerSlotsEnum::LUCKY_DRAW_NOT_WIN) {
            $row['no_win'] = 1;
        }
    }

    private static function receive($bet_fee, $multiple, &$slots_user, &$row)
    {
        // 计算当前积攒的奖励
        $reward = \common\AiSuperSixerSlots\SuperSixerSlotsEnum::calculateClaimableReward($slots_user);

        // 获得Top Prize后就收奖
        if ($multiple == 100000 && self::$top_price_count > 0) {
            self::$top_price_count = 0;
            goto receive;
        }
        // 获得1次超额奖后收奖
        elseif ($multiple == 100001 && self::$extra_reward_count >= 1) {
            self::$extra_reward_count = 0;
            goto receive;
        }
        // 获得2次超额奖后收奖
        elseif ($multiple == 100002 && self::$extra_reward_count >= 2) {
            self::$extra_reward_count = 0;
            goto receive;
        }
        // 获得3次超额奖后收奖
        elseif ($multiple == 100003 && self::$extra_reward_count >= 3) {
            self::$extra_reward_count = 0;
            goto receive;
        }
        // 如果当前积攒奖励超过规定的倍数 收奖
        elseif ($reward >= $bet_fee * $multiple) {
            goto receive;
        }
        // 不满足任何条件return
        else {
            return;
        }

        receive:
        $reward = \llogic\common\LogicUtil::moneyChangeValueS2B($reward);
        $row['total_rewards'] = ($row["total_rewards"] ?? 0) + $reward;
        $row['receive_rewards'] = ($row["receive_rewards"] ?? 0) + $reward;

        // 本轮游戏结束 清除用户临时信息的游戏数据
        \common\AiSuperSixerSlots\SuperSixerSlotsEnum::clearAccumulatedRewards($slots_user);
    }




    private static function export($rows, $title)
    {
        // 与php目录同级的tmp目录
        $slots_simulation = __DIR__ . '/../../../../tmp/simulation/super_six_slots.csv';
        @mkdir(dirname($slots_simulation), 0777, true);

        // 删除文件（如果存在）
        if (file_exists($slots_simulation)) {
            unlink($slots_simulation);
        }

        // 打开文件进行写入
        $file = fopen($slots_simulation, 'w');

        array_unshift($rows, $title);

        // 写入数据行，并在每行末尾添加换行符
        foreach ($rows as $key => $row) {
            fputcsv($file, $row);
        }

        // 关闭文件
        fclose($file);
    }
}
