<?php
/**
 * @todo SuperSixerSlots发牌
 */
namespace common\AiSuperSixerSlots;

use lib\Log;
use common\Common;

class SuperSixerSlotsDealCard extends SuperSixerSlotsDealCardHundred
{
    public static function create($roomType, $wild_step_info, $uid, $betAmount, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $raiseUserWalletList, $slots_user)
    {
        // 完全关掉大盘控
        if (0) {
            $obj = new SuperSixerSlotsDealCardNoControl($roomType, $uid, $betAmount, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $raiseUserWalletList, $slots_user);
        } else {
            $obj = new self($roomType, $uid, $betAmount, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $raiseUserWalletList, $slots_user);
        }
        return $obj;
    }

    //本次spin下注额
    protected $betFee;

    //用户临时数据
    protected array $slots_user;

    public function __construct($roomType, $uid, $betAmount, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $raiseUserWalletList, $slots_user)
    {
        $this->betFee = $betAmount;
        $this->slots_user = $slots_user;
        parent::__construct($roomType, [$uid], [0 => ["amount" => $betAmount]], $roomMold, $historyAllWinAmount, $historyAllBetAmount, $uid, 0 /*, $raiseUserWalletList*/);
    }

    //执行发牌
    protected function dealCardRun()
    {
        parent::dealCardRun();
    }

    // 屏蔽掉大扶小扶
    protected function getIsUpBuff()
    {
        return [];
    }


    //发牌以及设置发牌结果
    //=========================================================
    //执行一次发牌
    protected function dealCardOnce(): array
    {
        $rtn['dealCard'] = $this->getDealCardsList();

        // 预处理发牌结果 非最终结果
        $card = reset($rtn['dealCard']) ?? \common\AiSuperSixerSlots\SuperSixerSlotsEnum::LUCKY_DRAW_NOT_WIN;
        SuperSixerSlotsEnum::handleSpinRes($card, $this->betFee, $this->slots_user);

        // 如果得到了额外获得的奖励，需要影响扶控计算
        $rtn['winAmount'] = empty($this->slots_user['extra_reward']) ? 0 : $this->slots_user['extra_reward'];

        // 计算玩家预计可领取奖励，需要影响扶控计算
        $rtn['assumedExtraWinAmount'] = SuperSixerSlotsEnum::calculateClaimableReward($this->slots_user);

        return $rtn;
    }


    // 根据不同情况走不同的发牌权重
    public function getDealCardWeight()
    {
        //按照发牌权重执行发牌
        if ($this->winPointKillerRes) {
            $dealCardWeight = \common\AiSuperSixerSlots\SuperSixerSlotsEnum::LUCKY_DRAW_WIN_POINT_KILLER_WEIGHT;
        } else {
            $dealCardWeight = \common\AiSuperSixerSlots\SuperSixerSlotsEnum::LUCKY_DRAW_NORMAL_WEIGHT;
        }
        return $dealCardWeight;
    }


    //获取一组结果
    protected function getDealCardsList(): array
    {
        $dealCardWeight = $this->getDealCardWeight();

        $dealCards = [get_rand($dealCardWeight)];
        return $dealCards;
    }


    //计算获胜路线 以及 获胜的倍数
    protected function getWinRaiseIdList($dealCards): array
    {
        return [];
    }


    //根据获胜区域计算赢的钱 
    // jingzhao:这个函数计算出的钱数会影响到是否被系统大盘控
    //          实际给玩家身上加的钱是在server层面重新计算的
    protected function getWinAmount($winRaiseIdList)
    {
        return 0;
    }


    //设置发牌结果
    protected function setDealCardRes($dealCardInfo)
    {
        $this->baseSetDealCardRes($dealCardInfo);
        $this->dealCard = $dealCardInfo['dealCard'];
    }


    //无需操作
    protected function setPool($cardList)
    {
        // TODO: Implement setPool() method.
    }
    //=========================================================


    //只要奖池有亏空 就执行系统强杀
    protected function getSysKillerRes(): bool
    {
        $killerType = $this->getRoomKillConfigType();
        $killerRate = $this->sysKillerConfig['room_type_killer'][$killerType] ?? 0;
        $this->sysKillerRate = $killerRate;
        Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, '系统原始强杀：' . json_encode($killerRate), Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        if ($this->sysKillerWaveRate > 0) {
            return true;
        } else {
            return false;
        }
    }
}
