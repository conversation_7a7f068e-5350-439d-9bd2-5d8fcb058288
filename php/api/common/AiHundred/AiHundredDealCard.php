<?php

namespace common\AiHundred;

use llogic\common\Struct;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;

abstract class AiHundredDealCard
{
    protected $subLotteryKillDesc = ''; // 保底杀、压价未遂、压价，子类也可设置
    protected $humanWinRes = false; // 真人作为一个整体是否赚钱，这里不考虑佣金
    public $humanPrize = 0; // 真人奖金，减掉下注就是真人净赢，这里只有下注(allRaiseAmount)和奖金的概念，没有佣金的概念
    protected $isFreeSpin = false; // GaneshSlots 的免费摇

    //房间类型
    protected $roomType;
    //游戏类型
    protected $cls;
    //本局波动的系统强杀概率
    protected $sysKillerWaveRate = 0;
    //房间强杀类型
    protected $killerType = 0;
    //本局本玩法历史总赢钱金额
    protected $historyAllWinAmount = 0;
    //本局本玩法历史总下注金额
    protected $historyAllBetAmount = 0;
    //牌池
    protected $pool;
    //获胜的下注区域列表
    protected $windRaiseList = [];
    //获取发牌的开始下标
    protected $startKey = 0;
    //发牌数组
    protected $dealCard;
    //下注列表
    protected $raiseList;
    //下注的真人id列表
    protected $betUidList;
    //最多可以输的钱
    protected $maxWinAmount = 0;

    //房间强杀配置
    protected $sysKillerConfig = [];
    //buff用户的账户余额
    protected $balanceAmount = 0;
    //buff玩家所有下注金额
    public $allRaiseAmount = 0;


    //buff相关###################################
    //buff 判定人
    protected $buffPlayerIndex = 0;
    //充值buff 发牌结果随机次数
    protected $rechargeBuffDealCardRandCnt = 1;
    //房间buff类型
    protected $buffType = Common::USER_TAG_NORMAL_BUFF;
    //房间是单人还是多人
    protected $roomMold = Common::ROOM_UNION_TYPE_NORMAL;
    //计算结果是否扶植
    protected $isUpBuff = false;
    //被扶植也要被保底杀
    protected $upAndDown = false;
    //是否破产保护
    protected $isBankruptcyProtect = false;

    #----------------------------
    //系统强杀结果
    protected $sysKillerRes = false;
    //系统强杀概率
    protected $sysKillerRate = 0;
    //复仇强杀结果
    protected $winPointKillerRes = false;
    #----------------------------

    #----------------------------
    //百人场系统今日庄家收入
    protected $sysWinAmount = 0;
    //百人场系统此时庄家的收入目标
    protected $sysTargetAmount = 0;
    #----------------------------
    //最大赔付的不能超过下注钱数的x倍
    protected $maxWinAmountMultiple = 1;
    //金筛子
    protected $goldDiceMaxWinAmount;
    /**
     * @var array
     */
    protected $roomInfo;

    //循环发牌执行最大次数
    protected $dealResMaxCnt = 10;

    public function __construct($roomType, $betUidList, $raiseList, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $buffPlayerIndex, $balanceAmount)
    {
        //房间类型
        $this->roomType = $roomType;

        // 真人下注列表
        // 例如 "raise_list":{"1":{"amount":10000},"2":{"amount":10000},"3":{"amount":10000},"4":{"amount":20000},"5":{"amount":30000}}
        // 键是区域，值里面有个 amount 字段，表示这个区域的真人下注总额
        $this->raiseList = $raiseList;

        //所有下注的真人id
        $this->betUidList = $betUidList;
        //房间多人还是单人
        $this->roomMold = $roomMold;
        //buff判定人
        $this->buffPlayerIndex = $buffPlayerIndex;
        //buff用户的账户余额
        $this->balanceAmount = $balanceAmount;
        //房间信息
        $this->roomInfo = parse_room_type($this->roomType);
        $this->cls = $this->roomInfo['cls'];
        //设置buff状态
        $this->buffType = RedisOpt::getPlayerNowUseBuff($this->buffPlayerIndex, $this->roomInfo['cls']);

        //获取最多可以输的钱
        $jackPortName = Common::getPoolNameByClsAndBase($this->roomInfo['cls'], $this->roomInfo['base']);
        $this->sysKillerConfig = RedisOpt::getPrRoomConfigOne($jackPortName, $this->roomInfo['currency']);

        //计算所有真实玩家下注的钱
        if ($this->raiseList) {
            $allRaiseAmount = array_sum(array_column($this->raiseList, 'amount'));
        } else {
            $allRaiseAmount = 0;
        }
        $this->allRaiseAmount = $allRaiseAmount;

        //获取扶植buff带来的重新随机次数
        $this->rechargeBuffDealCardRandCnt = Common::getUserRandDealCardCntByBufferType($this->buffPlayerIndex, $allRaiseAmount, $this->buffType);

        //大盘控和系统强杀#############################################################################
        //本局本玩法真人玩家历史总赢钱 未抽水之前
        $this->historyAllWinAmount = $historyAllWinAmount;
        //本局本玩法真人玩家历史总下注的钱
        $this->historyAllBetAmount = $historyAllBetAmount;

        //当前房间强杀类型
        $this->killerType = Common::getSysKillerTypeByBuffer($this->buffType);

        if ($this->cls == Common::GAME_TYPE_LUCKY_LOTO) {
            $sysKillerWaveRateInfo = Common::getHundredLuckyLotoSysKillerWaveRate($this->betUidList, $this->roomType, $this->getRoomKillConfigType());
        } else {
            $sysKillerWaveRateInfo = Common::getHundredSysKillerWaveRate($this->betUidList, $this->roomType, $this->getRoomKillConfigType());
        }

        //今日波动的系统强杀概率
        $this->sysKillerWaveRate = $sysKillerWaveRateInfo['waveRate'];
        //Log::console_log(__FUNCTION__, '系统波动强杀概率' . $this->sysKillerWaveRate, Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        //如果前杀 则只能配不好过所有下注的钱 否则纯随机结果
        $this->maxWinAmount = $allRaiseAmount;
        //系统盈利目下超收的钱
        $sysExcessProfit = $sysKillerWaveRateInfo['sysWinAmount'];
        $sysExcessProfit = $sysExcessProfit > 0 ? 0 : -$sysExcessProfit;
        //金骰子最多可以输的钱
        $this->goldDiceMaxWinAmount = $this->maxWinAmount + $sysExcessProfit;
        //Log::console_log(__FUNCTION__, '最大可以输出去的钱 总下注的钱' . $allRaiseAmount, Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        //Log::console_log(__FUNCTION__, '最大可以输出去的钱 系统超赢的钱' . $sysExcessProfit, Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        //Log::console_log(__FUNCTION__, '最大可以输出去的钱 无金筛子总钱' . $this->maxWinAmount, Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        //Log::console_log(__FUNCTION__, '最大可以输出去的钱 有金筛子总钱' . $this->goldDiceMaxWinAmount, Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        ##############################################################################

        #--------------
        //系统盈利目下超收的钱  盈利是负数  亏损是正数
        $this->sysWinAmount = $sysKillerWaveRateInfo['sysWinAmount'];
        //Log::console_log(__FUNCTION__, 'sysWinAmount' . $this->sysWinAmount, Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        //系统此时的盈利目标
        $this->sysTargetAmount = $sysKillerWaveRateInfo['sysTargetAmount'];
        //Log::console_log(__FUNCTION__, 'sysTargetAmount' . $this->sysTargetAmount, Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        #--------------

        #--------------
        //Log::console_log(__FUNCTION__, 'buff类型：' . $this->buffType, Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        //系统强杀结果
        $this->sysKillerRes = $this->getSysKillerRes();
        //复仇杀结果
        $this->winPointKillerRes = $this->getWinPointKillerRes();
        //处理是否执行扶植 以及 覆盖大盘杀结果
        $upBuffRes = $this->getIsUpBuff();
        #--------------

        $marks = [];
        $userCount = count($this->betUidList);
        if ($userCount > 1) {
            $marks[] = 'MULTIPLE_USERS';
        } else if ($userCount == 1) {
            $marks[] = 'SINGLE_USER';
        } else {
            $marks[] = 'NO_USER';
        }

        \lbase\Log::debug('AiHundredDealCard.ctor', [
            'marks' => $marks,
            'userCount' => $userCount,
            'roomType' => $this->roomType,
            'gameTypeStr' => Common::getGameTypeTextList($this->cls),
            'betUidList' => $this->betUidList,
            'roomMold' => $this->roomMold,
            'buffPlayerIndex' => $this->buffPlayerIndex,
            'balanceAmount' => $this->balanceAmount,
            'buffType' => $this->buffType,
            'killerType' => $this->killerType,
            'jackPortName' => $jackPortName,
            'sysKillerConfig' => $this->sysKillerConfig,
            'rechargeBuffDealCardRandCnt' => $this->rechargeBuffDealCardRandCnt,
            'historyAllWinAmount' => $this->historyAllWinAmount,
            'historyAllBetAmount' => $this->historyAllBetAmount,
            'sysKillerWaveRateInfo' => $sysKillerWaveRateInfo,
            // 最大可以输出去的钱 总下注的钱
            'allRaiseAmount' => $allRaiseAmount,
            // 最大可以输出去的钱 系统超赢的钱
            'sysExcessProfit' => $sysExcessProfit,
            // 最大可以输出去的钱 无金筛子总钱
            'maxWinAmount' => $this->maxWinAmount,
            // 最大可以输出去的钱 有金筛子总钱
            'goldDiceMaxWinAmount' => $this->goldDiceMaxWinAmount,
            'sysKillerRes' => $this->sysKillerRes,
            'winPointKillerRes' => $this->winPointKillerRes,
            'isUpBuff' => $this->isUpBuff,
            'upBuffRes' => $upBuffRes,
        ]);

        //执行发牌处理
        $this->dealCardRun();
    }
    //发牌以及设置发牌结果
    //=========================================================
    //执行一次发牌
    abstract protected function dealCardOnce();
    //获取发牌
    abstract protected function getDealCardsList();
    //获取获胜区域
    abstract protected function getWinRaiseIdList($dealCards);
    //获取获胜的钱
    abstract protected function getWinAmount($winRaiseIdList);
    //设置发牌结果
    abstract protected function setDealCardRes($dealCardInfo);
    //设置 缓存牌池
    abstract protected function setPool($cardList);
    //发牌框架
    //=========================================================

    //==========================================================
    //发牌结果验算判定逻辑
    //获取是否执行扶植buff
    protected function getIsUpBuff()
    {
        //处理是否执行扶植 以及 覆盖大盘杀结果
        $upBuffRes = Common::getIsUpBuff($this->winPointKillerRes, $this->sysKillerRes, $this->sysKillerRate, $this->buffType, $this->killerType, $this->buffPlayerIndex, $this->balanceAmount, Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType), $this->roomInfo);
        //破产保护
        if (isset($upBuffRes['isBankruptcyProtect'])) {
            $this->isBankruptcyProtect = $upBuffRes['isBankruptcyProtect'];
        }
        if (isset($upBuffRes['sysKillerRes'])) {
            $this->sysKillerRes = $upBuffRes['sysKillerRes'];
        }
        if (isset($upBuffRes['upAndDown'])) {
            $this->upAndDown = $upBuffRes['upAndDown'];
        }
        if (isset($upBuffRes['isUpBuff'])) {
            $this->isUpBuff = $upBuffRes['isUpBuff'];
        }

        return $upBuffRes;
    }


    //执行发牌
    protected function dealCardRun()
    {
        if ($this->upAndDown) { // 小扶
            $this->dealCardByUpAndDown();
        } elseif ($this->isUpBuff) { // 大扶
            $this->dealCardByRechargeBuff();
        } else {
            $this->dealCardByNorma();
        }
    }


    private function allowBaodisha($assumedExtraWinAmount)
    {
        // 这个 SuperSixerSlots 比较特殊，待领的钱要计入 assumedExtraWinAmount 然后开启保底杀
        if ($this->cls == Struct::GAME_TYPE_SUPER_SIXER_SLOTS) {
            return true;
        }
        // 其他 slots 如果中了 FreeSpin 和 Jackpot 就不走保底杀逻辑
        return $assumedExtraWinAmount == 0;
    }


    //新充buff发牌
    protected function dealCardByUpAndDown()
    {
        $setDealCardInfo = $this->getSetDealCardInfo();
        $this->setDealCardRes($setDealCardInfo);
        $winAmount = $setDealCardInfo['winAmount'];
        $assumedExtraWinAmount = $setDealCardInfo['assumedExtraWinAmount'] ?? 0;
        $assumedWinAmount = $winAmount + $assumedExtraWinAmount;

        $killerConfigType = $this->getRoomKillConfigType();
        $buff_coefficient = $this->sysKillerConfig['buff_coefficient'][$killerConfigType];
        $lotteryRes = false;

        $branch = 'up';

        // FreeGame Jackpot 不走保底杀逻辑
        if ($this->allowBaodisha($assumedExtraWinAmount) && $assumedWinAmount > $this->maxWinAmount) {
            //保底杀中了 执行大盘杀发牌逻辑
            $lotteryRes = Common::getHunderGameIsSysKillerByLotteryResult($this->cls, $assumedWinAmount, $this->sysWinAmount, $this->sysTargetAmount, $this->maxWinAmount, $buff_coefficient, Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));

            if ($lotteryRes) {
                //Log::console_log(__FUNCTION__, '保底杀随中了 ，修改大盘杀为true  执行大盘杀逻辑', Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
                $branch = "lotteryKill";

                $this->sysKillerRes = true;
                $this->subLotteryKillDesc = '保底杀';
                $this->dealCardByNorma('dealCardByUpAndDown.lotteryKill');
                return;
            } else {
                $branch = "lotteryMiss";
            }
        }

        //设置剩余牌池
        $this->setPool($setDealCardInfo['cardList'] ?? []);

        //Log::console_log(__FUNCTION__, '最终赔的钱' . $winAmount, Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));

        \lbase\Log::info('SX.AiHundredDealCard.dealCardByUpAndDown', [
            'branch' => $branch,
            'game' => Common::getGameTypeTextList($this->cls),
            'killer' => Common::getSysKillerType($this->killerType),
            'winAmount' => $winAmount,
            'assumedWinAmount' => $assumedWinAmount,
            'maxWinAmount' => $this->maxWinAmount,
            'betUidList' => $this->betUidList,
            'roomType' => $this->roomType,
            'sysWinAmount' => $this->sysWinAmount,
            'sysTargetAmount' => $this->sysTargetAmount,
            'killerConfigType' => $killerConfigType,
            'buff_coefficient' => $buff_coefficient,
            'result' => $setDealCardInfo,
        ]);
    }
    //新充buff发牌
    protected function dealCardByRechargeBuff()
    {
        $setDealCardInfo = $this->getSetDealCardInfo();
        $winAmount = $setDealCardInfo['winAmount'];
        $assumedExtraWinAmount = $setDealCardInfo['assumedExtraWinAmount'] ?? 0;
        $assumedWinAmount = $winAmount + $assumedExtraWinAmount;

        $killerConfigType = $this->getRoomKillConfigType();
        $buff_coefficient = $this->sysKillerConfig['buff_coefficient'][$killerConfigType];
        $lotteryRes = false;

        $branch = "真人赢钱低于真人下注的" . $this->maxWinAmountMultiple . "倍了";

        //如果赔的钱比较多 最后固定执行5次 看有没有比上次开奖输钱结果少的
        // 一般会走到这个分支里面，找到赔钱比较少的，龙虎单个真人的话，系统赔钱只有一种可能，没有选择空间的
        // 中了 FreeGame Jackpot 就不走保底杀、大赔杀了
        if ($this->allowBaodisha($assumedExtraWinAmount) && $assumedWinAmount >= ($this->allRaiseAmount * $this->maxWinAmountMultiple)) {
            $lotteryRes = Common::getHunderGameIsSysKillerByLotteryResult($this->cls, $assumedWinAmount, $this->sysWinAmount, $this->sysTargetAmount, $this->maxWinAmount, $buff_coefficient, Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
            if ($lotteryRes) {
                $this->subLotteryKillDesc = "压价未遂";
                //Log::console_log(__FUNCTION__, '符合条件执行最后5次检测', Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));

                $branch = "尝试找个系统赔钱但赔钱比较少的";
                for ($j = 0; $j < 5; $j++) {
                    $checkDealCard = $this->dealCardOnce();
                    $assumedExtraWinAmount = $setDealCardInfo['assumedExtraWinAmount'] ?? 0;
                    $thisAssumedWinAmount = $checkDealCard['winAmount'] + $assumedExtraWinAmount;

                    if ($thisAssumedWinAmount >= ($this->maxWinAmount * $this->maxWinAmountMultiple) && $thisAssumedWinAmount < $winAmount) {
                        $this->subLotteryKillDesc = "压价";
                        //Log::console_log(__FUNCTION__, '5次检测得到了新的结果 原来要赔的钱：' . $winAmount . ' 新结果赔的钱：' . $checkDealCard['winAmount'], Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));

                        $branch = "找到一个系统赔钱但赔钱比较少的";
                        $setDealCardInfo = $checkDealCard;
                        $assumedWinAmount = $thisAssumedWinAmount;
                        $winAmount = $checkDealCard['winAmount'];
                    }
                }
            }
        }

        //设置发牌结果
        $this->setDealCardRes($setDealCardInfo);

        //如果赔的钱太多 超过了系统实际收益 则更改为大盘杀
        if ($assumedExtraWinAmount == 0 && $assumedWinAmount > max($this->sysWinAmount, 5000000)) {
            //Log::console_log(__FUNCTION__, '$winAmount' . $winAmount . '$this->sysWinAmount' . $this->sysWinAmount . '超出赔付范围执行大盘杀', Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));

            $branch = "系统赔钱太多改为强杀";
            $this->sysKillerRes = true;
            $this->subLotteryKillDesc = "大赔杀";
            $this->dealCardByNorma('dealCardByRechargeBuff.TOO_MUCH_LOSS');
            return;
        }

        //设置剩余牌池
        $this->setPool($setDealCardInfo['cardList'] ?? []);

        //Log::console_log(__FUNCTION__, '最终赔的钱' . $winAmount, Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));

        \lbase\Log::info('SX.AiHundredDealCard.dealCardByRechargeBuff', [
            'branch' => $branch,
            'game' => Common::getGameTypeTextList($this->cls),
            'killer' => Common::getSysKillerType($this->killerType),
            'winAmount' => $winAmount,
            'assumedWinAmount' => $assumedWinAmount,
            'maxWinAmount' => $this->maxWinAmount,
            'betUidList' => $this->betUidList,
            'roomType' => $this->roomType,
            'sysWinAmount' => $this->sysWinAmount,
            'sysTargetAmount' => $this->sysTargetAmount,
            'killerConfigType' => $killerConfigType,
            'buff_coefficient' => $buff_coefficient,
            'result' => $setDealCardInfo,
        ]);
    }
    //正常发牌
    protected function dealCardByNorma($from = 'dealCardRun')
    {
        $dealResCnt = 0;

        $killerConfigType = $this->getRoomKillConfigType();
        $buff_coefficient = $this->sysKillerConfig['buff_coefficient'][$killerConfigType];
        $lotteryRes = false;

        $branch = '';

        while (1) {
            $dealResCnt++;

            if ($dealResCnt >= $this->dealResMaxCnt) {
                $branch = 'OVERFLOW';

                $dealCardInfo = $this->dealCardOnce();
                $this->setDealCardRes($dealCardInfo);
                //去除牌池里的牌组
                $this->setPool($dealCardInfo['cardList'] ?? []);
                break;
            }

            //不强杀 随机返回结果
            if (!$this->sysKillerRes && !$this->winPointKillerRes) {
                $dealCardInfo = $this->dealCardOnce();
                $this->setDealCardRes($dealCardInfo);
                $assumedExtraWinAmount = $dealCardInfo['assumedExtraWinAmount'] ?? 0;
                $assumedWinAmount = $dealCardInfo['winAmount'] + $assumedExtraWinAmount;

                //如果赔的钱比较多 且随机到更改大盘杀结果  则重新执行开奖
                // FreeGame Jackpot 不走保底杀逻辑
                $lotteryRes = $this->allowBaodisha($assumedExtraWinAmount) && Common::getHunderGameIsSysKillerByLotteryResult($this->cls, $assumedWinAmount, $this->sysWinAmount, $this->sysTargetAmount, $this->maxWinAmount, $buff_coefficient, Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));

                if ($lotteryRes) {
                    //Log::console_log(__FUNCTION__, '赔的太多 修改大盘杀为true', Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));

                    $branch = "lotteryKill";
                    $this->sysKillerRes = true;
                    $this->subLotteryKillDesc = '保底杀';
                    $this->dealCardByNorma('dealCardByNorma.lotteryKill');
                    return;
                } else {
                    $branch = "lotteryMiss";
                    //去除牌池里的牌组
                    $this->setPool($dealCardInfo['cardList'] ?? []);
                }
                break;
            } else {
                //如果强杀 则执行金额判定 返回结果
                $dealCardInfo = $this->dealCardOnce();
                $assumedExtraWinAmount = $dealCardInfo['assumedExtraWinAmount'] ?? 0;
                $assumedWinAmount = $dealCardInfo['winAmount'] + $assumedExtraWinAmount;
                if (($assumedWinAmount <= $this->maxWinAmount || $assumedWinAmount == 0)) {
                    $branch = 'kill';
                    $this->setDealCardRes($dealCardInfo);
                    //去除牌池里的牌组
                    $this->setPool($dealCardInfo['cardList'] ?? []);
                    break;
                }
            }
        }

        //Log::console_log(__FUNCTION__, '最终赔的钱' . $dealCardInfo['winAmount'], Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));

        \lbase\Log::info('SX.AiHundredDealCard.dealCardByNorma', [
            'branch' => $branch,
            'from' => $from,
            'game' => Common::getGameTypeTextList($this->cls),
            'killer' => Common::getSysKillerType($this->killerType),
            'dealResCnt' => $dealResCnt,
            'dealResMaxCnt' => $this->dealResMaxCnt,
            'winAmount' => $dealCardInfo['winAmount'],
            'assumedWinAmount' => $assumedWinAmount,
            'maxWinAmount' => $this->maxWinAmount,
            'betUidList' => $this->betUidList,
            'roomType' => $this->roomType,
            'sysWinAmount' => $this->sysWinAmount,
            'sysTargetAmount' => $this->sysTargetAmount,
            'killerConfigType' => $killerConfigType,
            'buff_coefficient' => $buff_coefficient,
            'result' => $dealCardInfo,
        ]);
    }
    //==========================================================

    //==========================================================
    //大盘杀和复仇杀逻辑
    //非高手和大户房 读取普通房的杀率
    protected function getRoomKillConfigType(): int
    {
        $killerType = $this->killerType;
        if (!in_array($this->killerType, [Common::ROOM_UNION_TYPE_WIN, Common::ROOM_UNION_TYPE_RICH])) {
            $killerType = Common::ROOM_UNION_TYPE_NORMAL;
        }
        return $killerType;
    }

    //获取系统强杀结果
    protected function getSysKillerRes()
    {
        // 根据房间类型获取强杀概率
        // 普通、高手、大户
        $killerType = $this->getRoomKillConfigType();
        // 0.02 0.1 0.15
        $killerRate = $this->sysKillerConfig['room_type_killer'][$killerType] ?? 0;
        $killerRate = bcadd($killerRate, $this->sysKillerWaveRate, 3);

        //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, '系统原始强杀+波动概率：' . json_encode($killerRate), Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));

        // 强杀概率限高 0.333
        $killerRate = min(0.333, $killerRate);

        //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, '系统最终强杀概率：' . json_encode($killerRate), Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));

        $res = (rand() / (getrandmax() + 1)) < $killerRate;
        $this->sysKillerRate = $killerRate;

        //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, '系统最终强杀结果：' . json_encode($res), Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));

        if (!empty($this->betUidList)) {
            \lbase\Log::info("AB-大盘杀结果", [
                'killerType' => $killerType,
                'betUidList' => $this->betUidList,
                'killerRate' => $killerRate,
                'res' => $res,
            ]);
        }

        // 0-不强杀 1-强杀
        return $res;
    }

    //根据下注玩家贫富程度强杀
    protected function getWinPointKillerRes()
    {
        //执行结果随机
        if ($this->historyAllWinAmount == 0 || $this->historyAllBetAmount == 0) {
            return false;
        }
        //玩家有效下注=max（本局下注, 玩家在该玩法内历史总赢钱-玩家在该玩法内历史总下注+本局下注）
        $valid_bet = max($this->maxWinAmount, $this->historyAllWinAmount - $this->historyAllBetAmount + $this->maxWinAmount);
        //有效下注必须大于 复仇杀配置的最低线才执行
        //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, '系统贫富强杀总赢钱：有效下注：' . $valid_bet, Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        if ($valid_bet <= $this->sysKillerConfig['win_score_killer_base'] * 1000) {
            //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, '系统贫富强杀总赢钱：未达到劫富线 不执行', Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
            return false;
        }
        //新复仇杀概率=当时的大盘杀概率+min（1，玩家本局下注/总赢钱数/复仇杀容忍系数）*复仇杀浮动概率
        if ($this->historyAllBetAmount >= $this->historyAllWinAmount) {
            $scorePoint = $this->sysKillerRate;
        } else {
            $scorePoint = $this->sysKillerRate + min(1, ($this->maxWinAmount / ($this->historyAllWinAmount - $this->historyAllBetAmount)) / $this->sysKillerConfig['tolerance_factor']) * $this->sysKillerConfig['float_rate'];
        }
        //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, '系统贫富强杀赢钱率：' . json_encode($scorePoint), Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));

        $res = (rand() / (getrandmax() + 1)) < $scorePoint;
        //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, '系统贫富强杀结果：' . json_encode($res), Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        return $res;
    }
    //==========================================================

    //==========================================================
    //公共读取数据方法
    //获取发牌开始的位置
    public function getDealCardsStartKey(): int
    {
        return $this->startKey;
    }
    //获取当前剩余牌池
    public function getCardPool()
    {
        return $this->pool;
    }
    //下注赢的区域
    public function getWindRaiseList(): array
    {
        return $this->windRaiseList;
    }
    //获取发到的牌
    public function getDealCards()
    {
        return $this->dealCard;
    }
    //==========================================================

    /**
     * @return array
     */
    protected function getSetDealCardInfo(): array
    {
        $i = $this->rechargeBuffDealCardRandCnt;
        $branch = "真人赢钱不低于真人下注的" . $this->maxWinAmountMultiple . "倍";

        // 首次随机开奖结果
        $firstDealCard = $this->dealCardOnce();
        $assumedExtraWinAmount = $firstDealCard['assumedExtraWinAmount'] ?? 0;

        // 检测第一次发牌结果 如果赢的钱>=下注的钱  则使用该开奖结果 终止重随
        if ($firstDealCard['winAmount'] + $assumedExtraWinAmount >= ($this->allRaiseAmount * $this->maxWinAmountMultiple)) {
            $setDealCardInfo = $firstDealCard;
        } else {
            // 执行所有重随次数   挑选 能让用户赢钱的 且 小于等于4倍下注额的 结果
            $tmpWinAmount = $this->allRaiseAmount * 4;
            $newDealCard = [];
            for ($cnt = 0; $cnt < $i - 1; $cnt++) {
                $tmpDealCard = $this->dealCardOnce();
                $assumedExtraWinAmount = $tmpDealCard['assumedExtraWinAmount'] ?? 0;
                $assumedWinAmount = $tmpDealCard['winAmount'] + $assumedExtraWinAmount;

                if ($assumedWinAmount >= ($this->allRaiseAmount * $this->maxWinAmountMultiple) && $assumedWinAmount < $tmpWinAmount) {
                    $tmpWinAmount = $assumedWinAmount;
                    $newDealCard = $tmpDealCard;
                }
            }
            // 设置开奖结果  如果没有符合条件的且赢钱少的结果 就使用第一次的结果

            if (empty($newDealCard)) {
                $branch = "真人赢钱低于真人下注的" . $this->maxWinAmountMultiple . "倍了";
                $setDealCardInfo = $firstDealCard;
            } else {
                $branch = "真人赢钱不低于真人下注的" . $this->maxWinAmountMultiple . "倍不高于4倍";
                $setDealCardInfo = $newDealCard;
            }
        }

        \lbase\Log::info('SX.AiHundredDealCard.getSetDealCardInfo.制作扶植发牌', [
            'game' => Common::getGameTypeTextList($this->cls),
            'killer' => Common::getSysKillerType($this->killerType),
            'branch' => $branch,
            'result' => $setDealCardInfo,
        ]);

        return $setDealCardInfo;
    }

    // 截获发牌结果，标记流程分支用的
    protected function baseSetDealCardRes($dealCardInfo)
    {
        $this->humanWinRes = $dealCardInfo['winAmount'] > $this->allRaiseAmount;
        $this->humanPrize = $dealCardInfo['winAmount'];

        \lbase\Log::info('AiHundredDealCard-发牌真人下注奖金', [
            'game' => Common::getGameTypeTextList($this->cls),
            'betUidList' => $this->betUidList,
            'allRaiseAmount' => $this->allRaiseAmount,
            'humanPrize' => $this->humanPrize,
            'humanWinRes' => $this->humanWinRes,
        ]);
    }

    public function calcBranchName(): string
    {
        // 这里不关注具体人数，只关注是否有真人
        if (!$this->betUidList) {
            return '';
        }

        // 这次发牌真人整体是赚钱还是亏钱
        $humanWinDesc = $this->humanWinRes ? '赚钱' : '亏钱';

        if ($this->isFreeSpin) {
            return '免费摇' . $humanWinDesc;
        }

        // 这里关注用户分类 buffType
        $buffTypeName = Common::buffTypeToPretty($this->buffType);

        // 用户的增益效果
        if ($this->rechargeBuffDealCardRandCnt > 1) {
            //$buffTypeName .= "双摇";
        }

        // 流程分支
        $way = '';
        if ($this->upAndDown) { // 小扶
            $way .= "小扶";
            $way .= $this->subLotteryKillDesc;
        } elseif ($this->isUpBuff) { // 大扶
            $way .= "大扶";
            $way .= $this->subLotteryKillDesc;
        } else {
            if ($this->subLotteryKillDesc) {
                $way .= $this->subLotteryKillDesc;
            } else if ($this->winPointKillerRes) {
                $way .= "复仇杀";
            } else if ($this->sysKillerRes) {
                $way .= "大盘杀";
            } else {
                $way .= "正常";
            }
        }

        return $buffTypeName . $way . $humanWinDesc;
    }

    public function SendMetrics()
    {
        // 没有真人参与的不发送
        if (empty($this->betUidList)) {
            return;
        }
        // 不开奖的发牌不发送，比如 ABClassic 可能有两次发牌第一次发牌不开奖
        if (empty($this->windRaiseList)) {
            return;
        }

        Struct::room_type_to_game_name_base_rupee_type($this->roomType, $game_name, $base_rupee);

        $branchName = $this->calcBranchName();
        $stake_rupee = $this->allRaiseAmount / 1000;
        $prize_rupee = $this->humanPrize / 1000;

        \lbase\metric\PushAPIDeal::yyl_api_deal_total($game_name, $base_rupee, $branchName);
        \lbase\metric\PushAPIDeal::yyl_api_deal_stake_total($game_name, $base_rupee, $branchName, $stake_rupee);
        \lbase\metric\PushAPIDeal::yyl_api_deal_prize_total($game_name, $base_rupee, $branchName, $prize_rupee);

        \lbase\Log::info('AB-发牌指标', [
            'game' => Common::getGameTypeTextList($this->cls),
            'betUidList' => $this->betUidList,
            'windRaiseList' => $this->windRaiseList,
            'branchName' => $branchName,
            'stake_rupee' => $stake_rupee,
            'prize_rupee' => $prize_rupee,
        ]);
    }

}
