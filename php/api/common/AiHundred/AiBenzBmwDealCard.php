<?php
/**
 * @todo 奔驰宝马玩法发牌接口
 */


namespace common\AiHundred;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;

class AiBenzBmwDealCard extends AiHundredDealCard
{
    //开牌权重
    protected $winOptionWeight = [];

    public function __construct($roomType, $betUidList, $raiseList, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $buffPlayerIndex, $balanceAmount)
    {
        //开奖区域权重
        $this->winOptionWeight = BENZ_BMW_WIN_OPTION_WEIGHT;

        parent::__construct($roomType, $betUidList, $raiseList, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $buffPlayerIndex, $balanceAmount);
    }
    //随机发牌
    protected function dealCardOnce(): array
    {
        $dealCardInfo = $this->getDealCardsList();
        $rtn['windRaiseList'] = $dealCardInfo['windRaiseList'];
        $rtn['dealCard'] = $dealCardInfo['dealCard'];
        $rtn['winAmount'] = $this->getWinAmount($rtn['windRaiseList']);
        return $rtn;
    }
    //获取一副牌
    protected function getDealCardsList(): array
    {
        $rtn['windRaiseList'] = $this->getWinRaiseIdList([]);
        $dealCardList = BENZ_BMW_OPTION[$rtn['windRaiseList'][0]]['key_list'];
        shuffle($dealCardList);
        $rtn['dealCard'] = [array_pop($dealCardList)];
        return $rtn;
    }
    //获取获胜区域
    protected function getWinRaiseIdList($dealCards): array
    {
        return [get_rand($this->winOptionWeight)];
    }
    //根据获胜区域计算赢的钱
    protected function getWinAmount($winRaiseIdList)
    {
        $winAmount = 0;
        foreach ($winRaiseIdList as $raiseId) {
            if (isset($this->raiseList[$raiseId])) {
                $winAmount += $this->raiseList[$raiseId]['amount'] * BENZ_BMW_OPTION[$raiseId]['x'];
            }
        }
        return $winAmount;
    }
    //设置发牌结果
    protected function setDealCardRes($dealCardInfo)
    {
        $this->baseSetDealCardRes($dealCardInfo);
        $this->dealCard = $dealCardInfo['dealCard'];
        $this->windRaiseList = $dealCardInfo['windRaiseList'];
    }
    //无需处理
    protected function setPool($cardList)
    {
        // TODO: Implement setPool() method.
    }
}
