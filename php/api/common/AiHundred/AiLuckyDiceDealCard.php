<?php
/**
 * @todo 幸运骰子 6面玩法
 */

namespace common\AiHundred;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;

class AiLuckyDiceDealCard extends AiHundredDealCard
{

    public function __construct($roomType, $betUidList, $raiseList, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $buffPlayerIndex, $balanceAmount)
    {
        //初始化骰子
        $this->pool = array_keys(LUCKY_DICE);

        parent::__construct($roomType, $betUidList, $raiseList, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $buffPlayerIndex, $balanceAmount);
    }

    //随机发牌
    //发牌以及设置发牌结果
    //=========================================================
    protected function dealCardOnce(): array
    {
        //首次随机开奖结果
        $rtn['dealCard'] = $this->getDealCardsList();
        $rtn['windRaiseList'] = $this->getWinRaiseIdList($rtn['dealCard']);
        $rtn['winAmount'] = $this->getWinAmount($rtn['windRaiseList']);
        return $rtn;
    }
    //获取一组结果
    protected function getDealCardsList(): array
    {
        $dealCardsList = [];
        for ($i = 1; $i <= LUCKY_DICE_CNT; $i++) {
            $dealCardsList[] = random_int(min($this->pool), max($this->pool));
        }
        return $dealCardsList;
    }
    //根据获胜数字 计算获胜区域赢的钱
    protected function getWinRaiseIdList($dealCards): array
    {
        //计算元素个数
        $winRaiseIdList = [];
        $list = array_count_values($dealCards);
        foreach ($list as $k => $v) {
            if ($v > 1) {
                $winRaiseIdList[$k] = $v;
            }
        }
        return $winRaiseIdList;
    }
    //根据获胜区域计算赢的钱
    protected function getWinAmount($winRaiseIdList)
    {
        if (!$winRaiseIdList) {
            return 0;
        }
        $winAmount = 0;
        foreach ($winRaiseIdList as $raiseId => $cnt) {
            if (isset($this->raiseList[$raiseId])) {
                $winAmount += $this->raiseList[$raiseId]['amount'] * LUCKY_DICE_OPTION[$raiseId][$cnt . 'x'];
            }
        }
        return $winAmount;
    }
    //设置发牌结果
    protected function setDealCardRes($dealCardInfo)
    {
        $this->baseSetDealCardRes($dealCardInfo);
        $this->dealCard = $dealCardInfo['dealCard'];
        $this->windRaiseList = $dealCardInfo['windRaiseList'];
    }
    //无需操作
    protected function setPool($cardList)
    {
        // TODO: Implement setPool() method.
    }
    //=========================================================

}
