<?php
/**
 * @todo 7up7down
 */


namespace common\AiHundred;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;

class Ai7up7downDealCard extends AiHundredDealCard
{
    //金骰子最多可以输的钱
    protected $goldDiceMaxWinAmount = 0;
    //金筛子倍数
    protected $goldDoceX = 1;

    public function __construct($roomType, $betUidList, $raiseList, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $buffPlayerIndex, $balanceAmount)
    {
        //初始化骰子
        $this->pool = array_keys(D_7UP_7DOWN_IDCE);

        parent::__construct($roomType, $betUidList, $raiseList, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $buffPlayerIndex, $balanceAmount);
    }

    //发牌以及设置发牌结果
    //=========================================================
    //随机发牌 $dealGoldDiceX 是否发金筛子
    protected function dealCardOnce($dealGoldDiceX = true): array
    {
        $rtn['dealCard'] = $this->getDealCardsList();
        $winRaiseIdInfo = $this->getWinRaiseIdList($rtn['dealCard']);
        $rtn['windRaiseList'] = $winRaiseIdInfo['winRaiseIdList'];
        $rtn['diceSumScore'] = $winRaiseIdInfo['diceSumScore'];
        $rtn['winAmount'] = $this->getWinAmount($rtn['windRaiseList']);
        //获取金骰子倍数
        if ($dealGoldDiceX) {
            $rtn['goldDiceX'] = $this->setGoldDiceX($rtn['winAmount'], $rtn['diceSumScore']);
        } else {
            $rtn['goldDiceX'] = 1;
        }
        //赢的区域为所有选项
        if ($rtn['goldDiceX'] > 1) {
            $rtn['windRaiseList'] = array_keys(D_7UP_7DOWN_OPTION);
            $rtn['winAmount'] = $this->maxWinAmount * $rtn['diceSumScore'];
        }
        return $rtn;
    }
    //获取一组结果
    protected function getDealCardsList(): array
    {
        $dealCardsList = [];
        for ($i = 1; $i <= D_7UP_7DOWN_IDCE_CNT; $i++) {
            $dealCardsList[] = random_int(min($this->pool), max($this->pool));
        }
        return $dealCardsList;
    }
    //根据获胜数字 计算获胜区域赢的钱
    protected function getWinRaiseIdList($dealCards): array
    {
        $diceSumScore = 0;
        //计算点数的和
        $colorList = [];
        foreach ($dealCards as $val) {
            $colorList[D_7UP_7DOWN_IDCE[$val]] = 1;
            $diceSumScore += $val;
        }
        //计算元素个数
        $winRaiseIdList = [];
        foreach (D_7UP_7DOWN_OPTION as $k => $v) {
            //点数和不对
            if (!in_array($diceSumScore, $v['sum'])) {
                continue;
            }
            //骰子花色不对
            if (isset($v['color'])) {
                $colorCnt = count($colorList);
                if ($colorCnt != 1 || !isset($colorList[$v['color']])) {
                    continue;
                }
            }
            $winRaiseIdList[] = $k;
        }
        $rtn['diceSumScore'] = $diceSumScore;
        $rtn['winRaiseIdList'] = $winRaiseIdList;
        return $rtn;
    }
    //根据获胜区域计算赢的钱
    protected function getWinAmount($winRaiseIdList)
    {
        if (!$winRaiseIdList) {
            return 0;
        }
        $winAmount = 0;
        foreach ($winRaiseIdList as $raiseId) {
            if (isset($this->raiseList[$raiseId])) {
                $winAmount += $this->raiseList[$raiseId]['amount'] * D_7UP_7DOWN_OPTION[$raiseId]['x'];
            }
        }
        return $winAmount;
    }
    //获取金骰子加倍 倍数
    protected function setGoldDiceX($winAmount, $diceSumScore): int
    {
        Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, '金骰子$winAmount：' . json_encode($winAmount), Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, '金骰子$this->goldDiceMaxWinAmount：' . json_encode($this->goldDiceMaxWinAmount), Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        if (in_array($this->killerType, [Common::ROOM_UNION_TYPE_NORMAL, Common::ROOM_UNION_TYPE_BUFF])) {
            $killerType = Common::ROOM_UNION_TYPE_NORMAL;
        } else {
            $killerType = $this->killerType;
        }
        if (!isset(GOLD_DICE[$killerType])) {
            return 1;
        }
        $goldDiceConfig = RedisOpt::getGoldDiceConfig() ?? GOLD_DICE;
        $goldDiceConfig = $goldDiceConfig[$killerType];
        //随机是否出现金骰子翻倍
        $res = (rand() / (getrandmax() + 1)) < $goldDiceConfig['rate'];
        Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, '金骰子概率：' . json_encode($goldDiceConfig['rate']), Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, '金骰子随机结果：' . json_encode($res), Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        if (!$res) {
            return 1;
        }
        Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, '金骰子倍数：' . json_encode($diceSumScore), Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        //金筛子规则修改  随机出来的点数是多少 就按照多少倍赔
        if (($this->maxWinAmount * $diceSumScore) <= $this->goldDiceMaxWinAmount) {
            return $diceSumScore;
        } else {
            return 1;
        }
    }
    //设置发牌结果
    protected function setDealCardRes($dealCardInfo)
    {
        $this->baseSetDealCardRes($dealCardInfo);
        $this->dealCard = $dealCardInfo['dealCard'];
        $this->windRaiseList = $dealCardInfo['windRaiseList'];
        $this->goldDoceX = $dealCardInfo['goldDiceX'];
    }

    //无需操作
    protected function setPool($cardList)
    {
        // TODO: Implement setPool() method.
    }
    //=============================================================

    //获取发到的牌 需要最后处理牌 重写父法
    public function getDealCards()
    {
        foreach ($this->dealCard as $k => $v) {
            $this->dealCard[$k] = $v - 1;
        }
        return $this->dealCard;
    }

    //获取金筛子倍数
    public function getGoldDiceX(): int
    {
        return $this->goldDoceX;
    }

}
