<?php
/**
 * @todo 龙虎玩法发牌接口
 */

namespace common\AiHundred;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;

class AiLongHuDealCard extends AiHundredDealCard
{

    //房间id
    private $roomId;
    //龙牌
    private $longCard = 0;
    //虎牌
    private $huCard = 0;

    public function __construct($roomId, $roomType, $betUidList, $raiseList, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $buffPlayerIndex, $balanceAmount)
    {
        //房间id
        $this->roomId = $roomId;
        //初始化牌池 不带王
        $this->pool = $this->getPool();
        //每次发牌之前剔除一张牌
        array_pop($this->pool);
        shuffle($this->pool);

        parent::__construct($roomType, $betUidList, $raiseList, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $buffPlayerIndex, $balanceAmount);
    }
    //获取牌池如果牌池没有牌了 重新初始化牌池
    protected function getPool()
    {
        //获取牌池
        return RedisOpt::getLongHuPool($this->roomId);
    }

    //减掉牌池并存储
    protected function setPool($cardList)
    {
        foreach ($cardList as $key => $val) {
            unset($this->pool[$key]);
        }
        RedisOpt::setLongHuPool($this->pool, $this->roomId);
    }


    //发牌以及设置发牌结果
    //=========================================================
    //龙随机发牌
    protected function dealCardOnce(): array
    {
        //首次随机开奖结果
        $rtn['cardList'] = $this->getDealCardsList();
        $rtn['windRaiseList'] = $this->getWinRaiseIdList($rtn['cardList']);
        $rtn['dealCard'][0] = $this->longCard;
        $rtn['dealCard'][1] = $this->huCard;
        $rtn['winAmount'] = $this->getWinAmount($rtn['windRaiseList']);
        return $rtn;
    }
    //获取一组结果
    protected function getDealCardsList()
    {
        shuffle($this->pool);
        $dealCardsList[0] = $this->pool[0];
        $dealCardsList[1] = $this->pool[1];
        Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, '龙虎发牌 牌池：' . json_encode($this->pool), Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, '龙虎发牌 发到的牌：' . json_encode($dealCardsList), Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        return $dealCardsList;
    }
    //根据获胜数字 计算获胜区域赢的钱
    protected function getWinRaiseIdList($dealCards): array
    {
        $this->longCard = array_pop($dealCards);
        $this->huCard = array_pop($dealCards);
        $long = get_card_info($this->longCard)['num'];
        $hu = get_card_info($this->huCard)['num'];
        if ($long > $hu) {
            $winRaiseIdList[] = LONG_WIN;
        } elseif ($hu > $long) {
            $winRaiseIdList[] = HU_WIN;
        } else {
            $winRaiseIdList[] = LONG_HU_HE;
        }
        return $winRaiseIdList;
    }
    //根据获胜区域计算赢的钱
    protected function getWinAmount($winRaiseIdList)
    {
        $winAmount = 0;
        foreach ($winRaiseIdList as $raiseId) {
            if ($raiseId == LONG_HU_HE) {
                //如果是和局  龙和虎的区域各赔0.5
                $winAmount += (isset($this->raiseList[LONG_WIN]) ? $this->raiseList[LONG_WIN]['amount'] : 0) * 0.5;
                $winAmount += (isset($this->raiseList[HU_WIN]) ? $this->raiseList[HU_WIN]['amount'] : 0) * 0.5;
            }
            if (isset($this->raiseList[$raiseId])) {
                $winAmount += $this->raiseList[$raiseId]['amount'] * LONG_HU_OPTION[$raiseId]['x'];
            }
        }
        return $winAmount;
    }

    //设置发牌结果
    protected function setDealCardRes($dealCardInfo)
    {
        $this->baseSetDealCardRes($dealCardInfo);
        $this->dealCard[0] = $dealCardInfo['dealCard'][0];
        $this->dealCard[1] = $dealCardInfo['dealCard'][1];
        $this->windRaiseList = $dealCardInfo['windRaiseList'];

    }
    //=========================================================
}
