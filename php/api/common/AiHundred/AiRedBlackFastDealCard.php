<?php
/**
 * @todo 红黑玩法发牌接口
 */

namespace common\AiHundred;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;

class AiRedBlackFastDealCard extends AiHundredDealCard
{

    public function __construct($roomType, $betUidList, $raiseList, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $buffPlayerIndex, $balanceAmount)
    {
        //初始化牌池 不带王
        $pool = init_card_pool(1, false);
        //放入大小王
        $pool[] = G_CARD;
        $pool[] = G_SMALL_CARD;
        $this->pool = $pool;
        shuffle($this->pool);

        parent::__construct($roomType, $betUidList, $raiseList, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $buffPlayerIndex, $balanceAmount);
    }
    //随机发牌
    //发牌以及设置发牌结果
    //=========================================================
    protected function dealCardOnce(): array
    {
        $rtn['dealCard'] = $this->getDealCardsList();
        $rtn['windRaiseList'] = $this->getWinRaiseIdList($rtn['dealCard']);
        $rtn['winAmount'] = $this->getWinAmount($rtn['windRaiseList']);
        return $rtn;
    }
    protected function getDealCardsList()
    {
        $pool = $this->pool;
        shuffle($pool);
        return array_pop($pool);
    }
    //根据获胜数字 计算获胜区域赢的钱
    protected function getWinRaiseIdList($dealCards): array
    {
        $winRaiseIdList = [];
        $isJoker = false;
        $color = '';
        $num = '';
        //判定是否是鬼牌
        if ($dealCards == G_CARD || $dealCards == G_SMALL_CARD) {
            $isJoker = true;
        } else {
            //获取牌组的花色和点数
            $cardInfo = get_card_info($dealCards);
            $color = $cardInfo['text_type'];
            $num = $cardInfo['num'];
        }
        //挑选下注区域
        foreach (RED_BLACK_FAST_OPTION as $raiseId => $raiseRule) {
            if (isset($raiseRule['isJoker']) && $isJoker) {
                $winRaiseIdList[] = $raiseId;
            }
            if (isset($raiseRule['color']) && in_array($color, $raiseRule['color'])) {
                $winRaiseIdList[] = $raiseId;
            }
            if (isset($raiseRule['min']) && $num >= $raiseRule['min'] && $num <= $raiseRule['max']) {
                $winRaiseIdList[] = $raiseId;
            }
        }
        return $winRaiseIdList;
    }
    //根据获胜区域计算赢的钱
    protected function getWinAmount($winRaiseIdList)
    {
        $winAmount = 0;
        foreach ($winRaiseIdList as $raiseId) {
            if (isset($this->raiseList[$raiseId])) {
                $winAmount += $this->raiseList[$raiseId]['amount'] * RED_BLACK_FAST_OPTION[$raiseId]['x'];
            }
        }
        return $winAmount;
    }

    //设置发牌结果
    protected function setDealCardRes($dealCardInfo)
    {
        $this->baseSetDealCardRes($dealCardInfo);
        $this->dealCard = $dealCardInfo['dealCard'];
        $this->windRaiseList = $dealCardInfo['windRaiseList'];
    }
    //无需处理
    protected function setPool($cardList)
    {
        // TODO: Implement setPool() method.
    }
    //=========================================================



}
