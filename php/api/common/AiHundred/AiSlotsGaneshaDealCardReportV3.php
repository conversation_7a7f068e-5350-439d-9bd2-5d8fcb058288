<?php
/**
 * @todo slots数据模拟 
 * 要求不被任何大盘操控影响
 * 此脚本只跑freegame
 */
namespace common\AiHundred;

use common\Common;

class AiSlotsGaneshaDealCardReportV3 extends AiSlotsGaneshaDealCard
{
    // php hp php/api/common/AiHundred/AiSlotsGaneshaDealCardReportV3.php
    public static function Main()
    {
        for ($i = 0; $i < 100000; $i++) {
            $rows[] = self::randRow();
        }
        self::export($rows);

        //return $rows;
    }

    private static function newObj($isFree)
    {
        $roomType = "200_1_1_1000";
        $uid = 123456;
        $betAmount = 25 * 1000;
        $roomMold = Common::ROOM_UNION_TYPE_NORMAL;
        $historyAllWinAmount = 0;
        $historyAllBetAmount = 0;
        $raiseUserWalletList = [];
        $obj = new AiSlotsGaneshaDealCardReportV3($roomType, $isFree, $uid, $betAmount, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $raiseUserWalletList);
        return $obj;
    }

    private static function randRow()
    {
        $obj = self::newObj(true);
        $rows = [];
        self::ProcessInitRows($rows);
        $obj->rowCollect($rows);
        return $rows;
    }

    public function __construct($roomType, $isFree, $uid, $betAmount, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $raiseUserWalletList)
    {
        //每条线的下注钱数
        $this->baseLineBetAmount = $betAmount > 0 ? bcdiv($betAmount, count(SLOTS_WIN_WAY), 3) : 0;
        $this->isFree = $isFree;

        $this->allRaiseAmount = $betAmount;
    }

    private function rowCollect(&$rows)
    {
        // 真实随机不执行任何干预
        $this->isFreeSpin = $this->isFree;
        $dealCardInfo = $this->dealCardOnce();

        $dealCard = $dealCardInfo['dealCard'];
        $windRaiseList = $dealCardInfo['windRaiseList'];
        $scattrInfo = $dealCardInfo['scattrInfo'];

        // 总赔率
        $rows['total_multiple'] = !empty($windRaiseList) ? array_sum(array_column($windRaiseList, 'multiple')) : 0;

        // 是否中线
        $rows['is_win_line_hit'] = !empty($windRaiseList) ? 1 : 0;

        // 中线数量
        $rows['num_win_lines'] = count($windRaiseList);

        // 计算中奖线卡牌种类数量(比如 3 * 10  或者3 * tiger .......)
        $wild_arr = [];
        $this->ProcessWinRaiseIdList($windRaiseList, $wild_arr, $rows);

        // 生效的wild数量
        $wild_cnt = 0;
        // 生效的wild × 2数量
        $double_wild_cnt = 0;
        $this->ProcessWildCnt($wild_arr, $wild_cnt, $double_wild_cnt);
        $rows['wild_cnt'] = $wild_cnt;
        $rows['wild_×2_cnt'] = $double_wild_cnt;

        // // 发到的牌
        // $rows['dealcard'] = $dealCard;
        // $rows['windRaiseList'] = $windRaiseList;
        // $rows['scattrInfo'] = $scattrInfo;
        // time();
    }

    private static function ProcessInitRows(&$rows)
    {
        // 初始化统计项
        $rows['total_multiple'] = 0;
        $rows['is_win_line_hit'] = 0;
        $rows['num_win_lines'] = 0;
        $rows['3 × 10'] = 0;
        $rows['4 × 10'] = 0;
        $rows['5 × 10'] = 0;
        $rows['3 × J'] = 0;
        $rows['4 × J'] = 0;
        $rows['5 × J'] = 0;
        $rows['3 × Q'] = 0;
        $rows['4 × Q'] = 0;
        $rows['5 × Q'] = 0;
        $rows['3 × K'] = 0;
        $rows['4 × K'] = 0;
        $rows['5 × K'] = 0;
        $rows['3 × A'] = 0;
        $rows['4 × A'] = 0;
        $rows['5 × A'] = 0;
        $rows['3 × A'] = 0;
        $rows['4 × A'] = 0;
        $rows['5 × A'] = 0;
        $rows['3 × Snake'] = 0;
        $rows['4 × Snake'] = 0;
        $rows['5 × Snake'] = 0;
        $rows['3 × Peacock'] = 0;
        $rows['4 × Peacock'] = 0;
        $rows['5 × Peacock'] = 0;
        $rows['3 × Monkey'] = 0;
        $rows['4 × Monkey'] = 0;
        $rows['5 × Monkey'] = 0;
        $rows['3 × Tiger'] = 0;
        $rows['4 × Tiger'] = 0;
        $rows['5 × Tiger'] = 0;
        $rows['3 × Scatter'] = 0;
        $rows['4 × Scatter'] = 0;
        $rows['5 × Scatter'] = 0;
        $rows['wild_cnt'] = 0;
        $rows['wild_×2_cnt'] = 0;
    }

    private function ProcessWinRaiseIdList($windRaiseList, &$wild_arr, &$rows)
    {
        foreach ($windRaiseList as $key => $value) {
            // 统计所有中奖卡牌列表
            $count = intval(count($value['tmpWinRaiseIdList']) ?? 0);
            $first_card = $this->ProcessCardName(intval($value['firstCard'] ?? 0));
            $rows["{$count} × {$first_card}"] = ($rows["{$count} × {$first_card}"] ?? 0) + 1;

            // 提取所有 wild_arr 计算生效的wild数量
            if (!empty($value['wild_arr'])) {
                foreach ($value['wild_arr'] as $wild) {
                    $wild_arr[] = $wild;
                }
            }
        }
    }

    private function ProcessWildCnt($wild_arr, &$wild_cnt, &$double_wild_cnt)
    {
        // 使用 array_map 提取 card 值，并去重
        $uniqueArray = array_unique(array_map(function ($item) {
            return $item['card'] . '_' . $item['x'] . '_' . $item['y'];
        }, $wild_arr));

        // 统计去重后 card 为 11 || 12 的数量
        foreach ($uniqueArray as $item) {
            // 解析去重后的数组的元素（例如 '11_0_2'）
            list($card, $x, $y) = explode('_', $item);
            if ($card == 11) {
                $wild_cnt++;
            } else if ($card == 12) {
                $double_wild_cnt++;
            }
        }
    }

    private function ProcessCardName($card): string
    {
        /*
            1 ---> 10
            2 ---> J
            3 ---> Q
            4 ---> K
            5 ---> A
            6 ---> 蛇 Snake
            7 ---> 孔雀 Peacock
            8 ---> 猴 Monkey
            9 ---> 老虎 Tiger
            10 ---> SCATTER
            11 ---> WILD
            12 ---> WILD
        */
        switch ($card) {
            case 1:
                return "10";
            case 2:
                return "J";
            case 3:
                return "Q";
            case 4:
                return "K";
            case 5:
                return "A";
            case 6:
                return "Snake";
            case 7:
                return "Peacock";
            case 8:
                return "Monkey";
            case 9:
                return "Tiger";
            case 10:
                return "SCATTER";
            case 11:
                return "WILD";
            case 12:
                return "WILD";
            default:
                return "0";
        }
    }

    private static function export($rows)
    {
        // 与php目录同级的tmp目录
        $slots_simulation = __DIR__ . '/../../../../tmp/simulation/slots_simulation_free.csv';
        @mkdir(dirname($slots_simulation), 0777, true);

        // 删除文件（如果存在）
        if (file_exists($slots_simulation)) {
            unlink($slots_simulation);
        }

        // 打开文件进行写入
        $file = fopen($slots_simulation, 'w');

        // 设定标题行
        $title = '总赔率,是否中线,中线数量,3×10,4×10,5×10,3×J,4×J,5×J,3×Q,4×Q,5×Q,3×K,4×K,5×K,3×A,4×A,5×A,3×Snake,4×Snake,5×Snake,3×Peacock,4×Peacock,5×Peacock,3×Monkey,4×Monkey,5×Monkey,3×Tiger,4×Tiger,5×Tiger,3×Scatter,4×Scatter,5×Scatter,生效Wild数量,生效Wild×2数量';
        $row = explode(',', $title);
        array_unshift($rows, $row);

        // 写入数据行，并在每行末尾添加换行符
        foreach ($rows as $key => $row) {
            fputcsv($file, $row);
        }

        // 关闭文件
        fclose($file);
    }
}
