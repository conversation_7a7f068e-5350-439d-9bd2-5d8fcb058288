<?php
/**
 * @todo AB玩法发牌接口
 *
 * 玩法介绍：
 *   1、游戏随机得到一个点数 展示给用户
 *   2、随机一副不带王的牌  找到第一个跟亮牌点数相同的牌截止   此时压中牌的位置的区域 以及  压中牌的位置的单双 的获胜  不同区域赔率不同
 *
 *                              单（1.9X）       双（2X）
 *
 *           1-5【3.5X】      6-10【4.5X】      11-15【5.5X】     16-25【4.5X】
 *
 *           26-30【15X】     31-35【25X】      36-40【50X】      40-49【120X】
 *
 *      ###以上10个下注区域  不同区域有不同的赔率
 *
 */


namespace common\AiHundred;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;

class AiABDealCard extends AiHundredDealCard
{

    //癞子牌
    protected $xCard;
    //所有癞子列表
    protected $xCardList = [];
    //获胜位置
    protected $winNum = 0;

    public function __construct($xCard, $roomType, $betUidList, $raiseList, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $buffPlayerIndex, $balanceAmount)
    {
        //游戏随机得到的癞子牌
        $this->xCard = $xCard;
        //所有癞子列表
        $xCardList = get_all_color_cards_by_one_card($this->xCard);
        $this->xCardList = array_diff($xCardList, [$this->xCard]);
        //初始化牌池 不带王
        $pool = init_card_pool(1, false);
        $this->pool = array_diff($pool, [$this->xCard]);

        parent::__construct($roomType, $betUidList, $raiseList, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $buffPlayerIndex, $balanceAmount);
    }

    //发牌以及设置发牌结果
    //=========================================================
    //随机发牌
    protected function dealCardOnce(): array
    {
        $dealCardInfo = $this->getDealCardsList();
        $rtn['winNum'] = $dealCardInfo['winNum'];
        $rtn['dealCard'] = $dealCardInfo['dealCard'];
        $rtn['windRaiseList'] = $this->getWinRaiseIdList($rtn['winNum']);
        $rtn['winAmount'] = $this->getWinAmount($rtn['windRaiseList']);
        return $rtn;
    }
    //乱序查找第一个癞子出现的位置
    protected function getWinNumber()
    {
        $tmpNumber = 99;
        shuffle($this->pool);
        shuffle($this->pool);
        foreach ($this->xCardList as $xCardOne) {
            $number = array_search($xCardOne, $this->pool);
            if ($number === false) {
                continue;
            }
            $tmpNumber = min($tmpNumber, $number);
        }
        return $tmpNumber + 1;
    }
    //获取一组发牌
    protected function getDealCardsList(): array
    {
        $rtn['winNum'] = $this->getWinNumber();
        $rtn['dealCard'] = array_slice($this->pool, 0, $rtn['winNum']);
        return $rtn;
    }
    //根据获胜数字 计算获胜区域赢的钱
    protected function getWinRaiseIdList($dealCards): array
    {
        //此处为winNum
        $tmpNumber = $dealCards;
        //判定是奇数还是偶数
        $winRaiseIdList = [];
        $winRaiseIdList[] = is_odd($tmpNumber) ? 0 : 1;
        foreach (AB_OPTION as $raiseId => $raiseRule) {
            if (!isset($raiseRule['min'])) {
                continue;
            }
            if ($tmpNumber >= $raiseRule['min'] && $tmpNumber <= $raiseRule['max']) {
                $winRaiseIdList[] = $raiseId;
            }
        }
        return $winRaiseIdList;
    }
    //根据获胜区域计算赢的钱
    protected function getWinAmount($winRaiseIdList)
    {
        $winAmount = 0;
        foreach ($winRaiseIdList as $raiseId) {
            if (isset($this->raiseList[$raiseId])) {
                $winAmount += $this->raiseList[$raiseId]['amount'] * AB_OPTION[$raiseId]['x'];
            }
        }
        return $winAmount;
    }

    //设置发牌结果
    protected function setDealCardRes($dealCardInfo)
    {
        $this->baseSetDealCardRes($dealCardInfo);
        $this->winNum = $dealCardInfo['winNum'];
        $this->dealCard = $dealCardInfo['dealCard'];
        $this->windRaiseList = $dealCardInfo['windRaiseList'];
    }

    //无需操作
    protected function setPool($cardList)
    {
        // TODO: Implement setPool() method.
    }
    //=========================================================

}
