<?php
/**
 * @todo 幸运转盘 玩法发牌接口
 */

namespace common\AiHundred;

use common\Common;
use lib\Log;

class AiWheelDealCard extends AiHundredDealCard
{
    //房间id
    private $roomId;

    //转盘最后落点的色块
    private $colorBlock = 0;

    public function __construct($roomId, $roomType, $betUidList, $raiseList, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $buffPlayerIndex, $balanceAmount)
    {
        //房间id
        $this->roomId = $roomId;

        parent::__construct($roomType, $betUidList, $raiseList, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $buffPlayerIndex, $balanceAmount);
    }

    //发牌以及设置发牌结果
    //=========================================================
    //随机发牌
    protected function dealCardOnce(): array
    {
        //首次随机开奖结果
        $cardList = $this->getDealCardsList();
        $rtn['windRaiseList'] = $this->getWinRaiseIdList($cardList);
        $rtn['dealCard'] = [$this->colorBlock];
        $rtn['winAmount'] = $this->getWinAmount($rtn['windRaiseList']);
        return $rtn;
    }
    //获取一组结果
    protected function getDealCardsList()
    {
        $dealCardsList = [];
        $dealCardsList[0] = \lbase\Math::GetRandomKeyByWeight(WHEEL_COLOR_WEIGHT);
        Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, '幸运转盘 最后结果：' . json_encode($dealCardsList), Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        return $dealCardsList;
    }

    //获取最后获胜的区域
    // $winRaiseIdList 最后获胜的区域
    // $this->colorBlock 转盘的落点
    protected function getWinRaiseIdList($dealCards): array
    {
        $rusult = array_pop($dealCards);
        $keys = array_keys(WHEEL_COLOR_BLOCK, $rusult);
        $this->colorBlock = $keys[array_rand($keys)];
        $winRaiseIdList[] = $rusult;
        return $winRaiseIdList;
    }

    //根据获胜区域计算赢的钱
    protected function getWinAmount($winRaiseIdList)
    {
        $winAmount = 0;
        foreach ($winRaiseIdList as $raiseId) {
            if ($raiseId == WHEEL_RED_HE) {
                //如果是和局  蓝色池子 和 黄色池子的区域各赔0.5
                $winAmount += (isset($this->raiseList[WHEEL_BLUE_WIN]) ? $this->raiseList[WHEEL_BLUE_WIN]['amount'] : 0) * 0.5;
                $winAmount += (isset($this->raiseList[WHEEL_YELLOW_WIN]) ? $this->raiseList[WHEEL_YELLOW_WIN]['amount'] : 0) * 0.5;
            }
            if (isset($this->raiseList[$raiseId])) {
                $winAmount += $this->raiseList[$raiseId]['amount'] * WHEEL_OPTION[$raiseId]['x'];
            }
        }
        return $winAmount;
    }

    //设置发牌结果
    protected function setDealCardRes($dealCardInfo)
    {
        $this->baseSetDealCardRes($dealCardInfo);
        $this->dealCard[0] = $dealCardInfo['dealCard'][0];
        $this->windRaiseList = $dealCardInfo['windRaiseList'];

    }
    //无需处理
    protected function setPool($cardList)
    {
        // TODO: Implement setPool() method.
    }
    //=========================================================
}
