<?php
/**
 * @todo tpWar玩法发牌接口
 */

namespace common\AiHundred;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;

class AiTpWarDealCard extends AiHundredDealCard
{

    public function __construct($roomType, $betUidList, $raiseList, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $buffPlayerIndex, $balanceAmount)
    {
        //初始化牌池 不带王
        $pool = init_card_pool(1, false);
        $this->pool = $pool;
        shuffle($this->pool);

        parent::__construct($roomType, $betUidList, $raiseList, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $buffPlayerIndex, $balanceAmount);
    }

    //随机发牌
    protected function dealCardOnce(): array
    {

        $rtn['dealCard'] = $this->getDealCardsList();
        $rtn['windRaiseList'] = $this->getWinRaiseIdList($rtn['dealCard']);
        $rtn['winAmount'] = $this->getWinAmount($rtn['windRaiseList']);
        return $rtn;
    }

    //获取一副发牌
    protected function getDealCardsList(): array
    {
        $pool = $this->pool;
        $obj = new \common\AiTeenPatty\AiTeenPattyDealCardByGroupName($pool);
        $dealCard = [];
        for ($i = 1; $i <= 2; $i++) {
            $groupName = get_rand(TP_WAR_DEAL_GROUP_WEIGHT);
            $dealCard[] = $obj->getDealCards($groupName, $this->cls);
        }
        return $dealCard;
    }

    //根据获胜数字 计算获胜区域赢的钱
    protected function getWinRaiseIdList($dealCards): array
    {
        $winRaiseIdList = [];
        //获取2副牌的分数
        $infoList = [];
        foreach ($dealCards as $key => $val) {
            $obj = new \common\AiTeenPatty\AiTeenPattyHandCardsScore($val[0], $val[1], $val[2], $this->cls);
            $infoList[$key]['card'] = $val;
            $infoList[$key]['score'] = $obj->getHandCardsScore();
            $infoList[$key]['groupName'] = $obj->getGroupName();
            $infoList[$key]['maxCardColor'] = $obj::getTpWarMaxCardColor($val, $infoList[$key]['groupName']);
        }

        //判定2副牌是否分数相同
        if ($infoList[0]['score'] == $infoList[1]['score']) {
            if ($infoList[0]['maxCardColor'] > $infoList[1]['maxCardColor']) {
                $winRaiseIdList[] = 0; //queen 赢
            } else {
                $winRaiseIdList[] = 1; //king 赢
            }
        } else {
            if ($infoList[0]['score'] > $infoList[1]['score']) {
                $winRaiseIdList[] = 0; //queen 赢
            } else {
                $winRaiseIdList[] = 1; //king 赢
            }
        }
        //挑选下注区域
        foreach (TP_WAR_OPTION as $raiseId => $raiseRule) {
            if (!isset($raiseRule['group_name'])) {
                continue;
            }
            if ($infoList[$winRaiseIdList[0]]['groupName'] == $raiseRule['group_name']) {

                if ($raiseRule['group_name'] == Common::ZJ_GROUP_NAME_DUIZI) {
                    //对子  对8及以上才算赢
                    if ($infoList[$winRaiseIdList[0]]['score'] >= $raiseRule['min_score']) {
                        $winRaiseIdList[] = $raiseId;
                    }
                } else {
                    $winRaiseIdList[] = $raiseId;
                }
            }
        }
        return $winRaiseIdList;
    }

    //根据获胜区域计算赢的钱
    protected function getWinAmount($winRaiseIdList)
    {
        $winAmount = 0;
        foreach ($winRaiseIdList as $raiseId) {
            //1以后的下注区域 都是2下注区域
            if ($raiseId >= 2) {
                if (isset($this->raiseList[2])) {
                    $winAmount += $this->raiseList[2]['amount'] * TP_WAR_OPTION[$raiseId]['x'];
                }
            } else {
                if (isset($this->raiseList[$raiseId])) {
                    $winAmount += $this->raiseList[$raiseId]['amount'] * TP_WAR_OPTION[$raiseId]['x'];
                }
            }
        }
        return $winAmount;
    }

    //设置发牌结果
    protected function setDealCardRes($dealCardInfo)
    {
        $this->baseSetDealCardRes($dealCardInfo);
        $this->dealCard = $dealCardInfo['dealCard'];
        $this->windRaiseList = $dealCardInfo['windRaiseList'];
    }

    //无需操作
    protected function setPool($cardList)
    {
        // TODO: Implement setPool() method.
    }


}
