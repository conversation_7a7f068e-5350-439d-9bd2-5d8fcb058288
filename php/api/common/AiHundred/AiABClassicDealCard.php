<?php
/**
 * @todo AB玩法发牌接口
 *
 * 玩法介绍：
 *   1、游戏随机得到一个点数 展示给用户
 *   2、随机一副不带王的牌  找到第一个跟亮牌点数相同的牌截止   此时压中牌的位置的区域 以及  压中牌的位置的单双 的获胜  不同区域赔率不同
 *
 *                              单（1.9X）       双（2X）
 *
 *           1-5【3.5X】      6-10【4.5X】      11-15【5.5X】     16-25【4.5X】
 *
 *           26-30【15X】     31-35【25X】      36-40【50X】      40-49【120X】
 *
 *      ###以上10个下注区域  不同区域有不同的赔率
 *
 */


namespace common\AiHundred;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;

class AiABClassicDealCard extends AiHundredDealCard
{
    //房间号
    protected $roomId;
    //癞子牌
    protected $xCard;
    //当前轮数
    protected $round;
    //所有癞子列表
    protected $xCardList = [];

    public function __construct($xCard, $roomId, $round, $roomType, $betUidList, $raiseList, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $buffPlayerIndex, $balanceAmount)
    {
        //游戏随机得到的癞子牌
        $this->xCard = $xCard;
        //房间号
        $this->roomId = $roomId;
        //当前轮数
        $this->round = $round == 1 ? 1 : 2;
        //所有癞子列表
        $xCardList = get_all_color_cards_by_one_card($this->xCard);
        $this->xCardList = array_diff($xCardList, [$this->xCard]);
        //初始化牌池
        if ($this->round == 1) {
            //初始化牌池 不带王
            $pool = init_card_pool(1, false);
            $this->pool = array_diff($pool, [$this->xCard]);
        } else {
            //初始化牌池 不带王
            $this->pool = RedisOpt::getAbClassicPool($this->roomId);
            if (!$this->pool) {
                $pool = init_card_pool(1, false);
                $this->pool = array_diff($pool, [$this->xCard]);
            }
            //读取之后删除
            RedisOpt::delAbClassicPool($this->roomId);
        }
        parent::__construct($roomType, $betUidList, $raiseList, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $buffPlayerIndex, $balanceAmount);

        $this->startKey = 1;
    }

    //发牌以及设置发牌结果
    //=========================================================
    //随机发牌
    protected function dealCardOnce(): array
    {
        $dealCardInfo = $this->getDealCardsList();
        $rtn['winNum'] = $dealCardInfo['winNum'];
        $rtn['dealCard'] = $dealCardInfo['dealCard'];
        $rtn['windRaiseList'] = $this->getWinRaiseIdList($rtn['winNum']);
        $rtn['winAmount'] = $this->getWinAmount(['windRaiseList' => $rtn['windRaiseList'], 'winNum' => $rtn['winNum']]);
        return $rtn;
    }
    //获取一组发牌
    protected function getDealCardsList(): array
    {
        // 牌的张数，最后一张正好命中
        $rtn['winNum'] = $this->getWinNumber();
        // array_slice(array, offset, length)
        $rtn['dealCard'] = array_slice($this->pool, 0, $rtn['winNum']);
        return $rtn;
    }
    //乱序查找第一个癞子出现的位置
    protected function getWinNumber()
    {
        $tmpNumber = 99;
        shuffle($this->pool);
        shuffle($this->pool);
        foreach ($this->xCardList as $xCardOne) {
            $number = array_search($xCardOne, $this->pool);
            if ($number === false) {
                continue;
            }
            $tmpNumber = min($tmpNumber, $number);
        }
        return $tmpNumber + 1;
    }
    //根据获胜数字 计算获胜区域赢的钱
    protected function getWinRaiseIdList($dealCards): array
    {
        //此处 $dealCards 为 winNum
        $winNum = $dealCards;
        //判定是奇数还是偶数
        $winRaiseIdList = [];
        $winRaiseIdList[] = is_odd($winNum);
        return $winRaiseIdList;
    }
    //根据获胜区域计算赢的钱
    protected function getWinAmount($winRaiseIdList)
    {
        $winAmount = 0;
        $type = $winRaiseIdList['winNum'] == 1 ? 1 : 2;
        $optionList = constant('AB_CLASSIC_OPTION_' . $this->roomInfo['base']);
        foreach ($winRaiseIdList['windRaiseList'] as $raiseId) {
            if (isset($this->raiseList[$raiseId])) {
                $winAmount += $this->raiseList[$raiseId]['amount'] * $optionList[$type][$raiseId]['x'];
            }
        }
        return $winAmount;
    }

    //设置发牌结果
    protected function setDealCardRes($dealCardInfo)
    {
        $this->baseSetDealCardRes($dealCardInfo);
        $this->dealCard = $dealCardInfo['dealCard'];
        $this->windRaiseList = $dealCardInfo['windRaiseList'];

    }

    protected function setPool($cardList)
    {
        // TODO: Implement setPool() method.
    }
    //=========================================================


    //执行发牌
    protected function dealCardByUpAndDown()
    {
        if ($this->round == 1) {
            //$this->dealCardsRechargeBuffRound1();
            // 第一轮发牌不扶植，否则会比别的玩法多扶植一次
            $this->dealCardsRound1();
        }
        if ($this->round == 2) {
            $this->dealCardsUpAndDownRound2();
        }
    }

    //执行发牌
    protected function dealCardByRechargeBuff()
    {
        if ($this->round == 1) {
            //$this->dealCardsRechargeBuffRound1();
            // 第一轮发牌不扶植，否则会比别的玩法多扶植一次
            $this->dealCardsRound1();
        }
        if ($this->round == 2) {
            //$this->dealCardsRechargeBuffRound2();
            // 给大扶加上保底杀，直接调用小扶代码就行了
            $this->dealCardsUpAndDownRound2();
        }
    }

    //执行发牌
    protected function dealCardByNorma($from = 'dealCardRun')
    {
        if ($this->round == 1) {
            $this->dealCardsRound1();
        }
        if ($this->round == 2) {
            $this->dealCardsRound2();
        }
    }

    //第一轮发牌
    private function dealCardsRechargeBuffRound1()
    {
        $dealCardInfo = $this->dealCardOnce();
        if ($dealCardInfo['winNum'] <= 2 && $dealCardInfo['winNum'] >= 0) {
            $this->setDealCardRes($dealCardInfo);
        } else {
            //第一轮没有命中 需要扣除2张发牌 并存储剩余牌池
            $this->dealCard = [];
            $this->dealCard[] = array_shift($this->pool);
            $this->dealCard[] = array_shift($this->pool);
            $this->windRaiseList = [];
            RedisOpt::setAbClassicPool($this->pool, $this->roomId);
        }
    }

    //第二轮发牌
    private function dealCardsRechargeBuffRound2()
    {
        $i = $this->rechargeBuffDealCardRandCnt;
        //Log::console_log(__FUNCTION__, '重新随机次数' . ($this->rechargeBuffDealCardRandCnt - 1), Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        //Log::console_log(__FUNCTION__, '用户下注钱数' . $this->allRaiseAmount, Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        //Log::console_log(__FUNCTION__, '用户下注钱数的4倍' . $this->allRaiseAmount * 4, Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        //首次随机开奖结果
        $setDealCardInfo = $this->getAbcDealCardInfo($i);
        $winAmount = $setDealCardInfo['winAmount'];
        //如果赔的钱比较多 最后固定执行5次 看有没有比上次开奖输钱结果少的
        if ($winAmount > $this->maxWinAmount) {
            $res = Common::getIsSysKillerByLotteryResult($winAmount, $this->sysWinAmount, $this->sysTargetAmount, $this->maxWinAmount, $this->sysKillerConfig['buff_coefficient'][$this->getRoomKillConfigType()], Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
            if ($res) {
                $this->subLotteryKillDesc = "压价未遂";
                //Log::console_log(__FUNCTION__, '符合条件执行最后5次检测', Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
                for ($j = 0; $j < 5; $j++) {
                    $checkDealCard = $this->dealCardOnce();
                    if ($checkDealCard['winAmount'] >= $this->maxWinAmount && $checkDealCard['winAmount'] < $winAmount) {
                        $this->subLotteryKillDesc = "压价";
                        //Log::console_log(__FUNCTION__, '5次检测得到了新的结果 原来要赔的钱：' . $winAmount . ' 新结果赔的钱：' . $checkDealCard['winAmount'], Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
                        $setDealCardInfo = $checkDealCard;
                        $winAmount = $checkDealCard['winAmount'];
                    }
                }
            }
        }

        //设置发牌结果
        $this->setDealCardRes($setDealCardInfo);
        //如果赔的钱太多 超过了系统实际收益 则更改为大盘杀
        if ($winAmount > max($this->sysWinAmount, 5000000)) {
            //Log::console_log(__FUNCTION__, '$winAmount' . $winAmount . '$this->sysWinAmount' . $this->sysWinAmount . '超出赔付范围执行大盘杀', Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));

            $this->subLotteryKillDesc = "大赔杀";
            $this->sysKillerRes = true;
            $this->dealCardByNorma();
        }
        //Log::console_log(__FUNCTION__, '最终赔的钱' . $winAmount, Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
    }

    //第二轮发牌
    private function dealCardsUpAndDownRound2()
    {
        $i = $this->rechargeBuffDealCardRandCnt;
        //Log::console_log(__FUNCTION__, '重新随机次数' . ($this->rechargeBuffDealCardRandCnt - 1), Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        //Log::console_log(__FUNCTION__, '用户下注钱数' . $this->allRaiseAmount, Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        //Log::console_log(__FUNCTION__, '用户下注钱数的4倍' . $this->allRaiseAmount * 4, Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        //首次随机开奖结果
        $setDealCardInfo = $this->getAbcDealCardInfo($i);
        $this->setDealCardRes($setDealCardInfo);
        $winAmount = $setDealCardInfo['winAmount'];
        if ($winAmount > $this->maxWinAmount) {
            //保底杀中了 执行大盘杀发牌逻辑
            $res = Common::getIsSysKillerByLotteryResult($winAmount, $this->sysWinAmount, $this->sysTargetAmount, $this->maxWinAmount, $this->sysKillerConfig['buff_coefficient'][$this->getRoomKillConfigType()], Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
            if ($res) {
                $this->subLotteryKillDesc = "保底杀";
                //Log::console_log(__FUNCTION__, '保底杀随中了 ，修改大盘杀为true  执行大盘杀逻辑', Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
                $this->sysKillerRes = true;
                $this->dealCardByNorma();
            } else {
                //没有中执行结果
                //设置发牌结果
                $this->setDealCardRes($setDealCardInfo);
            }
        }
        //Log::console_log(__FUNCTION__, '最终赔的钱' . $winAmount, Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
    }

    //第一轮发牌
    private function dealCardsRound1()
    {
        $killerRes = $this->sysKillerRes || $this->winPointKillerRes;

        $loop = 0; // 安全避免死循环
        while (1) {
            $loop++;

            $dealCardInfo = $this->dealCardOnce();
            //如果第一轮 大于2的张数赢  则没有赢的结果
            if ($dealCardInfo['winNum'] > 2) {
                //第一轮没有命中 需要扣除2张发牌 并存储剩余牌池
                $this->dealCard = [];
                $this->dealCard[] = array_shift($this->pool);
                $this->dealCard[] = array_shift($this->pool);
                $this->windRaiseList = [];
                RedisOpt::setAbClassicPool($this->pool, $this->roomId);
                break;
            } else {
                if ($killerRes && $loop <= 9) {
                    //如果执行系统强杀 需要判定金额是否符合要求
                    if (($dealCardInfo['winAmount'] <= $this->maxWinAmount || $dealCardInfo['winAmount'] == 0)) {
                        $this->setDealCardRes($dealCardInfo);
                        break;
                    }
                } else {
                    //不执行系统强杀 且有结果 则返回结果
                    $this->setDealCardRes($dealCardInfo);
                    break;
                }
            }
        }
    }

    //第二轮发牌
    private function dealCardsRound2()
    {
        $killerRes = $this->sysKillerRes || $this->winPointKillerRes;

        while (1) {
            $dealCardInfo = $this->dealCardOnce();
            if ($killerRes) {
                if (($dealCardInfo['winAmount'] <= $this->maxWinAmount || $dealCardInfo['winAmount'] == 0)) {
                    $this->setDealCardRes($dealCardInfo);
                    break;
                }
            } else {
                $this->setDealCardRes($dealCardInfo);
                //如果赔的钱比较多 且随机到更改大盘杀结果  则重新执行开奖
                $res = Common::getIsSysKillerByLotteryResult($dealCardInfo['winAmount'], $this->sysWinAmount, $this->sysTargetAmount, $this->maxWinAmount, $this->sysKillerConfig['buff_coefficient'][$this->getRoomKillConfigType()], Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
                if ($res) {
                    $this->subLotteryKillDesc = "保底杀";
                    //Log::console_log(__FUNCTION__, '赔的太多 修改大盘杀为true', Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
                    $this->sysKillerRes = true;
                    $this->dealCardsRound2();
                }
                break;
            }
        }
    }

    /**
     * @param int $i
     * @return array
     */
    protected function getAbcDealCardInfo(int $i): array
    {
        $firstDealCard = $this->dealCardOnce();
        //Log::console_log(__FUNCTION__, '第一次发牌' . $firstDealCard['winAmount'], Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        //检测第一次发牌结果 如果赢的钱>=下注的钱  则使用该开奖结果 终止重随
        if ($firstDealCard['winAmount'] >= $this->allRaiseAmount) {
            //Log::console_log(__FUNCTION__, '第一次发牌' . $firstDealCard['winAmount'] . '>=' . $this->allRaiseAmount . '终止重随 使用该次结果', Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
            $setDealCardInfo = $firstDealCard;
        } else {
            //执行所有重随次数   挑选 能让用户赢钱的 且 小于等于4倍下注额的 结果
            $tmpWinAmount = $this->allRaiseAmount * 4;
            $newDealCard = [];
            for ($cnt = 0; $cnt < $i - 1; $cnt++) {
                $tmpDealCard = $this->dealCardOnce();
                //Log::console_log(__FUNCTION__, '重随第' . ($cnt + 1) . '次发牌：' . $tmpDealCard['winAmount'], Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
                if ($tmpDealCard['winAmount'] >= $this->allRaiseAmount && $tmpDealCard['winAmount'] < $this->allRaiseAmount * 4) {
                    if ($tmpDealCard['winAmount'] < $tmpWinAmount) {
                        //Log::console_log(__FUNCTION__, '符合能让用户赢钱的 且 小于等于4倍下注额的 结果', Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
                        $tmpWinAmount = $tmpDealCard['winAmount'];
                        $newDealCard = $tmpDealCard;
                    }
                }
            }
            //设置开奖结果  如果没有符合条件的且赢钱少的结果 就使用第一次的结果
            $setDealCardInfo = empty($newDealCard) ? $firstDealCard : $newDealCard;
        }
        return $setDealCardInfo;
    }
}
