<?php
/**
 * @todo 幸运奖池玩法发牌接口
 */

namespace common\AiHundred;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;

class AiLuckyLotoDealCard extends AiHundredDealCard
{

    //开奖权重
    protected $winOptionWeight = [];

    //当前奖池累积金额
    protected $luckyLotoAmount = 0;

    //本局在0区域下注的钱数 ai和真人
    protected $option0AllBetAmount = 0;
    protected $roomType;


    //获胜区域
    private $winOption;


    public function __construct($option0AllBetAmount, $luckyLotoAmount, $roomType, $betUidList, $raiseList, $roomMold, $historyAllWinAmount, $historyAllBetAmount)
    {
        //房间类型
        $this->roomType = $roomType;
        //奖池区域下注总金额
        $this->option0AllBetAmount = $option0AllBetAmount;
        //当前累积的奖池金额
        $this->luckyLotoAmount = $luckyLotoAmount;
        //开奖区域权重
        $this->winOptionWeight = LUCKY_LOTO_WIN_OPTION_WEIGHT;

        //没有buff房间
        $buffPlayerIndex = 0;
        $balanceAmount = 0;


        //初始化牌池 不带王
        $pool = init_card_pool(1, false);
        $this->pool = $pool;
        shuffle($this->pool);

        parent::__construct($roomType, $betUidList, $raiseList, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $buffPlayerIndex, $balanceAmount);
    }

    // 发牌以及设置发牌结果
    // =========================================================
    // 已经确定哪个区域赢了，随机一次发牌结果
    protected function dealCardOnce(): array
    {
        $rtn['dealCard'] = $this->getDealCardsList();
        $rtn['windRaiseList'] = $this->getWinRaiseIdList($rtn['dealCard']);
        $rtn['winAmount'] = $this->getWinAmount($rtn['windRaiseList']);
        return $rtn;
    }

    // 已经确定哪个区域赢了，按照那个区域的牌型，随机三张牌出来
    protected function getDealCardsList(): array
    {
        //按照牌型执行发牌
        $obj = new \common\AiTeenPatty\AiTeenPattyDealCardByGroupName($this->pool);
        $dealCards = $obj->getDealCards(LUCKY_LOTO_OPTION[$this->winOption]['group_name'], $this->cls);
        Log::console_log(__FUNCTION__, '发牌 名称' . json_encode(LUCKY_LOTO_OPTION[$this->winOption]['group_name']) . "发到的牌：" . json_encode($dealCards), Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        return $dealCards;
    }


    // 获胜区域列表
    protected function getWinRaiseIdList($dealCards): array
    {
        return [$this->winOption];
    }

    // 计算获胜区域的真人下注总额对应的系统赔付总额
    protected function getWinAmount($winRaiseIdList)
    {
        $winAmount = 0;
        foreach ($winRaiseIdList as $raiseId) {
            if (isset($this->raiseList[$raiseId])) {
                $winAmount += $this->raiseList[$raiseId]['amount'] * LUCKY_LOTO_OPTION[$raiseId]['x'];
            }
        }
        return $winAmount;
    }

    // 设置发牌结果
    protected function setDealCardRes($dealCardInfo)
    {
        $this->baseSetDealCardRes($dealCardInfo);
        $this->dealCard = $dealCardInfo['dealCard'];
        $this->windRaiseList = $dealCardInfo['windRaiseList'];
    }

    // 无需操作
    protected function setPool($cardList)
    {
        // TODO: Implement setPool() method.
    }
    //=========================================================



    // 执行发牌，重写父类方法
    // 此玩法没有 buff 房间
    protected function dealCardRun()
    {
        $this->dealCardByNorma();
    }

    // 执行发牌
    protected function dealCardByNorma($from = 'dealCardRun')
    {
        // 初步决定开奖区域
        $this->getWinOptionWeight();
        $winOption = get_rand($this->winOptionWeight);
        $branch = "SMALL";

        // 在大奖区域，真人下注总额
        $raise0Amount = isset($this->raiseList[0]) ? $this->raiseList[0]['amount'] : 0;

        // 在大奖区域，真人下注占比
        $bigHumanStakeRatio = $this->option0AllBetAmount > 0 ? $raise0Amount / $this->option0AllBetAmount : 0;
        $bigHumanStakeRatio = round($bigHumanStakeRatio, 3);

        // 大奖奖池金额 $this->luckyLotoAmount
        // 若开大奖对真人赔付总额
        $bigHumanPayoutAmount = $this->luckyLotoAmount * LUCKY_LOTO_OPTION[0]['x'] * $bigHumanStakeRatio;
        // 若开大奖真人毛利
        $bigHumanGrossProfit = $bigHumanPayoutAmount - $raise0Amount;

        // 真人下注总额 allRaiseAmount maxWinAmount

        // 如果开大奖，并且有真人在大奖下注
        if ($winOption == 0 && $raise0Amount > 0 && $this->option0AllBetAmount > 0) {
            $branch = "BIG";

            // 如果开大奖系统亏钱，那么就随机开个小奖
            if ($bigHumanPayoutAmount > $this->maxWinAmount) {
                $winOption = rand(1, 4);
                $branch = "RAND14";
            }
        }

        // 已经确定哪个区域赢了，随机一次发牌结果
        $this->winOption = $winOption;
        $this->setDealCardRes($this->dealCardOnce());

        // 各区域真人下注
        $humanStakeRupeeByZone = array_map(fn($r) => round(($r['amount'] ?? 0) / 1000, 2), $this->raiseList);
        // 真人赔付总额
        $humanPayoutAmount = $this->getWinAmount($this->windRaiseList);
        // 真人毛利
        $humanGrossProfit = $humanPayoutAmount - $this->allRaiseAmount;

        // 真人参与的才打日志
        if ($this->allRaiseAmount > 0) {
            \lbase\Log::info('LuckyLoto-dealCardByNorma', [
                'branch' => $branch,
                '开奖区域' => $this->winOption,
                '三张牌' => $this->dealCard,
                '真人毛利' => round($humanGrossProfit / 1000, 2),
                '对真人赔付总额' => round($humanPayoutAmount / 1000, 2),
                '真人下注总额' => round($this->allRaiseAmount / 1000, 2),
                '各区域真人下注' => $humanStakeRupeeByZone,
                '大奖真人下注' => round($raise0Amount / 1000, 2),
                '大奖所有人下注总额' => round($this->option0AllBetAmount / 1000, 2),
                '大奖真人下注占比' => $bigHumanStakeRatio,
                '大奖奖池金额' => round($this->luckyLotoAmount / 1000, 2),
                '若开大奖对真人赔付总额' => round($bigHumanPayoutAmount / 1000, 2),
                '若开大奖真人毛利' => round($bigHumanGrossProfit / 1000, 2),
            ]);
        }
    }

    // 计算各个区域的获胜权重
    private function getWinOptionWeight()
    {
        // 原始发牌权重
        $this->winOptionWeight = LUCKY_LOTO_WIN_OPTION_WEIGHT;

        // waveRate 在 1 附近波动，超额就大于 1，有缺口就 小于 1
        // 这里不要超额的，超额就当打平
        $this->sysKillerWaveRate = min(1, $this->sysKillerWaveRate);
        // 安全约束
        $this->sysKillerWaveRate = max(0.05, $this->sysKillerWaveRate);

        // 对于有利于系统的开奖区域，其权重要进行放大，放大倍数是多少
        $boostFactor = 1 / $this->sysKillerWaveRate;
        $boostFactor = round($boostFactor, 2);

        // 统计各个开奖区域的真人赔付总额
        $humanPayoutRupeeByZone = [];
        // 统计各个开奖区域的放大倍数
        $boostFactorByHouseFavoredZone = [];

        foreach ($this->winOptionWeight as $zone => $weight) {
            $winAmount = $this->getWinAmount([$zone]);
            $humanPayoutRupeeByZone[$zone] = round($winAmount / 1000, 2);

            // allRaiseAmount 是真人下注总额
            // winAmount 是获胜区域的真人下注总额对应的系统赔付总额
            // 放大那些系统不亏钱的开奖区域的权重
            if ($zone != 0 && $this->allRaiseAmount >= $winAmount && $boostFactor != 1) {
                $boostFactorByHouseFavoredZone[$zone] = $boostFactor;
                $weight = intval($weight * $boostFactor);
                $this->winOptionWeight[$zone] = $weight;
            }
        }

        // 真人下注总额
        $humanStakeRupee = round($this->allRaiseAmount / 1000, 2);
        // 各区域真人毛利
        $humanGrossProfitByZone = [];
        // 利庄区域真人毛利
        $humanGrossProfitByHouseFavoredZone = [];
        // 总权重
        $totalWeight = array_sum($this->winOptionWeight);
        // 各区域概率
        $probByZone = [];
        // 利庄区域概率
        $probByHouseFavoredZone = [];
        foreach ($this->winOptionWeight as $zone => $weight) {
            $humanGrossProfitByZone[$zone] = $humanPayoutRupeeByZone[$zone] - $humanStakeRupee;
            $probByZone[$zone] = round($weight / $totalWeight, 3);
            if ($zone != 0 && $humanStakeRupee >= $humanPayoutRupeeByZone[$zone]) {
                $humanGrossProfitByHouseFavoredZone[$zone] = $humanGrossProfitByZone[$zone];
                $probByHouseFavoredZone[$zone] = $probByZone[$zone];
            }
        }

        // 缺口越大，waveRate 越接近于 0，放大倍数就越大，有利于系统的开奖区域的权重就越大

        // 真人参与的才打日志
        if ($this->allRaiseAmount > 0) {
            \lbase\Log::info('LuckyLoto-getWinOptionWeight', [
                'waveRate' => $this->sysKillerWaveRate,
                '权重放大倍数' => $boostFactor,
                '利庄区域真人毛利' => $humanGrossProfitByHouseFavoredZone,
                '利庄区域概率' => $probByHouseFavoredZone,
                '各区域真人毛利' => $humanGrossProfitByZone,
                '真人下注总额' => $humanStakeRupee,
                '各区域对真人赔付' => $humanPayoutRupeeByZone,
                '各区域概率' => $probByZone,
                '各区域权重' => $this->winOptionWeight,
            ]);
        }
    }

    public function calcBranchName(): string
    {
        // 这里不关注具体人数，只关注是否有真人
        if (!$this->betUidList) {
            return '';
        }

        // 这次发牌真人整体是赚钱还是亏钱
        $humanWinDesc = $this->humanWinRes ? '赚钱' : '亏钱';

        if ($this->sysKillerWaveRate == 1) {
            return '随机' . $humanWinDesc;
        } else {
            return '权重杀' . $humanWinDesc;
        }

    }
}
