<?php
/**
 * @todo slots发牌
 */
namespace common\AiHundred;

use lib\Log;
use common\Common;

class AiSlotsGaneshaDealCard extends AiSlotsHundredDealCard
{

    //扶植重随最大中奖倍数
    protected $upBuffMaxWinAmountMultiple = 10;

    //scattr集合
    protected $scattrList = [];

    //是否免费发牌
    protected $isFree;

    //scattr计算结果
    protected $scattrInfo;

    //每条线的下注金额
    protected $baseLineBetAmount;


    public function __construct($roomType, $isFree, $uid, $betAmount, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $raiseUserWalletList)
    {
        //每条线的下注钱数
        $this->baseLineBetAmount = $betAmount > 0 ? bcdiv($betAmount, count(SLOTS_WIN_WAY), 3) : 0;
        $this->isFree = $isFree;
        parent::__construct($roomType, [$uid], [0 => ["amount" => $betAmount]], $roomMold, $historyAllWinAmount, $historyAllBetAmount, $uid, 0 /*, $raiseUserWalletList*/);
    }

    //执行发牌
    protected function dealCardRun()
    {
        if ($this->isFree) {
            //真实随机  不执行任何干预
            $this->isFreeSpin = true;
            $this->dealCardByFreeSpin();
        } else {
            parent::dealCardRun();
        }
    }


    //执行免费真是发牌
    protected function dealCardByFreeSpin()
    {
        $this->setDealCardRes($this->dealCardOnce());
    }



    //发牌以及设置发牌结果
    //=========================================================
    //执行一次发牌
    protected function dealCardOnce(): array
    {
        $rtn['dealCard'] = $this->getDealCardsList();
        $rtn['scattrInfo'] = $this->calculationScattrInfo();
        $rtn['windRaiseList'] = array_values($this->getWinRaiseIdList($rtn['dealCard']));
        $rtn['winAmount'] = $this->getWinAmount($rtn);

        // 如果获得了免费次数，则相当于赢钱了，影响扶控的计算
        if ($rtn['scattrInfo']['free_spin_cnt'] > 0) {
            $rtn['assumedExtraWinAmount'] = $this->allRaiseAmount * 15;
        }

        return $rtn;
    }

    //根据二位坐标 生成标识的数字
    public function getCodeByXAndY(int $x, int $y): array
    {
        return ['x' => $x, 'y' => $y];

    }

    //获取一组结果
    protected function getDealCardsList(): array
    {
        $dealCards = [];
        $this->scattrList = [];
        //按照发牌权重执行发牌
        if ($this->isFree) {
            $dealCardWeight = SOTS_FREE_DEAL_CARDS_WEIGHT;
        } elseif ($this->winPointKillerRes) {
            $dealCardWeight = SOTS_WIN_POINT_KILLER_DEAL_CARDS_WEIGHT;
        } else {
            $dealCardWeight = SLOTS_DEAL_CARDS_WEIGHT;
        }
        //Log::console_log(__FUNCTION__, "发牌权重：" . json_encode($dealCardWeight), Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        for ($x = 0; $x < SLOTS_X; $x++) {
            for ($y = 0; $y < SLOTS_Y; $y++) {
                $dealCards[$x][$y] = get_rand($dealCardWeight);
                if ($dealCards[$x][$y] == SLOTS_SCATTR) {
                    $this->scattrList[] = $this->getCodeByXAndY($x, $y);
                }
                if (($this->winPointKillerRes || $this->sysKillerRes) && count($this->scattrList) == 2) {
                    //复仇杀 和 系统杀 状态下 且scattr不能超过2个 去除权重scattr选项
                    unset($dealCardWeight[SLOTS_SCATTR]);
                    //Log::console_log(__FUNCTION__, "发牌权重：" . json_encode($dealCardWeight), Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
                }
            }
        }
        return $dealCards;
    }


    //计算获胜路线 以及 获胜的倍数
    protected function getWinRaiseIdList($dealCards): array
    {
        $winRaiseIdList = [];
        //根据路线计算是否获胜
        foreach (SLOTS_WIN_WAY as $key => $way) {
            $firstCard = "";
            $tmpWinRaiseIdList = [];
            $highlightXY = [];
            $wild_arr = [];
            foreach ($way as $x => $y) {
                $card = $dealCards[$x][$y];
                //scatter不计算倍数奖励 只有免费次数
                if ($card == SLOTS_SCATTR) {
                    break;
                }
                if (!in_array($card, SLOTS_WILD_LIST)) {
                    //设置首个 不是wild 的手牌
                    if (!$firstCard) {
                        $firstCard = $card;
                    }
                    //手牌跟当前牌不相等 且不是癞子 则终止连续
                    if ($firstCard && $card != $firstCard) {
                        break;
                    }
                }
                //路线里加入临时手牌
                $tmpWinRaiseIdList[] = $card;
                $highlightXY[] = $this->getCodeByXAndY($x, $y);

                // 拿到参与中奖线的wild坐标
                if (in_array($card, SLOTS_WILD_LIST)) {
                    $wild_arr[] = ['card' => $card, 'x' => $x, 'y' => $y];
                }
            }
            //计算元素个数 只有3个且以上的个数 才能获奖
            $tmpWinRaiseIdListCnt = count($tmpWinRaiseIdList);
            if ($tmpWinRaiseIdListCnt >= 3) {
                //如果全是癞子 则指定为最高赔率的团
                $firstCard = $firstCard == "" ? SLOTS_MAX_SCORE_ELEMENT : $firstCard;
                $winRaiseIdList[$key]['multiple'] = SLOTS_X_OPTION[$firstCard][$tmpWinRaiseIdListCnt];
                $winRaiseIdList[$key]['highlight'] = $highlightXY;
                $winRaiseIdList[$key]['line'] = $key;

                // 中奖线的首牌
                $winRaiseIdList[$key]['firstCard'] = $firstCard;
                // 中奖线的所有牌
                $winRaiseIdList[$key]['tmpWinRaiseIdList'] = $tmpWinRaiseIdList;
                // 中奖线里的wild坐标
                $winRaiseIdList[$key]['wild_arr'] = $wild_arr;

                $elementCntList = array_count_values($tmpWinRaiseIdList);
                foreach ($elementCntList as $element => $cnt) {
                    if (in_array($element, SLOTS_WILD_LIST)) {
                        $winRaiseIdList[$key]['multiple'] = $winRaiseIdList[$key]['multiple'] * SLOTS_X_OPTION[$element][$cnt];
                    }
                }
                $multiple = $winRaiseIdList[$key]['multiple'];
                $baseLineBetAmount = $this->baseLineBetAmount;
                $win_amount = $multiple * $baseLineBetAmount;
                $winRaiseIdList[$key]['win_amount'] = $win_amount;
            }
        }
        return $winRaiseIdList;
    }


    //计算是否有免费个数
    protected function calculationScattrInfo(): array
    {
        $rtn['highlight'] = $this->scattrList;
        $rtn['scattr_cnt'] = count($this->scattrList);
        $maxScattrCnt = max(array_keys(SLOTS_FREE_SPIN));
        $scattrCnt = min($rtn['scattr_cnt'], $maxScattrCnt);
        $freeSpinCnt = SLOTS_FREE_SPIN[$scattrCnt] ?? 0;
        $rtn['free_spin_cnt'] = $freeSpinCnt;
        return $rtn;
    }

    //根据获胜区域计算赢的钱
    protected function getWinAmount($winRaiseIdList)
    {
        //Log::console_log(__FUNCTION__,$this->cls."[".json_encode($this->raiseList).json_encode($winRaiseIdList)."]",'error');
        //计算获胜线路上的赢钱金额
        $winAmount = 0;
        if ($winRaiseIdList['windRaiseList']) {
            foreach ($winRaiseIdList['windRaiseList'] as $info) {
                $winAmount += $info['win_amount'];
            }
        }

        //赢钱基数为  下注金额/赢钱线路
        return $winAmount;
    }

    //设置发牌结果
    protected function setDealCardRes($dealCardInfo)
    {
        $this->baseSetDealCardRes($dealCardInfo);
        $this->dealCard = $dealCardInfo['dealCard'];
        $this->windRaiseList = $dealCardInfo['windRaiseList'];
        $this->scattrInfo = $dealCardInfo['scattrInfo'];
        $this->winAmount = $dealCardInfo['winAmount'];
    }


    //读取免费次数
    public function getScattrInfo(): array
    {
        return $this->scattrInfo;
    }

    //无需操作
    protected function setPool($cardList)
    {
        // TODO: Implement setPool() method.
    }
    //=========================================================


    //只要奖池有亏空 就执行系统强杀
    protected function getSysKillerRes(): bool
    {
        $killerType = $this->getRoomKillConfigType();
        $killerRate = $this->sysKillerConfig['room_type_killer'][$killerType] ?? 0;
        $this->sysKillerRate = $killerRate;
        Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, '系统原始强杀：' . json_encode($killerRate), Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        if ($this->sysKillerWaveRate > 0) {
            return true;
        } else {
            return false;
        }
    }
}
