<?php
/**
 * 此逻辑为ai下注
 */


namespace common\AiHundred;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;

class AiAbClassicBet
{

    //设置下注的筹码面值列表
    private $chipFaceValueList = [];

    //AI列表
    private $aiList = [];

    //房间类型信息
    private $roomInfo = [];

    //AI下注配置
    private $betConfig = [];

    //下注区域
    private $betOption = [];

    //当前游戏进行到了第几秒
    private $curSecond;

    //当前第几秒的配置
    private $curSecondConfig = [];

    //设置可变选项权重
    private $betOptionChangeWeight = [];

    //当前ai的类型
    private $aiType = 'other';

    public function __construct()
    {

    }

    //设置下注区域
    public function setBetOption(array $betOption)
    {
        $this->betOption = $betOption;
    }

    //设置下注配置
    public function setBetConfig(array $betConfig)
    {
        $this->betConfig = $betConfig;
    }

    //设置当前游戏是第几秒
    public function setCurSecond(int $second)
    {
        $this->curSecond = $second;
    }

    //设置当前下注的ai列表
    public function setAiList(array $aiList)
    {
        $this->aiList = $aiList;
    }

    //设置当前下注筹码面值列表
    public function setChipFaceValueList(array $chipFaceValueList)
    {
        $this->chipFaceValueList = $chipFaceValueList;
    }

    //设置当前下注轮数
    public function setRoomInfo($roomType)
    {
        $roomInfo = parse_room_type($roomType);
        $this->roomInfo = $roomInfo;
    }

    //设置下注可变选项权重
    public function setBetOptionChangeWeight($betOptionChangeWeight)
    {
        $this->betOptionChangeWeight = $betOptionChangeWeight;
    }

    //设置下注的ai类型
    public function setAiType($type)
    {
        if (in_array($type, ['other', 'rank'])) {
            $this->aiType = $type;
        } else {
            $this->aiType = 'other';
        }
    }

    //获取下注列表
    public function getBetList()
    {
        $list = [];
        $this->getCurSecondConfig();
        foreach ($this->aiList as $playerIndex => $betInfo) {
            if (!$this->getIsBet()) {
                continue;
            }
            if (!$betInfo['bet_option']) {
                continue;
            }
            //如果存在上次的下注区域   则
            $lastBetOption = 100;
            if (isset($betInfo['last_bet_option']) && !empty($betInfo['last_bet_option'])) {
                $lastBetOptionList = $betInfo['last_bet_option'];
                $lastBetOption = array_pop($lastBetOptionList);
            }
            $betOptionRand = isset($this->betOptionChangeWeight[$this->aiType][$lastBetOption]) ? $this->betOptionChangeWeight[$this->aiType][$lastBetOption] : $this->betOptionChangeWeight[$this->aiType][-1];
            $betOption = get_rand($betOptionRand);
            //如果随机到的结果和上次相反 且余额不足总的需要下注的金额的一半 则不下注
            if ($betOption < 0) {
                continue;
            }
            if ($lastBetOption != 100 && $betOption != $lastBetOption && $betInfo['over_bet_amount'] > $betInfo['all_bet_amount'] * 0.5) {
                continue;
            }

            //获取本次下注金额
            if ($this->aiType == 'rank') {
                $allBetAmount = $this->getBetAmountRank($betInfo['all_bet_amount'], $betInfo['over_bet_amount'], $betOption);
            } else {
                $allBetAmount = $this->getBetAmountOther($betInfo['all_bet_amount'], $betInfo['over_bet_amount'], $betOption);
            }
            $chipInfo = $this->getNearChipAndCntRank($allBetAmount['surplusAmount'], $allBetAmount['betAmount']);
            if (!$chipInfo) {
                continue;
            }
            $chipInfo['bet_option'] = $betOption;
            $list[$playerIndex] = $chipInfo;
        }
        //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__,'房间ab请求'.$this->roomInfo['base'].'：'.json_encode($this->aiList));
        //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__,'房间ab返回'.$this->roomInfo['base'].'：'.json_encode($list));
        return $list;
    }

    //获取适合本轮的配置
    private function getCurSecondConfig()
    {
        //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__,'房间ab调试'.$this->roomInfo['base'].'：'.json_encode( $this->betConfig));
        foreach ($this->betConfig as $key => $val) {
            if ($this->curSecond >= $val['min_second'] && $this->curSecond <= $val['max_second']) {
                $this->curSecondConfig = $val;
                break;
            }
        }
    }

    //获取本次是否下注
    private function getIsBet()
    {
        $betRate = $this->aiType == 'rank' ? $this->curSecondConfig['rank_bet_rate'] : $this->curSecondConfig['other_bet_rate'];
        $rand[1] = $betRate * 100;
        $rand[0] = 100 - $rand[1];
        return get_rand($rand) ? true : false;
    }

    //获取本轮下注金额
    private function getBetAmountOther($allBetAmount, $overBetAmount, $betOption)
    {
        $regionId = get_rand($this->curSecondConfig['bet_amount_region_weight']);
        if (!isset($this->curSecondConfig['bet_amount_region'][$regionId]['min'])) {
            $min = 0;
            //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__,'房间ab调试'.$this->roomInfo['base'].'：'.json_encode( $this->curSecondConfig));
            //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__,'房间ab调试'.$this->roomInfo['base'].'：'.json_encode( $regionId));
            //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__,'房间ab调试'.$this->roomInfo['base'].'：'.json_encode( $this->curSecondConfig['bet_amount_region'][$regionId]));
        } else {
            $min = $this->curSecondConfig['bet_amount_region'][$regionId]['min'] * 10000;
        }
        $max = $this->curSecondConfig['bet_amount_region'][$regionId]['max'] * 10000;
        //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__,'房间ab调试'.$this->roomInfo['base'].'：'.json_encode( $min));
        //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__,'房间ab调试'.$this->roomInfo['base'].'：'.json_encode( $max));
        $betRate = random_int($min, $max) * 0.0001;
        $rtn['surplusAmount'] = $allBetAmount - $overBetAmount;
        if (!$this->betOption) {
            $betAmountX = 1;
        } else {
            $betAmountX = isset($this->betOption[$betOption]['bet_amount_x']) ? $this->betOption[$betOption]['bet_amount_x'] : 1;
        }
        $randBetAmount = intval($allBetAmount * $betRate * $betAmountX);
        $rtn['betAmount'] = min($rtn['surplusAmount'], $randBetAmount);
        return $rtn;
    }



    //获取本轮下注金额
    private function getBetAmountRank($allBetAmount, $overBetAmount, $betOption)
    {
        $betRate = random_int($this->curSecondConfig['min_bet_amount_rate'] * 100, $this->curSecondConfig['max_bet_amount_rate'] * 100) * 0.01;
        $rtn['surplusAmount'] = $allBetAmount - $overBetAmount;
        if (!$this->betOption) {
            $betAmountX = 1;
        } else {
            $betAmountX = isset($this->betOption[$betOption]['bet_amount_x']) ? $this->betOption[$betOption]['bet_amount_x'] : 1;
        }
        $randBetAmount = intval($allBetAmount * $betRate * $betAmountX);
        $rtn['betAmount'] = min($rtn['surplusAmount'], $randBetAmount);
        return $rtn;
    }



    //找出距离最近的筹码 并返回下注个数
    private function getNearChipAndCntRank($surplusAmount, $amount)
    {
        $chipFaceValueList = isset($this->chipFaceValueList[$this->roomInfo['base']]) ? $this->chipFaceValueList[$this->roomInfo['base']] : [];
        $chipInfo = [];
        $chip = min($chipFaceValueList) * 1000;
        if ($amount <= 0 || $chip > $surplusAmount) {
            return $chipInfo;
        }
        foreach ($chipFaceValueList as $key => $val) {
            if ($amount < $val * 1000) {
                break;
            }
            $chip = $val * 1000;
        }
        $cnt = 1;
        $chipInfo['chip_face_value'] = $chip;
        $chipInfo['cnt'] = $cnt;
        return $chipInfo;
    }
}
