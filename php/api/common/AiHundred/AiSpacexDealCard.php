<?php
/**
 * @todo 大火箭
 */

namespace common\AiHundred;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;

class AiSpacexDealCard extends AiHundredDealCard
{

    protected $maxWinAmountMultiple = 2;

    public function __construct($roomType, $betUidList, $raiseList, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $buffPlayerIndex)
    {
        $balanceAmount = 0;
        parent::__construct($roomType, $betUidList, $raiseList, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $buffPlayerIndex, $balanceAmount);
    }


    //随机发牌
    protected function dealCardOnce(): array
    {
        if ($this->sysKillerRes || $this->winPointKillerRes) {
            //大盘杀 抑制发牌模式
            $dealCardType = "down";
        } elseif ($this->maxWinAmount > 0) {
            // maxWinAmount 是真人下注额
            //有真人玩家参与 执行大盘参数带入 计算倍数
            $dealCardType = "up";
        } else {
            //没有真人玩家参与 执行纯范围随机 计算倍数
            $dealCardType = "normal";
        }
        // 爆炸时的开奖倍数
        $rtn['dealCard'] = $this->getDealCardsList($dealCardType);
        $rtn['windRaiseList'] = $this->getWinRaiseIdList($rtn['dealCard']);
        $rtn['winAmount'] = $this->getWinAmount($rtn['windRaiseList']);
        return $rtn;
    }
    //获取一组结果
    protected function getDealCardsList($randType = 'normal'): float
    {
        Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, '$randType' . $randType, Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));

        $c1 = 1.02;

        if ($randType == 'up') {
            // sysKillerRate 是后台配置的 room_type_killer 普通 高手 大户
            // sysKillerWaveRate 是系统利润缺口导致的波动杀率
            // up 开奖倍率 = 0.995 / random(min(0.999999, max(0.005, 0.005 + 配置杀率 + 波动杀率)), 0.999999)
            // 3. 有真人随机发牌：开奖倍率 = 1 / random( min( $c1, max( 0.0009,0.0009 + 配置杀率 + 波动杀率 )), $c1 )
            // up 开奖倍率 = 1 / random(min($c1, max(0.0009, 0.0009 + 配置杀率 + 波动杀率)), $c1)
            $randomMin = intval(max(0.0009, 0.0009 + ($this->sysKillerRate + $this->sysKillerWaveRate)) * 1000000);
            $randomMax = $c1 * 1000000;
            $randomMin = min($randomMin, $randomMax);
            $random = random_int($randomMin, $randomMax) * 0.000001;
            $baseX = 1 / $random;
            \lbase\Log::info('SX.CARDS.getDealCardsList.UP', [
                'cls' => Common::getGameTypeTextList($this->cls),
                'killerType' => Common::getSysKillerType($this->killerType),
                'sysKillerRate' => $this->sysKillerRate,
                'sysKillerWaveRate' => $this->sysKillerWaveRate,
                'randType' => $randType,
                'randomMin' => $randomMin,
                'randomMax' => $randomMax,
                'random' => $random,
                'baseX' => $baseX,
            ]);
        } elseif ($randType == 'down') {
            // down 开奖倍率 = random(1.01, 1.25)
            $randomMin = 1.01 * 1000;
            $randomMax = 1.25 * 1000;
            $baseX = random_int($randomMin, $randomMax) * 0.001;
            // 2. 有真人大盘控（强杀）&复仇杀  发牌：开奖倍率 = 1 / random( 0.5, $c1 )
            $randomMin = 0.5 * 1000000;
            $randomMax = $c1 * 1000000;
            $random = random_int($randomMin, $randomMax) * 0.000001;
            $baseX = 1 / $random;
            \lbase\Log::info('SX.CARDS.getDealCardsList.DOWN', [
                'cls' => Common::getGameTypeTextList($this->cls),
                'killerType' => Common::getSysKillerType($this->killerType),
                'sysKillerRate' => $this->sysKillerRate,
                'sysKillerWaveRate' => $this->sysKillerWaveRate,
                'randType' => $randType,
                'randomMin' => $randomMin,
                'randomMax' => $randomMax,
                'random' => $random,
                'baseX' => $baseX
            ]);
        } else {
            // normal 开奖倍率 = 0.995 / random(0.005, 0.999999)
            $randomMin = 0.005 * 1000000;
            $randomMax = 0.999999 * 1000000;
            $random = random_int($randomMin, $randomMax) * 0.000001;
            $baseX = 0.995 / $random;
            // 1. 无真人发牌（游戏表演）：开奖倍率 = 1 / random( 0.001, $c1 )
            $randomMin = 0.001 * 1000000;
            $randomMax = $c1 * 1000000;
            $random = random_int($randomMin, $randomMax) * 0.000001;
            $baseX = 1 / $random;
            \lbase\Log::info('SX.CARDS.getDealCardsList.NORMAL', [
                'cls' => Common::getGameTypeTextList($this->cls),
                'killerType' => Common::getSysKillerType($this->killerType),
                'sysKillerRate' => $this->sysKillerRate,
                'sysKillerWaveRate' => $this->sysKillerWaveRate,
                'randType' => $randType,
                'randomMin' => $randomMin,
                'randomMax' => $randomMax,
                'random' => $random,
                'baseX' => $baseX,
            ]);
        }
        return round($baseX, 2);
    }

    //根据获胜数字 计算获胜区域赢的钱
    protected function getWinRaiseIdList($dealCards): array
    {
        //设置获胜区域 以及倍数
        $winRaiseIdList[0] = $dealCards;
        return $winRaiseIdList;
    }

    //根据获胜区域计算赢的钱
    protected function getWinAmount($winRaiseIdList)
    {
        if (!$winRaiseIdList) {
            return 0;
        }
        $winAmount = 0;
        foreach ($winRaiseIdList as $raiseId => $baseX) {
            if (isset($this->raiseList[$raiseId])) {
                $winAmount += $this->raiseList[$raiseId]['amount'] * $baseX;
            }
        }
        return $winAmount;
    }
    //设置发牌结果
    protected function setDealCardRes($dealCardInfo)
    {
        $this->baseSetDealCardRes($dealCardInfo);
        // 爆炸时的开奖倍数
        $this->dealCard = $dealCardInfo['dealCard'];
        $this->windRaiseList = $dealCardInfo['windRaiseList'];
    }

    //无需处理
    protected function setPool($cardList)
    {
        // TODO: Implement setPool() method.
    }


}
