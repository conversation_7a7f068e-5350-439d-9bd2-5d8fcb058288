<?php
/**
 * @todo   ai version 1 组排 出牌模拟算法
 * <AUTHOR>
 *
 */


namespace common\AiRummy\AutoGroup;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;

trait AiRummyFindSeqReal
{
    use AiRummyFindSeq;
    /**
     * 找出所有真顺
     * @param array $formatSeqHandCards
     * @return array
     */
    private function findAllRealSeq(array $formatSeqHandCards): array
    {
        return $this->grepSeq(RUMMY_REAL_SEQ_TMP, $formatSeqHandCards, 0);
    }
}

?>

