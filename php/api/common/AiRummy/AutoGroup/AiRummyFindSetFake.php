<?php

namespace common\AiRummy\AutoGroup;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;

trait AiRummyFindSetFake
{
    use AiRummyFindSet;
    /**
     * 找出所有set
     * @param int $xcnt
     * @return array
     */
    private function findAllFakeSet(array $formatSetHandCards, int $xcnt): array
    {
        $list = [];
        if ($xcnt >= 1) {
            $list = array_merge($list, $this->grepSet(RUMMY_FAKE_SET_1XCARD_TMP, $formatSetHandCards, 1));
        }
        if ($xcnt >= 2) {
            $list = array_merge($list, $this->grepSet(RUMMY_FAKE_SET_2XCARD_TMP, $formatSetHandCards, 2));
        }
        return $list;
    }

}

?>

