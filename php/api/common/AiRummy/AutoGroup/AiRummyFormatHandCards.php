<?php
/**
 * @todo   ai version 1 组排 出牌模拟算法
 * <AUTHOR>
 *
 */

namespace common\AiRummy\AutoGroup;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;

trait AiRummyFormatHandCards
{

    //找出癞子数量
    private $xCnt = 0;
    //格式化的seq手牌
    private $formatSeqHandCards = [];
    //格式化的set手牌
    private $formatSetHandCards = [];
    //手牌点数 花色数量
    private $handCardsColorAndNumCnt = [];
    //手里所有的癞子牌
    private $handCarsAllXcards = [];

    //格式化手牌按照花色和点数
    private function formatHandCards($handCards, $xcard)
    {
        $this->initFormatHandCards();
        //获取癞子牌点数
        $xCardPoint = -1;
        if ($xcard != G_CARD) {
            $xCardPoint = get_card_info($xcard)['num'];
        }
        //去重
        $handsCards = $handCards;
        foreach ($handsCards as $card) {
            if ($card == G_CARD) {
                if (!isset($this->handCardsColorAndNumCnt[G_CARD])) {
                    $this->handCardsColorAndNumCnt[G_CARD] = 0;
                }
                $this->handCardsColorAndNumCnt[G_CARD] += 1;
                $this->handCarsAllXcards[] = $card;
                //手牌里癞子数量
                $this->xCnt++;
                continue;
            }
            $cardInfo = get_card_info($card);
            if ($cardInfo['num'] == $xCardPoint) {
                $this->handCarsAllXcards[] = $card;
                $this->xCnt++;
            }
            //花色以及点数手牌数量
            if (!isset($this->handCardsColorAndNumCnt[$cardInfo['type']][$cardInfo['num']])) {
                $this->handCardsColorAndNumCnt[$cardInfo['type']][$cardInfo['num']] = 0;
            }
            $this->handCardsColorAndNumCnt[$cardInfo['type']][$cardInfo['num']] += 1;

            $colorIndex = RUMMY_COLOR_INDEX[$cardInfo['type']];
            //执行顺子填充
            if (!isset($this->formatSeqHandCards[$colorIndex])) {
                $this->formatSeqHandCards[$colorIndex] = str_pad(0, 13, 0);
            }
            $this->formatSeqHandCards[$colorIndex][$cardInfo['num'] - 1] = 1;
            //执行set填充
            if (!isset($this->formatSetHandCards[$cardInfo['num']])) {
                $this->formatSetHandCards[$cardInfo['num']] = str_pad(0, 4, 0);
            }
            $this->formatSetHandCards[$cardInfo['num']][$colorIndex] = 1;
        }
    }


    //初始化数据信息
    private function initFormatHandCards()
    {
        $this->xCnt = 0;
        //格式化的seq手牌
        $this->formatSeqHandCards = [];
        //格式化的set手牌
        $this->formatSetHandCards = [];
        //手牌点数 花色数量
        $this->handCardsColorAndNumCnt = [];
        //手里所有的癞子牌
        $this->handCarsAllXcards = [];
    }
}

?>

