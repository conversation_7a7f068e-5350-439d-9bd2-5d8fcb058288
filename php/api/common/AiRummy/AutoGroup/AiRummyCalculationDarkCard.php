<?php

namespace common\AiRummy\AutoGroup;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use common\AiRummy\AiRummyGroupCard;

trait AiRummyCalculationDarkCard
{
    use AiRummyFormatHandCards;


    //扶植 需求牌计算
// 需求牌计算方法
// 在AI没有真顺时
//      1、枚举4个花色每一张1~13，判断其左右各是否能和剩余散牌组成纯顺，如果能，则该张为需求牌
// 在AI有真顺时，但不满足基胡条件时
//      1、枚举4个花色每一张1~13，判断其左右各是否能和剩余散牌组成纯顺，如果能，则该张为需求牌
//      2、癞子牌
// 在AI满足基胡条件，且剩余算分单排大于2张时
//      1、枚举4个花色每一张1~13，判断其左右上下是否能和剩余散牌组成纯顺或set，如果能，则该张为需求牌
//      2、癞子牌
// 在AI满足基胡条件，且剩余算分单排小于等于2张时
//      1、枚举4个花色每一张1~13，判断其左右上下是否能和剩余散牌组成纯顺或set，如果能，则该张为需求牌
//      2、癞子牌
//      3、已有成组牌的延长牌
//
//从所有需求牌中，从剩余牌堆里随机一张即可，如果剩余牌堆没有，则放弃喂牌
    //计算暗牌
    private function calculationDarkCard($cls, $handCards, $pool, $xcard, $controlType = "up")
    {
        if (!in_array($controlType, ['up', 'down'])) {
            return "";
        }
        $calculationDarkAllXCardList = get_all_gui_lai_cards_by_card($xcard);
        if ($cls == Common::GAME_TYPE_RUMMY_10_CARD) {
            $gameType = Common::GAME_TYPE_10_CARD;
        } else {
            $gameType = Common::GAME_TYPE_13_CARD;
        }
        $obj = new AiRummyGroupCard($handCards, $xcard, $gameType);
        //检测是否有胡牌基础
        $huPaiJiChu = $obj->getHuPaiJiChu();
        $groupInfo = $obj->getDealCardsByDarkCardGroup();
        //格式化剩余散牌
        $this->formatHandCards($groupInfo['sanpai'], $xcard);
        //去重牌池
        $pool = array_unique($pool);
        //遍历剩余牌池 检测是否是需求牌
        foreach ($pool as $card) {
            $cardInfo = get_card_info($card);
            $this->checkIsCombinationRealSeq($cardInfo);
            if ($huPaiJiChu) {
                if (count($groupInfo['sanpai']) > 2) {
                    $seqRes = $this->checkIsCombinationRealSeq($cardInfo);
                } else {
                    //格式化剩余散牌
                    $this->formatHandCards($handCards, $xcard);
                    $seqRes = $this->checkIsCombinationRealSeq($cardInfo);
                }
                $setRes = $this->checkIsCombinationRealSet($cardInfo);
                $res = ($seqRes || $setRes);
            } else {
                //有纯顺的情况下 癞子也是需求牌
                if (!empty($groupInfo['group']) && in_array($cardInfo['card'], $calculationDarkAllXCardList)) {
                    $res = true;
                } else {
                    $res = $this->checkIsCombinationRealSeq($cardInfo);
                }
            }

            //根据结果控制
            if ($controlType == 'up' && $res) {
                return $card;
            }
            if ($controlType == 'down' && !$res) {
                return $card;
            }
        }
        return "";
    }


    //校验是否能组成纯顺
    private function checkIsCombinationRealSeq(array $cardInfo): bool
    {
        $res = false;
        if (isset($this->formatSeqHandCards[RUMMY_COLOR_INDEX[$cardInfo['type']]])) {
            $cardIndexString = $this->formatSeqHandCards[RUMMY_COLOR_INDEX[$cardInfo['type']]];
            $cardIndexString[$cardInfo['num'] - 1] = 1;
            $x = $cardInfo['num'] - 1;
            $cardIndexList = [
                [$x, $x + 1, $x + 2],
                [$x - 1, $x, $x + 1],
                [$x - 1, $x - 2, $x],
            ];
            foreach ($cardIndexList as $k => $group) {
                if (array_sum($group) == 0) {
                    continue;
                }
                $checkString = "000";
                foreach ($group as $key => $index) {
                    //不符合要求
                    if ($index > 13 || $index < -2) {
                        break;
                    }
                    if ($index <= 0) {
                        $index = $index + 13;
                    }
                    if ($cardIndexString[$index] != 1) {
                        break;
                    }
                    $checkString[$key] = 1;
                }
                if ($checkString == '111') {
                    $res = true;
                    break;
                }
            }
        }
        return $res;

    }

    //校验是否能组成set
    private function checkIsCombinationRealSet(array $cardInfo): bool
    {
        $res = false;
        if (isset($this->formatSetHandCards[$cardInfo['num']])) {
            $setString = $this->formatSetHandCards[$cardInfo['num']];
            $colorIndex = RUMMY_COLOR_INDEX[$cardInfo['type']];
            $setString[$colorIndex] = 1;
            $checkCnt = 0;
            foreach (RUMMY_COLOR_INDEX as $index) {
                if ($setString[$index] == 0) {
                    continue;
                }
                $checkCnt += 1;
            }
            if ($checkCnt == 3 || $checkCnt == 4) {
                $res = true;
            }
        }
        return $res;
    }




}

?>

