<?php
/**
 * @todo   ai version 1 组排 出牌模拟算法
 * <AUTHOR>
 *
 */


namespace common\AiRummy\AutoGroup;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;

class AiRummyAutoGroup
{
    use AiRummyFormatHandCards, AiRummyFindSeqReal, AiRummyFindSeqFake, AiRummyFindSetFake, AiRummyFindSetReal, AiRummyCombinations;
    //手牌
    private $handCards;
    //癞子牌
    private $xcard;

    //执行手牌
    private $dealCardsGroup = [];

    public function __construct($handCards, $xcard)
    {
        $this->handCards = $handCards;
        $this->xcard = $xcard;
        //格式化手牌
        $this->formatHandCards($this->handCards, $this->xcard);

        //找出所有有效的组合
        $realSeqList = $this->findAllRealSeq($this->formatSeqHandCards);
        $fakeSeqList = $this->findAllFakeSeq($this->formatSeqHandCards, $this->xCnt);
        $realSetList = $this->findAllRealSet($this->formatSetHandCards);
        $fakeSetList = $this->findAllFakeSet($this->formatSetHandCards, $this->xCnt);
        $list = array_values(array_merge($realSeqList, $fakeSeqList, $realSetList, $fakeSetList));
        $keyList = array_keys($list);
        $combinationsList = $this->getAllCombinations($this->handCards, $this->xCnt, $this->handCarsAllXcards, $keyList, $list);
        $this->setDeclearCardsGroup($combinationsList);
        var_dump($combinationsList);
        die;
    }


    //找出最优的组合
    private function setDeclearCardsGroup($combinationsList)
    {
        if (empty($combinationsList)) {
            $this->dealCardsGroup = $this->handCards;
        }
        //找出有基胡条件的
        foreach ($combinationsList as $val) {
            //优先有胡牌基础的

            //在有胡牌基础的情况下 优先使用分数组合最多的

        }
    }

}

?>

