<?php

namespace common\AiRummy\AutoGroup;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;

use Exception;

trait AiRummyCombinations
{
    /**
     * 求出所有有效的排列组合
     * @param array $handCars
     * @param int $xcnt
     * @param $combinationsList
     * @param $groupList
     * @return array
     */
    private function getAllCombinations(array $handCars, int $xcnt, array $handCarsAllXcards, $combinationsList, $groupList): array
    {
        $combinationsList = array_unique($combinationsList);
        $results = [[]];
        $data = [];
        foreach ($combinationsList as $combinations) {
            foreach ($results as $result) {
                $combinationsTmp = array_merge([$combinations], $result);
                $combinationsInfo = $this->getCombinationsInfo($handCars, $xcnt, $handCarsAllXcards, $combinationsTmp, $groupList);
                if (!$combinationsInfo) {
                    break;
                }
                $results[] = $combinationsTmp;
                $data[] = $combinationsInfo;
            }
        }
        return array_values($data);
    }



    //根据组合的id获取group汇总信息
    private function getCombinationsInfo(array $handCars, int $xcnt, array $handCarsAllXcards, array $combinations, array $groupList): array
    {
        //已经使用的癞子数量
        $usedXcardCnt = 0;
        //真顺数量
        $realSeqCnt = 0;
        //假顺数量
        $fakeSeqCnt = 0;
        try {
            //返回数据
            $rtn = [
                'cardCnt' => 0,
                //已组合的手牌数量
                'xCardCnt' => 0,
                //已使用癞子数量
                'cardList' => [],
                //已经组合的手牌
                'groupList' => [],
                //已经组合的组合
                'hasJiHu' => false, //是否有基础胡牌条件
            ];
            foreach ($combinations as $index) {
                $rtn['cardCnt'] += $groupList[$index]['cardsCnt'];
                //操作了手牌数量
                if ($rtn['cardCnt'] > count($handCars)) {
                    throw new Exception($rtn['cardCnt'] . '>' . count($handCars));
                }
                //获取假顺数量
                if ($groupList[$index]['groupName'] == 'fakeSeql') {
                    $fakeSeqCnt += 1;
                }
                //获取真顺数量
                if ($groupList[$index]['groupName'] == 'realSeql') {
                    $realSeqCnt += 1;
                }
                $rtn['groupList'][] = $groupList[$index];
                foreach ($groupList[$index]['cards'] as $card) {
                    $rtn['cardList'][] = $card;
                    //检测癞子使用情况
                    if ($usedXcardCnt > $xcnt) {
                        //癞子数量不匹配 终止循环
                        throw new Exception('$usedXcardCnt>$allXcardCnt:' . $usedXcardCnt . '>' . $xcnt);
                    }
                    //校验癞子使用情况
                    if ($card == G_CARD) {
                        $usedXcardCnt++;
                        continue;
                    }
                    //校验手牌占用情况
                    $key = array_search($card, $handCars);
                    if ($key !== false) {
                        unset($handCars[$key]);
                        if (in_array($card, $handCarsAllXcards)) {
                            $usedXcardCnt++;
                        }
                    } else {
                        //有不匹配的牌 终止循环
                        throw new Exception('$card不在$handCards里:' . $card . ' not in ' . json_encode($handCars));
                    }
                }
            }
            $rtn['usedXcardCnt'] = $usedXcardCnt;
            //判定是否有基础胡牌条件
            if ($realSeqCnt >= 2 || ($realSeqCnt >= 1 && $fakeSeqCnt >= 1)) {
                $rtn['hasJiHu'] = true;
            }
        } catch (Exception $e) {
            $rtn = [];
        }
        return $rtn;
    }

}

?>

