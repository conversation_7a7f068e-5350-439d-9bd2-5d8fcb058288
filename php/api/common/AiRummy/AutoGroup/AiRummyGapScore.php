<?php

namespace common\AiRummy\AutoGroup;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;

trait AiRummyGapScore
{
    //
//    分数计算方式
//    1.计算纯顺
//        3张纯顺 30分
//        4张纯顺 35分
//        5张纯顺 40分
//    2.癞子
//        存在纯顺中的癞子   每张4分
//        不算在纯顺中的癞子 每张10分
//    3、缺口牌每张2分


    /**
     * @param array $realSeqList
     * @param array $gapCardList
     * @param int $allHandXcardCnt
     * @return int
     */
    private function setGapScore(array $realSeqList, array $gapCardList, int $usedXcardCnt, int $allHandXcardCnt): string
    {
        $score = 0;
        if (!empty($realSeqList)) {
            foreach ($realSeqList as $val) {
                $score += RUMMY_GAP_SCORE['realSeqScore'][$val['cardsCnt']];
            }
        }
        $score += (count($gapCardList) * RUMMY_GAP_SCORE['normalCardScore']);
        $score += ($usedXcardCnt * RUMMY_GAP_SCORE['usedXcardScore']);
        $score += (($allHandXcardCnt - $usedXcardCnt) * RUMMY_GAP_SCORE['notUsedXcardScore']);
        return $score;
    }

}

?>

