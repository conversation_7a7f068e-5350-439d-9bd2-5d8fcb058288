<?php
/**
 * @todo   ai version 1 组排 出牌模拟算法
 * <AUTHOR>
 *
 */


namespace common\AiRummy\AutoGroup;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;

use Exception;
use lib\Log;

class AiRummyAutoGroupV3
{

    //点数对应的分值
    const CARD_SCORE = [0, 10, 2, 3, 4, 5, 6, 7, 8, 9, 10, 10, 10, 10, 0, 0];

    const POKER = [
        // 方块 1 - k
        // 1,2,3,4,5,6,7,8,9,10,11,12,13
        [0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D],
        // 梅花 1 - k
        // 17 18 19 20 21 22 23 24 25 26 27 28 29
        [0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D],
        // 红桃 1 - k
        // 33 34 35 36 37 38 39 40 41 42 43 44 45
        [0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C, 0x2D],
        // 黑桃 1 - k
        // 49 50 51 52 53 54 55 56 57 58 59 60 61
        [0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C, 0x3D],
        // 79 79 大小王一致
        [0x4F, 0x4F]
    ];


    //花色索引
    const RUMMY_COLOR_INDEX = ['fk' => 0, 'mh' => 1, 'hht' => 2, 'ht' => 3];

    //rummy所有纯顺模板
    const RUMMY_REAL_SEQ_TMP = [
        '0000000000111',
        '0000000001110',
        '0000000011100',
        '0000000111000',
        '0000001110000',
        '0000011100000',
        '0000111000000',
        '0001110000000',
        '0011100000000',
        '0111000000000',
        '1110000000000',
        '1000000000011',
        '0000000001111',
        '0000000011110',
        '0000000111100',
        '0000001111000',
        '0000011110000',
        '0000111100000',
        '0001111000000',
        '0011110000000',
        '0111100000000',
        '1111000000000',
        '1000000000111',
        '0000000011111',
        '0000000111110',
        '0000001111100',
        '0000011111000',
        '0000111110000',
        '0001111100000',
        '0011111000000',
        '0111110000000',
        '1111100000000',
        '1000000001111',
    ];
    //rummy所有混顺模板  一个混  混3 混4 混5
    const RUMMY_FAKE_SEQ_1XCARD_TMP = [
        '0000000000110',
        '0000000000101',
        '0000000000011',
        '0000000001100',
        '0000000001010',
        '1000000001110',
        '0000000010100',
        '0000000110000',
        '0000000101000',
        '0000000011000',
        '0000001100000',
        '0000001010000',
        '0000011000000',
        '0000010100000',
        '0000101000000',
        '0001100000000',
        '0001010000000',
        '0000110000000',
        '0011000000000',
        '0010100000000',
        '0110000000000',
        '0101000000000',
        '1100000000000',
        '1010000000000',
        '1000000000001',
        '1000000000010',
        '0000000001011',
        '0000000001101',
        '0000000010110',
        '0000000011010',
        '0000000101100',
        '0000000110100',
        '0000001011000',
        '0000001101000',
        '0000010110000',
        '0000011010000',
        '0000101100000',
        '0000110100000',
        '0001011000000',
        '0001101000000',
        '0010110000000',
        '0011010000000',
        '0101100000000',
        '0110100000000',
        '1011000000000',
        '1101000000000',
        '1000000000110',
        '1000000000101',
        '0000000010111',
        '0000000011011',
        '0000000011101',
        '0000000101110',
        '0000000110110',
        '0000000111010',
        '0000001011100',
        '0000001101100',
        '0000001110100',
        '0000010111000',
        '0000011011000',
        '0000011101000',
        '0000101110000',
        '0000110110000',
        '0000111010000',
        '0001011100000',
        '0001101100000',
        '0001110100000',
        '0010111000000',
        '0011011000000',
        '0011101000000',
        '0101110000000',
        '0110110000000',
        '0111010000000',
        '1011100000000',
        '1101100000000',
        '1110100000000',
        '1000000001011',
        '1000000001101',
    ];

    //rummy所有混顺模板  2个混  混3 混4 混5
    const RUMMY_FAKE_SEQ_2XCARD_TMP = [
        '0000000000001',
        '0000000000010',
        '0000000000100',
        '0000000001000',
        '0000000010000',
        '0000000100000',
        '0000001000000',
        '0000010000000',
        '0000100000000',
        '0001000000000',
        '0010000000000',
        '0100000000000',
        '1000000000000',
        '0000000001001',
        '0000000010010',
        '0000000100100',
        '0000001001000',
        '0000010010000',
        '0000100100000',
        '1000000001010',
        '0001001000000',
        '0010010000000',
        '0100100000000',
        '1001000000000',
        '1000000000100',
        '0000000010011',
        '0000000011001',
        '0000000010101',
        '0000000100110',
        '0000000110010',
        '0000000101010',
        '0000001001100',
        '0000001100100',
        '0000001010100',
        '0000010011000',
        '0000011001000',
        '0000010101000',
        '0000100110000',
        '0000110010000',
        '0000101010000',
        '0001001100000',
        '0001100100000',
        '0001010100000',
        '0010011000000',
        '0011001000000',
        '0010101000000',
        '0100110000000',
        '0110010000000',
        '0101010000000',
        '1001100000000',
        '1100100000000',
        '1010100000000',
        '1000000001001',
        '1000000001100',
    ];

    //rummy所有真set模板
    const RUMMY_REAL_SET_TMP = [
        '0111',
        '1011',
        '1101',
        '1110',
        '1111',
    ];

    //rummy所有混set模板
    const RUMMY_FAKE_SET_1XCARD_TMP = [
        '1100',
        '0110',
        '0011',
        '1010',
        '0101',
        '1001',
    ];

    //rummy所有混set模板
    const RUMMY_FAKE_SET_2XCARD_TMP = [
        '1000',
        '0100',
        '0001',
    ];



    //手牌
    private $handCards;
    //癞子牌
    private $xcard;
    //癞子牌
    //找出癞子数量
    private $xCnt;
    //手牌里所有的癞子牌
    private $handCarsAllXcards = [];

    //格式化的seq手牌
    private $formatSeqHandCards = [];
    //格式化的set手牌
    private $formatSetHandCards = [];

    //手牌点数 花色数量
    private $handCardsColorAndNumCnt = [];

    //执行手牌
    private $dealCardsGroup = [];


    public function __construct($handCards, $xcard)
    {

        $this->handCards = $handCards;
        $this->xcard = $xcard;

        //格式化手牌
        $this->formatHandCards();

        //获取手牌癞子数量
        $this->xCnt = $this->findXCardCnt();

        //找出所有有效的组合
        $realSeqList = $this->findAllRealSeq();
        $fakeSeqList = $this->findAllFakeSeq();
        $setList = $this->findAllSet();
        $list = array_values(array_merge($realSeqList, $fakeSeqList, $setList));
        $keyList = array_keys($list);
        $combinationsList = $this->getAllCombinations($keyList, $list);
        $this->setDeclearCardsGroup($combinationsList);
        var_dump($combinationsList);
        die;
    }

    //找出最优的组合
    private function setDeclearCardsGroup($combinationsList)
    {
        if (empty($combinationsList)) {
            $this->dealCardsGroup = $this->handCards;
        }
        //找出有基胡条件的
        foreach ($combinationsList as $val) {
            //优先有胡牌基础的

            //在有胡牌基础的情况下 优先使用分数组合最多的


        }
    }

    //根据组合的id获取group汇总信息
    private function getCombinationsInfo($combinations, $list)
    {
        //校验手牌是否完整
        $handCars = $this->handCards;
        //已经组合的手牌数量
        //手牌里拥有癞子的数量
        $allXcardCnt = $this->xCnt;
        //已经使用的癞子数量
        $usedXcardCnt = 0;
        //真顺数量
        $realSeqCnt = 0;
        //假顺数量
        $fakeSeqCnt = 0;
        try {
            //返回数据
            $rtn = [
                'cardCnt' => 0,
                //已组合的手牌数量
                'cardList' => [],
                //已经组合的手牌
                'groupList' => [],
                //已经组合的组合
                'hasJiHu' => false, //是否有基础胡牌条件
            ];
            foreach ($combinations as $index) {
                $rtn['cardCnt'] += $list[$index]['cardsCnt'];
                //操作了手牌数量
                if ($rtn['cardCnt'] > count($handCars)) {
                    throw new Exception($rtn['cardCnt'] . '>' . count($handCars));
                }
                //获取假顺数量
                if ($list[$index]['groupName'] == 'fakeSeql') {
                    $fakeSeqCnt += 1;
                }
                //获取真顺数量
                if ($list[$index]['groupName'] == 'realSeql') {
                    $realSeqCnt += 1;
                }
                $rtn['groupList'][] = $list[$index];
                foreach ($list[$index]['cards'] as $card) {
                    $rtn['cardList'][] = $card;
                    //检测癞子使用情况
                    if ($usedXcardCnt > $allXcardCnt) {
                        //癞子数量不匹配 终止循环
                        throw new Exception('$usedXcardCnt>$allXcardCnt:' . $usedXcardCnt . '>' . $allXcardCnt);
                    }
                    //校验癞子使用情况
                    if ($card == G_CARD) {
                        $usedXcardCnt++;
                        continue;
                    }
                    //校验手牌占用情况
                    $key = array_search($card, $handCars);
                    if ($key !== false) {
                        unset($handCars[$key]);
                        if (in_array($card, $this->handCarsAllXcards)) {
                            $allXcardCnt -= 1;
                        }
                    } else {
                        //有不匹配的牌 终止循环
                        throw new Exception('$card不在$handCards里:' . $card . ' not in ' . json_encode($handCars));
                    }
                }
            }
            //判定是否有基础胡牌条件
            if ($realSeqCnt >= 2 || ($realSeqCnt >= 1 && $fakeSeqCnt >= 1)) {
                $rtn['hasJiHu'] = true;
            }
        } catch (Exception $e) {
            $rtn = [];
        }
        return $rtn;
    }

    //求出所有有效的排列组合
    private function getAllCombinations(array $combinationsList, $list): array
    {
        $combinationsList = array_unique($combinationsList);
        $results = [[]];
        $data = [];
        foreach ($combinationsList as $combinations) {
            foreach ($results as $result) {
                $combinationsTmp = array_merge([$combinations], $result);
                $combinationsInfo = $this->getCombinationsInfo($combinationsTmp, $list);
                if (!$combinationsInfo) {
                    break;
                }
                $results[] = $combinationsTmp;
                $data[] = $combinationsInfo;
            }
        }
        return array_values($data);
    }



    //格式化手牌按照花色和点数
    private function formatHandCards()
    {
        //去重
        $handsCards = array_unique($this->handCards);
        foreach ($handsCards as $card) {
            if ($card == G_CARD) {
                if (!isset($this->handCardsColorAndNumCnt[G_CARD])) {
                    $this->handCardsColorAndNumCnt[G_CARD] = 0;
                }
                $this->handCardsColorAndNumCnt[G_CARD] += 1;
                continue;
            }
            $cardInfo = get_card_info($card);
            //花色以及点数手牌数量
            if (!isset($this->handCardsColorAndNumCnt[$cardInfo['type']][$cardInfo['num']])) {
                $this->handCardsColorAndNumCnt[$cardInfo['type']][$cardInfo['num']] = 0;
            }
            $this->handCardsColorAndNumCnt[$cardInfo['type']][$cardInfo['num']] += 1;

            $colorIndex = self::RUMMY_COLOR_INDEX[$cardInfo['type']];
            //执行顺子填充
            if (!$this->formatSeqHandCards[$colorIndex]) {
                $this->formatSeqHandCards[$colorIndex] = str_pad(0, 13, 0);
            }
            $this->formatSeqHandCards[$colorIndex][$cardInfo['num'] - 1] = 1;
            //执行set填充
            if (!$this->formatSetHandCards[$cardInfo['num']]) {
                $this->formatSetHandCards[$cardInfo['num']] = str_pad(0, 4, 0);
            }
            $this->formatSetHandCards[$cardInfo['num']][$colorIndex] = 1;
        }
    }



    //--------------------------------
    //获取手牌所有的癞子
    private function findXCardCnt(): int
    {
        $allXCards = get_all_gui_lai_cards_by_card($this->xcard);
        $handCarsAllXcards = array_intersect($allXCards, $this->handCards);
        $this->handCarsAllXcards = $handCarsAllXcards;
        if ($handCarsAllXcards) {
            return count($handCarsAllXcards);
        } else {
            return 0;
        }
    }

    //根据花色和牌组位置转换为手牌
    private function getCardByColorAndGroup($color, $groupBin, $xcardCnt): array
    {
        $cardList = [];
        for ($i = 0; $i < 13; $i++) {
            if ($groupBin[$i] == 1) {
                $cardList[] = self::POKER[$color][$i];
            }
        }
        if ($cardList && $xcardCnt > 0) {
            for ($i = 0; $i < $xcardCnt; $i++) {
                $cardList[] = G_CARD;
            }
        }
        return $cardList;
    }

    //找出所有真顺
    private function findAllRealSeq(): array
    {
        return $this->grepSeq(self::RUMMY_REAL_SEQ_TMP, 0);
    }

    //找出所有假顺
    private function findAllFakeSeq(): array
    {
        $list = [];
        if ($this->xCnt >= 1) {
            $list = array_merge($list, $this->grepSeq(self::RUMMY_FAKE_SEQ_1XCARD_TMP, 1));
        }
        if ($this->xCnt >= 2) {
            $list = array_merge($list, $this->grepSeq(self::RUMMY_FAKE_SEQ_2XCARD_TMP, 2));
        }
        return $list;
    }

    //查找seql
    private function grepSeq($groupList, $xcardCnt): array
    {
        if ($xcardCnt > 0) {
            $groupName = "fakeSeql";
        } else {
            $groupName = "realSeql";
        }
        $list = [];
        foreach ($this->formatSeqHandCards as $color => $cards) {
            $cards = bindec($cards);
            foreach ($groupList as $groupBin) {
                $group = bindec($groupBin);
                if (($group & $cards) == $group) {
                    //找到了
                    $cardList = $this->getCardByColorAndGroup($color, $groupBin, $xcardCnt);
                    $list[] = ['groupName' => $groupName, 'cards' => $cardList, 'cardsCnt' => count($cardList)];
                }
            }
        }
        return $list;
    }
    //==========================================

    //找出所有set
    private function findAllSet(): array
    {
        $list = $this->grepSet(self::RUMMY_REAL_SET_TMP, 0);
        if ($this->xCnt >= 1) {
            $list = array_merge($list, $this->grepSet(self::RUMMY_FAKE_SET_1XCARD_TMP, 1));
        }
        if ($this->xCnt >= 2) {
            $list = array_merge($list, $this->grepSet(self::RUMMY_FAKE_SET_2XCARD_TMP, 2));
        }
        return $list;
    }

    //查找所有set
    private function grepSet($groupList, $xcardCnt): array
    {
        if ($xcardCnt > 0) {
            $groupName = "fakeSet";
        } else {
            $groupName = "realSet";
        }
        $list = [];
        foreach ($this->formatSetHandCards as $num => $cards) {
            $cards = bindec($cards);
            foreach ($groupList as $group) {
                $group = bindec($group);
                if (($group & $cards) == $group) {
                    //找到了
                    $cardList = $this->getCardByNumAndGroup($num, decbin(($group & $cards)), $xcardCnt);
                    $list[] = ['groupName' => $groupName, 'cards' => $cardList, 'cardsCnt' => count($cardList)];
                }
            }
        }
        return $list;
    }

    //根据花色和牌组位置转换为手牌
    private function getCardByNumAndGroup($num, $groupBin, $xcardCnt): array
    {
        $cardList = [];
        for ($i = 0; $i < 4; $i++) {
            if ($groupBin[$i] == 1) {
                $cardList[] = self::POKER[$i][$num - 1];
            }
        }
        if ($cardList && $xcardCnt > 0) {
            for ($i = 0; $i < $xcardCnt; $i++) {
                $cardList[] = G_CARD;
            }
        }
        //填充癞子标识
        return $cardList;
    }

    //计算出 手牌剩余分数最少的组合
}

?>

