<?php

namespace common\AiRummy\AutoGroup;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;

trait AiRummyFindSetReal
{
    use AiRummyFindSet;

    /**
     * @param array $formatSetHandCards
     * @return array
     */
    private function findAllRealSet(array $formatSetHandCards): array
    {
        return $this->grepSet(RUMMY_REAL_SET_TMP, $formatSetHandCards, 0);
    }

}

?>

