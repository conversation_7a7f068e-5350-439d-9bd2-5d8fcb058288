<?php
/**
 * @todo   ai version 1 组排 出牌模拟算法
 * <AUTHOR>
 *
 */

namespace common\AiRummy\AutoGroup;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;

trait AiRummyFindSeq
{


    /**
     * 查找符合要求的真顺 或者 假顺
     * @param array $groupList   比对模板
     * @param array $formatSeqHandCards  格式化的手牌
     * @param int $xcardCnt  手牌里癞子牌的数量
     * @return array
     */
    private function grepSeq(array $groupList, array $formatSeqHandCards, int $xcardCnt, bool $isFillGuiCard = true): array
    {
        if ($xcardCnt > 0) {
            $groupName = "fakeSeql";
        } else {
            $groupName = "realSeql";
        }
        $list = [];
        foreach ($formatSeqHandCards as $color => $cards) {
            $cards = bindec($cards);
            foreach ($groupList as $groupBin) {
                $group = bindec($groupBin);
                if (($group & $cards) == $group) {
                    //找到了
                    $cardList = $this->getCardByColorAndGroup($color, $groupBin, $xcardCnt, $isFillGuiCard);
                    $list[] = ['groupName' => $groupName, 'cards' => $cardList, 'cardsCnt' => count($cardList)];
                }
            }
        }
        return $list;
    }


    /**
     * 根据花色和牌组位置转换为手牌
     * @param int $color        花色
     * @param string $groupBin
     * @param int $xcardCnt
     * @return array
     */
    private function getCardByColorAndGroup(int $color, string $groupBin, int $xcardCnt, bool $isFillGuiCard = true): array
    {
        $cardList = [];
        for ($i = 0; $i < 13; $i++) {
            if ($groupBin[$i] == 1) {
                $cardList[] = POKER[$color][$i];
            }
        }
        if ($cardList && $xcardCnt > 0 && $isFillGuiCard) {
            for ($i = 0; $i < $xcardCnt; $i++) {
                $cardList[] = G_CARD;
            }
        }
        return $cardList;
    }
}

?>

