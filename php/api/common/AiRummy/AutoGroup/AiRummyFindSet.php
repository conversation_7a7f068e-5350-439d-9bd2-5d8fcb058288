<?php

namespace common\AiRummy\AutoGroup;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;

trait AiRummyFindSet
{

    //查找所有set
    private function grepSet(array $groupList, array $formatSetHandCards, int $xcardCnt): array
    {
        if ($xcardCnt > 0) {
            $groupName = "fakeSet";
        } else {
            $groupName = "realSet";
        }
        $list = [];
        foreach ($formatSetHandCards as $num => $cards) {
            $cards = bindec($cards);
            foreach ($groupList as $group) {
                $group = bindec($group);
                if (($group & $cards) == $group) {
                    //找到了
                    $cardList = $this->getCardByNumAndGroup($num, decbin(($group & $cards)), $xcardCnt);
                    $list[] = ['groupName' => $groupName, 'cards' => $cardList, 'cardsCnt' => count($cardList)];
                }
            }
        }
        return $list;
    }


    //根据花色和牌组位置转换为手牌
    private function getCardByNumAndGroup($num, $groupBin, $xcardCnt): array
    {
        $cardList = [];
        for ($i = 0; $i < 4; $i++) {
            if ($groupBin[$i] == 1) {
                $cardList[] = POKER[$i][$num - 1];
            }
        }
        if ($cardList && $xcardCnt > 0) {
            for ($i = 0; $i < $xcardCnt; $i++) {
                $cardList[] = G_CARD;
            }
        }
        //填充癞子标识
        return $cardList;
    }
}

?>

