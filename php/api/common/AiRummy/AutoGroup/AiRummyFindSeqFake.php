<?php

namespace common\AiRummy\AutoGroup;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;


trait AiRummyFindSeqFake
{

    use AiRummyFindSeq;

    //找出所有假顺
    private function findAllFakeSeq(array $formatSeqHandCards, int $xcnt, bool $isFillGuiCard = true): array
    {
        $list = [];
        if ($xcnt >= 1) {
            $list = array_merge($list, $this->grepSeq(RUMMY_FAKE_SEQ_1XCARD_TMP, $formatSeqHandCards, 1, $isFillGuiCard));
            $list = array_merge($list, $this->grepSeq(RUMMY_FAKE_SEQ_1XCARD_3SEQ_TMP, $formatSeqHandCards, 1, $isFillGuiCard));
        }
        if ($xcnt >= 2) {
            $list = array_merge($list, $this->grepSeq(RUMMY_FAKE_SEQ_2XCARD_TMP, $formatSeqHandCards, 2, $isFillGuiCard));
        }
        return $list;
    }

    //找出所有3连假顺
    private function findFake3Seq1Xcard(array $formatSeqHandCards, bool $isFillGuiCard = true): array
    {
        return $this->grepSeq(RUMMY_FAKE_SEQ_1XCARD_3SEQ_TMP, $formatSeqHandCards, 1, $isFillGuiCard);
    }
}

?>

