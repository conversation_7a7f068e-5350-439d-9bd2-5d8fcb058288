<?php
/**
 * @todo   AI摸牌判定
 * <AUTHOR>
 *
 */


namespace common\AiRummy;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;

class AiRummyDisCard extends AiRummyGroupCard
{
    //手牌信息
    protected $handCards = [];
    //本局的癞子牌
    protected $lCard;
    //本局的牌数  13rummy 还是 10rummy
    protected $maxCard;
    //组好的牌
    protected $groupList = [];
    //是否能够胡牌
    private $canFinish = false;
    //需要打掉的牌
    private $disCard = '';
    //如果出现如下牌型 则不能胡牌
    private $notFinishGroupList = [
        'group_2_lian_2_dui',
        'group_bian_jia_2_dui',
        'group_2_lian_dui',
        'group_bian_jia_dui',
        'group_2_lian',
        'group_bian_jia',
        'group_dui'
    ];
    //实例化手牌和癞子
    public function __construct($handCards, $lCard, $maxCard)
    {
        parent::__construct($handCards, $lCard, $maxCard);
        $this->handCards = $handCards;
        $this->lCard = $lCard;
        $this->maxCard = $maxCard;
        //执行组排
        $this->disCardRun();
        //执行log写入
        //Log::console_object_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, json_encode(object_to_array($this)));
    }
    //执行打牌操作
    private function disCardRun()
    {
        $this->checkHuPai();
        $this->initSendCard();
    }
    //获取打掉的牌
    public function getDisCard()
    {
        return $this->disCard;
    }
    //获取是否能够胡牌
    public function getCanFinishRes()
    {
        return $this->canFinish;
    }

    //检测是否能够胡牌 并打掉一张牌
    private function checkHuPai()
    {
        //如果没有基础胡牌条件 不执行此操作
        if (!$this->hupaiJichu) {
            return;
        }
        //手里还有其他的非顺子 和 条子的牌
        $cnt = 0;
        foreach ($this->groupList as $key => $val) {
            if (in_array($key, $this->notFinishGroupList)) {
                $cnt += count_two_dimensional_array($val);
            }
        }
        if ($cnt > 0) {
            return;
        }

        $sanpaiCnt = isset($this->groupList['group_san_pai']) ? count_one_dimensional_array($this->groupList['group_san_pai']) : 0;
        $laiZiGuiPaiCnt = isset($this->groupList['group_laizi_guipai']) ? count_one_dimensional_array($this->groupList['group_laizi_guipai']) : 0;
        //1、手里的单牌数量只有一张 且没有鬼牌或者癞子在手里  需要打出去的那张
        //手里有2张散牌 且癞子数量大于2张
        if (($sanpaiCnt == 1 && $laiZiGuiPaiCnt == 0) || ($sanpaiCnt == 2 && $laiZiGuiPaiCnt >= 2)) {
            //打手牌胡牌了
            $this->canFinish = true;
            //Log::console_log(__METHOD__ . '[' . __LINE__ . ']', '手里的单牌数量只有一张 且没有鬼牌或者癞子在手里  需要打出去的那张' . json_encode($this->canFinish));
            return;
        }

        //如果手里没有手牌 只有一张癞子鬼牌直接打掉
        if ($sanpaiCnt == 0 && $laiZiGuiPaiCnt >= 1) {
            //手牌放进一张癞子鬼牌
            //array_push($this->groupList['group_san_pai'],array_pop($this->groupList['group_laizi_guipai']));
            //其余散列到分组里
            //20210731修改 胡牌不打赖子牌
            $this->setLGcardToGroup(true);
            //Log::console_log(__METHOD__ . '[' . __LINE__ . ']', '如果手里没有手牌 只有一张癞子鬼牌直接打掉');
            return;
        }

        //4、手里有一张单牌  有鬼和癞子
        if ($sanpaiCnt == 1 && $laiZiGuiPaiCnt >= 1) {
            //其余散列到分组里
            $this->setLGcardToGroup();
            //Log::console_log(__METHOD__ . '[' . __LINE__ . ']', '手里有一张单牌  有鬼和癞子');
            return;
        }

        //3、手里没有单牌 没有癞子鬼牌
        if ($sanpaiCnt <= 0 && $laiZiGuiPaiCnt <= 0) {
            $this->popCardFromGroupToHuPai();
            //Log::console_log(__METHOD__ . '[' . __LINE__ . ']', '手里没有单牌 没有癞子鬼牌');
            return;
        }


    }
    //处理剩余的癞子鬼牌 到分组里
    //是否需要替换一张非癞子的牌
    private function setLGcardToGroup($isDisOneNotIsLCard = false)
    {
        $canSetHunGroupKeyList = array('group_hun_shun_3_lian', 'group_hun_shun_4_lian', 'group_hun_shun_5_lian');
        $canSetChunGroupKeyList = array('group_chun_shun_3_lian', 'group_chun_shun_4_lian', 'group_chun_shun_5_lian');


        //合并到剩余手牌中 处理不带base下标 下次摸牌会重新排列
        foreach ($this->groupList as $key => $val) {
            if ((in_array($key, $canSetHunGroupKeyList) && count_two_dimensional_array($val) >= 1) || (in_array($key, $canSetChunGroupKeyList) && count_two_dimensional_array($val) >= 2)) {
                $this->groupList[$key] = array_filter($this->groupList[$key]);
                $baseKey = array_key_first($this->groupList[$key]);
                $tmpKey = array_key_first($this->groupList[$key][$baseKey]);


                if ($isDisOneNotIsLCard == true) {
                    //挑选出一张非癞子的牌
                    foreach ($this->groupList[$key][$baseKey][$tmpKey] as $k => $v) {
                        if (!in_array($v, $this->allLCards)) {
                            array_push($this->groupList['group_san_pai'], $v);
                            unset($this->groupList[$key][$baseKey][$tmpKey][$k]);
                            break;
                        }
                    }
                }

                $this->groupList[$key][$baseKey][$tmpKey] = array_merge_one_dimensional_array($this->groupList[$key][$baseKey][$tmpKey], $this->groupList['group_laizi_guipai']);
                $this->groupList['group_laizi_guipai'] = [];
                $this->canFinish = true;
                break;
            }
        }
        return;
    }
    //处理胡牌 从高的张数分组里挤出一张非癞子鬼牌的牌
    private function popCardFromGroupToHuPai()
    {
        //已经到达胡牌阶段 无需理会牌型组成
        $canPopGroupKeyList = array('group_chun_shun_4_lian', 'group_chun_shun_5_lian', 'group_chun_4_tiao', 'group_hun_shun_5_lian', 'group_hun_shun_4_lian');
        foreach ($this->groupList as $key => $val) {
            if (in_array($key, $canPopGroupKeyList) && count_two_dimensional_array($val) >= 1) {
                $this->groupList[$key] = array_filter($this->groupList[$key]);
                $baseKey = array_key_first($this->groupList[$key]);
                $tmpKey = array_key_first($this->groupList[$key][$baseKey]);
                $this->groupList['group_san_pai'][] = array_pop($this->groupList[$key][$baseKey][$tmpKey]);
                $this->canFinish = true;
                break;
            }
        }
        return;
    }
    //挑选出牌
    private function initSendCard()
    {
        //挑选出牌
        if ($this->hupaiJichu) {
            $this->initSendCardB();
        } else {
            $this->initSendCardA();
        }
    }
    //a策略出牌
    public function initSendCardA()
    {
        //Log::console_log(__FUNCTION__, 'AI 执行A策略');
        $this->initSendDanCard();
        $this->initSendDuiCard();
        $this->initSendBianjiaCard();
        $this->initSendBianJiaDuiA();
        $this->initSendBianJia2DuiCard();
        $this->initSend2LianCard();
        $this->initSend2Lian2DuiCard();
        $this->initSendChun4TiaoCard();
        $this->initSendChunShun5LianCard();
        $this->initSendChunShun4LianCard();
        $this->initSendHunShun4LianCard();
        $this->initSendHunShun5LianCard();
        $this->initSendChun3TiaoCard();
        $this->initSendHun3LianCard();
    }
    //b策略出牌
    public function initSendCardB()
    {
        //Log::console_log(__FUNCTION__, 'AI 执行B策略');
        $this->initSendDanCard();
        $this->initSendBianjiaCard();
        $this->initSendDuiCard();
        $this->initSend2LianCard();
        $this->initSendBianJiaDuiB();
        $this->initSend2LianDui();
        $this->initSendBianJia2DuiCard();
        $this->initSend2Lian2DuiCard();
        $this->initSendChunShun5LianCard();
        $this->initSendChunShun4LianCard();
        $this->initSendChun4TiaoCard();
        $this->initSendHunShun4LianCard();
        $this->initSendHunShun5LianCard();
        $this->initSendHun3TiaoCard();
        $this->initSendChun3TiaoCard();
    }
    //挑选分值最大的单牌
    public function initSendDanCard()
    {
        if (count_one_dimensional_array($this->groupList['group_san_pai']) > 0) {
            $this->groupList['group_san_pai'] = sort_rymmy_cards_asc($this->groupList['group_san_pai']);
            if (!isset($this->groupList['group_san_pai'][0]) || empty($this->groupList['group_san_pai'][0])) {
                return;
            }
            //判断第一张牌是否是A 如果是先打A 再打其他
            if (get_card_info($this->groupList['group_san_pai'][0])['num'] == 1) {
                $disCard = array_shift($this->groupList['group_san_pai']);
            } else {
                $disCard = array_pop($this->groupList['group_san_pai']);
            }
            $this->disCard = $disCard;
            $cardInfo = get_card_info($disCard);
            //Log::console_log(__FUNCTION__, 'AI 执行打单牌' . $cardInfo['text_type'] . $cardInfo['num']);
        } else {
            //Log::console_log(__FUNCTION__, 'AI 执行打单牌 无可以打的单牌');
        }

    }
    //挑选分值最大的边夹
    public function initSendBianjiaCard()
    {
        if ($this->disCard || !isset($this->groupList['group_bian_jia'])) {
            return;
        }
        $this->groupList['group_bian_jia'] = array_filter($this->groupList['group_bian_jia']);
        if (count_two_dimensional_array($this->groupList['group_bian_jia']) > 0) {
            $tmpKey = array_key_first($this->groupList['group_bian_jia']);
            $tmp = array_shift($this->groupList['group_bian_jia'][$tmpKey]);
            //牌型整理 降为2张单牌 放入剩余手牌中
            $this->groupList['group_san_pai'] = array_merge_one_dimensional_array($this->groupList['group_san_pai'], $tmp);
            //Log::console_log(__FUNCTION__, 'AI 执行打单牌 initSendBianjiaCard 执行拆牌');
            $this->initSendDanCard();
        }
    }
    //挑选分值最大的一对
    public function initSendDuiCard()
    {
        if ($this->disCard || !isset($this->groupList['group_dui'])) {
            return;
        }
        $this->groupList['group_dui'] = array_filter($this->groupList['group_dui']);
        if (count_two_dimensional_array($this->groupList['group_dui']) > 0) {
            $tmpKey = array_key_first($this->groupList['group_dui']);
            $tmp = array_shift($this->groupList['group_dui'][$tmpKey]);
            //牌型整理 降为2张单牌
            $this->groupList['group_san_pai'] = array_merge_one_dimensional_array($this->groupList['group_san_pai'], $tmp);
            //Log::console_log(__FUNCTION__, 'AI 执行打单牌 initSendDuiCard 执行拆牌');
            $this->initSendDanCard();
        }
    }
    //挑选分值最大的2连
    public function initSend2LianCard()
    {
        if ($this->disCard || !isset($this->groupList['group_2_lian'])) {
            return;
        }
        $this->groupList['group_2_lian'] = array_filter($this->groupList['group_2_lian']);
        if (count_two_dimensional_array($this->groupList['group_2_lian']) > 0) {
            $tmpKey = array_key_first($this->groupList['group_2_lian']);
            $tmp = array_shift($this->groupList['group_2_lian'][$tmpKey]);

            //牌型整理 降为2张单牌
            $this->groupList['group_san_pai'] = array_merge_one_dimensional_array($this->groupList['group_san_pai'], $tmp);
            //Log::console_log(__FUNCTION__, 'AI 执行打单牌 initSend2LianCard 执行拆牌');
            $this->initSendDanCard();
        }
    }
    //挑选分值最大的边夹2对
    public function initSendBianJia2DuiCard()
    {
        if ($this->disCard || !isset($this->groupList['group_bian_jia_2_dui'])) {
            return '';
        }
        $this->groupList['group_bian_jia_2_dui'] = array_filter($this->groupList['group_bian_jia_2_dui']);
        if (count_two_dimensional_array($this->groupList['group_bian_jia_2_dui']) > 0) {
            //取出边夹 放入剩余的牌中
            $tmpKey = array_key_first($this->groupList['group_bian_jia_2_dui']);
            $tmp = array_shift($this->groupList['group_bian_jia_2_dui'][$tmpKey]);
            //牌型整理 降为边夹
            $this->groupList['group_san_pai'] = array_merge_one_dimensional_array($this->groupList['group_san_pai'], $tmp);


            //降为2连对
            $this->initBianJiaDui(100, true);
            //Log::console_log(__FUNCTION__, 'AI 执行打单牌 initSendBianJia2DuiCard 执行拆牌');
            $this->initSendDanCard();
        } else {
            return '';
        }

    }
    //挑选分值最大的2连2对
    public function initSend2Lian2DuiCard()
    {
        if ($this->disCard || !isset($this->groupList['group_2_lian_2_dui'])) {
            return '';
        }
        $this->groupList['group_2_lian_2_dui'] = array_filter($this->groupList['group_2_lian_2_dui']);
        if (count_two_dimensional_array($this->groupList['group_2_lian_2_dui']) > 0) {

            //取出边夹 放入剩余的牌中
            $tmpKey = array_key_first($this->groupList['group_2_lian_2_dui']);
            $tmp = array_shift($this->groupList['group_2_lian_2_dui'][$tmpKey]);
            //牌型整理 降为边夹
            $this->groupList['group_san_pai'] = array_merge_one_dimensional_array($this->groupList['group_san_pai'], $tmp);
            //降为2连对
            $this->init2LianDui(100, true);
            //Log::console_log(__FUNCTION__, 'AI 执行打单牌 initSend2Lian2DuiCard 执行拆牌');
            //处理单牌逻辑
            $this->initSendDanCard();
        }
    }
    //出牌处理边夹对 a策略 留夹子
    public function initSendBianJiaDuiA()
    {
        if ($this->disCard || !isset($this->groupList['group_bian_jia_dui'])) {
            return '';
        }
        $this->groupList['group_bian_jia_dui'] = array_filter($this->groupList['group_bian_jia_dui']);
        if (count_two_dimensional_array($this->groupList['group_bian_jia_dui']) > 0) {

            //取出边夹 放入剩余的牌中
            $tmpKey = array_key_first($this->groupList['group_bian_jia_dui']);
            $tmp = array_shift($this->groupList['group_bian_jia_dui'][$tmpKey]);
            //牌型整理 降为边夹
            $this->groupList['group_san_pai'] = array_merge_one_dimensional_array($this->groupList['group_san_pai'], $tmp);
            //降为边夹
            $this->initBianJia(100, true);
            //Log::console_log(__FUNCTION__, 'AI 执行打单牌 initSendBianJiaDuiA 执行拆牌  a策略 拆边夹对 留夹子');
            //处理单牌逻辑
            $this->initSendDanCard();
        }
    }
    //出牌处理边夹对 b策略 留对子
    public function initSendBianJiaDuiB()
    {
        if ($this->disCard || !isset($this->groupList['group_bian_jia_dui'])) {
            return '';
        }
        $this->groupList['group_bian_jia_dui'] = array_filter($this->groupList['group_bian_jia_dui']);
        if (count_two_dimensional_array($this->groupList['group_bian_jia_dui']) > 0) {
            //取出边夹 放入剩余的牌中
            $tmpKey = array_key_first($this->groupList['group_bian_jia_dui']);
            $tmp = array_shift($this->groupList['group_bian_jia_dui'][$tmpKey]);
            //牌型整理 降为对子
            $this->groupList['group_san_pai'] = array_merge_one_dimensional_array($this->groupList['group_san_pai'], $tmp);
            $this->initDui(100, true);
            //Log::console_log(__FUNCTION__, 'AI 执行打单牌 执行拆牌 initSendBianJiaDuiB b策略 拆边夹对 留对子 ');
            //处理单牌逻辑
            $this->initSendDanCard();
        }
    }
    //出牌处理2连对  挑选分值最大的 花色最少的牌
    public function initSend2LianDui()
    {
        if ($this->disCard || !isset($this->groupList['initSend2LianDui'])) {
            return '';
        }
        $this->groupList['group_2_lian_dui'] = array_filter($this->groupList['group_2_lian_dui']);
        if (count_two_dimensional_array($this->groupList['group_2_lian_dui']) > 0) {

            //取出边夹 放入剩余的牌中
            $tmpKey = array_key_first($this->groupList['group_2_lian_dui']);
            $tmp = array_shift($this->groupList['group_2_lian_dui'][$tmpKey]);
            $newTmp = array();
            $huase = array();
            $dianshu = array();
            foreach ($tmp as $k => $v) {
                $info = get_card_info($v);
                $newTmp[$info['type'] . '_' . $info['num']]['card'] = $v;
                $newTmp[$info['type'] . '_' . $info['num']]['num'] = $info['num'];
                $newTmp[$info['type'] . '_' . $info['num']]['type'] = $info['num'];
                if (!isset($huase[$info['type']])) {
                    $huase[$info['type']] = 0;
                }
                if (!isset($dianshu[$info['num']])) {
                    $dianshu[$info['num']] = 0;
                }
                $huase[$info['type']] += 1;
                $dianshu[$info['num']] += 1;
            }

            //求出不能打的牌
            $notSendCardKey = array_flip($huase)[2] . '_' . array_flip($dianshu)[2];
            $notSendCard = $newTmp[$notSendCardKey];
            //剔除不能打的牌
            unset($newTmp[$notSendCardKey]);
            //求出需要打掉的牌
            $sendCard = array_pop($newTmp);
            $lastCard = array_pop($newTmp);
            if ($lastCard['num'] > $sendCard['num']) {
                $tmpCard = $sendCard;
                $sendCard = $lastCard;
                $lastCard = $tmpCard;
            }
            //查看剩余的牌是2连  还是2对
            if ($notSendCard['num'] == $lastCard['num']) {
                //留下1对 放入1对分组中
                array_push($this->groupList['group_san_pai'], $notSendCard['card'], $lastCard['card']);
                //放入手牌中
                $this->initDui();
            } else {
                //放入2连 分组中
                array_push($this->groupList['group_san_pai'], $notSendCard['card'], $lastCard['card']);
                self::init2Lian();
            }
            //需要打掉的手牌 放入剩余单牌中
            array_push($this->groupList['group_san_pai'], $sendCard['card']);
            //Log::console_log(__FUNCTION__, 'AI 执行打单牌 执行拆牌 initSend2LianDui 2连对  挑选分值最大的 花色最少的牌 ');
            //处理单牌逻辑
            $this->initSendDanCard();
        }
    }
    //挑选分值最大的纯4条
    public function initSendChun4TiaoCard()
    {
        if ($this->disCard || !isset($this->groupList['group_chun_4_tiao'])) {
            return '';
        }
        $this->groupList['group_chun_4_tiao'] = array_filter($this->groupList['group_chun_4_tiao']);
        if (count_two_dimensional_array($this->groupList['group_chun_4_tiao']) > 0) {
            //取出边夹 放入剩余的牌中
            $tmpKey = array_key_first($this->groupList['group_chun_4_tiao']);
            $tmp = array_shift($this->groupList['group_chun_4_tiao'][$tmpKey]);
            //牌型整理 降为边夹
            $this->groupList['group_san_pai'] = array_merge_one_dimensional_array($this->groupList['group_san_pai'], $tmp);
            //降为纯3条
            $this->initChun3tiao(100, true);
            //Log::console_log(__FUNCTION__, 'AI 执行打单牌 执行拆牌 initSendChun4TiaoCard');
            //处理单牌逻辑
            $this->initSendDanCard();
        }
    }
    //挑选分值最大的纯4条
    public function initSendChun3TiaoCard()
    {
        if ($this->disCard || !isset($this->groupList['group_chun_3_tiao'])) {
            return '';
        }
        $this->groupList['group_chun_3_tiao'] = array_filter($this->groupList['group_chun_3_tiao']);
        if (count_two_dimensional_array($this->groupList['group_chun_3_tiao']) > 0) {
            //取出边夹 放入剩余的牌中
            $tmpKey = array_key_first($this->groupList['group_chun_3_tiao']);
            $tmp = array_shift($this->groupList['group_chun_3_tiao'][$tmpKey]);
            //牌型整理 降为边夹
            $this->groupList['group_san_pai'] = array_merge_one_dimensional_array($this->groupList['group_san_pai'], $tmp);
            //降为纯3条
            $this->initDui(100, true);
            //Log::console_log(__FUNCTION__, 'AI 执行打单牌 执行拆牌 initSendChun3TiaoCard');
            //处理单牌逻辑
            $this->initSendDanCard();
        }
    }
    //挑选分值最大的纯5顺
    public function initSendChunShun5LianCard()
    {
        if ($this->disCard || !isset($this->groupList['group_chun_shun_5_lian'])) {
            return '';
        }
        $this->groupList['group_chun_shun_5_lian'] = array_filter($this->groupList['group_chun_shun_5_lian']);
        if (count_two_dimensional_array($this->groupList['group_chun_shun_5_lian']) > 0) {
            //取出边夹 放入剩余的牌中
            $tmpKey = array_key_first($this->groupList['group_chun_shun_5_lian']);
            $tmp = array_shift($this->groupList['group_chun_shun_5_lian'][$tmpKey]);
            //牌型整理 降为边夹
            $this->groupList['group_san_pai'] = array_merge_one_dimensional_array($this->groupList['group_san_pai'], $tmp);

            //降为纯4条
            $this->initChunshun4lian(100, true);
            //Log::console_log(__FUNCTION__, 'AI 执行打单牌 执行拆牌 initSendChunShun5LianCard');
            //处理单牌逻辑
            $this->initSendDanCard();
        }
    }
    //挑选分值最大的纯4顺
    public function initSendChunShun4LianCard()
    {
        if ($this->disCard || !isset($this->groupList['group_chun_shun_4_lian'])) {
            return '';
        }
        $this->groupList['group_chun_shun_4_lian'] = array_filter($this->groupList['group_chun_shun_4_lian']);
        if (count_two_dimensional_array($this->groupList['group_chun_shun_4_lian']) > 0) {
            //取出边夹 放入剩余的牌中
            $tmpKey = array_key_first($this->groupList['group_chun_shun_4_lian']);
            $tmp = array_shift($this->groupList['group_chun_shun_4_lian'][$tmpKey]);
            //牌型整理 降为边夹
            $this->groupList['group_san_pai'] = array_merge_one_dimensional_array($this->groupList['group_san_pai'], $tmp);
            //降为纯3条
            $this->initChunshun3lian(100, true);
            //Log::console_log(__FUNCTION__, 'AI 执行打单牌 执行拆牌 initSendChunShun4LianCard');
            //处理单牌逻辑
            $this->initSendDanCard();
        }
    }
    //挑选分值最大的混5顺
    public function initSendHunShun5LianCard()
    {
        if ($this->disCard || !isset($this->groupList['group_hun_shun_5_lian'])) {
            return '';
        }
        $this->groupList['group_hun_shun_5_lian'] = array_filter($this->groupList['group_hun_shun_5_lian']);
        if (count_two_dimensional_array($this->groupList['group_hun_shun_5_lian']) > 0) {
            //取出边夹 放入剩余的牌中
            $tmpKey = array_key_first($this->groupList['group_hun_shun_5_lian']);
            $tmp = array_shift($this->groupList['group_hun_shun_5_lian'][$tmpKey]);
            //牌型整理 降为边夹
            $this->groupList['group_san_pai'] = array_merge_one_dimensional_array($this->groupList['group_san_pai'], $tmp);
            $this->initHunshun4lian(100, true);
            //Log::console_log(__FUNCTION__, 'AI 执行打单牌 执行拆牌 initSendHunShun5LianCard');
            //处理单牌逻辑
            $this->initSendDanCard();
        }
    }
    //挑选分值最大的混4顺
    public function initSendHunShun4LianCard()
    {
        if ($this->disCard || !isset($this->groupList['group_hun_shun_4_lian'])) {
            return '';
        }
        $this->groupList['group_hun_shun_4_lian'] = array_filter($this->groupList['group_hun_shun_4_lian']);
        if (count_two_dimensional_array($this->groupList['group_hun_shun_4_lian']) > 0) {
            //取出边夹 放入剩余的牌中
            $tmpKey = array_key_first($this->groupList['group_hun_shun_4_lian']);
            $tmp = array_shift($this->groupList['group_hun_shun_4_lian'][$tmpKey]);
            //牌型整理 降为边夹
            $this->groupList['group_san_pai'] = array_merge_one_dimensional_array($this->groupList['group_san_pai'], $tmp);
            //降为纯3条
            $this->initHunshun3lian(100, true);
            //Log::console_log(__FUNCTION__, 'AI 执行打单牌 执行拆牌 initSendHunShun4LianCard');
            //处理单牌逻辑
            $this->initSendDanCard();
        }
    }
    //挑选分值最大的混4顺
    public function initSendHun3LianCard()
    {
        if ($this->disCard || !isset($this->groupList['group_hun_shun_3_lian'])) {
            return '';
        }
        $this->groupList['group_hun_shun_3_lian'] = array_filter($this->groupList['group_hun_shun_3_lian']);
        if (count_two_dimensional_array($this->groupList['group_hun_shun_3_lian']) > 0) {
            //取出边夹 放入剩余的牌中
            $tmpKey = array_key_first($this->groupList['group_hun_shun_3_lian']);
            $tmp = array_shift($this->groupList['group_hun_shun_3_lian'][$tmpKey]);
            //牌型整理 降为边夹
            $this->groupList['group_san_pai'] = array_merge_one_dimensional_array($this->groupList['group_san_pai'], $tmp);
            //挑选出鬼牌癞子
            $this->pickLaiZiGuipaiFromHandCards();
            //降为散牌
            //Log::console_log(__FUNCTION__, 'AI 执行打单牌 执行拆牌 initSendHun3LianCard');
            //处理单牌逻辑
            $this->initSendDanCard();
        } else {
            return '';
        }

    }
    //挑选分值最大的混4顺
    public function initSendHun3TiaoCard()
    {
        if ($this->disCard || !isset($this->groupList['group_hun_3_tiao'])) {
            return '';
        }
        $this->groupList['group_hun_3_tiao'] = array_filter($this->groupList['group_hun_3_tiao']);
        if (count_two_dimensional_array($this->groupList['group_hun_3_tiao']) > 0) {

            //取出边夹 放入剩余的牌中
            $tmpKey = array_key_first($this->groupList['group_hun_3_tiao']);
            $tmp = array_shift($this->groupList['group_hun_3_tiao'][$tmpKey]);
            //牌型整理 降为边夹
            $this->groupList['group_san_pai'] = array_merge_one_dimensional_array($this->groupList['group_san_pai'], $tmp);

            //挑选出鬼牌癞子
            $this->pickLaiZiGuipaiFromHandCards();

            //降为散牌
            //Log::console_log(__FUNCTION__, 'AI 执行打单牌 执行拆牌 initSendHun3TiaoCard');
            //处理单牌逻辑
            $this->initSendDanCard();
        }
    }
}
