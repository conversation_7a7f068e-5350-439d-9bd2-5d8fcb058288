<?php
/**
 * @todo 盈利率发牌接口  给Ai发牌首牌
 */


namespace common\AiRummy;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;
use common\AiRummy\AiRummyDealCardProfit as AiRummyDealCardProfit;

class AiRummyDealCardProfitToAi
{

    //最大发牌牌组数量
    private $maxCardNum = 13;
    //当前用户id
    private $uid;
    //当前用户类型
    private $utype;
    //当前房间类型
    private $roomType;
    //底注
    private $base;
    //玩法
    private $cls;
    //货币类型
    private $currency;

    //当前牌池
    private $pool;

    //发牌组名称
    private $groupName;

    //用户最后的手牌
    private $userDealCards = [];

    //奖池名称
    private $poolName;

    //奖池波动参数
    private $dymaicInfo;

    //当前癞子
    private $xcard;

    //房间ai方案分配情况
    private $aiLeveId = 0;

    //波动概率
    private $waveRate = 1;
    //房间信息
    private $roomInfo = [];

    public function __construct($uid, $roomType, $pool, $xcard, $waveRate = 1)
    {
        $this->waveRate = $waveRate;
        $this->utype = Common::PLAYER_TYPE_AI;
        $this->uid = $uid;
        $this->roomType = $roomType;
        //解析房间类型
        $roomInfo = parse_room_type($this->roomType);
        $this->roomInfo = $roomInfo;
        $this->base = $roomInfo['base'];
        $this->cls = $roomInfo['cls'];
        $this->currency = $roomInfo['currency'];
        $this->pool = $pool;
        $this->xcard = $xcard;
        //根据玩法获取最大牌数
        $this->maxCardNum = Common::getMaxCardNumByCls($this->cls);

        //奖池名称
        $this->poolName = Common::getPoolNameByClsAndBase($this->cls, $this->base);

        //获取干预参数
        $this->dymaicInfo = RedisOpt::getPrDymaicOptConfigOne($this->currency, $this->roomType, $this->poolName);

        //执行发牌
        $this->dealCardsRun();

        //执行log写入
        Log::console_object_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, json_encode(object_to_array($this)));
    }

    //获取发到的牌
    public function getDealCards()
    {
        return $this->userDealCards;
    }

    //获取当前剩余牌池
    public function getCardPool()
    {
        return $this->pool;
    }

    //获取发牌的名称
    public function getDealCardsGroupName()
    {
        return $this->groupName;
    }

    //ai发牌
    private function dealCardsRun()
    {

        //测试
//        $obj = new AiRummyDealCardScore($this->pool,$this->xcard,$this->maxCardNum);
//        $ruleInfo = [
//            'group_chun_shun_3_lian'=>4,
//            'group_chun_3_tiao'=>0,
//            'group_bian_jia'=>0,
//            'group_2_lian'=>0,
//            'group_dui'=>0,
//            'group_san_pai'=>0,
//            'group_hun'=>1,
//            'group_no_hun'=>2,
//        ];
//        $this->userDealCards = $obj->getDealCardsByRuleInfo($ruleInfo);
//        $this->pool = $obj->getPool();
//        Log::console_log(__FUNCTION__,'[盈利率AI发牌]['.json_encode($this->userDealCards).']['.$this->xcard.'] 玩家id:'.$this->uid);
//        return;
        $this->getAiLevelId();
        $dealRate = $this->getDealRateAi();
        $groupName = get_rand($dealRate);
        $this->groupName = $groupName;
        Log::console_log(__FUNCTION__, '[盈利率AI发牌][' . $groupName . '] 玩家id:' . $this->uid);

        $obj = new AiRummyDealCardProfit($this->pool, $this->xcard, $this->maxCardNum);
        $this->userDealCards = $obj->getDealCardsByGroup($groupName);
        $this->pool = $obj->getPool();
    }

    //获取ai执行概率
    private function getDealRateAi()
    {
        $rateList = [];
        $list = RedisOpt::getPrAiLevelConfigList();
        if (is_array($list)) {
            foreach ($list as $key => $val) {
                $rateList[$key] = $val[$this->aiLeveId];
            }
        }
        Log::console_log(__FUNCTION__, '[盈利率AI发牌suiji概率][' . json_encode($rateList) . '] 玩家id:' . $this->uid);
        return $rateList;
    }


    //获取房间ai方案分配情况
    private function getAiLevelId()
    {
        $roomPlanInfo = RedisOpt::getPrPlanInfoOne($this->roomType, $this->utype);
        ///设置默认值
        $newRoomPlanInfo = $roomPlanInfo;
        //小白
        $newRoomPlanInfo[1] = $roomPlanInfo[1] * 0.01 * $this->waveRate;

        //普通
        $newRoomPlanInfo[2] = $roomPlanInfo[2] * 0.01 * $this->waveRate;

        //高手
        $newRoomPlanInfo[3] = 1 - $newRoomPlanInfo[1] - $newRoomPlanInfo[2];
        foreach ($newRoomPlanInfo as $key => $val) {
            $val = $val < 0 ? 0 : $val;
            $newRoomPlanInfo[$key] = (int) ($val * 10000);
        }
        Log::console_log(__FUNCTION__, '[盈利率AI发牌suiji概率][' . json_encode($newRoomPlanInfo) . '] 玩家id:' . $this->uid);
        $this->aiLeveId = get_rand($newRoomPlanInfo);
        Log::console_log(__FUNCTION__, '[盈利率AI发牌suiji概率][$this->aiLeveId ' . $this->aiLeveId . '] 玩家id:' . $this->uid);
        return $this;
    }
}
