<?php
/**
 * @todo   计算手牌分数
 * <AUTHOR>
 */


namespace common\AiRummy;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;

class AiRummyHandCardsScore
{
    //本局牌组结果
    private $groupList;
    //当前手牌是否有胡牌基础
    private $hupaiJichu;
    //本组手牌的分数
    private $score = 0;
    //实例化手牌和癞子
    public function __construct($groupList, $hupaiJichu)
    {
        $this->groupList = $groupList;
        $this->hupaiJichu = $hupaiJichu;
    }
    //计算分值
    public function getHandCardsScore()
    {
        if ($this->hupaiJichu) {
            $this->getHandCardsScoreB();
        } else {
            $this->getHandCardsScoreA();
        }

        //执行log写入
        //Log::console_object_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, json_encode(object_to_array($this)));
        return $this->score;
    }
    //胡牌基础
    public function getHuPaiJiChu()
    {
        return $this->hupaiJichu;
    }

    //计算分a策略
    private function getHandCardsScoreA()
    {
        return $this->getHandCardsScoreV2(self::AScoreMap);

        $scor = 0;
        foreach ($this->groupList as $key => $val) {
            if (empty($val)) {
                continue;
            }
            if ($key == 'group_chun_shun_3_lian' || $key == 'group_hun_shun_3_lian') {
                $scor += count_two_dimensional_array($val) * 3 * 1000;
            }
            if ($key == 'group_chun_shun_4_lian' || $key == 'group_hun_shun_4_lian') {
                $scor += count_two_dimensional_array($val) * 4 * 1000;
            }
            if ($key == 'group_chun_shun_5_lian' || $key == 'group_hun_shun_5_lian') {
                $scor += count_two_dimensional_array($val) * 5 * 1000;
            }
            if ($key == 'group_2_lian_2_dui') {
                $scor += count_two_dimensional_array($val) * 4 * 100;
            }
            if ($key == 'group_2_lian') {
                $scor += count_two_dimensional_array($val) * 2 * 100;
            }
            if ($key == 'group_chun_3_tiao') {
                $scor += count_two_dimensional_array($val) * 3 * 10;
            }
            if ($key == 'group_chun_4_tiao') {
                $scor += count_two_dimensional_array($val) * 4 * 10;
            }
            if ($key == 'group_bian_jia_2_dui') {
                $scor += count_two_dimensional_array($val) * 4 * 1;
            }
            if ($key == 'group_bian_jia_dui') {
                $scor += count_two_dimensional_array($val) * 3 * 1;
            }
            if ($key == 'group_bian_jia' || $key == 'group_dui') {
                $scor += count_two_dimensional_array($val) * 2 * 1;
            }
            if ($key == 'group_laizi_guipai') {
                $scor += count_one_dimensional_array($val) * 1000;
            }
        }
        $this->score = $scor;


        $score1 = $this->score;
        $this->getHandCardsScoreV2(self::AScoreMap);
        $score2 = $this->score;
        if ($score1 != $score2) {
            time();
        }
    }

    private const AScoreMap = [
        'group_chun_shun_3_lian' => 3 * 1000,
        'group_hun_shun_3_lian' => 3 * 1000,
        'group_chun_shun_4_lian' => 4 * 1000,
        'group_hun_shun_4_lian' => 4 * 1000,
        'group_chun_shun_5_lian' => 5 * 1000,
        'group_hun_shun_5_lian' => 5 * 1000,
        'group_2_lian_2_dui' => 4 * 100,
        'group_2_lian' => 2 * 100,
        'group_chun_3_tiao' => 3 * 10,
        'group_chun_4_tiao' => 4 * 10,
        'group_bian_jia_2_dui' => 4 * 1,
        'group_bian_jia_dui' => 3 * 1,
        'group_bian_jia' => 2 * 1,
        'group_dui' => 2 * 1,
        'group_laizi_guipai' => 1000
    ];

    private function getHandCardsScoreV2($scoreMap)
    {
        $scor = 0;
        foreach ($this->groupList as $key => $val) {
            $unit = $scoreMap[$key] ?? 0;

            if ($unit) {
                if ($key == 'group_laizi_guipai') {
                    $n = self::count1d($val);
                } else {
                    $n = self::count2d($val);
                }

                $scor += $n * $unit;
            }
        }
        $this->score = $scor;
    }

    private const BScoreMap = [
        'group_chun_shun_3_lian' => 3 * 1000,
        'group_hun_shun_3_lian' => 3 * 1000,
        'group_chun_3_tiao' => 3 * 1000,
        'group_hun_3_tiao' => 3 * 1000,
        'group_chun_shun_4_lian' => 4 * 1000,
        'group_hun_shun_4_lian' => 4 * 1000,
        'group_chun_4_tiao' => 4 * 1000,
        'group_chun_shun_5_lian' => 5 * 1000,
        'group_hun_shun_5_lian' => 5 * 1000,
        'group_2_lian_2_dui' => 4 * 100,
        'group_bian_jia_2_dui' => 4 * 100,
        'group_bian_jia_dui' => 3 * 100,
        'group_2_lian_dui' => 3 * 100,
        'group_2_lian' => 2 * 10,
        'group_dui' => 2 * 10,
        'group_bian_jia' => 2 * 1,
        'group_laizi_guipai' => 1000,
    ];



    //计算分b策略
    private function getHandCardsScoreB()
    {
        return $this->getHandCardsScoreV2(self::BScoreMap);


        $scor = 0;
        foreach ($this->groupList as $key => $val) {
            if (empty($val)) {
                continue;
            }
            if ($key == 'group_chun_shun_3_lian' || $key == 'group_hun_shun_3_lian' || $key == 'group_chun_3_tiao' || $key == 'group_hun_3_tiao') {
                $scor += count_two_dimensional_array($val) * 3 * 1000;
            }
            if ($key == 'group_chun_shun_4_lian' || $key == 'group_hun_shun_4_lian' || $key == 'group_chun_4_tiao') {
                $scor += count_two_dimensional_array($val) * 4 * 1000;
            }
            if ($key == 'group_chun_shun_5_lian' || $key == 'group_hun_shun_5_lian') {
                $scor += count_two_dimensional_array($val) * 5 * 1000;
            }
            if ($key == 'group_2_lian_2_dui' || $key == 'group_bian_jia_2_dui') {
                $scor += count_two_dimensional_array($val) * 4 * 100;
            }
            if ($key == 'group_bian_jia_dui' || $key == 'group_2_lian_dui') {
                $scor += count_two_dimensional_array($val) * 3 * 100;
            }
            if ($key == 'group_2_lian' || $key == 'group_dui') {
                $scor += count_two_dimensional_array($val) * 2 * 10;
            }
            if ($key == 'group_bian_jia') {
                $scor += count_two_dimensional_array($val) * 2 * 1;
            }
            if ($key == 'group_laizi_guipai') {
                $scor += count_one_dimensional_array($val) * 1000;
            }
        }
        $this->score = $scor;

        $score1 = $this->score;
        $this->getHandCardsScoreV2(self::BScoreMap);
        $score2 = $this->score;
        if ($score1 != $score2) {
            time();
        }
    }



    private static function count1d($a)
    {
        if (is_array($a)) {
            return count($a);
        }
        return 0;
    }

    private static function count2d($a)
    {
        $n = 0;
        if (is_array($a)) {
            foreach ($a as $v) {
                if (is_array($v)) {
                    $n += count($v);
                }
            }
        }
        return $n;
    }
}
