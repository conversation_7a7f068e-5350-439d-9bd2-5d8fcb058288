<?php
/**
 * @todo 盈利率发牌接口  给真人发首牌
 */

namespace common\AiRummy;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use app\common\controller\CommonCode;
use lib\Log;
use common\AiRummy\AiRummyDealCardProfit as AiRummyDealCardProfit;

class AiRummyDealCardProfitToPlayer
{

    //当前房间类型
    private $roomType;
    private $roomInfo;

    //最大发牌牌组数量
    private $maxCardNum = 13;

    //当前牌池
    private $pool;

    //当前癞子
    private $xcard;

    //新手保护
    private $rookieBuff;

    //是否是新充保护
    private $rechargeBuff = false;

    //当前用户的pr值
    private $pr;

    //适用当前用户的pr配置
    private $userPrConfig = [];

    //当前用户适用的加权配置
    private $ratePwConfig = [];

    //所有加权配置列表
    private $ratePwConfigList = [];

    //基础概率
    private $rateBaseConfig = [];

    //当前用户id
    private $uid;

    //当前用户类型
    private $utype;

    //当前玩法底注
    private $base = 0;

    //当前玩法
    private $cls = 0;


    //发牌牌组名称
    private $groupName;

    //用户最后的手牌
    private $userDealCards = [];

    //当前翻拍加权类型
    private $ratePwType;

    //当前房间号
    private $rid = 0;


    //盈利率基础发牌配置
    const PROFIT_RATE_DEAL_CARD_BASE_RATE = [
        13 => [
            Common::PROFIT_GROUP_NAME_3LV => 87,
            Common::PROFIT_GROUP_NAME_2LV => 213,
            Common::PROFIT_GROUP_NAME_1LV => 278,
            Common::PROFIT_GROUP_NAME_NOLV_YESLAI => 347,
            Common::PROFIT_GROUP_NAME_NOLV_NOLAI => 76,
        ],
        10 => [
            Common::PROFIT_GROUP_NAME_3LV => 87,
            Common::PROFIT_GROUP_NAME_2LV => 213,
            Common::PROFIT_GROUP_NAME_1LV => 278,
            Common::PROFIT_GROUP_NAME_NOLV_YESLAI => 347,
            Common::PROFIT_GROUP_NAME_NOLV_NOLAI => 76,
        ]
    ];


    public function __construct($uid, $roomType, $pool, $xcard, $rookieBuff = false, $rid, $user_wallet)
    {
        $this->utype = Common::PLAYER_TYPE_PEOPLE;
        $this->uid = $uid;
        $this->rid = $rid;

        $this->rookieBuff = $rookieBuff;

        //获取玩家是否是新充buff
        $this->rechargeBuff = RedisOpt::getUserTagIsRechargeBuffer($this->uid);
        Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->rid, $this->uid . '新充buff：' . json_encode($this->rechargeBuff));



        $this->pool = $pool;

        $this->xcard = $xcard;

        $this->roomType = $roomType;
        //解析房间类型
        $roomInfo = parse_room_type($this->roomType);
        $this->roomInfo = $roomInfo;

        //根据玩法获取最大牌数
        $this->maxCardNum = Common::getMaxCardNumByCls($roomInfo['cls']);

        //底注
        $this->base = $roomInfo['base'];

        //玩法
        $this->cls = $roomInfo['cls'];

        //用户pr值
        $prConfig = RedisOpt::getPrConfigList();
        $prObj = new \common\AiCommon\AiPlayerPr();
        $prObj->setPrConfig($prConfig);
        //$allPr = $prObj->getPlayerAllPr((int)$this->uid,(int)$this->cls,(int)$user_wallet);
        //Log::console_log(__FUNCTION__.'rid'.$this->rid,$this->uid.'玩家总pr：'.json_encode($allPr));
        $clsPr = $prObj->getPlayerPrByCls((int) $this->uid, (int) $this->cls, (int) $user_wallet);
        Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->rid, $this->uid . '玩家rummy pr：' . json_encode($clsPr));
        $pr = $clsPr; //$allPr['pr']>=$clsPr['pr']?$allPr:$clsPr;
        $this->pr = $pr['pr'];
        Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->rid, $this->uid . '取最大 使用pr：' . $this->pr);

        //获取当前pr对应的档位信息
        $this->userPrConfig = $pr['prConfig'];

        //根据用户状态和 pr档位 获取用户当前发牌加权类型
        $this->ratePwType = $this->getUseRatePwType();
        Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->rid, $this->uid . '用户控制类型：' . $this->ratePwType);
        //获取发牌加权计算参数配置列表
        $this->ratePwConfigList = RedisOpt::getPrCardsGroupConfigByMaxCardNum($this->maxCardNum);

        //获取适用于用户档位的加权计算参数配置
        $this->ratePwConfig = $this->ratePwConfigList[$this->ratePwType];

        //基础配置加权底数
        $this->rateBaseConfig = isset($this->ratePwConfigList['normal']) ? $this->ratePwConfigList['normal'] : self::PROFIT_RATE_DEAL_CARD_BASE_RATE[$this->maxCardNum];

        $this->dealCardsRun();


        //执行完发牌之后 设置
        $this->setPlayMaxBase();

        //执行log写入
        Log::console_object_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, json_encode(object_to_array($this)));
    }

    //获取发到的牌
    public function getDealCards()
    {
        return $this->userDealCards;
    }

    //获取当前剩余牌池
    public function getCardPool()
    {
        return $this->pool;
    }

    //获取发牌的名称
    public function getDealCardsGroupName()
    {
        return $this->groupName;
    }

    //执行最大底注校验更新
    private function setPlayMaxBase()
    {
        //根据玩法转换此次的底注
        $base = Common::getPoolBaseByClsAndBase($this->cls, $this->base);
        $tagPlayMaxBase = RedisOpt::getUserTagPlayMaxBase($this->uid);
        if ($base > $tagPlayMaxBase) {
            //更新玩过的最大底注
            RedisOpt::setUserTagPlayMaxBase($this->uid, $base);

            //重置跳场保护次数为0
            RedisOpt::setUserTagChangeBaseProtectUseCnt($this->uid, 0);
        }
    }

    //真人发牌
    private function dealCardsRun()
    {
        //获取执行概率
        $dealRate = $this->getDealRate();
        $this->groupName = get_rand($dealRate);
        Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->rid, '[盈利率真人发牌概率][' . $this->ratePwType . ']：玩家id：' . $this->uid . json_encode($this->groupName));
        $obj = new AiRummyDealCardProfit($this->pool, $this->xcard, $this->maxCardNum);
        $this->userDealCards = $obj->getDealCardsByGroup($this->groupName);
        //设置最终发牌结果
        $this->groupName = $obj->getDealCardGroupName();
        $this->pool = $obj->getPool();
    }


    //计算用户当前的加权概率
    private function getUseRatePwType()
    {
        $ratePwType = 'normal';


        if ($this->rookieBuff) {
            $ratePwType = 'novice';
        } else {
            //正常状态状态
            $uBegin = isset($this->userPrConfig['u_begin']) ? $this->userPrConfig['u_begin'] : 0; //限制起点
            $dBegin = isset($this->userPrConfig['d_begin']) ? $this->userPrConfig['d_begin'] : 0; //扶植起点
            //a、玩家((小计输钱流水-小计赢钱流水)-max(总计赢钱流水-总计输钱流水+小计输钱流水-小计赢钱流水,0))/新充buff额度>0.2时，发牌权重走以下配置
            $upBuffRes = Common::getIsUpBuff(false, false, 0, Common::USER_TAG_CHARGE_BUFF, Common::ROOM_UNION_TYPE_BUFF, $this->uid, 'notBankruptcyProtect', '', $this->roomInfo);
            $noUp = $upBuffRes["noUp"] ?? false;

            $woolKillRate = $upBuffRes["woolKillRate"] ?? 0;
            if ((rand() / (getrandmax() + 1)) < $woolKillRate) {
                $ratePwType = 'down';
                return $ratePwType;
            }

            //新充buff
            if ($this->rechargeBuff) {
                if (isset($upBuffRes['isUpBuff']) && $upBuffRes['isUpBuff']) {
                    $ratePwType = 'recharge';
                } else {
                    $ratePwType = 'normal';
                }
            }
            if ($this->pr >= $uBegin) {
                //盈利状态 走抑制
                $ratePwType = 'down';
            }
            if ($this->pr <= $dBegin && !$noUp) {
                //亏损状态 走扶植
                if ($this->rechargeBuff && isset($upBuffRes['isUpBuff']) && $upBuffRes['isUpBuff']) {
                    $ratePwType = 'recharge';
                    Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->rid, '[盈利率真人发牌概率][' . $ratePwType . ']：玩家id：' . $this->uid . '((小计输钱流水-小计赢钱流水)-max(总计赢钱流水-总计输钱流水+小计输钱流水-小计赢钱流水,0))/新充buff额度>0.2 符合条件');
                } else {
                    $ratePwType = 'up';
                    Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->rid, '[盈利率真人发牌概率][' . $ratePwType . ']：玩家id：' . $this->uid . '((小计输钱流水-小计赢钱流水)-max(总计赢钱流水-总计输钱流水+小计输钱流水-小计赢钱流水,0))/新充buff额度>0.2 not符合条件');
                }
            }
        }

        return $ratePwType;
    }


    //获取执行概率
    private function getDealRate()
    {
        if (in_array($this->ratePwType, ['up'])) {
            //扶植概率加权计算
            $rtn_rate = [
                Common::PROFIT_GROUP_NAME_3LV => $this->luckyPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_3LV], $this->ratePwConfig[Common::PROFIT_GROUP_NAME_3LV]),
                Common::PROFIT_GROUP_NAME_2LV => $this->luckyPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_2LV], $this->ratePwConfig[Common::PROFIT_GROUP_NAME_2LV]),
                Common::PROFIT_GROUP_NAME_1LV => $this->luckyPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_1LV], $this->ratePwConfig[Common::PROFIT_GROUP_NAME_1LV]),
                Common::PROFIT_GROUP_NAME_NOLV_YESLAI => $this->luckyPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_NOLV_YESLAI], $this->ratePwConfig[Common::PROFIT_GROUP_NAME_NOLV_YESLAI]),
                    //不参与计算
                Common::PROFIT_GROUP_NAME_NOLV_NOLAI => $this->luckyPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_NOLV_NOLAI], $this->ratePwConfig[Common::PROFIT_GROUP_NAME_NOLV_NOLAI]),
                //不参与计算
            ];
        } elseif (in_array($this->ratePwType, ['down'])) {
            //抑制概率加权计算
            $rtn_rate = [
                Common::PROFIT_GROUP_NAME_3LV => $this->unluckyPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_3LV], $this->ratePwConfig[Common::PROFIT_GROUP_NAME_3LV]),
                Common::PROFIT_GROUP_NAME_2LV => $this->unluckyPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_2LV], $this->ratePwConfig[Common::PROFIT_GROUP_NAME_2LV]),
                Common::PROFIT_GROUP_NAME_1LV => $this->unluckyPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_1LV], $this->ratePwConfig[Common::PROFIT_GROUP_NAME_1LV]),
                Common::PROFIT_GROUP_NAME_NOLV_YESLAI => $this->unluckyPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_NOLV_YESLAI], $this->ratePwConfig[Common::PROFIT_GROUP_NAME_NOLV_YESLAI]),
                    //不参与计算
                Common::PROFIT_GROUP_NAME_NOLV_NOLAI => $this->unluckyPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_NOLV_NOLAI], $this->ratePwConfig[Common::PROFIT_GROUP_NAME_NOLV_NOLAI]),
                //不参与计算
            ];
        } elseif (in_array($this->ratePwType, ['novice', 'unlucky', 'lucky'])) {
            //使用原有加权概率
            $rtn_rate = [
                Common::PROFIT_GROUP_NAME_3LV => $this->upPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_3LV], $this->ratePwConfig[Common::PROFIT_GROUP_NAME_3LV]),
                Common::PROFIT_GROUP_NAME_2LV => $this->upPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_2LV], $this->ratePwConfig[Common::PROFIT_GROUP_NAME_2LV]),
                Common::PROFIT_GROUP_NAME_1LV => $this->upPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_1LV], $this->ratePwConfig[Common::PROFIT_GROUP_NAME_1LV]),
                Common::PROFIT_GROUP_NAME_NOLV_YESLAI => $this->upPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_NOLV_YESLAI], $this->ratePwConfig[Common::PROFIT_GROUP_NAME_NOLV_YESLAI]),
                    //不参与计算
                Common::PROFIT_GROUP_NAME_NOLV_NOLAI => $this->upPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_NOLV_NOLAI], $this->ratePwConfig[Common::PROFIT_GROUP_NAME_NOLV_NOLAI]),
                //不参与计算
            ];
        } elseif (in_array($this->ratePwType, ['recharge'])) {
            $upRatePwConfig = $this->ratePwConfigList['up'];
            //充值buff
            $rtn_rate = [
                Common::PROFIT_GROUP_NAME_3LV => max($this->luckyPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_3LV], $upRatePwConfig[Common::PROFIT_GROUP_NAME_3LV]), $this->upPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_3LV], $this->ratePwConfig[Common::PROFIT_GROUP_NAME_3LV])),
                Common::PROFIT_GROUP_NAME_2LV => max($this->luckyPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_2LV], $upRatePwConfig[Common::PROFIT_GROUP_NAME_2LV]), $this->upPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_2LV], $this->ratePwConfig[Common::PROFIT_GROUP_NAME_2LV])),
                Common::PROFIT_GROUP_NAME_1LV => min($this->luckyPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_1LV], $upRatePwConfig[Common::PROFIT_GROUP_NAME_1LV]), $this->upPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_1LV], $this->ratePwConfig[Common::PROFIT_GROUP_NAME_1LV])),
                Common::PROFIT_GROUP_NAME_NOLV_YESLAI => max($this->luckyPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_NOLV_YESLAI], $upRatePwConfig[Common::PROFIT_GROUP_NAME_NOLV_YESLAI]), $this->upPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_NOLV_YESLAI], $this->ratePwConfig[Common::PROFIT_GROUP_NAME_NOLV_YESLAI])),
                    //不参与计算
                Common::PROFIT_GROUP_NAME_NOLV_NOLAI => min($this->luckyPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_NOLV_NOLAI], $upRatePwConfig[Common::PROFIT_GROUP_NAME_NOLV_NOLAI]), $this->upPw($this->rateBaseConfig[Common::PROFIT_GROUP_NAME_NOLV_NOLAI], $this->ratePwConfig[Common::PROFIT_GROUP_NAME_NOLV_NOLAI])),
                //不参与计算
            ];
        } else {
            $rtn_rate = $this->rateBaseConfig;
        }
        Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->rid, '[盈利率真人发牌概率][' . $this->ratePwType . ']：玩家id：' . $this->uid . json_encode($rtn_rate));
        return $rtn_rate;
    }


    //幸运 霉运 新手状态 按照固定调节发牌
    private function upPw($base, $pw)
    {
        //@20210611修改  加权默认倍数+1
        $rtn = (int) $base * (1 + $pw);
        Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->rid, '[盈利率真人发牌up概率][ ' . $this->ratePwType . ':对应系数 *  倍数][' . $base . '*（1+' . $pw . ')= ' . $rtn . ']：玩家id：' . $this->uid);
        return $rtn;
    }


    //扶植计算公式
    private function luckyPw($base, $pw)
    {
        $pr = $this->pr;
        //扶植起点
        $d_begin = $this->userPrConfig['d_begin'];
        //扶极限
        $d_limit = $this->userPrConfig['d_limit'];

        //（1+（扶植起点-pr）/（扶植起点-扶植极致）*对应系数) *  倍数
        $fenzi = $d_begin - $pr;

        $fenmu = $d_begin - $d_limit;
        if ($fenmu == 0 || $fenzi == 0) {
            $y = 0;
        } else {
            $y = $fenzi / $fenmu;
        }
        $y = $y > 1 ? 1 : $y;
        $y = $y < 0 ? 0 : $y;
        $rtn = $base * (1 + $y * $pw);
        Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->rid, '[盈利率真人发牌幸运up概率][（1+（扶植起点-pr）/（扶植起点-扶植极致）* 对应系数 ) * 倍数 ][（1+（' . $d_begin . '-' . $pr . '）/（' . $d_begin . '-' . $d_limit . '）*' . $pw . ')*' . $base . '=' . $rtn . ']：玩家id：' . $this->uid);
        return $rtn;
    }

    //扶植计算公式
    private function unluckyPw($base, $pw)
    {

        $pr = $this->pr;
        //抑制起点
        $u_begin = $this->userPrConfig['u_begin'];
        //抑制极值
        $u_limit = $this->userPrConfig['u_limit'];

        //（1+（pr-抑制起点）/（抑制极值-抑制起点）*对应系数) *  倍数gb
        $fenzi = bcsub($pr, $u_begin, 4);
        $fenmu = bcsub($u_limit, $u_begin, 4);
        if ($fenmu == 0 || $fenzi == 0) {
            $y = 0;
        } else {
            $y = $fenzi / $fenmu;
        }
        $y = $y > 1 ? 1 : $y;
        $y = $y < 0 ? 0 : $y;
        $rtn = $base * (1 + $y * $pw);
        Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $this->rid, '[盈利率真人发牌霉运up概率][（1+（pr-抑制起点）/（抑制极值-抑制起点）*对应系数) * 倍数   ][（1+（' . $pr . '-' . $u_begin . '）/（' . $u_limit . '-' . $u_begin . '）*' . $pw . ')*' . $base . '=' . $rtn . ']：玩家id：' . $this->uid);
        return $rtn;
    }
}
