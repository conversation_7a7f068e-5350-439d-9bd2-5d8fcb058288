<?php

namespace common\AiRummy;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;

class AiRummyGap
{
    use AutoGroup\AiRummyFormatHandCards, AutoGroup\AiRummyFindSeqReal, AutoGroup\AiRummyFindSeqFake, AutoGroup\AiRummyCombinations, AutoGroup\AiRummyGapScore;
    //手牌
    private $handCards;
    //癞子牌
    private $xcard;


    //缺口牌
    private $gapCardList = [];
    //缺口牌算分
    private $gapScore = 0;


    public function __construct($handCards, $xcard)
    {
        $this->handCards = $handCards;
        $this->xcard = $xcard;
        //格式化手牌
        $this->formatHandCards($this->handCards, $this->xcard);
        //找出所有有效的组合
        $realSeqList = $this->findAllRealSeq($this->formatSeqHandCards);
        $groupList = [];
        $usedXcardCnt = 0;
        if ($realSeqList) {
            $list = array_values($realSeqList);
            $keyList = array_keys($list);
            $combinationsList = $this->getAllCombinations($this->handCards, $this->xCnt, $this->handCarsAllXcards, $keyList, $list);
            $combinationsInfo = $this->setDeclearCardsGroup($combinationsList);
            $groupList = $combinationsInfo['groupList'];
            $usedXcardCnt = $combinationsInfo['usedXcardCnt'];

            //去除纯顺手牌
            foreach ($realSeqList as $val) {
                $this->handCards = del_b_arr_from_a_arr($this->handCards, $val['cards']);
            }
            //格式化手牌
            $this->formatHandCards($this->handCards, $this->xcard);
            $this->xCnt = $this->xCnt + $usedXcardCnt;
        }
        //缺口牌
        $this->gapCardList = $this->setGapCardList();
        //缺口分数
        $this->gapScore = $this->setGapScore($groupList, $this->gapCardList, $usedXcardCnt, $this->xCnt);
    }


    //返回缺口牌
    public function getGapCardList(): array
    {
        return $this->gapCardList;
    }

    //返回缺口分数
    public function getGapScore(): string
    {
        return $this->gapScore;
    }

    //找出最优的组合
    private function setDeclearCardsGroup($combinationsList)
    {
        if (empty($combinationsList)) {
            return [];
        }
        if (count($combinationsList) >= 2) {
            //查找牌组合张数最高的
            $cardsCntList = array_column($combinationsList, 'cardCnt');
            array_multisort($cardsCntList, SORT_ASC, $combinationsList);
        }
        return array_pop($combinationsList);
    }

    //获取缺口牌
    //缺口牌定义：在纯顺之外，检测4个花色的1·13，检测每张牌补上之后能否组成 至少3张的纯顺 如果可以 则该牌算缺口牌  如果缺口牌是癞子 则不算缺口牌
    private function setGapCardList(): array
    {
        //找出有一个癞子的混顺
        $fakeSeqList = $this->findFake3Seq1Xcard($this->formatSeqHandCards, false);
        if (!$fakeSeqList) {
            return [];
        }
        $gapCardList = [];
        //['groupName'=>$groupName,'cards'=>$cardList,'cardsCnt'=>count($cardList)];
        foreach ($fakeSeqList as $info) {
            $cardList = $info['cards'];
            //按照从小到大排序
            sort($cardList);
            //最大的牌 减去最小的牌  如果是1 则取2端
            $subVal = $cardList[1] - $cardList[0];
            //花色

            $cardInfo1 = get_card_info($cardList[1]);
            $cardInfo0 = get_card_info($cardList[0]);
            $color = $cardInfo1['type'];
            $poker = POKER[RUMMY_COLOR_INDEX[$color]];
            $tmpGapCardList = [];
            if ($subVal == 1) {
                //2连顺
                $tmpGapCardList[] = $cardInfo0['num'] - 1;
                if ($cardInfo1['num'] == 13) {
                    $tmpGapCardList[] = 1;
                } else {
                    $tmpGapCardList[] = $cardInfo1['num'] + 1;
                }

            }
            if ($subVal == 2) {
                //夹顺
                $tmpGapCardList[] = $cardInfo0['num'] + 1;
            }
            //13 A
            if ($subVal == 12) {
                $tmpGapCardList[] = $cardInfo1['num'] - 1;
            }
            //12 A
            if ($subVal == 11) {
                $tmpGapCardList[] = $cardInfo1['num'] + 1;
            }
            foreach ($tmpGapCardList as $v) {
                if (isset($poker[$v - 1])) {
                    $gapCardList[] = $poker[$v - 1];
                }
            }
        }
        //去重处理
        $gapCardList = array_unique($gapCardList);
        //去手牌交集
        $poker = array_merge(FANG_KUAI_LIST, MEI_HUA_LIST, HONG_TAO_LIST, HEI_TAO_LIST, [G_CARD]);
        $gapCardList = array_intersect($gapCardList, $poker);
        //去除癞子牌
        $allXCard = get_all_gui_lai_cards_by_card($this->xcard);
        return array_values(array_diff($gapCardList, $allXCard));
    }
}

?>

