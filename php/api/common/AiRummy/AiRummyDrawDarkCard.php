<?php

namespace common\AiRummy;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;

class AiRummyDrawDarkCard
{

    use AutoGroup\AiRummyCalculationDarkCard;
    //房间类型
    private $roomType;
    //房间信息
    private $roomInfo;
    //当前用户id
    private $uid;

    //当前用户类型
    private $utype;

    //当前牌桌玩家列表
    private $userList;

    //玩家初始发牌类型
    private $dealCardType = Common::PROFIT_GROUP_NAME_SANPAI;

    //当前摸的第几手牌
    private $userRound;

    //当前牌池
    private $pool;

    //当前癞子
    private $xcard;

    //当前所有的癞子集合
    private $allxCardList = [];

    //手牌
    private $handCards;

    //设置暗牌
    private $darkCard = "";

    //用户是否是新手buff
    private $isRookieBuffer = false;

    //控牌结果
    private $controlRes = 'normal';

    public function __construct($uid, $handCards, $userList, $roomType, $utype, $xcard, $pool, $circle)
    {
        $this->userList = $userList;
        $this->uid = $uid;
        $this->handCards = $handCards;
        $this->roomType = $roomType;
        //获取房间的动态参数
        $this->roomInfo = parse_room_type($this->roomType);
        //固定为真人
        $this->utype = $utype;
        $this->userRound = $circle;
        $this->pool = $pool;
        $this->xcard = $xcard;
        if ($utype == Common::PLAYER_TYPE_PEOPLE) {
            //获取用户是否是新充buff
            $this->dealCardType = RedisOpt::getUserTagLastRummyDealCardType($uid);
            if (!$this->dealCardType) {
                $this->dealCardType = Common::PROFIT_GROUP_NAME_SANPAI;
            }

            $this->isRookieBuffer = RedisOpt::getUserTagIsRookieBuffer($uid);
        }
        //枚举所有癞子
        $this->allxCardList = get_all_gui_lai_cards_by_card($this->xcard);

        //获取控制结果
        $this->setControlRes();

        //计算暗牌
        $this->setDarkCard();
    }


    //获取暗牌
    public function getDarkCard(): string
    {
        Log::console_log(__FUNCTION__, "最终暗牌结果：" . $this->darkCard);
        return $this->darkCard;
    }

    //根据用户类型获取控牌结果
    private function setControlRes()
    {
        if ($this->utype == Common::PLAYER_TYPE_PEOPLE) {
            $this->setPeopleControlRes();
        } else {
            $this->setAiControlRes();
        }
    }


    //执行发牌
    private function setDarkCard()
    {
        $this->darkCard = $this->calculationDarkCard($this->roomInfo['cls'], $this->handCards, $this->pool, $this->xcard, $this->controlRes);
    }


    //获取真人的控牌结果
    private function setPeopleControlRes()
    {
        //获取暗牌控制概率
        $darkRateConfig = RedisOpt::getDarkRateConfigList();
        if ($this->isRookieBuffer) {
            Log::console_log(__FUNCTION__, $this->uid . "为新手buff");
            Log::console_log(__FUNCTION__, $this->uid . "初始牌型为：" . $this->dealCardType);
            //校验是否扶植
            if (!isset($darkRateConfig[$this->dealCardType])) {
                return;
            }
            //执行概率随机
            $rand[1] = $darkRateConfig[$this->dealCardType]['up' . Common::GAME_TYPE_13_CARD] * 1000;
            Log::console_log(__FUNCTION__, $this->uid . "扶植概率为：" . $rand[1]);
            $rand[0] = 1000 - $rand[1];
            if (get_rand($rand)) {
                Log::console_log(__FUNCTION__, $this->uid . "扶植结果：up");
                $this->controlRes = 'up';
            } else {
                Log::console_log(__FUNCTION__, $this->uid . "扶植结果：normal");
            }
        } else {
            //校验是否抑制
            //玩家控牌系数  Rummy大盘有缺口是 有x的概率执行Rummy大盘控
            //X = min(0.33,玩家控牌系数*(当日实时缺口额度/max(当日实时盈利目标,1000)))
            //大盘控随中
            Log::console_log(__FUNCTION__, $this->uid . "非新手buff");
            $rand[1] = $this->getZSysKillerRate() * 1000;
            Log::console_log(__FUNCTION__, $this->uid . "大盘杀率" . $rand[1]);
            $rand[0] = 1000 - $rand[1];
            if (get_rand($rand)) {
                Log::console_log(__FUNCTION__, $this->uid . "大盘杀随中了");
                //校验是否抑制
                if (!isset($darkRateConfig[$this->dealCardType])) {
                    return;
                }
                //执行概率随机
                $rand[1] = $darkRateConfig[$this->dealCardType]['down' . Common::GAME_TYPE_13_CARD] * 1000;
                Log::console_log(__FUNCTION__, $this->uid . "抑制概率" . $rand[1]);
                $rand[0] = 1000 - $rand[1];
                if (get_rand($rand)) {
                    Log::console_log(__FUNCTION__, $this->uid . "抑制结果 down");
                    $this->controlRes = 'down';
                } else {
                    Log::console_log(__FUNCTION__, $this->uid . "抑制结果 normal");
                }

            } else {
                Log::console_log(__FUNCTION__, $this->uid . "大盘杀没随中");
            }
        }
    }


    //获取ai的控牌结果
    private function setAiControlRes()
    {
        //测试使用
//        $this->controlRes = "up";
//        return;
        //在ai摸暗牌的时候，每一张都有x%的概率摸到一张需求牌
        //X = min(0.333,默认喂牌率+AI扶植系数*(当日实时缺口额度/max(当日实时盈利魔表,1000)))
        $rand[1] = $this->getASysKillerRate();
        Log::console_log(__FUNCTION__, "AI 大盘杀概率" . $rand[1]);
        $rand[0] = 1000 - $rand[1];
        if (get_rand($rand)) {
            Log::console_log(__FUNCTION__, "AI 大盘杀结果：up");
            $this->controlRes = "up";
        } else {
            Log::console_log(__FUNCTION__, "AI 大盘杀结果：normal");
        }
    }



    //获取用户当前大盘池
    private function getASysKillerRate()
    {
        $dymaicInfo = RedisOpt::getPrDymaicOptConfigOne($this->roomInfo['currency'], $this->roomType);
        $waveRate = 0;
        foreach ($this->userList as $key => $val) {
            if ($val['utype'] != Common::PLAYER_TYPE_PEOPLE) {
                continue;
            }
            $capitalType = Common::getSysKillerTypeByPlayerIndex($key);
            //获取大盘缺口金额
            $jackPortName = Common::getPoolNameByClsAndBase($this->roomInfo['cls'], $this->roomInfo['base']);
            $overallLossAmountObj = new \common\AiCommon\AiOverallWinRateV2($jackPortName, $this->roomInfo, $capitalType);
            $tmpWaveRate = $overallLossAmountObj->getRummyADarkSysKillerWaveRate($dymaicInfo['a_control_rate'], $dymaicInfo['a_control_coefficient']);
            if ($waveRate == 0) {
                $waveRate = $tmpWaveRate;
            } else {
                $waveRate = max($waveRate, $tmpWaveRate);
            }
        }
        return $waveRate;
    }


    //获取用户当前大盘池
    private function getZSysKillerRate()
    {
        $dymaicInfo = RedisOpt::getPrDymaicOptConfigOne($this->roomInfo['currency'], $this->roomType);
        $waveRate = 0;
        foreach ($this->userList as $key => $val) {
            if ($val['utype'] != Common::PLAYER_TYPE_PEOPLE) {
                continue;
            }
            $capitalType = Common::getSysKillerTypeByPlayerIndex($key);
            //获取大盘缺口金额
            $jackPortName = Common::getPoolNameByClsAndBase($this->roomInfo['cls'], $this->roomInfo['base']);
            $overallLossAmountObj = new \common\AiCommon\AiOverallWinRateV2($jackPortName, $this->roomInfo, $capitalType);
            $tmpWaveRate = $overallLossAmountObj->getRummyZDarkSysKillerWaveRate($dymaicInfo['z_control_coefficient']);
            if ($waveRate == 0) {
                $waveRate = $tmpWaveRate;
            } else {
                $waveRate = max($waveRate, $tmpWaveRate);
            }
        }
        return $waveRate;
    }
}
