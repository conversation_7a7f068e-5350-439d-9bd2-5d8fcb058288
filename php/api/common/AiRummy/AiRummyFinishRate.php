<?php
/**
 * @todo   AI摸牌判定
 * <AUTHOR>
 *
 */


namespace common\AiRummy;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;

class AiRummyFinishRate
{

    //当前操作的胡牌概率
    protected $finishRate;

    //房间类型
    protected $roomType;

    //解析之后的roomInfo
    protected $roomInfo;

    //当前的进行的轮数
    protected $round;

    //是否能够胡牌
    private $canFinish;



    //实例化手牌和癞子
    public function __construct($rtype, $round)
    {
        $this->roomType = $rtype;
        $this->round = $round;

        //解析roomType
        $this->roomInfo = parse_room_type($this->roomType);

        //获取设置当前的胡牌概率
        $this->setCurrentFinishRate();
        //执行log写入
        Log::console_object_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, json_encode(object_to_array($this)));
    }

    //获取胡牌结果
    public function getFinishRes()
    {
        $this->getFinishResByRate();
        Log::console_log(__FUNCTION__, "pr概率胡牌 roomType:" . $this->roomType);
        Log::console_log(__FUNCTION__, "pr概率胡牌 胡牌概率:" . $this->finishRate);
        Log::console_log(__FUNCTION__, "pr概率胡牌 回合数:" . $this->round);
        Log::console_log(__FUNCTION__, "pr概率胡牌 执行结果:" . json_encode($this->canFinish));
        return $this->canFinish;
    }



    //根据概率获取胡牌结果
    protected function getFinishResByRate()
    {
        if ($this->finishRate >= 1) {
            $this->canFinish = true;
            return;
        }
        if ($this->finishRate <= 0) {
            $this->canFinish = false;
            return;
        }
        //概率执行放大10000倍
        $randData[1] = bcmul($this->finishRate, 10000, 0);
        $randData[0] = bcsub(10000, $randData[1], 0);
        $randDataKey = get_rand($randData);
        $this->canFinish = $randDataKey ? true : false;
    }


    //获取当前适用的胡牌
    protected function setCurrentFinishRate()
    {
        //获取奖池名称
        $jackpotName = Common::getPoolNameByClsAndBase($this->roomInfo['cls'], $this->roomInfo['base']);

        //获取干预参数配置
        $dynamicInfo = RedisOpt::getPrDymaicOptConfigOne($this->roomInfo['currency'], $this->roomType, $jackpotName);

        //获取胡牌设定
        $finishRateStr = isset($dynamicInfo['finish_rate']) ? $dynamicInfo['finish_rate'] : '';

        //获取当前适用的胡牌设定
        $finishRateListArr = explode(',', $finishRateStr);

        //如果存在设置
        if ((isset($finishRateListArr[$this->round]) && $finishRateListArr[$this->round] === '') || !isset($finishRateListArr[$this->round])) {
            $this->finishRate = 1;
        } else {
            $this->finishRate = $finishRateListArr[$this->round];
        }
    }

}
