<?php
/**
 * @todo 批量发牌接口
 */

namespace common\AiRummy;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;
use common\AiRummy\AiRummyDealCardProfit as AiRummyDealCardProfit;

class AiRummyDealCardBat
{

    //【盈利率批量发牌】 针对新手保护发牌
    public static function dealCardsBatForRookieBuff(array $userList, $roomType, $cardsPool, $xcard, $rid)
    {
        $rtn = [
            'cardsPool' => $cardsPool,
            'userDealCardsList' => [],
        ];
        //牌型名称
        $aGroupName = Common::PROFIT_GROUP_NAME_SANPAI;
        $zGroupName = Common::PROFIT_GROUP_NAME_SANPAI;
        //初始化用户发牌
        $dealCardsList = [];
        //此模式下玩家根据配置的牌型数量 加权概率随机得出  如果没有了就纯随机
        $userTypeList = array_column($userList, 'uid', 'utype');
        if (!isset($userTypeList[Common::PLAYER_TYPE_PEOPLE])) {
            return $dealCardsList;
        }
        //获取真人的id
        $playerIndexZ = $userTypeList[Common::PLAYER_TYPE_PEOPLE];

        //解析房间信息
        $roomInfo = parse_room_type($roomType);
        //最大发牌数量
        $maxCardsNum = Common::getMaxCardNumByCls($roomInfo['cls']);

        //获取配置的保护牌型
        $rookieBuffConfig = RedisOpt::getPrRookieBuffDealCardConfig();
        if ($rookieBuffConfig) {
            //获取真人已经发过的牌
            $rookieBuffDealCardZ = RedisOpt::getUserTagRookieBuffDealCardsZ($playerIndexZ);
            $zLastGroupName = isset($rookieBuffDealCardZ['last_group_name']) ? $rookieBuffDealCardZ['last_group_name'] : '';

            //获取AI已经发过的牌
            $rookieBuffDealCardA = RedisOpt::getUserTagRookieBuffDealCardsA($playerIndexZ);
            $aLastGroupName = isset($rookieBuffDealCardA['last_group_name']) ? $rookieBuffDealCardA['last_group_name'] : '';
            //牌组名称列表
            $groupNameList = Common::getProfitGroupNameList();

            //配置的数量 减去已经使用的数量  为当前可随机数量
            $rookieBuffConfigZ = [];
            $rookieBuffConfigA = [];
            foreach ($groupNameList as $val => $groupNameText) {
                if ($zLastGroupName == $val && in_array($zLastGroupName, [Common::PROFIT_GROUP_NAME_2LV, Common::PROFIT_GROUP_NAME_NOLV_NOLAI])) {
                } else {
                    $zUsedCnt = isset($rookieBuffDealCardZ['z' . $maxCardsNum][$val]) ? $rookieBuffDealCardZ['z' . $maxCardsNum][$val] : 0;
                    $zCnt = max(0, ($rookieBuffConfig[$val]['z' . $maxCardsNum] - $zUsedCnt));
                    if ($zCnt > 0) {
                        $rookieBuffConfigZ[$val] = $zCnt;
                    }
                }
                if ($aLastGroupName == $val && in_array($aLastGroupName, [Common::PROFIT_GROUP_NAME_2LV, Common::PROFIT_GROUP_NAME_NOLV_NOLAI])) {
                } else {
                    $aUsedCnt = isset($rookieBuffDealCardA['a' . $maxCardsNum][$val]) ? $rookieBuffDealCardA['a' . $maxCardsNum][$val] : 0;
                    $aCnt = max(0, ($rookieBuffConfig[$val]['a' . $maxCardsNum] - $aUsedCnt));
                    if ($aCnt > 0) {
                        $rookieBuffConfigA[$val] = $aCnt;
                    }
                }
            }
            if (!empty($rookieBuffConfigA)) {
                $aGroupName = get_rand($rookieBuffConfigA);
            }
            if (!empty($rookieBuffConfigZ)) {
                $zGroupName = get_rand($rookieBuffConfigZ);
            }
        }

        //根据名称执行发牌
        $dealCardsObj = new AiRummyDealCardProfit($cardsPool, $xcard, $maxCardsNum);
        //循环根据名称执行发牌
        foreach ($userList as $key => $val) {
            if ($key == $playerIndexZ) {
                //真人执行真人发牌
                $dealCardsList[$key] = $dealCardsObj->getDealCardsByGroup($zGroupName);
                //真人发牌设置发牌名称结果
                RedisOpt::setUserTagLastRummyDealCardType($playerIndexZ, $dealCardsObj->getDealCardGroupName());
            } else {
                //根据牌组名称发牌
                $dealCardsList[$key] = $dealCardsObj->getDealCardsByGroup($aGroupName);
            }
        }
        //设置用户已经发过的牌
        $rookieBuffDealCardZ['last_group_name'] = $zGroupName;
        if (!isset($rookieBuffDealCardZ['z13'][$zGroupName])) {
            $rookieBuffDealCardZ['z13'][$zGroupName] = 1;
            $rookieBuffDealCardZ['z10'][$zGroupName] = 1;
        } else {
            $rookieBuffDealCardZ['z13'][$zGroupName] += 1;
            $rookieBuffDealCardZ['z10'][$zGroupName] += 1;
        }

        RedisOpt::setUserTagRookieBuffDealCardsZ($playerIndexZ, $rookieBuffDealCardZ);
        $rookieBuffDealCardA['last_group_name'] = $aGroupName;
        if (!isset($rookieBuffDealCardA['a13'][$aGroupName])) {
            $rookieBuffDealCardA['a13'][$aGroupName] = 1;
            $rookieBuffDealCardA['a10'][$aGroupName] = 1;
        } else {
            $rookieBuffDealCardA['a13'][$aGroupName] += 1;
            $rookieBuffDealCardA['a10'][$aGroupName] += 1;
        }

        RedisOpt::setUserTagRookieBuffDealCardsA($playerIndexZ, $rookieBuffDealCardA);
        //获取最后剩余的牌池
        $cardsPool = $dealCardsObj->getPool();
        $rtn['cardsPool'] = $cardsPool;
        $rtn['userDealCardsList'] = $dealCardsList;
        Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $rid, '[新手保护]真人id:' . $playerIndexZ . 'AI：' . $aGroupName . ' 真人：' . $zGroupName);
        return $rtn;
    }


    //【盈利率批量发牌】 针对正常发牌
    public static function dealCardsBatForNormal(array $userList, $roomType, $cardsPool, $xcard, $rid)
    {
        Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $rid, '[真人批量发牌]$userList:' . json_encode($userList));
        $waveRate = 0;
        $roomInfo = parse_room_type($roomType);
        //获取房间的动态参数
        $dymaicInfo = RedisOpt::getPrDymaicOptConfigOne($roomInfo['currency'], $roomType);
        //先执行真人用户的循环 得到房间的波动概率
        foreach ($userList as $key => $val) {
            if ($val['utype'] != Common::PLAYER_TYPE_PEOPLE) {
                continue;
            }
            $capitalType = Common::getSysKillerTypeByPlayerIndex($key);
            //获取大盘缺口金额
            $jackPortName = Common::getPoolNameByClsAndBase($roomInfo['cls'], $roomInfo['base']);
            $overallLossAmountObj = new \common\AiCommon\AiOverallWinRateV2($jackPortName, $roomInfo, $capitalType);
            $tmpWaveRate = $overallLossAmountObj->getRummySysKillerWaveRate($dymaicInfo);
            if ($waveRate == 0) {
                $waveRate = $tmpWaveRate;
            } else {
                $waveRate = max($waveRate, $tmpWaveRate);
            }
        }
        //初始化用户发牌
        $dealCardsList = [];
        foreach ($userList as $key => $val) {
            if ($val['utype'] == Common::PLAYER_TYPE_PEOPLE) {
                //真人发牌
                $dealCardsObj = new AiRummyDealCardProfitToPlayer($key, $roomType, $cardsPool, $xcard, $val['rookie_buff'], $rid, $val['user_wallet']);
                //真人发牌设置发牌名称结果
                RedisOpt::setUserTagLastRummyDealCardType($key, $dealCardsObj->getDealCardsGroupName());

                Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $rid, '[真人批量发牌]真人id:' . $key . '发牌：' . $dealCardsObj->getDealCardsGroupName());
            } else {
                //ai发牌
                $dealCardsObj = new AiRummyDealCardProfitToAi($key, $roomType, $cardsPool, $xcard, $waveRate);
                Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $rid, '[ai批量发牌]ai id:' . $key . '发牌：' . $dealCardsObj->getDealCardsGroupName($waveRate));
            }

            $dealCardsList[$key] = $dealCardsObj->getDealCards();
            $cardsPool = $dealCardsObj->getCardPool();
        }
        $rtn = [
            'cardsPool' => $cardsPool,
            'userDealCardsList' => $dealCardsList,
        ];
        return $rtn;
    }


}
