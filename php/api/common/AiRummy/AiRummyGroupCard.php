<?php
/**
 * @todo   手牌分组
 * <AUTHOR>
 */


namespace common\AiRummy;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;

class AiRummyGroupCard
{
    //手牌信息
    protected $handCards = [];
    //本局的癞子牌
    protected $lCard;
    //本局的牌数  13rummy 还是 10rummy
    protected $maxCard;
    //本局所有标记为癞子的牌
    protected $allLCards = [];
    //当前手牌是否有胡牌基础
    protected $hupaiJichu = false;
    //本局牌组结果
    protected $groupList = [
        'group_chun_shun_3_lian' => [],
        'group_chun_shun_4_lian' => [],
        'group_chun_shun_5_lian' => [],
        'group_hun_shun_3_lian' => [],
        'group_hun_shun_4_lian' => [],
        'group_hun_shun_5_lian' => [],
        'group_2_lian_2_dui' => [],
        'group_2_lian_dui' => [],
        'group_2_lian' => [],
        'group_chun_3_tiao' => [],
        'group_chun_4_tiao' => [],
        'group_bian_jia_2_dui' => [],
        'group_bian_jia_dui' => [],
        'group_hun_3_tiao' => [],
        'group_bian_jia' => [],
        'group_dui' => [],
        'group_laizi_guipai' => [],
        'group_san_pai' => [],
    ];
    //实例化手牌和癞子
    public function __construct($handCards, $lCard, $maxCard)
    {
        $this->handCards = $handCards;
        $this->lCard = $lCard;
        $this->maxCard = $maxCard;
        $this->allLCards = get_all_gui_lai_cards_by_card($this->lCard);
        $this->groupList['group_san_pai'] = $this->handCards;
        $this->initGroupCard();

        //执行log写入
        //Log::console_object_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, json_encode(object_to_array($this)));
    }
    //获取组好的牌
    public function getGroupList()
    {
        return $this->groupList;
    }
    //获取是否有胡牌条件
    public function getHuPaiJiChu()
    {
        return $this->hupaiJichu;
    }

    //获取二位数组牌组牌
    public function getDeclearCards()
    {
        if ($this->hupaiJichu) {
            return $this->getDealCardsByAiGroup();
        } else {
            return $this->getDealCardsByColorGroup();
        }
    }

    //展示牌按照ai的组排逻辑展示
    private function getDealCardsByAiGroup()
    {
        $groupList = $this->groupList;
        //需要展示的牌型
        $aloneGroupKeyList = ['group_chun_shun_3_lian', 'group_chun_shun_4_lian', 'group_chun_shun_5_lian', 'group_hun_shun_3_lian', 'group_hun_shun_4_lian', 'group_hun_shun_5_lian', 'group_chun_3_tiao', 'group_chun_4_tiao', 'group_hun_3_tiao',];
        $handCards = [];
        $dealCards = [];
        shuffle($aloneGroupKeyList);
        foreach ($aloneGroupKeyList as $key) {
            if (isset($groupList[$key]) && !empty($groupList[$key])) {
                //需要独立分组的
                $val = array_filter($groupList[$key]);
                while (!empty($val)) {
                    $tmp = array_filter(array_shift($val));
                    foreach ($tmp as $v) {
                        if (random_int(1, 10) >= 5) {
                            shuffle($v);
                        }
                        $dealCards[] = $v;
                    }
                }
                unset($groupList[$key]);
            }
        }
        foreach ($groupList as $key => $val) {
            if (in_array($key, ['group_laizi_guipai', 'group_san_pai'])) {
                $handCards = array_merge($handCards, $val);
                continue;
            }
            //7个分组
            //需要独立分组的
            $val = array_filter($val);
            while (!empty($val)) {
                $tmp = array_filter(array_shift($val));
                foreach ($tmp as $v) {
                    if (random_int(1, 10) >= 5) {
                        shuffle($v);
                    }
                    if (count($dealCards) >= 7) {
                        $dealCards[6] = array_merge($dealCards[7], $v);
                    } else {
                        $dealCards[] = $v;
                    }
                }
            }
        }
        if ($handCards) {
            if (count($dealCards) >= 7) {
                $dealCards[6] = array_merge($dealCards[6], $handCards);
            } else {
                $dealCards[] = $handCards;
            }
        }

        /*foreach($this->groupList as $key=>$val){
        if(in_array($key,['group_laizi_guipai','group_san_pai'])){
        $handCards = array_merge($handCards,$val);
        continue;
        }
        //需要独立分组的
        $val = array_filter($val);
        while (!empty($val)){
        $tmp = array_filter(array_shift($val));
        foreach ($tmp as $v){
        if (in_array($key,$aloneGroupKeyList)){
        $dealCards[] = $v;
        }else{
        $handCards = array_merge($handCards,$v);
        }
        }
        }
        }
        $finalDealCards = $this->getdealCardsByColor($dealCards,$handCards);
        return $finalDealCards;*/
        return $dealCards;
    }

    //展示牌按照花色组合
    public function getDealCardsByColorGroup()
    {
        //需要展示的牌型
        $aloneGroupKeyList = ['group_chun_shun_3_lian', 'group_chun_shun_4_lian', 'group_chun_shun_5_lian'];
        $handCards = [];
        $dealCards = [];
        foreach ($this->groupList as $key => $val) {
            if (in_array($key, ['group_laizi_guipai', 'group_san_pai'])) {
                $handCards = array_merge($handCards, $val);
                continue;
            }
            //需要独立分组的
            $val = array_filter($val);
            while (!empty($val)) {
                $tmp = array_filter(array_shift($val));
                foreach ($tmp as $v) {
                    if (in_array($key, $aloneGroupKeyList)) {
                        $dealCards[] = $v;
                    } else {
                        $handCards = array_merge($handCards, $v);
                    }
                }
            }
        }
        return $this->getdealCardsByColor($dealCards, $handCards);
    }



    //计算暗牌使用
    public function getDealCardsByDarkCardGroup()
    {

        if (!$this->hupaiJichu) {
            //没有胡牌基础 检测是否有纯顺
            if (empty($this->groupList['group_chun_shun_3_lian']) && empty($this->groupList['group_chun_shun_4_lian']) && empty($this->groupList['group_chun_shun_5_lian'])) {
                $rtn['group'] = [];
                $rtn['sanpai'] = $this->handCards;
                return $rtn;
            } else {
                $excludeGroupList = ['group_chun_shun_3_lian', 'group_chun_shun_4_lian', 'group_chun_shun_5_lian'];
            }
        } else {
            $excludeGroupList = [
                'group_chun_shun_3_lian',
                'group_chun_shun_4_lian',
                'group_chun_shun_5_lian',
                'group_hun_shun_3_lian',
                'group_hun_shun_4_lian',
                'group_hun_shun_5_lian',
                'group_chun_3_tiao',
                'group_chun_4_tiao',
                'group_hun_3_tiao'
            ];
        }
        $groupList = $this->groupList;
        unset($groupList['group_laizi_guipai']);
        $sanpai = $groupList['group_san_pai'];
        unset($groupList['group_san_pai']);
        $group = [];
        foreach ($groupList as $key => $val) {
            //需要独立分组的
            $val = array_filter($val);
            while (!empty($val)) {
                $tmp = array_filter(array_shift($val));
                foreach ($tmp as $v) {
                    if (in_array($key, $excludeGroupList)) {
                        $group[] = $v;
                    } else {
                        $sanpai = array_merge($sanpai, $v);
                    }

                }
            }
        }
        $rtn['group'] = $group;
        $rtn['sanpai'] = $sanpai;
        return $rtn;
    }


    //根据花色分组合并最多5组牌型
    public function getdealCardsByColor($dealCards, $handCards)
    {
        //已经占用的牌堆数量
        $usedGroupCnt = count($dealCards);
        if (!empty($handCards)) {
            $groupColorList = group_rummy_cards_by_color($handCards);
            $groupColorList = array_values($groupColorList);
            $mergerCnt = ($usedGroupCnt + count($groupColorList)) - 5;
            $mergerCardArr = [];
            if ($mergerCnt > 0) {
                //执行花色牌组合并
                shuffle($groupColorList);
                for ($i = 0; $i <= 3; $i++) {
                    if (!isset($groupColorList[$i])) {
                        continue;
                    }
                    if ($i <= $mergerCnt) {
                        $mergerCardArr = array_merge($mergerCardArr, $groupColorList[$i]);
                        unset($groupColorList[$i]);
                    }
                }
            }
            $dealCards = array_merge($dealCards, $groupColorList);
            if ($mergerCardArr) {
                $dealCards[] = $mergerCardArr;
            }
        }
        return $dealCards;
    }

    //获取绿色牌型个数
    //优化计算绿的数量公式
    public function getGroupGreenCnt()
    {
        $cnt = 0;
        $chunCnt = 0;
        $chunCnt += count_two_dimensional_array($this->groupList['group_chun_shun_3_lian']);
        $chunCnt += count_two_dimensional_array($this->groupList['group_chun_shun_4_lian']);
        $chunCnt += count_two_dimensional_array($this->groupList['group_chun_shun_5_lian']);
        if ($this->hupaiJichu) {
            //有基础胡牌条件  set 也是绿色
            $cnt += count_two_dimensional_array($this->groupList['group_hun_shun_3_lian']);
            $cnt += count_two_dimensional_array($this->groupList['group_hun_shun_4_lian']);
            $cnt += count_two_dimensional_array($this->groupList['group_hun_shun_5_lian']);
            $cnt += count_two_dimensional_array($this->groupList['group_chun_3_tiao']);
            $cnt += count_two_dimensional_array($this->groupList['group_chun_4_tiao']);
            $cnt += count_two_dimensional_array($this->groupList['group_hun_3_tiao']);
            $cnt += $chunCnt;
        } else {
            $cnt += $chunCnt;
        }
        if ($chunCnt >= 1) {
            $cnt += bcdiv(count($this->groupList['group_laizi_guipai']), 2, 0);
        }
        return $cnt;
    }


    //执行组排
    private function initGroupCard()
    {
        //执行顺子牌组
        $this->initGroupShunzi();
        //检测是否达到胡牌基础条件
        $this->checkHuPaiJiChu();
        //根据胡牌基础条件检测结果 执行下列分组牌型的优先级
        if ($this->hupaiJichu) {
            //达到了基础胡牌条件 执行B策略组牌方式
            $this->initGroupCardB();
        } else {
            //未达到基础胡牌条件  执行A策略组牌方式
            $this->initGroupCardA();
        }
    }
    //AB策略先决条件匹配顺子
    private function initGroupShunzi()
    {
        //挑选出癞子以及鬼牌
        $this->pickLaiZiGuipaiFromHandCards();

        //【1、纯三顺】匹配3连纯顺子 每张牌1000分
        $this->initChunshun3lian();

        //癞子鬼牌不做自己的情况下 如果匹配不到纯顺 则把癞子加入再执行纯顺匹配
        $chunShun3Cnt = isset($this->groupList['group_chun_shun_3_lian']) ? count_two_dimensional_array($this->groupList['group_chun_shun_3_lian']) : 0;
        $laiziGuiCnt = isset($this->groupList['group_laizi_guipai']) ? count_one_dimensional_array($this->groupList['group_laizi_guipai']) : 0;
        if ($chunShun3Cnt < 1 && $laiziGuiCnt >= 1) {
            //如果没有非癞子的纯顺 合并癞子手牌  再匹配一边
            $this->memgerLaiZiGuiPaiToHandCards();

            //再执行一次纯3匹配  且只匹配一个就终止
            $this->initChunshun3lian(1);
        }
        //挑选出癞子以及鬼牌
        $this->pickLaiZiGuipaiFromHandCards();
        //【2、混五顺】匹配混5连 策略原因（可能会拆为2个混3连）优先级高于3 4连 节约癞子和鬼牌
        $this->initHunshun5lian();
        //【3、混三顺】匹配3连混顺子 每张牌1000分
        $this->initHunshun3lian();
    }
    //B策略组排方式
    private function initGroupCardB()
    {
        $this->initChun3tiao(); //【4、纯三条】
        $this->initHun3Tiao(); //【5、混三条】
        $this->initChun3tiaoTo4tiao(); //【7、纯三条升级为纯4条】
        $this->initHunshun3lianTo4lian(); //【6、混三顺升级到混4顺】
        $this->initChunshun3lianTo4lian(); //【8、纯3连升级为纯4连】
        $this->initChunshun4lianTo5lian(); //【9、纯4连升级为纯5连】
        $this->init2Lian2Dui(); //【10、两连两对】
        $this->initBianJia2Dui(); //【11、边夹两对】
        $this->init2LianDui(); //【12、两连对】
        $this->initBianJiaDui(); //【13、边夹对】
        $this->init2Lian(); //【14、两连】
        $this->initDui(); //【15、一对】
        $this->initBianJia(); //【16、边夹】
    }
    //A策略组排方式
    private function initGroupCardA()
    {
        $this->initHunshun3lianTo4lian(); //【4、混3顺升级到混4顺】
        $this->initChunshun3lianTo4lian(); //【5、纯3连升级为纯4连】
        $this->initChunshun4lianTo5lian(); //【6、纯4连升级为纯5连】
        $this->init2Lian2Dui(); //【7、两连两对】
        $this->init2Lian(); //【8、两连】
        $this->initChun3tiao(); //【9、纯三条】
        $this->initChun4tiao(); //【10、纯四条】
        $this->initBianJia2Dui(); //【11、边夹两对】
        $this->initBianJiaDui(); //【12、边夹对】
        //$this->initHun3Tiao();			//【13、混三条】
        $this->initBianJia(); //【14、边夹】
        $this->initDui(); //【15、一对】
    }
    //取出手牌中剩余的赖子牌以及鬼牌
    protected function pickLaiZiGuipaiFromHandCards()
    {
        if (!$this->groupList['group_san_pai']) {
            return;
        }
        foreach ($this->groupList['group_san_pai'] as $key => $val) {
            if (in_array($val, $this->allLCards)) {
                //挑选手牌的所有癞子
                unset($this->groupList['group_san_pai'][$key]);
                $this->groupList['group_laizi_guipai'] = isset($this->groupList['group_laizi_guipai']) ? $this->groupList['group_laizi_guipai'] : [];
                array_push($this->groupList['group_laizi_guipai'], $val);
            }
        }
    }
    //合并鬼牌癞子到手牌中
    protected function memgerLaiZiGuiPaiToHandCards()
    {
        $this->groupList['group_san_pai'] = array_merge_one_dimensional_array($this->groupList['group_san_pai'], $this->groupList['group_laizi_guipai']);
        $this->groupList['group_laizi_guipai'] = [];
    }
    //获取当前牌组中的鬼牌癞子牌数量
    private function getGroupListLaiZiGuiPaiNum()
    {
        return isset($this->groupList['group_laizi_guipai']) ? count_one_dimensional_array($this->groupList['group_laizi_guipai']) : 0;
    }
    //从手牌中取出一张癞子或者鬼牌
    private function getOneLOrGCard()
    {
        if (empty($this->groupList['group_laizi_guipai'])) {
            return '';
        } else {
            return array_pop($this->groupList['group_laizi_guipai']);
        }
    }


    //匹配基础牌型不包含混子
    private function initGroupByBaseGroupCardsNotUseHun($groupName, $baseGroup, $isDescBaseGroup = false, $matchCnt = 100)
    {
        return $this->initGroupByBaseGroupCardsNotUseHunV2($groupName, $baseGroup, $isDescBaseGroup, $matchCnt);

        $savedGroupList = $this->groupList;

        if ($isDescBaseGroup) {
            $baseGroup = array_reverse($baseGroup, true);
        }
        $nowMatchCnt = 0;
        //匹配牌型
        foreach ($baseGroup as $key => $val) {
            if ($nowMatchCnt >= $matchCnt) {
                break;
            }
            //2副牌 每个牌型执行2次匹配
            for ($i = 0; $i < 2; $i++) {
                if ($nowMatchCnt >= $matchCnt) {
                    break;
                }
                //手牌去重
                if (!is_array($this->groupList['group_san_pai']) || empty($this->groupList['group_san_pai'])) {
                    break;
                }
                $uniqueHandCards = array_unique_one_dimensional($this->groupList['group_san_pai']);
                if (count_one_dimensional_array($uniqueHandCards) < count_one_dimensional_array($val)) {
                    //剩余手牌不足匹配当前牌型数量 则终止
                    break;
                }

                //初始化数据
                $intersectCards = [];
                $diffCards = [];

                //求手牌交集
                $intersectCards = array_intersect($uniqueHandCards, $val);
                //Log::err_log(__FUNCTION__,json_encode($uniqueHandCards));
                //Log::err_log(__FUNCTION__,json_encode($val));
                if (count_one_dimensional_array($intersectCards) != count_one_dimensional_array($val)) {
                    //不符合当前牌型进行下一组匹配
                    continue;
                }
                //匹配到牌型 移除手牌中的牌型牌
                $diffCards = del_b_arr_from_a_arr($this->groupList['group_san_pai'], $val);
                $this->groupList['group_san_pai'] = $diffCards;

                //放入牌组池中  保持下标
                $this->groupList[$groupName][$key][] = $val;
                //重新排序
                asort($this->groupList[$groupName]);

                //当前已匹配到的个数累加
                $nowMatchCnt++;
            }
        }

        $savedResult1 = $this->groupList;
        $this->groupList = $savedGroupList;
        $this->initGroupByBaseGroupCardsNotUseHunV2($groupName, $baseGroup, false, $matchCnt);
        $savedResult2 = $this->groupList;
        $eq = $savedResult1 == $savedResult2;
        if (!$eq) {
            time();
        }
    }

    private static function cardArrayToBits($cards): int
    {
        if (empty($cards)) {
            return 0;
        }
        $bits = 0;
        foreach ($cards as $card) {
            // 牌值设计超过了 64 位整数，得处理一下
            // 把大小王和鬼牌映射到空位上
            if ($card >= 0x41) {
                $card = $card - 0x41 + 0x0E;
            }
            $bits |= 1 << $card;
        }
        return $bits;
    }

    // 匹配基础牌型不包含混子
    // 这个函数是热点，尝试利用位操作优化一下：
    // 牌的交集可以利用位与
    // 手牌的位向量可以用局部变量缓存
    private function initGroupByBaseGroupCardsNotUseHunV2($groupName, $baseGroup, $isDescBaseGroup = false, $matchCnt = 100)
    {
        if ($isDescBaseGroup) {
            $baseGroup = array_reverse($baseGroup, true);
        }
        $nowMatchCnt = 0;

        if (empty($baseGroup)) {
            return;
        }

        $meld_size = count(reset($baseGroup));

        // 局部变量缓存散牌状态
        $group_san_pai = $this->groupList['group_san_pai'] ?? [];
        if (count($group_san_pai) < $meld_size) {
            return;
        }
        $group_san_pai_bits = self::cardArrayToBits($group_san_pai);

        //匹配牌型
        foreach ($baseGroup as $key => $val) {
            if ($nowMatchCnt >= $matchCnt) {
                break;
            }

            $val_bits = self::cardArrayToBits($val);

            //2副牌 每个牌型执行2次匹配
            for ($i = 0; $i < 2; $i++) {
                if ($nowMatchCnt >= $matchCnt) {
                    break;
                }

                $intersect_bits = $group_san_pai_bits & $val_bits;
                if ($intersect_bits != $val_bits) {
                    // 剩余手牌不足匹配当前牌型
                    break;
                }

                // 匹配到牌型 移除手牌中的牌型牌
                $diffCards = del_b_arr_from_a_arr($this->groupList['group_san_pai'], $val);

                $this->groupList['group_san_pai'] = $diffCards;

                //放入牌组池中  保持下标
                $this->groupList[$groupName][$key][] = $val;
                //重新排序
                asort($this->groupList[$groupName]);

                //当前已匹配到的个数累加
                $nowMatchCnt++;


                // 局部变量缓存散牌状态
                $group_san_pai = $this->groupList['group_san_pai'] ?? [];
                if (count($group_san_pai) < $meld_size) {
                    return;
                }
                $group_san_pai_bits = self::cardArrayToBits($group_san_pai);
            }
        }
    }


    private static function isPowerOfTwo($num)
    {
        // 判断是否只有一个 1
        return ($num & ($num - 1)) == 0;
    }

    // 匹配基础牌型包含混子
    private function initGroupByBaseGroupCardsUseHunV2($groupName, $baseGroup, $isDescBaseGroup = false, $matchCnt = 100)
    {

        //挑选出癞子以及鬼牌
        $this->pickLaiZiGuipaiFromHandCards();

        // 没有癞子不执行
        if ($this->getGroupListLaiZiGuiPaiNum() <= 0) {
            return;
        }

        $nowMatchCnt = 0;

        //基础牌型牌组是否需要倒叙排序
        if ($isDescBaseGroup) {
            $baseGroup = array_reverse($baseGroup, true);
        }

        if (empty($baseGroup)) {
            return;
        }

        // 不算那张鬼牌
        $meld_size = count(reset($baseGroup)) - 1;

        // 局部变量缓存散牌状态
        $group_san_pai = $this->groupList['group_san_pai'] ?? [];
        if (count($group_san_pai) < $meld_size) {
            return;
        }
        $group_san_pai_bits = self::cardArrayToBits($group_san_pai);

        foreach ($baseGroup as $key => $val) {
            if ($nowMatchCnt >= $matchCnt) {
                break;
            }

            $val_bits = self::cardArrayToBits($val);

            for ($i = 0; $i < 2; $i++) {
                if ($nowMatchCnt >= $matchCnt) {
                    break;
                }

                $intersect_bits = $group_san_pai_bits & $val_bits;
                $lost_bits = $val_bits & ~$intersect_bits;
                // 得正好缺一张
                if (!self::isPowerOfTwo($lost_bits)) {
                    // 剩余手牌不足匹配当前牌型
                    break;
                }

                // 重新设置等待排序的剩余牌
                $diffCards = del_b_arr_from_a_arr($this->groupList['group_san_pai'], $val);
                $this->groupList['group_san_pai'] = $diffCards;

                // 替换基础牌型中的鬼牌癞子标识
                $glKey = array_search(GL_CARD, $val);
                $glArr = array($glKey => $this->getOneLOrGCard());
                $this->groupList[$groupName][$key][] = array_replace($val, $glArr);

                // 按照下标排序
                // 这个怎么就是按下标排序的？谁依赖于这里？
                asort($this->groupList[$groupName]);

                // 当前已匹配到的个数累加
                //$nowMatchCnt++; // 之前的代码漏了这行，所以暂时也保留这个问题

                // 局部变量缓存散牌状态
                $group_san_pai = $this->groupList['group_san_pai'] ?? [];
                if (count($group_san_pai) < $meld_size) {
                    return;
                }
                $group_san_pai_bits = self::cardArrayToBits($group_san_pai);

                // 没有癞子不执行
                if ($this->getGroupListLaiZiGuiPaiNum() <= 0) {
                    return;
                }
            }
        }

    }



    // 匹配基础牌型包含混子
    private function initGroupByBaseGroupCardsUseHun($groupName, $baseGroup, $isDescBaseGroup = false, $matchCnt = 100)
    {
        return $this->initGroupByBaseGroupCardsUseHunV2($groupName, $baseGroup, $isDescBaseGroup, $matchCnt);

        $savedGroupList = $this->groupList;

        //挑选出癞子以及鬼牌
        $this->pickLaiZiGuipaiFromHandCards();

        //判定当前是否还有癞子  没有癞子 则终止
        if ($this->getGroupListLaiZiGuiPaiNum() <= 0) {
            return;
        }
        $nowMatchCnt = 0;

        //基础牌型牌组是否需要倒叙排序
        if ($isDescBaseGroup) {
            $baseGroup = array_reverse($baseGroup, true);
        }
        foreach ($baseGroup as $key => $val) {
            if ($nowMatchCnt >= $matchCnt) {
                break;
            }
            for ($i = 0; $i < 2; $i++) {
                if ($nowMatchCnt >= $matchCnt) {
                    break;
                }

                //没有癞子不执行
                if ($this->getGroupListLaiZiGuiPaiNum() <= 0) {
                    break;
                }

                //去重处理剩余手牌
                $uniqueCards = array_unique_one_dimensional($this->groupList['group_san_pai']);
                //剩余牌数量不足以匹配牌型 终止
                if (count_one_dimensional_array($uniqueCards) < (count_one_dimensional_array($val) - 1)) {
                    break;
                }

                //初始化数据
                $intersectCards = [];
                $diffCards = [];

                $intersectCards = array_intersect($uniqueCards, $val);
                if (count_one_dimensional_array($intersectCards) == (count_one_dimensional_array($val) - 1)) {

                    //重新设置等待排序的剩余牌
                    $diffCards = del_b_arr_from_a_arr($this->groupList['group_san_pai'], $val);
                    $this->groupList['group_san_pai'] = $diffCards;


                    //替换基础牌型中的鬼牌癞子标识
                    $glKey = array_search(GL_CARD, $val);
                    $glArr = array($glKey => $this->getOneLOrGCard());
                    $this->groupList[$groupName][$key][] = array_replace($val, $glArr);
                    //按照下标排序
                    asort($this->groupList[$groupName]);

                }
            }
        }


        $savedResult1 = $this->groupList;
        $this->groupList = $savedGroupList;
        $this->initGroupByBaseGroupCardsUseHunV2($groupName, $baseGroup, false, $matchCnt);
        $savedResult2 = $this->groupList;
        $eq = $savedResult1 == $savedResult2;
        if (!$eq) {
            time();
        }
    }



    //牌型升级  每次固定升级一张牌
    private function initGroupToHighGroup($fromGroupName, $fromBaseGroup, $toGroupName, $toBaseGroup, $isUseHun = false, $isSet = false)
    {
        if (empty($this->groupList[$fromGroupName])) {
            return;
        }
        $fromGroupTmp = $this->groupList[$fromGroupName];
        foreach ($fromGroupTmp as $key => $val) {
            foreach ($val as $k => $v) {
                $valKey = $k;
                $valTmp = $v;
                $res = false;
                if ($isSet) {
                    //获取缺失的 4条 牌
                    $setAllCardList = get_all_color_cards_by_one_card(array_shift($valTmp));
                    $loseCard = del_b_arr_from_a_arr($setAllCardList, $v);
                    if (array_intersect($loseCard, $this->groupList['group_san_pai'])) {
                        $res = true;
                    }
                } else {
                    //获取缺失的 左右 牌
                    $LoseLeftCard = get_adjacent_left_card(array_shift($valTmp));
                    $LoserightCard = get_adjacent_right_card(array_pop($valTmp));
                    if (in_array($LoseLeftCard, $this->groupList['group_san_pai']) || in_array($LoserightCard, $this->groupList['group_san_pai'])) {
                        $res = true;
                    }
                }

                if ($res) {
                    //合并手牌   这里重新匹配 是因为 需要枚举牌型的下标
                    $this->groupList['group_san_pai'] = array_merge($this->groupList[$fromGroupName][$key][$valKey], $this->groupList['group_san_pai']);
                    unset($this->groupList[$fromGroupName][$key][$valKey]);
                    if ($isUseHun) {
                        //取出癞子 放入癞子牌组中
                        $this->pickLaiZiGuipaiFromHandCards();
                        //预匹配牌型
                        $this->initGroupByBaseGroupCardsUseHun($toGroupName, $toBaseGroup);
                        //防止遗漏
                        $this->initGroupByBaseGroupCardsUseHun($fromGroupName, $fromBaseGroup);
                    } else {
                        //预匹配牌型
                        $this->initGroupByBaseGroupCardsNotUseHun($toGroupName, $toBaseGroup);
                        //防止遗漏
                        $this->initGroupByBaseGroupCardsNotUseHun($fromGroupName, $fromBaseGroup);
                    }

                }
            }
        }

    }
    //纯顺3连
    protected function initChunshun3lian($matchCnt = 100, $isDescBaseGroup = false)
    {
        //牌组名称
        $groupName = 'group_chun_shun_3_lian';
        //牌型
        $baseGroup = BASE_CHUN_SHUN_3_LIAN_LIST;
        //执行匹配操作
        $this->initGroupByBaseGroupCardsNotUseHun($groupName, $baseGroup, $isDescBaseGroup, $matchCnt);
    }
    //初始化匹配4连顺子
    protected function initChunshun4lian($matchCnt = 100, $isDescBaseGroup = false)
    {
        //牌组名称
        $groupName = 'group_chun_shun_4_lian';
        //牌型
        $baseGroup = BASE_CHUN_SHUN_4_LIAN_LIST;
        //执行匹配操作
        $this->initGroupByBaseGroupCardsNotUseHun($groupName, $baseGroup, $isDescBaseGroup, $matchCnt);
    }
    //纯3条
    protected function initChun3tiao($matchCnt = 100, $isDescBaseGroup = false)
    {
        //牌组名称
        $groupName = 'group_chun_3_tiao';
        //牌型
        $baseGroup = BASE_CHUN_3_TIAO_LIST;
        //执行匹配操作
        $this->initGroupByBaseGroupCardsNotUseHun($groupName, $baseGroup, $isDescBaseGroup, $matchCnt);
    }
    //纯4条
    protected function initChun4tiao($matchCnt = 100, $isDescBaseGroup = false)
    {
        //牌组名称
        $groupName = 'BASE_CHUN_4_TIAO_LIST';
        //牌型
        $baseGroup = BASE_CHUN_3_TIAO_LIST;
        //执行匹配操作
        $this->initGroupByBaseGroupCardsNotUseHun($groupName, $baseGroup, $isDescBaseGroup, $matchCnt);
    }
    //2连2对
    protected function init2Lian2Dui($matchCnt = 100, $isDescBaseGroup = false)
    {
        //牌组名称
        $groupName = 'group_2_lian_2_dui';
        //牌型
        $baseGroup = BASE_2_LIAN_2_DUI_LIST;
        //执行匹配操作
        $this->initGroupByBaseGroupCardsNotUseHun($groupName, $baseGroup, $isDescBaseGroup, $matchCnt);
    }
    //2连
    protected function init2Lian($matchCnt = 100, $isDescBaseGroup = false)
    {
        //牌组名称
        $groupName = 'group_2_lian';
        //牌型
        $baseGroup = BASE_2_LIAN_LIST;
        //执行匹配操作
        $this->initGroupByBaseGroupCardsNotUseHun($groupName, $baseGroup, $isDescBaseGroup, $matchCnt);
    }
    //边夹2对
    protected function initBianJia2Dui($matchCnt = 100, $isDescBaseGroup = false)
    {
        //牌组名称
        $groupName = 'group_bian_jia_2_dui';
        //牌型
        $baseGroup = BASE_BIAN_JIA_2_DUI_LIST;
        //执行匹配操作
        $this->initGroupByBaseGroupCardsNotUseHun($groupName, $baseGroup, $isDescBaseGroup, $matchCnt);
    }
    //边夹2对
    protected function initBianJiaDui($matchCnt = 100, $isDescBaseGroup = false)
    {
        //牌组名称
        $groupName = 'group_bian_jia_dui';
        //牌型
        $baseGroup = BASE_BIAN_JIA_DUI_LIST;
        //执行匹配操作
        $this->initGroupByBaseGroupCardsNotUseHun($groupName, $baseGroup, $isDescBaseGroup, $matchCnt);
    }
    //边夹2对
    protected function initBianJia($matchCnt = 100, $isDescBaseGroup = false)
    {
        //牌组名称
        $groupName = 'group_bian_jia';
        //牌型
        $baseGroup = BASE_BIANJIA_LIST;
        //执行匹配操作
        $this->initGroupByBaseGroupCardsNotUseHun($groupName, $baseGroup, $isDescBaseGroup, $matchCnt);
    }
    //边夹2对
    protected function initDui($matchCnt = 100, $isDescBaseGroup = false)
    {
        //牌组名称
        $groupName = 'group_dui';
        //牌型
        $baseGroup = BASE_DUI_LIST;
        //执行匹配操作
        $this->initGroupByBaseGroupCardsNotUseHun($groupName, $baseGroup, $isDescBaseGroup, $matchCnt);
    }
    //边夹2对
    protected function init2LianDui($matchCnt = 100, $isDescBaseGroup = false)
    {
        //牌组名称
        $groupName = 'group_2_lian_dui';
        //牌型
        $baseGroup = BASE_2_LIAN_DUI_LIST;
        //执行匹配操作
        $this->initGroupByBaseGroupCardsNotUseHun($groupName, $baseGroup, $isDescBaseGroup, $matchCnt);
    }
    //纯3连 匹配变更到纯4连
    protected function initChunshun3lianTo4lian()
    {
        //需要升级的低牌型
        $fromGroupName = 'group_chun_shun_3_lian';
        $fromBaseGroup = BASE_CHUN_SHUN_3_LIAN_LIST;
        //需要升级到的高牌型
        $toGroupName = 'group_chun_shun_4_lian';
        $toBaseGroup = BASE_CHUN_SHUN_4_LIAN_LIST;
        $this->initGroupToHighGroup($fromGroupName, $fromBaseGroup, $toGroupName, $toBaseGroup);
    }
    //纯4连 匹配变更到纯5连
    protected function initChunshun4lianTo5lian()
    {
        //需要升级的低牌型
        $fromGroupName = 'group_chun_shun_4_lian';
        $fromBaseGroup = BASE_CHUN_SHUN_4_LIAN_LIST;
        //需要升级到的高牌型
        $toGroupName = 'group_chun_shun_5_lian';
        $toBaseGroup = BASE_CHUN_SHUN_5_LIAN_LIST;
        $this->initGroupToHighGroup($fromGroupName, $fromBaseGroup, $toGroupName, $toBaseGroup, );
    }
    //纯3条升级到纯4条
    protected function initChun3tiaoTo4tiao()
    {
        //需要升级的低牌型
        $fromGroupName = 'group_chun_3_tiao';
        $fromBaseGroup = BASE_CHUN_3_TIAO_LIST;
        //需要升级到的高牌型
        $toGroupName = 'group_chun_4_tiao';
        $toBaseGroup = BASE_CHUN_4_TIAO_LIST;
        $isUseHun = false;
        $isSet = true;
        $this->initGroupToHighGroup($fromGroupName, $fromBaseGroup, $toGroupName, $toBaseGroup, $isUseHun, $isSet);
    }
    //混3连
    protected function initHunshun3lian($matchCnt = 100, $isDescBaseGroup = false)
    {
        //牌组名称
        $groupName = 'group_hun_shun_3_lian';
        //牌型
        $baseGroup = BASE_HUN_SHUN_3_LIAN_LIST;
        //执行匹配操作
        $this->initGroupByBaseGroupCardsUseHun($groupName, $baseGroup, $isDescBaseGroup, $matchCnt);

    }
    //混4连
    protected function initHunshun4lian($matchCnt = 100, $isDescBaseGroup = false)
    {
        //牌组名称
        $groupName = 'group_hun_shun_4_lian';
        //牌型
        $baseGroup = BASE_HUN_SHUN_4_LIAN_LIST;
        //执行匹配操作
        $this->initGroupByBaseGroupCardsUseHun($groupName, $baseGroup, $isDescBaseGroup, $matchCnt);
    }
    //混5连
    protected function initHunshun5lian($matchCnt = 100, $isDescBaseGroup = false)
    {
        //牌组名称
        $groupName = 'group_hun_shun_5_lian';
        //牌型
        $baseGroup = BASE_HUN_SHUN_5_LIAN_LIST;
        //执行匹配操作
        $this->initGroupByBaseGroupCardsUseHun($groupName, $baseGroup, $isDescBaseGroup, $matchCnt);

    }
    //匹配混3条
    protected function initHun3Tiao($matchCnt = 100, $isDescBaseGroup = false)
    {
        //牌组名称
        $groupName = 'group_hun_3_tiao';
        //牌型
        $baseGroup = BASE_HUN_3_TIAO_LIST;
        //执行匹配操作
        $this->initGroupByBaseGroupCardsUseHun($groupName, $baseGroup, $isDescBaseGroup, $matchCnt);

    }
    //混3连 匹配变更到混4连
    protected function initHunshun3lianTo4lian()
    {
        //需要升级的低牌型
        $fromGroupName = 'group_hun_shun_3_lian';
        $fromBaseGroup = BASE_HUN_SHUN_3_LIAN_LIST;
        //需要升级到的高牌型
        $toGroupName = 'group_hun_shun_4_lian';
        $toBaseGroup = BASE_HUN_SHUN_4_LIAN_LIST;
        $isUseHun = true;
        $isSet = false;
        $this->initGroupToHighGroup($fromGroupName, $fromBaseGroup, $toGroupName, $toBaseGroup, $isUseHun, $isSet);
    }
    //获取纯顺数量
    private function getChunShunCnt()
    {
        $group_chun_shun_3_lian_cnt = isset($this->groupList['group_chun_shun_3_lian']) ? count_one_dimensional_array($this->groupList['group_chun_shun_3_lian']) : 0;
        $group_chun_shun_4_lian_cnt = isset($this->groupList['group_chun_shun_4_lian']) ? count_one_dimensional_array($this->groupList['group_chun_shun_4_lian']) : 0;
        $group_chun_shun_5_lian_cnt = isset($this->groupList['group_chun_shun_5_lian']) ? count_one_dimensional_array($this->groupList['group_chun_shun_5_lian']) : 0;
        return $group_chun_shun_3_lian_cnt + $group_chun_shun_4_lian_cnt + $group_chun_shun_5_lian_cnt;
    }
    //获取混顺数量
    private function getHunShunCnt()
    {
        $group_hun_shun_3_lian_cnt = isset($this->groupList['group_hun_shun_3_lian']) ? count_one_dimensional_array($this->groupList['group_hun_shun_3_lian']) : 0;
        $group_hun_shun_4_lian_cnt = isset($this->groupList['group_hun_shun_4_lian']) ? count_one_dimensional_array($this->groupList['group_hun_shun_4_lian']) : 0;
        $group_hun_shun_5_lian_cnt = isset($this->groupList['group_hun_shun_5_lian']) ? count_one_dimensional_array($this->groupList['group_hun_shun_5_lian']) : 0;
        return $group_hun_shun_3_lian_cnt + $group_hun_shun_4_lian_cnt + $group_hun_shun_5_lian_cnt;
    }
    //检测胡牌基础条件
    private function checkHuPaiJiChu()
    {
        $chunShunCnt = $this->getChunShunCnt();
        //执行10card判定1纯顺）
        if (($this->maxCard == 10 || $this->maxCard == 11) && $chunShunCnt >= 1) {
            $this->hupaiJichu = true;
            return;
        }
        //执行13card判定1纯1混 or  2纯）
        $hunShunCnt = $this->getHunShunCnt();
        if (($this->maxCard == 13 || $this->maxCard == 14) && ($chunShunCnt >= 2 || ($chunShunCnt >= 1 && $hunShunCnt >= 1))) {
            $this->hupaiJichu = true;
            return;
        }
    }
}
