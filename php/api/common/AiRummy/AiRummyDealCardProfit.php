<?php
/**
 * @todo 盈利率发牌接口
 */

namespace common\AiRummy;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use common\AiRummy\AiRummyGroupCard;
use common\Config;
use lib\Log;

class AiRummyDealCardProfit
{
    //最大发牌牌组数量
    private $maxCardNum = 13;
    //当前癞子
    private $xcard;
    //牌池
    private $pool;
    //牌组绿的个数
    private $greenCnt = 0;
    //牌组癞子数量
    private $xcardCnt = 0;
    //需要发的牌型名称
    private $groupName;
    //组成的牌
    private $userDealCards = [];
    //当前所有癞子
    private $allXcardList = [];

    //牌池中的所有癞子牌
    private $poolAllXcardList = [];

    //支持的牌组名称
    private $groupNameList = [];

    //纯顺3连中有癞子的
    private $baseGroupChunShun3LianHasXcardList = [];

    //癞子的数量概率分布
    private $groupNameXcardCntRateList = [
        Common::PROFIT_GROUP_NAME_SANPAI => [0 => 0, 1 => 0, 2 => 0, 3 => 0, 4 => 0], //几个都可以  需要特殊处理
        Common::PROFIT_GROUP_NAME_NOLV_NOLAI => [0 => 0, 1 => 0, 2 => 0, 3 => 0, 4 => 0],
        Common::PROFIT_GROUP_NAME_NOLV_YESLAI => [0 => 0, 1 => 560, 2 => 319, 3 => 101, 4 => 2],
        Common::PROFIT_GROUP_NAME_1LV => [0 => 749, 1 => 243, 2 => 8, 3 => 0, 4 => 0],
        Common::PROFIT_GROUP_NAME_2LV => [0 => 78, 1 => 659, 2 => 231, 3 => 32, 4 => 0],
        Common::PROFIT_GROUP_NAME_3LV => [0 => 14, 1 => 212, 2 => 517, 3 => 216, 4 => 41],
    ];

    //定义绿的数量
    private $groupNameGreenCntList = [
        Common::PROFIT_GROUP_NAME_SANPAI => -1, //几个都可以
        Common::PROFIT_GROUP_NAME_NOLV_NOLAI => 0,
        Common::PROFIT_GROUP_NAME_NOLV_YESLAI => 0,
        Common::PROFIT_GROUP_NAME_1LV => 1,
        Common::PROFIT_GROUP_NAME_2LV => 2,
        Common::PROFIT_GROUP_NAME_3LV => 3,
    ];

    public function __construct($pool, $xcard, $maxCardNum)
    {
        $this->maxCardNum = $maxCardNum;
        $this->pool = $pool;
        $this->xcard = $xcard;
        //当前所有的癞子
        $this->allXcardList = get_all_gui_lai_cards_by_card($this->xcard);
        $this->groupNameList = Common::getProfitGroupNameList();
        $this->baseGroupChunShun3LianHasXcardList = $this->getBaseGroupChunShun3LianHasXcard();
    }

    //获取发牌名称结果
    public function getDealCardGroupName()
    {
        return $this->groupName;
    }
    //根据牌组进行发牌
    public function getDealCardsByGroup($groupName)
    {

        $this->userDealCards = [];
        $this->groupName = isset($this->groupNameList[$groupName]) ? $groupName : Common::PROFIT_GROUP_NAME_SANPAI;

        $this->greenCnt = $this->groupNameGreenCntList[$this->groupName];

        //随机得出癞子
        $randXcardCnt = get_rand($this->groupNameXcardCntRateList[$this->groupName]);
        $randXcardCnt = $randXcardCnt <= 0 ? 0 : $randXcardCnt;

        //查看牌池剩余的癞子数量
        $poolXcardCnt = array_intersect($this->allXcardList, $this->pool);

        //最大不超过牌池中的癞子数量
        $this->xcardCnt = min($randXcardCnt, $poolXcardCnt);

        $this->getRandCardByBaseGroup();

        //执行log写入
        Log::console_object_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, json_encode(object_to_array($this)));
        shuffle($this->userDealCards);
        return $this->userDealCards;
    }
    //获取剩余牌池
    public function getPool()
    {
        return $this->pool;
    }

    //取出手牌中剩余的赖子牌以及鬼牌
    protected function pickLaiZiGuipaiFromHandCards(&$pool)
    {
        $poolAllXcardList = [];
        foreach ($pool as $key => $val) {
            if (in_array($val, $this->allXcardList)) {
                $poolAllXcardList[] = $val;
                unset($pool[$key]);
            }
        }
        shuffle($pool);
        shuffle($poolAllXcardList);
        return $poolAllXcardList;
    }

    //随机获取指定牌组的牌
    private function getRandCardByBaseGroup()
    {
        if ($this->groupName == Common::PROFIT_GROUP_NAME_SANPAI) {
            //散牌直接纯随机处理
            $this->userDealCards = $this->getRandCardByNum($this->pool, $this->maxCardNum, $dealCards = []);
            return;
        }
        $poolAllXcardList = [];
        //如果牌组没有癞子  先挑出手牌中的所有癞子再执行循环
        if ($this->xcardCnt == 0) {
            $poolAllXcardList = $this->pickLaiZiGuipaiFromHandCards($this->pool);
        }
        //设定一个终止次数 防止死循环  暂定1000次
        for ($i = 0; $i < 200; $i++) {
            //生成一副随机牌
            $dealCards = $this->getRandCardByBaseGroupByGreenNum();
            $dealXcard = array_intersect($this->allXcardList, $dealCards);
            $dealXcardCnt = $dealXcard ? count($dealXcard) : 0;
            //判定当前牌是否符合癞子情况
            if ($dealXcardCnt > $this->xcardCnt) {
                continue;
            }
            //判断当前牌是否符合绿的数量
            $aiRummyGroupCardObj = new AiRummyGroupCard($dealCards, $this->xcard, $this->maxCardNum);
            $cnt = $aiRummyGroupCardObj->getGroupGreenCnt();
            if ($this->greenCnt == 3 && $cnt < 3) {
                continue;
            }
            if ($this->greenCnt != 3 && $this->greenCnt != $cnt) {
                continue;
            }
            $this->userDealCards = $dealCards;

            //从牌池中取出已经发的牌
            $this->pool = del_b_arr_from_a_arr($this->pool, $dealCards);
            break;
        }
        //循环执行完之后没有所需要的的牌型 就纯随机处理
        if (empty($this->userDealCards)) {
            Log::console_log(__FUNCTION__, '1000次没有发出牌 随机牌处理');
            $this->groupName = Common::PROFIT_GROUP_NAME_SANPAI;
            $this->userDealCards = $this->getRandCardByNum($this->pool, $this->maxCardNum, $dealCards = []);
        }
        //合并癞子和牌池
        $this->pool = array_merge($this->pool, $poolAllXcardList);
        shuffle($this->pool);
        return;
    }

    //获取纯顺中占用癞子的牌组
    private function getBaseGroupChunShun3LianHasXcard()
    {
        $list = [];
        $baseGroup = BASE_CHUN_SHUN_3_LIAN_LIST;
        foreach ($baseGroup as $val) {
            if (array_intersect($val, $this->allXcardList)) {
                $list[] = $val;
            }
        }
        return $list;
    }

    //发3绿的牌
    private function getRandCardByBaseGroupByGreenNum()
    {
        $pool = $this->pool;
        $dealCards = [];
        if ($this->greenCnt <= 0) {
            if ($this->xcardCnt > 0) {
                //处理没有绿
                return $this->getRandCardByNum($pool, $this->maxCardNum, $dealCards, $this->xcardCnt, true);
            } else {
                return $this->getRandCardByNum($pool, $this->maxCardNum, $dealCards);
            }

        }
        $surplusXcardCnt = $this->xcardCnt;

        for ($i = 1; $i <= $this->greenCnt; $i++) {
            $baseGroup = $this->getBaseGroupListByGreenCntAndXcardCnt($i, $this->xcardCnt, $surplusXcardCnt);
            $this->getCardByBaseGroup($baseGroup, $pool, $dealCards, $surplusXcardCnt);
        }

        $cnt = $this->maxCardNum - count($dealCards);
        if ($cnt > 0) {
            $dealCards = $this->getRandCardByNum($pool, $cnt, $dealCards, $surplusXcardCnt, true);
        }
        return $dealCards;
    }




    //根据绿的数量和癞子数量 获取发牌枚举
    private function getBaseGroupListByGreenCntAndXcardCnt($greenCnt, $xcardCnt, $surplusXcardCnt)
    {
        if ($greenCnt == 1) {
            return $this->getBaseGroupListGreen1($xcardCnt, $surplusXcardCnt);
        }
        if ($greenCnt == 2) {
            return $this->getBaseGroupListGreen2($xcardCnt, $surplusXcardCnt);
        }
        if ($greenCnt == 3) {
            return $this->getBaseGroupListGreen2($xcardCnt, $surplusXcardCnt);
        }
    }

    //1绿牌组
    private function getBaseGroupListGreen1($xcardCnt, $surplusXcardCnt)
    {
        //1绿的情况
        if ($this->greenCnt == 1) {
            if ($xcardCnt <= 1) {
                //一个癞子 随机处理
                $baseGroup = BASE_CHUN_SHUN_3_LIAN_LIST;
            }
            if ($xcardCnt >= 2) {
                //2个癞子 第一个纯顺必然占用癞子
                $baseGroup = $this->baseGroupChunShun3LianHasXcardList;
            }
        }
        //2绿
        if ($this->greenCnt == 2) {
            if ($xcardCnt <= 2) {
                //1个癞子 第一个纯顺 随机 占用癞子
                //2个癞子 第一个纯顺 随机 占用癞子
                $baseGroup = BASE_CHUN_SHUN_3_LIAN_LIST;
            }
            if ($xcardCnt >= 3) {
                //3个癞子 第一个纯顺 必然 占用癞子
                //4个癞子 第一个纯顺 必然 占用癞子
                $baseGroup = $this->baseGroupChunShun3LianHasXcardList;
            }
        }
        //3绿
        if ($this->greenCnt == 3) {
            if ($xcardCnt <= 3) {
                //1个癞子 第一个纯顺 随机 占用癞子
                //2个癞子 第一个纯顺 随机 占用癞子
                //3个癞子 第一个纯顺 随机 占用癞子
                $baseGroup = BASE_CHUN_SHUN_3_LIAN_LIST;
            }
            if ($xcardCnt >= 4) {
                //4个癞子 第一个纯顺 必然 占用癞子
                $baseGroup = $this->baseGroupChunShun3LianHasXcardList;
            }
        }
        return $baseGroup;
    }


    //2绿牌组
    private function getBaseGroupListGreen2($xcardCnt, $surplusXcardCnt)
    {

        //2绿
        if ($this->greenCnt == 2) {
            if ($xcardCnt < 1) {
                $baseGroup = BASE_CHUN_SHUN_3_LIAN_LIST;
            }
            if ($xcardCnt == 1) {
                //1个癞子 第一个纯顺 随机 占用癞子  第二个 随机占用癞子
                $baseGroup = array_merge(BASE_CHUN_SHUN_3_LIAN_LIST, BASE_HUN_SHUN_3_LIAN_LIST);
            }
            if ($xcardCnt == 2) {
                if ($xcardCnt - $surplusXcardCnt == 1) {
                    //1个癞子 第一个纯顺 随机 占用癞子  已 使用癞子  第二个 随机 占用癞子
                    $baseGroup = array_merge(BASE_CHUN_SHUN_3_LIAN_LIST, BASE_HUN_SHUN_3_LIAN_LIST);
                } else {
                    //1个癞子 第一个纯顺 随机 占用癞子  未 使用癞子  第二个 必然 占用癞子
                    $baseGroup = array_merge($this->baseGroupChunShun3LianHasXcardList, BASE_HUN_SHUN_3_LIAN_LIST);
                }
            }
            if ($xcardCnt == 3) {
                //1个癞子 第一个纯顺 必然 占用癞子   第二个 必然 占用癞子
                $baseGroup = array_merge($this->baseGroupChunShun3LianHasXcardList, BASE_HUN_SHUN_3_LIAN_LIST);
            }
            if ($xcardCnt == 4) {
                //不执行发牌 走最后的癞子补齐
                $baseGroup = [];
            }
        }
        //3绿
        if ($this->greenCnt == 3) {
            if ($xcardCnt < 1) {
                $baseGroup = BASE_CHUN_SHUN_3_LIAN_LIST;
            }
            if ($xcardCnt == 1) {
                //1个癞子 第一个纯顺 随机 占用癞子  第二个 随机占用癞子
                $baseGroup = array_merge(BASE_CHUN_SHUN_3_LIAN_LIST, BASE_HUN_SHUN_3_LIAN_LIST);
            }
            if ($xcardCnt == 2) {
                //1个癞子 第一个纯顺 随机 占用癞子   第二个 随机 占用癞子
                $baseGroup = array_merge(BASE_CHUN_SHUN_3_LIAN_LIST, BASE_HUN_SHUN_3_LIAN_LIST);
            }
            if ($xcardCnt == 3) {
                //纯顺 混顺随机
                if ($xcardCnt - $surplusXcardCnt == 1) {
                    //1个癞子 第一个纯顺 随机 占用癞子  已 使用癞子  第二个 随机 占用癞子
                    $baseGroup = array_merge(BASE_CHUN_SHUN_3_LIAN_LIST, BASE_HUN_SHUN_3_LIAN_LIST);
                } else {
                    //1个癞子 第一个纯顺 随机 占用癞子  未 使用癞子  第二个 必然 占用癞子
                    $baseGroup = array_merge($this->baseGroupChunShun3LianHasXcardList, BASE_HUN_SHUN_3_LIAN_LIST);
                }
            }
            if ($xcardCnt == 4) {
                //1个癞子 第一个纯顺 必然 占用癞子   第二个 必然 占用癞子
                $baseGroup = array_merge($this->baseGroupChunShun3LianHasXcardList, BASE_HUN_SHUN_3_LIAN_LIST);
            }

        }
        return $baseGroup;
    }


    //3绿牌组
    private function getBaseGroupListGreen3($xcardCnt, $surplusXcardCnt)
    {
        //3绿
        if ($this->greenCnt == 3) {
            if ($xcardCnt < 1) {
                $baseGroup = array_merge(BASE_CHUN_SHUN_3_LIAN_LIST, BASE_CHUN_3_TIAO_LIST);
            }
            if ($xcardCnt == 1) {
                //1个癞子 [第一个纯顺 随机 占用癞子] [第二个 随机 占用癞子] [第三个 随机 占用癞子]
                $baseGroup = array_merge(BASE_CHUN_SHUN_3_LIAN_LIST, BASE_HUN_SHUN_3_LIAN_LIST, BASE_CHUN_3_TIAO_LIST, BASE_HUN_3_TIAO_LIST);
            }
            if ($xcardCnt == 2) {
                //1个癞子 [第一个纯顺 随机 占用癞子] [第二个 随机 占用癞子]
                if ($xcardCnt != $surplusXcardCnt) {
                    // 癞子已使用  [第三个随机 随机 占用癞子]
                    $baseGroup = array_merge(BASE_CHUN_SHUN_3_LIAN_LIST, BASE_HUN_SHUN_3_LIAN_LIST, BASE_HUN_3_TIAO_LIST);
                } else {
                    // 癞子未使用  [第三个随机 必然 占用癞子]
                    $baseGroup = array_merge($this->baseGroupChunShun3LianHasXcardList, BASE_HUN_SHUN_3_LIAN_LIST, BASE_HUN_3_TIAO_LIST);
                }
            }
            if ($xcardCnt == 3) {
                //1个癞子 第一个纯顺 随机 占用癞子  已 使用癞子  第二个 随机 占用癞子
                if ($xcardCnt - $surplusXcardCnt >= 2) { //未使用2个
                    $baseGroup = array_merge(BASE_CHUN_SHUN_3_LIAN_LIST, BASE_HUN_SHUN_3_LIAN_LIST, BASE_HUN_3_TIAO_LIST);
                } else {
                    //1个癞子 第一个纯顺 随机 占用癞子  未 使用癞子  第二个 必然 占用癞子
                    $baseGroup = array_merge($this->baseGroupChunShun3LianHasXcardList, BASE_HUN_SHUN_3_LIAN_LIST, BASE_HUN_3_TIAO_LIST);
                }
            }

            if ($xcardCnt == 4) {
                //1个癞子 第一个纯顺 必然 占用癞子   第二个 必然 占用癞子   第三个 必然 占用癞子
                $baseGroup = array_merge($this->baseGroupChunShun3LianHasXcardList, BASE_HUN_SHUN_3_LIAN_LIST, BASE_HUN_3_TIAO_LIST);
            }
        }

        return $baseGroup;
    }



    //按照给定的数量随机生成牌 $isFill  是否补充剩余的癞子
    private function getRandCardByNum(&$pool, $num, $dealCards = [], $surplusXcardCnt = 999, $isFill = false)
    {
        $num = (int) $num;
        if ($num <= 0) {
            return $dealCards;
        }
        $pool = array_values($pool);

        //纯随机发牌
        if ($surplusXcardCnt == 999) {
            //出随机散牌处理
            for ($i = 1; $i <= $num; $i++) {
                shuffle($pool);
                array_push($dealCards, array_pop($pool));
            }
            return $dealCards;
        }

        //需要补齐癞子数量
        if ($surplusXcardCnt != 999 && $isFill) {
            $poolAllXcardList = $this->pickLaiZiGuipaiFromHandCards($pool);
            //保证癞子数量不会超出牌堆数量
            $surplusXcardCnt = min($surplusXcardCnt, $num);
            //不能超过剩余牌池的癞子数量
            $surplusXcardCnt = min(count($poolAllXcardList), $surplusXcardCnt);
            $num = $num - $surplusXcardCnt;
            for ($i = 1; $i <= $surplusXcardCnt; $i++) {
                array_push($dealCards, array_pop($poolAllXcardList));
            }
            $surplusXcardCnt = 0;
            for ($i = 1; $i <= $num; $i++) {
                array_push($dealCards, array_pop($pool));
            }
            //最后合并赖子牌到牌池
            $pool = array_merge($pool, $poolAllXcardList);
            shuffle($pool);
            return $dealCards;
        }


        //不补充癞子
        if ($surplusXcardCnt != 999 && !$isFill) {
            for ($i = 1; $i <= $num; $i++) {
                shuffle($pool);
                if (in_array($pool[0], $this->allXcardList)) {
                    if ($surplusXcardCnt <= 0) {
                        $i = $i - 1;
                        //去除所有癞子
                        $poolAllXcardList = $this->pickLaiZiGuipaiFromHandCards($pool);
                        continue;
                    } else {
                        $surplusXcardCnt = $surplusXcardCnt - 1;
                    }
                }
                array_push($dealCards, array_pop($pool));
            }
            //最后合并赖子牌到牌池
            $pool = array_merge($pool, $poolAllXcardList);
            shuffle($pool);
            return $dealCards;
        }
    }


    //随机获取指定牌组的牌
    private function getCardByBaseGroup($baseGroup, &$pool, &$dealCards, &$surplusXcardCnt)
    {
        if (!$baseGroup) {
            return $dealCards;
        }
        //乱序
        shuffle($baseGroup);
        foreach ($baseGroup as $key => $val) {
            $xcardCnt = count(array_intersect($val, $this->allXcardList));
            if ($xcardCnt >= 2) {
                //点数相同的混3条直接跳过
                continue;
            }
            //检测当前牌组是否包含癞子
            if ($surplusXcardCnt <= 0) {
                if (in_array(0X51, $val)) {
                    continue;
                }
                if (array_intersect($val, $this->allXcardList)) {
                    continue;
                }
            }
            //判断是否为混顺
            if (in_array(0X51, $val)) {
                //判定当前牌池是否还有癞子和鬼牌
                $xList = $this->pickLaiZiGuipaiFromHandCards($pool);
                if (!$xList) {
                    continue;
                } else {
                    $xkey = array_search(0X51, $val);
                    $val[$xkey] = $xList[0];
                }
                $pool = array_merge($pool, $xList);
            }

            //判定牌型中是否存在癞子
            $usedXcardCnt = count(array_intersect($val, $this->allXcardList));
            if ($usedXcardCnt > $surplusXcardCnt) {
                //癞子不够发放
                continue;
            }

            //去重剩余牌池
            $tmp_card_list = array_unique_one_dimensional($pool);
            //对比牌池中的牌 是否包含当前的牌型
            $tmp_jiao_ji = array_intersect($tmp_card_list, $val);
            if (count_one_dimensional_array($tmp_jiao_ji) != count_one_dimensional_array($val)) {
                //牌池的牌不够发放此牌型  放弃发放
                continue;
            }

            //去除挑出来的牌组之后的牌池
            $pool = del_b_arr_from_a_arr($pool, $val);
            //合并手牌并返回
            $dealCards = array_merge_one_dimensional_array($dealCards, $val);

            if ($usedXcardCnt > 0) {
                $surplusXcardCnt = $surplusXcardCnt - $usedXcardCnt;
            }
            break;
        }
        return $dealCards;
    }

}
