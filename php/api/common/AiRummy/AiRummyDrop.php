<?php
/**
 * @todo   ai version 1 组排 出牌模拟算法
 * <AUTHOR>
 *
 */


namespace common\AiRummy;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;
use common\AiRummy\AiRummyHandCardsScore;

class AiRummyDrop
{
    //玩法
    private $cls;
    //正在玩的玩家数量
    private $playerNum;
    //drop玩家数量
    private $dropPlayerNum;
    //摸牌回合数
    private $roundNum;
    //本次是否需要drop
    private $dropRes = false;
    //本次手牌缺口分数
    private $gapScore = 0;
    //缺口牌列表
    private $gapList = [];

    //起手drop判定分数 人数*10=》分数
    const RUMMY_ROUND_0_DROP_CONFIG = [
        '20' => 15,
        '25' => 18,
        '30' => 20,
        '35' => 23,
        '40' => 25,
        '45' => 27.5,
        '50' => 30,
        '55' => 30,
        '60' => 30,
    ];

    //实例化手牌和癞子
    public function __construct($handCards, $lCard, $roomType, $playerNum, $dropPlayerNum, $roundNum)
    {
        $roomInfo = parse_room_type($roomType);
        $this->cls = $roomInfo['cls'];
        $this->playerNum = $playerNum;
        $this->dropPlayerNum = $dropPlayerNum;
        $this->roundNum = $roundNum;
        //获取手牌缺口分数
        $obj = new AiRummyGap($handCards, $lCard);
        $this->gapScore = $obj->getGapScore();
        $this->gapList = $obj->getGapCardList();
        //执行drop判定
        $this->getDropResRun();
    }

    //获取drop结果
    public function getDropRes(): bool
    {
        //Log::console_log(__FUNCTION__, "drop判定结果：" . json_encode($this->dropRes));
        return $this->dropRes;
    }


    public function getGapList(): array
    {
        return $this->gapList;
    }
    //获取drop计算分数
    public function getGapScore(): string
    {
        return $this->gapScore;
    }

    //判定是否需要drop
    private function getDropResRun()
    {
        if ($this->cls != Common::GAME_TYPE_RUMMY_POINTS) {
            return;
        }
        //非首轮不drop
        if ($this->roundNum != 0) {
            return;
        }
        //Log::console_log(__FUNCTION__, "正在玩的人数：" . $this->playerNum);
        //Log::console_log(__FUNCTION__, "已离开的人数：" . $this->dropPlayerNum);
        //Log::console_log(__FUNCTION__, "缺口分数：" . $this->gapScore);
        $playerNum = ($this->playerNum + $this->dropPlayerNum * 0.5) * 10;
        //Log::console_log(__FUNCTION__, "最终人数：" . $playerNum);
        //Log::console_log(__FUNCTION__, "drop判定标准：" . self::RUMMY_ROUND_0_DROP_CONFIG[$playerNum]);
        //执行判定
        if (!isset(self::RUMMY_ROUND_0_DROP_CONFIG[$playerNum])) {
            return;
        }
        if ($this->gapScore < self::RUMMY_ROUND_0_DROP_CONFIG[$playerNum]) {
            $this->dropRes = true;
        }
    }

}
