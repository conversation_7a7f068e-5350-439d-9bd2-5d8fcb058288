<?php
/**
 * @todo   AI摸牌判定
 * <AUTHOR>
 *
 */


namespace common\AiRummy;

use common\Common;
use common\ErrorCode;
use common\LogicHttpApi;
use common\RedisOpt;
use lib\Log;

class AiRummyDrawCard
{
    //手牌信息
    protected $handCards = [];
    //手牌分数
    private $handScore = 0;
    //本局的癞子牌
    private $lCard;
    //本局的明牌
    private $mCard;
    //摸明牌分数
    private $mScore = 0;
    //本局的暗牌
    private $aCard;
    //摸暗牌分数
    private $aScore = 0;
    //本局判定摸明牌 还是 摸暗牌结果
    private $drawCardRes;
    //本局的牌数  13rummy 还是 10rummy
    private $maxCard;

    //实例化手牌和癞子
    public function __construct($handCards, $lCard, $maxCard, $mCard, $aCard = '')
    {
        $this->handCards = $handCards;
        $this->lCard = $lCard;
        $this->maxCard = $maxCard;
        $this->mCard = $mCard;
        $this->aCard = $aCard;
        $this->drawCardRun();
    }

    //获取摸牌决策结果
    public function getDrawCardRes()
    {
        return $this->drawCardRes;
    }

    //获取手牌分数
    public function getHandScore(): int
    {
        return $this->handScore;
    }

    //获取暗牌分数
    public function getDrawAScore(): int
    {
        return $this->aScore;
    }

    //获取摸明牌分数
    public function getDrawMScore(): int
    {
        return $this->mScore;
    }

    //获取算分结果
    private function drawCardRun()
    {
        $mScoreInfo = $this->drawMCardScore();
        $this->mScore = $mScoreInfo['handScore'];
        //优先胡牌选择
        if ($mScoreInfo['finishRes']) {
            //Log::console_log(__FUNCTION__, 'AI 摸明后 胡牌');
            $this->drawCardRes = Common::TAKE_CARD_TYPE_M;
            return;
        }
        if ($this->aCard) {
            $aScoreInfo = $this->drawACardScore();
            $this->aScore = $aScoreInfo['handScore'];
            //优先胡牌选择
            if ($aScoreInfo['finishRes']) {
                //Log::console_log(__FUNCTION__, 'AI 摸暗后 胡牌');
                $this->drawCardRes = Common::TAKE_CARD_TYPE_A;
                return;
            }
        }
        //没有胡牌情况 在根据算分计算摸牌结果
        //获取摸牌之前的分数
        $handScoreInfo = $this->getHandScoreInfo();
        $this->handScore = $handScoreInfo['handScore'];
        $this->drawCardRes = $this->getDrawCardByScore($mScoreInfo['handScore'], $handScoreInfo['handScore']);
        if ($this->drawCardRes == Common::TAKE_CARD_TYPE_M) {
            //Log::console_log(__FUNCTION__, 'AI 摸牌决策结果 摸了明牌' . $this->mCard);
        } else {
            //Log::console_log(__FUNCTION__, 'AI 摸牌决策结果 摸了暗牌' . $this->aCard);
        }
    }

    //摸明分数决策
    private function getDrawCardByScore($mScore, $handScore): int
    {
        $score = $mScore - $handScore;
        //Log::console_log(__FUNCTION__, 'AI 摸明决策分数对比 ' . $handScore . '---' . $mScore . '差分：' . $score);
        if ($score < 16) {
            //直接跳转摸暗牌
            return Common::TAKE_CARD_TYPE_A;
        } else {
            return Common::TAKE_CARD_TYPE_M;
        }
    }

    //计算摸暗算分
    private function drawACardScore(): array
    {
        return $this->getDisCardScoreInfo($this->aCard);
    }

    //计算摸明算分
    private function drawMCardScore(): array
    {
        return $this->getDisCardScoreInfo($this->mCard);
    }

    //计算摸牌前的算分
    private function getHandScoreInfo(): array
    {
        $obj = new AiRummyGroupCard($this->handCards, $this->lCard, $this->maxCard);
        $rtn['finishRes'] = false;
        $rtn['groupList'] = $obj->getGroupList();
        $huPaiJiChu = $obj->getHuPaiJiChu();
        $scoreObj = new AiRummyHandCardsScore($rtn['groupList'], $huPaiJiChu);
        $rtn['handScore'] = $scoreObj->getHandCardsScore();
        return $rtn;
    }

    //计算手牌情况
    private function getDisCardScoreInfo($drawCard): array
    {
        $drawCardHands = $this->handCards;
        $drawCardHands[] = $drawCard;
        $obj = new AiRummyDisCard($drawCardHands, $this->lCard, $this->maxCard);
        $rtn['finishRes'] = $obj->getCanFinishRes();
        $rtn['groupList'] = $obj->getGroupList();
        $huPaiJiChu = $obj->getHuPaiJiChu();
        $scoreObj = new AiRummyHandCardsScore($rtn['groupList'], $huPaiJiChu);
        $rtn['handScore'] = $scoreObj->getHandCardsScore();
        return $rtn;
    }
}
