<?php

namespace common\AiExplorerSlots;

use common\Common;

class ExplorerSlotsDealCardReport extends ExplorerSlotsDealCard
{

    public static function Main()
    {
        $eta = -hrtime(true);
        $agg = [];
        for ($i = 0; $i < 200; $i++) {
            $row = self::randRow();
            self::aggRow($agg, $row);
        }
        self::aggEnd($agg);
        $eta += hrtime(true);
        $agg["seconds"] = round($eta / 1e9, 1);
        return $agg;
    }

    private static function newObj($isFree)
    {
        $roomType = "22_1_1_1000";
        $uid = 123456;
        $betAmount = 90 * 1000;
        $roomMold = Common::ROOM_UNION_TYPE_NORMAL;
        $historyAllWinAmount = 0;
        $historyAllBetAmount = 0;
        $raiseUserWalletList = [];
        $obj = new self($roomType, $isFree, $uid, $betAmount, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $raiseUserWalletList);
        return $obj;
    }

    private static function aggRow(&$agg, $row)
    {
        $agg["count"] = ($agg["count"] ?? 0) + 1;
        $agg["betAmount"] = ($agg["betAmount"] ?? 0) + $row["betAmount"];
        $agg["winAmount"] = ($agg["winAmount"] ?? 0) + $row["winAmount"];
        $agg["free_spin_cnt"] = ($agg["free_spin_cnt"] ?? 0) + $row["free_spin_cnt"];
    }

    private static function aggEnd(&$agg)
    {
        $agg["avgBetAmount"] = $agg["betAmount"] / $agg["count"];
        $agg["avgWinAmount"] = $agg["winAmount"] / $agg["count"];
        $agg["returnRate"] = $agg["avgWinAmount"] / $agg["avgBetAmount"];
    }

    private static function randRow()
    {
        $obj = self::newObj(false);
        $row = [];
        $obj->rowCollect($row);

        // 免费次数要消耗掉，数值合并到该行
        $free_spin_cnt = $row["free_spin_cnt"] ?? 0;
        for ($i = 0; $i < $free_spin_cnt; $i++) {
            $obj = self::newObj(true);
            $obj->rowCollect($row);
        }

        return $row;
    }

    public function __construct($roomType, $isFree, $uid, $betAmount, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $raiseUserWalletList)
    {
        //每条线的下注钱数
        $this->baseLineBetAmount = $betAmount > 0 ? bcdiv($betAmount, count(SLOTS_WIN_WAY), 3) : 0;
        $this->isFree = $isFree;

        $this->allRaiseAmount = $betAmount;
    }

    private function rowCollect(&$row)
    {
        // 真实随机不执行任何干预
        $this->isFreeSpin = $this->isFree;
        $dealCardInfo = $this->dealCardOnce();

        $dealCard = $dealCardInfo['dealCard'];
        $windRaiseList = $dealCardInfo['windRaiseList'];
        $scattrInfo = $dealCardInfo['scattrInfo'];
        $winAmount = $dealCardInfo['winAmount'];

        $free_spin_cnt = $scattrInfo["free_spin_cnt"] ?? 0;

        // 下注额
        if (!$this->isFree) {
            $row["betAmount"] = ($row["betAmount"] ?? 0) + $this->allRaiseAmount;
        }
        // 返奖额
        $row["winAmount"] = ($row["winAmount"] ?? 0) + $winAmount;
        // 免费次数
        $row["free_spin_cnt"] = ($row["free_spin_cnt"] ?? 0) + $free_spin_cnt;

        time();
    }


}
