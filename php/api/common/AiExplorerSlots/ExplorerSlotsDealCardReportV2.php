<?php
/**
 * @todo slots数据模拟 
 * 要求不被任何大盘操控影响
 * 此脚本是basegame里掺杂着freegame
 */
namespace common\AiExplorerSlots;

ini_set('memory_limit', '2G');

use common\Common;

class ExplorerSlotsDealCardReportV2 extends ExplorerSlotsDealCard
{
    public function __construct($roomType, $isFree, $uid, $betAmount, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $raiseUserWalletList)
    {
        //每条线的下注钱数
        $this->baseLineBetAmount = $betAmount > 0 ? bcdiv($betAmount, count(SLOTS_WIN_WAY), 3) : 0;
        $this->isFree = $isFree;
        $this->betFee = $betAmount;
        $this->allRaiseAmount = $betAmount;
    }

    public function dealCard()
    {
        return $this->dealCardOnce();
    }

    public static function MainAll()
    {
        self::Main();
        ExplorerSlotsDealCardReportV3::Main();
    }

    //php hp php/api/common/AiExplorerSlots/ExplorerSlotsDealCardReportV2.php
    public static function Main()
    {
        for ($i = 0; $i < 500000; $i++) {
            $rows[] = self::randRow();
        }
        self::export($rows);

        // return $rows;
    }

    private static function newObj($isFree)
    {
        $roomType = "22_1_1_1000";
        $uid = 1;
        $betAmount = 90 * 1000;
        $roomMold = Common::ROOM_UNION_TYPE_NORMAL;
        $historyAllWinAmount = 0;
        $historyAllBetAmount = 0;
        $raiseUserWalletList = [];

        $obj = new ExplorerSlotsDealCardReportV2($roomType, $isFree, $uid, $betAmount, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $raiseUserWalletList);
        return $obj;
    }

    private static function randRow()
    {
        echo 123;
        $obj = self::newObj(false);
        $free_spin_cnt = 0;
        $rows = [];
        self::ProcessInitRows($rows);
        $obj->rowCollect($rows, $free_spin_cnt);

        // 免费次数要消耗掉，数值合并到该行
        $begin_free_spin_cnt = $rows["sum_free_spin_cnt"] ?? 0;
        for ($i = 0; $i < $begin_free_spin_cnt; $i++) {
            $obj = self::newObj(true);
            $obj->rowCollect($rows, $free_spin_cnt);
            if ($free_spin_cnt) {
                $begin_free_spin_cnt += $free_spin_cnt;
                continue;
            }
        }

        return $rows;
    }

    private function rowCollect(&$rows, &$free_spin_cnt)
    {
        // 真实随机不执行任何干预
        $this->isFreeSpin = $this->isFree;
        $dealCardInfo = $this->dealCardOnce();
        $dealCardInfo['jackpot_info'] = $dealCardInfo['jackpotInfo'];

        // 获取bet总奖池金额
        $bet_pool = 0;
        \common\AiExplorerSlots\ExplorerSlotsEnum::getBetPool($bet_pool);

        $save = [];
        $this->check_jackpot($bet_pool, $this->betFee, $dealCardInfo, $save);
        \lbase\GetRedis::Master()->Pipeline($save);

        $dealCard = $dealCardInfo['dealCard'];
        $windRaiseList = $dealCardInfo['windRaiseList'];
        $scattrInfo = $dealCardInfo['scattrInfo'];
        $jackpotInfo = $dealCardInfo['jackpot_info'];

        // 根据中奖线路计算本次spin赢的钱
        $user_win_fee = 0;
        foreach ($windRaiseList as $win_line_data) {
            $win_amount = $win_line_data['win_amount'] ?? 0;
            if ($win_amount <= 0) {
                continue;
            }
            $user_win_fee += $win_amount;
        }

        // 根据jackpot_info计算本次是否中奖jackpot
        $user_win_fee += $jackpotInfo['jackpot_amount'] ?? 0;

        // 总中奖金额
        $rows['total_win'] = ($rows["total_win"] ?? 0) + \llogic\common\LogicUtil::moneyChangeValueS2B($user_win_fee);

        // 总jackpot中奖金额
        $rows['total_jackpot_win'] = ($rows["total_jackpot_win"] ?? 0) + \llogic\common\LogicUtil::moneyChangeValueS2B(($jackpotInfo['jackpot_amount'] ?? 0));

        // 是否中奖 free games不参与进去
        if (!$this->isFree) {
            $is_win_line_hit = 0;
            if (!empty($windRaiseList)) {
                $is_win_line_hit = 1;
            } else if ($scattrInfo['scattr_cnt'] >= min(array_keys(\common\AiExplorerSlots\ExplorerSlotsEnum::FREE_SPIN_CNT))) {
                $is_win_line_hit = 1;
            } else if ($jackpotInfo['jackpot_cnt'] >= min(array_keys(\common\AiExplorerSlots\ExplorerSlotsEnum::JACKETPOT_CNT))) {
                $is_win_line_hit = 1;
            }
            $rows['is_win_line_hit'] = (($rows['is_win_line_hit'] ?? 0) + $is_win_line_hit) ? 1 : 0;
        }

        // 中线数量 free games不参与进去
        if (!$this->isFree) {
            $num_win_lines = count($windRaiseList);
            $rows['num_win_lines'] = ($rows['num_win_lines'] ?? 0) + $num_win_lines;
        }

        // FreeGames次数
        $free_spin_cnt = $scattrInfo["free_spin_cnt"] ?? 0;
        $rows["sum_free_spin_cnt"] = ($rows["sum_free_spin_cnt"] ?? 0) + $free_spin_cnt;

        // FreeGames赢钱
        if ($this->isFree) {
            $rows['free_games_total_win'] = ($rows["free_games_total_win"] ?? 0) + \llogic\common\LogicUtil::moneyChangeValueS2B($user_win_fee);
            $rows['free_games_total_jackpot_win'] = ($rows["free_games_total_jackpot_win"] ?? 0) + \llogic\common\LogicUtil::moneyChangeValueS2B(($jackpotInfo['jackpot_amount'] ?? 0));
        }

        // 计算中奖线卡牌种类数量(比如 3 * 10  或者3 * tiger .......)
        if (!$this->isFree) {
            $wild_cnt = 0;
            $this->ProcessWinRaiseIdList($windRaiseList, $free_spin_cnt, $wild_cnt, $rows);
            // 生效的wild数量
            $rows['wild_cnt'] = ($rows['wild_cnt'] ?? 0) + $wild_cnt;
        }

        // Bonus数量(Jactpot宝箱)，FreeGame中的Bonus不计入
        if (!$this->isFree) {
            if ($jackpotInfo['jackpot_cnt'] >= min(array_keys(\common\AiExplorerSlots\ExplorerSlotsEnum::JACKETPOT_CNT))) {
                $rows['bonus_cnt'] = ($rows['bonus_cnt'] ?? 0) + $jackpotInfo['jackpot_cnt'];
            }
        }

        // Scatter数量(猫)，FreeGame中的Scatter不计入
        if (!$this->isFree) {
            if ($scattrInfo['scattr_cnt'] >= min(array_keys(\common\AiExplorerSlots\ExplorerSlotsEnum::FREE_SPIN_CNT))) {
                $rows['scattr_cnt'] = ($rows['scattr_cnt'] ?? 0) + $scattrInfo['scattr_cnt'];
            }
        }

        // 计算当前spin后的奖池剩余金额
        $rows['jackpot_pool'] = \llogic\common\LogicUtil::moneyChangeValueS2B($bet_pool);

        // // 发到的牌
        // $rows['dealcard'] = $dealCard;
        // $rows['windRaiseList'] = $windRaiseList;
        // $rows['scattrInfo'] = $scattrInfo;
        // $rows['jackpotInfo'] = $jackpotInfo;
        // time();
    }

    private static function ProcessInitRows(&$rows)
    {
        // 初始化统计项
        $rows['total_win'] = 0;
        $rows['total_jackpot_win'] = 0;
        $rows['is_win_line_hit'] = 0;
        $rows['num_win_lines'] = 0;
        $rows['sum_free_spin_cnt'] = 0;
        $rows['free_games_total_win'] = 0;
        $rows['free_games_total_jackpot_win'] = 0;
        $rows['3 × 骷髅金币'] = 0;
        $rows['4 × 骷髅金币'] = 0;
        $rows['5 × 骷髅金币'] = 0;
        $rows['3 × 灯笼'] = 0;
        $rows['4 × 灯笼'] = 0;
        $rows['5 × 灯笼'] = 0;
        $rows['3 × 锄头'] = 0;
        $rows['4 × 锄头'] = 0;
        $rows['5 × 锄头'] = 0;
        $rows['3 × 一袋金币'] = 0;
        $rows['4 × 一袋金币'] = 0;
        $rows['5 × 一袋金币'] = 0;
        $rows['3 × 藏宝图'] = 0;
        $rows['4 × 藏宝图'] = 0;
        $rows['5 × 藏宝图'] = 0;
        $rows['3 × 乌鸦'] = 0;
        $rows['4 × 乌鸦'] = 0;
        $rows['5 × 乌鸦'] = 0;
        $rows['3 × 宝石'] = 0;
        $rows['4 × 宝石'] = 0;
        $rows['5 × 宝石'] = 0;
        $rows['3 × 龙'] = 0;
        $rows['4 × 龙'] = 0;
        $rows['5 × 龙'] = 0;
        $rows['3 × Bonus(Jactpot宝箱)'] = 0;
        $rows['4 × Bonus(Jactpot宝箱)'] = 0;
        $rows['5 × Bonus(Jactpot宝箱)'] = 0;
        $rows['3 × Scatter(猫)'] = 0;
        $rows['4 × Scatter(猫)'] = 0;
        $rows['5 × Scatter(猫)'] = 0;
        $rows['wild_cnt'] = 0;
        $rows['bonus_cnt'] = 0;
        $rows['scattr_cnt'] = 0;
        $rows['jackpot_pool'] = 0;
    }

    private function ProcessWinRaiseIdList($windRaiseList, $free_spin_cnt, &$wild_cnt, &$rows)
    {
        foreach ($windRaiseList as $key => $value) {
            // 统计所有中奖卡牌列表
            $count = intval(count($value['tmpWinRaiseIdList']) ?? 0);
            $first_card = $this->ProcessCardName(intval($value['firstCard'] ?? 0));
            $rows["{$count} × {$first_card}"] = ($rows["{$count} × {$first_card}"] ?? 0) + 1;

            // 计算生效的wild数量
            foreach ($value['tmpWinRaiseIdList'] as $card) {
                if ($card == \common\AiExplorerSlots\ExplorerSlotsEnum::WILD) {
                    $wild_cnt++;
                }
            }
        }

        /*
            // Scatter奖励免费次数
            const SLOTS_FREE_SPIN = [
                3 => 5,
                4 => 10,
                5 => 15
            ];
        */
        if ($free_spin_cnt == 5) {
            $rows["3 × Scatter(猫)"] = ($rows["3 × Scatter(猫)"] ?? 0) + 1;
        } else if ($free_spin_cnt == 10) {
            $rows["4 × Scatter(猫)"] = ($rows["4 × Scatter(猫)"] ?? 0) + 1;
        } else if ($free_spin_cnt == 15) {
            $rows["5 × Scatter(猫)"] = ($rows["5 × Scatter(猫)"] ?? 0) + 1;
        }

    }

    private function ProcessCardName($card): string
    {
        /*
            奖池图标配置				
            symbol	            ID	    ×3	    ×4	    ×5
            骷髅金币	        1	    3	    10	    75
            灯笼	            2	    3	    10	    85
            锄头	            3	    15	    40	    250
            一袋金币	        4	    25	    50	    400
            藏宝图	            5	    30	    70	    550
            乌鸦	            6	    35	    80	    650
            宝石	            7	    45	    100	    800
            龙	                8	    75	    175	    1250
            Bonus(Jactpot宝箱)	101	    100	    200	    1750
            Scatter(猫)	        102	    25	    40	    400
            Wild	            103	    -	    -	    -
        */

        switch ($card) {
            case 1:
                return "骷髅金币";
            case 2:
                return "灯笼";
            case 3:
                return "锄头";
            case 4:
                return "一袋金币";
            case 5:
                return "藏宝图";
            case 6:
                return "乌鸦";
            case 7:
                return "宝石";
            case 8:
                return "龙";
            case 101:
                return "Bonus(Jactpot宝箱)";
            case 102:
                return "Scatter(猫)";
            case 103:
                return "Wild";
            default:
                return "0";
        }
    }

    private static function export($rows)
    {
        // 与php目录同级的tmp目录
        $slots_simulation = __DIR__ . '/../../../../tmp/simulation/ExplorerSlots_Simulation_Base.csv';
        @mkdir(dirname($slots_simulation), 0777, true);

        // 删除文件（如果存在）
        if (file_exists($slots_simulation)) {
            unlink($slots_simulation);
        }

        // 打开文件进行写入
        $file = fopen($slots_simulation, 'w');

        // 设定标题行
        $title = '中奖总金额,
                jackpot中奖总金额,
                是否中奖,
                中奖线数量,
                FreeGame次数,
                FreeGame中奖金额,
                FreeGame里jackpot中奖金额,
                3×骷髅金币,
                4×骷髅金币,
                5×骷髅金币,
                3×灯笼,
                4×灯笼,
                5×灯笼,
                3×锄头,
                4×锄头,
                5×锄头,
                3×一袋金币,
                4×一袋金币,
                5×一袋金币,
                3×藏宝图,
                4×藏宝图,
                5×藏宝图,
                3×乌鸦,
                4×乌鸦,
                5×乌鸦,
                3×宝石,
                4×宝石,
                5×宝石,
                3×龙,
                4×龙,
                5×龙,
                3×Bonus(Jactpot宝箱),
                4×Bonus(Jactpot宝箱),
                5×Bonus(Jactpot宝箱),
                3×Scatter(猫),
                4×Scatter(猫),
                5×Scatter(猫),
                生效Wild数量,
                Bonus数量(Jactpot宝箱),
                Scatter数量(猫),
                jackpot奖池金额,';
        $row = explode(',', $title);
        array_unshift($rows, $row);

        // 写入数据行，并在每行末尾添加换行符
        foreach ($rows as $key => $row) {
            fputcsv($file, $row);
        }

        // 关闭文件
        fclose($file);
    }

    private function check_jackpot($bet_pool, $bet_fee, &$res, &$save)
    {
        $jackpot_cnt = min(($res['jackpotInfo']['jackpot_cnt'] ?? 0), max(array_keys(\common\AiExplorerSlots\ExplorerSlotsEnum::JACKETPOT_CNT)));

        // 判断是否中jackpot，如果没中需要入池
        $is_jackpot = \common\AiExplorerSlots\ExplorerSlotsEnum::isWinJackpot($bet_fee, $jackpot_cnt);
        if (!$is_jackpot) {
            \common\AiExplorerSlots\ExplorerSlotsEnum::joinJackpotPool($this->isFree, $bet_fee, $bet_pool, $save);
            return;
        }

        // 计算jackpot中奖金额
        $jackpot_amount = \common\AiExplorerSlots\ExplorerSlotsEnum::calculateJackpotAmount($bet_fee, $jackpot_cnt, $this->isFree, $bet_pool);

        // 更新res的jackpot_amount
        $res['jackpot_info']['jackpot_amount'] = $jackpot_amount;

        // redis更新jackpot奖池
        \common\AiExplorerSlots\ExplorerSlotsEnum::updateJackpot($jackpot_amount, $bet_pool, $save);
    }
}
