<?php
namespace common\AiExplorerSlots;

class ExplorerSlotsEnum
{
    /* <注意，此配置文件里所有金额单位都是卢比，和此玩法有关存储在redis里的金额都是千分位，注意区分！！！！！！！！>*/

    /*
        奖池图标配置				
        symbol	            ID	    ×3	    ×4	    ×5
        骷髅金币	        1	    3	    10	    75
        灯笼	            2	    3	    10	    85
        锄头	            3	    15	    40	    250
        一袋金币	        4	    25	    50	    400
        藏宝图	            5	    30	    70	    550
        乌鸦	            6	    35	    80	    650
        宝石	            7	    45	    100	    800
        龙	                8	    75	    175	    1250
        Scatter(猫)	        102	    25	    40	    400
        Bonus(Jactpot宝箱)	101	    100	    200	    1750
        Wild	            103	    -	    -	    -
    */

    const EXPLORER_SLOTS_X = 5;

    const EXPLORER_SLOTS_Y = 3;


    //获胜路线图
    const WIN_WAY = [
        1 => [1, 1, 1, 1, 1],
        2 => [2, 2, 2, 2, 2],
        3 => [0, 0, 0, 0, 0],
        4 => [2, 1, 0, 1, 2],
        5 => [0, 1, 2, 1, 0],
        6 => [2, 2, 1, 0, 0],
        7 => [0, 0, 1, 2, 2],
        8 => [1, 0, 1, 2, 1],
        9 => [1, 2, 1, 0, 1],
    ];


    // 图标连续个数对应的赔率
    const X_OPTION = [
        1 => [3 => 3, 4 => 10, 5 => 75],
        2 => [3 => 3, 4 => 10, 5 => 85],
        3 => [3 => 15, 4 => 40, 5 => 250],
        4 => [3 => 25, 4 => 50, 5 => 400],
        5 => [3 => 30, 4 => 70, 5 => 550],
        6 => [3 => 35, 4 => 80, 5 => 650],
        7 => [3 => 45, 4 => 100, 5 => 800],
        8 => [3 => 75, 4 => 175, 5 => 1250],
        102 => [3 => 25, 4 => 40, 5 => 400],
        101 => [3 => 100, 4 => 200, 5 => 1750],
        103 => [1 => 1, 2 => 1, 3 => 1, 4 => 1, 5 => 1],
    ];


    // freeSpin的奖励次数
    const FREE_SPIN_CNT = [
        3 => 5,
        4 => 10,
        5 => 15
    ];

    // jackpot的奖池类型
    const JACKETPOT_CNT = [
        3 => 'minor',
        4 => 'major',
        5 => 'grand'
    ];



    // 猫咪的元素id
    const SCATTR = 102;

    // jackpot宝箱的元素id
    const JACKPOT_BOX = 101;

    // 最高赔率的元素id
    const MAX_SCORE_ELEMENT = 101;

    // wild的元素id
    const WILD = 103;


    /*********************************** 普通发牌权重 每一列走的配置都不一样 **********************************************/
    const DEAL_CARDS_WEIGHT = [
        0 => [
            1 => 110,
            2 => 105,
            3 => 100,
            4 => 95,
            5 => 90,
            6 => 85,
            7 => 80,
            8 => 60,
            102 => 33,
            101 => 16,
            103 => 60,
        ],
        1 => [
            1 => 110,
            2 => 105,
            3 => 100,
            4 => 95,
            5 => 90,
            6 => 85,
            7 => 80,
            8 => 60,
            102 => 38,
            101 => 16,
            103 => 65,
        ],
        2 => [
            1 => 110,
            2 => 105,
            3 => 100,
            4 => 95,
            5 => 90,
            6 => 85,
            7 => 80,
            8 => 60,
            102 => 28,
            101 => 12,
            103 => 65,
        ],
        3 => [
            1 => 110,
            2 => 105,
            3 => 100,
            4 => 95,
            5 => 90,
            6 => 85,
            7 => 80,
            8 => 60,
            102 => 23,
            101 => 5,
            103 => 70,
        ],
        4 => [
            1 => 110,
            2 => 105,
            3 => 100,
            4 => 95,
            5 => 90,
            6 => 85,
            7 => 80,
            8 => 60,
            102 => 23,
            101 => 5,
            103 => 50,
        ]
    ];


    /*********************************** 复仇杀状态下的发牌权重 每一列走的配置都不一样 **********************************************/
    // 系统杀(大盘杀)和复仇杀的情况下，不会触发Free Game和Jackpot，即不会随机到3个或以上Scatter、3个或以上Bonus			

    const WIN_POINT_KILLER_DEAL_CARDS_WEIGHT = [
        0 => [
            1 => 130,
            2 => 115,
            3 => 80,
            4 => 60,
            5 => 80,
            6 => 50,
            7 => 55,
            8 => 40,
            102 => 30,
            101 => 2,
            103 => 2,
        ],
        1 => [
            1 => 135,
            2 => 120,
            3 => 90,
            4 => 80,
            5 => 55,
            6 => 60,
            7 => 55,
            8 => 30,
            102 => 35,
            101 => 2,
            103 => 2,
        ],
        2 => [
            1 => 145,
            2 => 125,
            3 => 100,
            4 => 100,
            5 => 50,
            6 => 50,
            7 => 40,
            8 => 30,
            102 => 25,
            101 => 2,
            103 => 2,
        ],
        3 => [
            1 => 135,
            2 => 105,
            3 => 85,
            4 => 75,
            5 => 60,
            6 => 70,
            7 => 55,
            8 => 30,
            102 => 20,
            101 => 2,
            103 => 2,
        ],
        4 => [
            1 => 135,
            2 => 105,
            3 => 90,
            4 => 80,
            5 => 70,
            6 => 60,
            7 => 55,
            8 => 40,
            102 => 20,
            101 => 2,
            103 => 2,
        ]
    ];



    /*********************************** FreeSpin状态下的发牌权重 每一列走的配置都不一样 **********************************************/
    const FREE_DEAL_CARDS_WEIGHT = [
        0 => [
            1 => 90,
            2 => 90,
            3 => 95,
            4 => 95,
            5 => 90,
            6 => 85,
            7 => 85,
            8 => 70,
            102 => 35,
            101 => 15,
            103 => 80,
        ],
        1 => [
            1 => 90,
            2 => 90,
            3 => 95,
            4 => 95,
            5 => 90,
            6 => 85,
            7 => 85,
            8 => 70,
            102 => 40,
            101 => 12,
            103 => 85,
        ],
        2 => [
            1 => 90,
            2 => 90,
            3 => 95,
            4 => 95,
            5 => 90,
            6 => 85,
            7 => 85,
            8 => 70,
            102 => 30,
            101 => 5,
            103 => 85,
        ],
        3 => [
            1 => 90,
            2 => 90,
            3 => 95,
            4 => 95,
            5 => 90,
            6 => 85,
            7 => 85,
            8 => 70,
            102 => 25,
            101 => 3,
            103 => 90,
        ],
        4 => [
            1 => 90,
            2 => 90,
            3 => 95,
            4 => 95,
            5 => 90,
            6 => 85,
            7 => 85,
            8 => 70,
            102 => 25,
            101 => 2,
            103 => 80,
        ]
    ];


    // Big Win / Mega Win / Super Win的触发条件
    const BET_WIN_CONFIG = [
        'big' => ['min' => 3, 'max' => 6],
        'mega' => ['min' => 6, 'max' => 15],
        'super' => ['min' => 15, 'max' => 0],
    ];


    // 玩家看到的奖池 分数的分母
    const DENOMINATOR = 300000;

    // 关于spin档位的配置 单位卢比
    // balance          : 根据余额找出对应的默认spin档位
    // numerator        : 玩家看到的奖池 分数的分子 
    // TODO-- jackpot_rate 等前端更新完可以删掉了
    const BET_LEVEL_CONFIG = [
        "1.8" => [
            'balance' => ['min' => 0, 'max' => 900],
            'numerator' => ['minor' => 9000, 'major' => 15000, 'grand' => 30000],
            'jackpot_rate' => ['minor' => 0, 'major' => 0, 'grand' => 0],
        ],
        "4.5" => [
            'balance' => ['min' => 900, 'max' => 1800],
            'numerator' => ['minor' => 9000, 'major' => 15000, 'grand' => 30000],
            'jackpot_rate' => ['minor' => 0, 'major' => 0., 'grand' => 0],
        ],
        "9" => [
            'balance' => ['min' => 1800, 'max' => 3600],
            'numerator' => ['minor' => 9000, 'major' => 15000, 'grand' => 30000],
            'jackpot_rate' => ['minor' => 0, 'major' => 0, 'grand' => 0],
        ],
        "18" => [
            'balance' => ['min' => 3600, 'max' => 18000],
            'numerator' => ['minor' => 9000, 'major' => 15000, 'grand' => 30000],
            'jackpot_rate' => ['minor' => 0, 'major' => 0, 'grand' => 0],
        ],
        "90" => [
            'balance' => ['min' => 18000, 'max' => 36000],
            'numerator' => ['minor' => 9000, 'major' => 15000, 'grand' => 30000],
            'jackpot_rate' => ['minor' => 0.10, 'major' => 0.15, 'grand' => 0.25],
        ],
        "180" => [
            'balance' => ['min' => 36000, 'max' => 135000],
            'numerator' => ['minor' => 15000, 'major' => 25000, 'grand' => 50000],
            'jackpot_rate' => ['minor' => 0.15, 'major' => 0.20, 'grand' => 0.30],
        ],
        "450" => [
            'balance' => ['min' => 135000, 'max' => 270000],
            'numerator' => ['minor' => 25000, 'major' => 45000, 'grand' => 90000],
            'jackpot_rate' => ['minor' => 0.20, 'major' => 0.25, 'grand' => 0.35],
        ],
        "900" => [
            'balance' => ['min' => 270000, 'max' => 900000],
            'numerator' => ['minor' => 40000, 'major' => 65000, 'grand' => 130000],
            'jackpot_rate' => ['minor' => 0.25, 'major' => 0.30, 'grand' => 0.40],
        ],
        "1800" => [
            'balance' => ['min' => 900000, 'max' => 1800000],
            'numerator' => ['minor' => 60000, 'major' => 100000, 'grand' => 200000],
            'jackpot_rate' => ['minor' => 0.30, 'major' => 0.35, 'grand' => 0.45],
        ],
        "3600" => [
            'balance' => ['min' => 1800000, 'max' => 0],
            'numerator' => ['minor' => 90000, 'major' => 150000, 'grand' => 300000],
            'jackpot_rate' => ['minor' => 0.35, 'major' => 0.40, 'grand' => 0.50],
        ],
    ];


    // jackpot假记录下注档位权重配置
    const JACKPOT_BET_FAKE_CONFIG = [
        "90" => 11000, // 11
        "180" => 2500, // 2.5
        "450" => 1000, // 1
        "900" => 350,  // 0.35
        "1800" => 150  // 0.15
    ];


    // jackpot假记录中奖奖池类型权重配置
    const JACKPOT_TYPE_FAKE_CONFIG = [
        "90" => [
            "minor" => 11,
            "major" => 3,
            "grand" => 1,
        ],
        "180" => [
            "minor" => 12,
            "major" => 2,
            "grand" => 1,
        ],
        "450" => [
            "minor" => 13,
            "major" => 1,
            "grand" => 1,
        ],
        "900" => [
            "minor" => 14,
            "major" => 1,
            "grand" => 0,
        ],
        "1800" => [
            "minor" => 15,
            "major" => 0,
            "grand" => 0,
        ],
    ];


    // 下注池金额上线
    const MAX_BET_POOL = 300000;

    // 下注池金额下线
    const MIN_BET_POOL = 99999;

    // Bet入池比例 30%
    const ADD_JACKPOT_RATE = 0.30;

    // 玩法组group name
    const SEND_GROUP_NAME = 'ExplorerSlots';

    // 房间座位显示最大数量
    const MAX_ROOM_SEATS_CNT = 4;

    // 玩家临时信息存储最近对局记录的最大条数
    const MAX_ROUND_LOG = 50;

    // 玩家临时信息存储最近赢钱记录的最大条数
    const MAX_WIN_AMOUNT_LOG = 20;

    // jackpot解锁档位
    const JACKPOT_UNLOCK_LEVEL = 90;




    /**
     * 获取Bet总奖池金额
     * @param mixed $bet_pool
     * @return void
     */
    public static function getBetPool(&$bet_pool)
    {
        \lbase\GetRedis::Master()->Pipeline(\wm\games\ExplorerSlots\Keys::KeyBetPool()->GET($bet_pool));
    }


    /**
     * 根据spin的下注额入池
     * @param mixed $is_free
     * @param mixed $bet_fee
     * @param mixed $bet_pool
     * @return void
     */
    public static function joinJackpotPool($is_free, $bet_fee, $bet_pool, &$save)
    {
        // freespin不入池
        if ($is_free) {
            return;
        }

        // 本次入池金额
        $change_amount = $bet_fee * self::ADD_JACKPOT_RATE;

        // 更新入池后的Bet总奖池
        $bet_pool += $change_amount;

        // 校验下限
        $min_bet_pool = \llogic\common\LogicUtil::moneyChangeValueB2S(self::MIN_BET_POOL);
        if ($bet_pool < $min_bet_pool) {
            $save[] = \wm\games\ExplorerSlots\Keys::KeyBetPool()->SET($min_bet_pool);
            return;
        }

        // 校验上限
        $max_bet_pool = \llogic\common\LogicUtil::moneyChangeValueB2S(self::MAX_BET_POOL);
        if ($bet_pool > $max_bet_pool) {
            $save[] = \wm\games\ExplorerSlots\Keys::KeyBetPool()->SET($max_bet_pool);
            return;
        }

        $save[] = \wm\games\ExplorerSlots\Keys::KeyBetPool()->INCRBY($change_amount);
    }


    /**
     * 是否中奖jackpot
     * @param mixed $bet_fee
     * @param mixed $jackpot_cnt
     * @return bool
     */
    public static function isWinJackpot($bet_fee, $jackpot_cnt)
    {
        // 获取本次spin档位
        $bet_level = \llogic\common\LogicUtil::moneyChangeValueS2B($bet_fee);

        // 没中jackpot，入池
        if ($jackpot_cnt < min(array_keys(\common\AiExplorerSlots\ExplorerSlotsEnum::JACKETPOT_CNT))) {
            return false;
        }

        // 没到解锁jackpot的档位，不参与中奖jackpot的逻辑，入池
        if ($bet_level < \common\AiExplorerSlots\ExplorerSlotsEnum::JACKPOT_UNLOCK_LEVEL) {
            return false;
        }

        return true;
    }


    /**
     * 更新jackpot奖池
     * @param mixed $jackpot_amount
     * @param mixed $bet_pool
     * @param mixed $save
     * @return void
     */
    public static function updateJackpot($jackpot_amount, &$bet_pool, &$save)
    {
        // 计算更新jackpot奖池后的Bet池金额
        $bet_pool += $jackpot_amount * -1;

        // 校验下限
        $min_bet_pool = \llogic\common\LogicUtil::moneyChangeValueB2S(self::MIN_BET_POOL);
        if ($bet_pool < $min_bet_pool) {
            $bet_pool = $min_bet_pool;
            $save[] = \wm\games\ExplorerSlots\Keys::KeyBetPool()->SET($min_bet_pool);
            return;
        }

        // 校验上限
        $max_bet_pool = \llogic\common\LogicUtil::moneyChangeValueB2S(self::MAX_BET_POOL);
        if ($bet_pool > $max_bet_pool) {
            $bet_pool = $max_bet_pool;
            $save[] = \wm\games\ExplorerSlots\Keys::KeyBetPool()->SET($max_bet_pool);
            return;
        }

        $save[] = \wm\games\ExplorerSlots\Keys::KeyBetPool()->INCRBY($jackpot_amount * -1);
    }


    /**
     * 记录jackpot中奖记录
     * @param mixed $slots_user
     * @param mixed $res
     * @param mixed $record
     * @param mixed $save
     * @return void
     */
    public static function addJackpotRecord($slots_user, $res, &$record, &$save)
    {
        $time = time();
        $record['uid'] = $slots_user['uid'];
        $record['name'] = $slots_user['name'];
        $record['head_url'] = $slots_user['head_url'];
        $record['bet_fee'] = $slots_user['last_bet_fee'];
        $record['jackpot_amount'] = $res['jackpot_info']['jackpot_amount'];
        $record['jackpot_type'] = $res['jackpot_info']['jackpot_type'];
        $record['time'] = $time;

        $save[] = \wm\games\ExplorerSlots\Keys::KeyJackpotRecord()->ZADD(\lbase\Cast::json_encode_silently($record), $time);
        $save[] = \wm\games\ExplorerSlots\Keys::KeyJackpotRecord()->ZREMRANGEBYRANK(0, -101);
        $save[] = \wm\games\ExplorerSlots\Keys::KeyJackpotRecord()->EXPIRE(86400 * 7);
    }


    /**
     * 计算本次jackpot中奖金额
     * @param mixed $bet_fee
     * @param mixed $jackpot_cnt
     * @param mixed $is_free
     * @param mixed $bet_pool
     */
    public static function calculateJackpotAmount($bet_fee, $jackpot_cnt, $is_free, $bet_pool)
    {
        // 获取jackpot奖励类型
        $jackpot_type = self::JACKETPOT_CNT[$jackpot_cnt];

        // 本次bet池入池金额，freespin不入池
        $join_amount = 0;
        if (!$is_free) {
            $join_amount = $bet_fee * self::ADD_JACKPOT_RATE;
        }

        // 校验bet池上限和下限，如果超出或低于，按限制的金额走
        $bet_pool += $join_amount;

        $max_bet_pool = \llogic\common\LogicUtil::moneyChangeValueB2S(self::MAX_BET_POOL);
        $bet_pool = min($bet_pool, $max_bet_pool);

        $min_bet_pool = \llogic\common\LogicUtil::moneyChangeValueB2S(self::MIN_BET_POOL);
        $bet_pool = max($bet_pool, $min_bet_pool);

        // 获取本次spin档位
        $bet_level = \llogic\common\LogicUtil::moneyChangeValueS2B($bet_fee);

        // 计算本次jackpot中奖金额
        $jackpot_amount = self::getUserVisiblePoolAmount($bet_level, $bet_pool, $jackpot_type);

        return $jackpot_amount;
    }


    /**
     * 刷新用户可见的三个jackpot奖池金额
     * @param mixed $bet_pool
     * @param mixed $back_data
     * @return void
     */
    public static function refreshJackpotPool($bet_pool, &$back_data)
    {
        // 奖池金额 = bet_pool * 分子 / 分母
        $jackpot_pool_config = self::BET_LEVEL_CONFIG;
        foreach ($jackpot_pool_config as $bet_level => $item) {
            $back_data['jackpot_pool'][$bet_level] = [
                'minor' => self::getUserVisiblePoolAmount($bet_level, $bet_pool, 'minor'),
                'major' => self::getUserVisiblePoolAmount($bet_level, $bet_pool, 'major'),
                'grand' => self::getUserVisiblePoolAmount($bet_level, $bet_pool, 'grand'),
            ];
        }
    }


    /**
     * 计算用户可见的某个jackpot池子金额
     * ps：计算出来的$pool是万分位，需要 / 10000 后向下取整，最终计算出的金额是千分位数额
     * @param mixed $bet_level      ：spin档位
     * @param mixed $bet_pool       ：redis存储的Bet池总额
     * @param mixed $jackpot_type   ：需要计算的是哪个池子的金额("minor"、"major"、"grand")
     */
    public static function getUserVisiblePoolAmount($bet_level, $bet_pool, $jackpot_type)
    {
        // bet_level可能为float，作为数组下标使用需要转为string
        $bet_level = strval($bet_level);

        // 配置
        $config = self::BET_LEVEL_CONFIG;

        // 比例分子
        $numerator = $config[$bet_level]['numerator'][$jackpot_type];

        // 比例分母
        $denominator = self::DENOMINATOR;

        $pool = $bet_pool * ($numerator / $denominator * 10000);

        return floor($pool / 10000);
    }
}


