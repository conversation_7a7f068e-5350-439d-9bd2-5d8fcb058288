<?php
/**
 * @todo ExplorerSlots发牌
 */
namespace common\AiExplorerSlots;

use lib\Log;
use common\Common;

class ExplorerSlotsDealCard extends ExplorerSlotsDealCardHundred
{
    public static function create($roomType, $isFree, $wild_step_info, $uid, $betAmount, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $raiseUserWalletList, $bet_pool)
    {
        // 完全关掉大盘控
        if (0) {
            $obj = new ExplorerSlotsDealCardNoControl($roomType, $isFree, $uid, $betAmount, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $raiseUserWalletList, $bet_pool);
        } else {
            $obj = new self($roomType, $isFree, $uid, $betAmount, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $raiseUserWalletList, $bet_pool);
        }
        return $obj;
    }

    //scattr集合
    protected $scattrList = [];

    //jackpot集合
    protected $jackpotList = [];


    //是否免费发牌
    protected $isFree;

    //scattr计算结果
    protected $scattrInfo;

    //jackpot计算结果
    protected $jackpotInfo;

    //每条线的下注金额
    protected $baseLineBetAmount;

    //本次spin下注额
    protected $betFee;

    //当前bet池金额
    protected $betPool;

    public function __construct($roomType, $isFree, $uid, $betAmount, $roomMold, $historyAllWinAmount, $historyAllBetAmount, $raiseUserWalletList, $bet_pool)
    {
        //每条线的下注钱数
        $this->baseLineBetAmount = $betAmount > 0 ? bcdiv($betAmount, count(\common\AiExplorerSlots\ExplorerSlotsEnum::WIN_WAY), 3) : 0;
        $this->isFree = $isFree;
        $this->betFee = $betAmount;
        $this->betPool = $bet_pool;
        parent::__construct($roomType, [$uid], [0 => ["amount" => $betAmount]], $roomMold, $historyAllWinAmount, $historyAllBetAmount, $uid, 0 /*, $raiseUserWalletList*/);
    }

    //执行发牌
    protected function dealCardRun()
    {
        if ($this->isFree) {
            //真实随机  不执行任何干预
            $this->isFreeSpin = true;
            $this->dealCardByFreeSpin();
        } else {
            parent::dealCardRun();
        }
    }

    // 屏蔽掉大扶小扶
    protected function getIsUpBuff()
    {
        return [];
    }


    //执行免费真是发牌
    protected function dealCardByFreeSpin()
    {
        $this->setDealCardRes($this->dealCardOnce());
    }



    //发牌以及设置发牌结果
    //=========================================================
    //执行一次发牌
    protected function dealCardOnce(): array
    {
        $rtn['dealCard'] = $this->getDealCardsList();
        $rtn['scattrInfo'] = $this->calculationScattrInfo();
        $rtn['jackpotInfo'] = $this->calculationJackpotInfo();
        $rtn['windRaiseList'] = array_values($this->getWinRaiseIdList($rtn['dealCard']));

        // 如果获得了免费次数，则相当于赢钱了，影响扶控的计算
        if ($rtn['scattrInfo']['free_spin_cnt'] > 0) {
            $rtn['assumedExtraWinAmount'] = intval($this->allRaiseAmount * 8.5);
        }

        // 如果中了jackpot，估一个中奖额，影响扶控的计算
        $this->estimateJackpotAmount($rtn);

        $rtn['winAmount'] = $this->getWinAmount($rtn);
        return $rtn;
    }


    /**
     * 估算jackpot中奖金额 影响大盘扶控
     * @param mixed $rtn
     * @return void
     */
    private function estimateJackpotAmount(&$rtn)
    {
        $jackpot_cnt = $rtn['jackpotInfo']['jackpot_cnt'];
        $jackpot_cnt = min($jackpot_cnt, max(array_keys(\common\AiExplorerSlots\ExplorerSlotsEnum::JACKETPOT_CNT)));

        $is_jackpot = \common\AiExplorerSlots\ExplorerSlotsEnum::isWinJackpot($this->betFee, $jackpot_cnt);
        if (!$is_jackpot) {
            return;
        }

        $jackpot_amount = \common\AiExplorerSlots\ExplorerSlotsEnum::calculateJackpotAmount($this->betFee, $jackpot_cnt, $this->isFree, $this->betPool);

        $rtn['assumedExtraWinAmount'] = ($rtn['assumedExtraWinAmount'] ?? 0) + $jackpot_amount;
    }


    //根据二位坐标 生成标识的数字
    public function getCodeByXAndY(int $x, int $y): array
    {
        return ['x' => $x, 'y' => $y];
    }


    // 根据x轴获取不同的发牌权重 每一列都是不同权重
    public function getDealCardWeight(int $y_index)
    {
        //按照发牌权重执行发牌
        if ($this->isFree) {
            $dealCardWeight = \common\AiExplorerSlots\ExplorerSlotsEnum::FREE_DEAL_CARDS_WEIGHT[$y_index];
        } elseif ($this->winPointKillerRes) {
            $dealCardWeight = \common\AiExplorerSlots\ExplorerSlotsEnum::WIN_POINT_KILLER_DEAL_CARDS_WEIGHT[$y_index];
        } else {
            $dealCardWeight = \common\AiExplorerSlots\ExplorerSlotsEnum::DEAL_CARDS_WEIGHT[$y_index];
        }
        return $dealCardWeight;
    }


    //获取一组结果
    protected function getDealCardsList(): array
    {
        $dealCards = [];
        $this->scattrList = [];
        $this->jackpotList = [];

        $dealCardWeight = [];
        for ($x = 0; $x < \common\AiExplorerSlots\ExplorerSlotsEnum::EXPLORER_SLOTS_X; $x++) {
            $dealCardWeight = $this->getDealCardWeight($x);
            for ($y = 0; $y < \common\AiExplorerSlots\ExplorerSlotsEnum::EXPLORER_SLOTS_Y; $y++) {
                $dealCards[$x][$y] = get_rand($dealCardWeight);

                if ($dealCards[$x][$y] == \common\AiExplorerSlots\ExplorerSlotsEnum::SCATTR) {
                    $this->scattrList[] = $this->getCodeByXAndY($x, $y);
                }
                if ($dealCards[$x][$y] == \common\AiExplorerSlots\ExplorerSlotsEnum::MAX_SCORE_ELEMENT) {
                    $this->jackpotList[] = $this->getCodeByXAndY($x, $y);
                }

                //复仇杀 和 系统杀 状态下 且scattr不能超过2个 去除权重scattr选项
                if (($this->winPointKillerRes || $this->sysKillerRes) && count($this->scattrList) == 2) {
                    unset($dealCardWeight[\common\AiExplorerSlots\ExplorerSlotsEnum::SCATTR]);
                }

                //复仇杀 和 系统杀 状态下 且jactpot宝箱不能超过2个 去除权重jactpot宝箱选项
                if (($this->winPointKillerRes || $this->sysKillerRes) && count($this->jackpotList) == 2) {
                    unset($dealCardWeight[\common\AiExplorerSlots\ExplorerSlotsEnum::MAX_SCORE_ELEMENT]);
                }
            }
        }
        return $dealCards;
    }


    //计算获胜路线 以及 获胜的倍数
    protected function getWinRaiseIdList($dealCards): array
    {
        $winRaiseIdList = [];
        //根据路线计算是否获胜
        foreach (\common\AiExplorerSlots\ExplorerSlotsEnum::WIN_WAY as $key => $way) {
            $firstCard = "";
            $tmpWinRaiseIdList = [];
            $highlightXY = [];
            foreach ($way as $x => $y) {
                $card = $dealCards[$x][$y];
                if ($card != \common\AiExplorerSlots\ExplorerSlotsEnum::WILD) {
                    //设置首个不是wild 的手牌
                    if (!$firstCard) {
                        $firstCard = $card;
                    }
                    //手牌跟当前牌不相等 且不是癞子 则终止连续
                    if ($firstCard && $card != $firstCard) {
                        break;
                    }
                }

                // 如果首张牌是猫咪/jackpot 且 当前牌是wild 则终止
                if (
                    ($firstCard == \common\AiExplorerSlots\ExplorerSlotsEnum::SCATTR || $firstCard == \common\AiExplorerSlots\ExplorerSlotsEnum::JACKPOT_BOX) &&
                    $card == \common\AiExplorerSlots\ExplorerSlotsEnum::WILD
                ) {
                    break;
                }

                // 如果该路线参与中奖的手牌有wild了 且 当前牌是猫咪/jackpot 则终止
                if (
                    in_array(\common\AiExplorerSlots\ExplorerSlotsEnum::WILD, $tmpWinRaiseIdList) &&
                    ($card == \common\AiExplorerSlots\ExplorerSlotsEnum::SCATTR || $card == \common\AiExplorerSlots\ExplorerSlotsEnum::JACKPOT_BOX)
                ) {
                    break;
                }

                // 路线里加入临时手牌
                $tmpWinRaiseIdList[] = $card;
                $highlightXY[] = $this->getCodeByXAndY($x, $y);
            }

            //计算元素个数 只有3个且以上的个数 才能获奖
            $tmpWinRaiseIdListCnt = count($tmpWinRaiseIdList);
            if ($tmpWinRaiseIdListCnt >= 3) {

                //如果全是癞子 则指定为最高赔率的团
                $firstCard = $firstCard == "" ? \common\AiExplorerSlots\ExplorerSlotsEnum::MAX_SCORE_ELEMENT : $firstCard;
                $winRaiseIdList[$key]['multiple'] = \common\AiExplorerSlots\ExplorerSlotsEnum::X_OPTION[$firstCard][$tmpWinRaiseIdListCnt];
                $winRaiseIdList[$key]['highlight'] = $highlightXY;
                $winRaiseIdList[$key]['line'] = $key;

                // 中奖线的首牌
                $winRaiseIdList[$key]['firstCard'] = $firstCard;
                // 中奖线的所有牌
                $winRaiseIdList[$key]['tmpWinRaiseIdList'] = $tmpWinRaiseIdList;

                $multiple = $winRaiseIdList[$key]['multiple'];
                $baseLineBetAmount = $this->baseLineBetAmount;
                $win_amount = $multiple * $baseLineBetAmount;

                $winRaiseIdList[$key]['win_amount'] = $win_amount;
            }
        }
        return $winRaiseIdList;
    }


    //计算是否有免费个数
    protected function calculationScattrInfo(): array
    {
        $rtn['highlight'] = $this->scattrList;
        $rtn['scattr_cnt'] = count($this->scattrList);
        $maxScattrCnt = max(array_keys(\common\AiExplorerSlots\ExplorerSlotsEnum::FREE_SPIN_CNT));
        $scattrCnt = min($rtn['scattr_cnt'], $maxScattrCnt);
        $freeSpinCnt = \common\AiExplorerSlots\ExplorerSlotsEnum::FREE_SPIN_CNT[$scattrCnt] ?? 0;
        $rtn['free_spin_cnt'] = $freeSpinCnt;
        return $rtn;
    }

    // 计算是否中奖jackpot
    protected function calculationJackpotInfo(): array
    {
        $bet_level = \llogic\common\LogicUtil::moneyChangeValueS2B($this->betFee);
        if ($bet_level < \common\AiExplorerSlots\ExplorerSlotsEnum::JACKPOT_UNLOCK_LEVEL) {
            goto end;
        }

        $jackpot_cnt = count($this->jackpotList);
        if ($jackpot_cnt < min(array_keys(\common\AiExplorerSlots\ExplorerSlotsEnum::JACKETPOT_CNT))) {
            goto end;
        }

        $jackpot_cnt = min($jackpot_cnt, max(array_keys(\common\AiExplorerSlots\ExplorerSlotsEnum::JACKETPOT_CNT)));
        $jackpot_type = \common\AiExplorerSlots\ExplorerSlotsEnum::JACKETPOT_CNT[$jackpot_cnt];

        end:
        return [
            'bet_level' => $bet_level,
            'jackpot_list' => $this->jackpotList,
            'jackpot_cnt' => count($this->jackpotList),
            'jackpot_type' => strval($jackpot_type ?? ""),
            'jackpot_amount' => 0
        ];
    }


    //根据获胜区域计算赢的钱 
    // jingzhao:这个函数计算出的钱数会影响到是否被系统大盘控
    //          实际给玩家身上加的钱是在server层面重新计算的
    protected function getWinAmount($winRaiseIdList)
    {
        //计算获胜线路上的赢钱金额    赢钱基数为  下注金额/赢钱线路
        $winAmount = 0;
        if ($winRaiseIdList['windRaiseList']) {
            foreach ($winRaiseIdList['windRaiseList'] as $info) {
                $winAmount += $info['win_amount'];
            }
        }
        return $winAmount;
    }


    //设置发牌结果
    protected function setDealCardRes($dealCardInfo)
    {
        $this->baseSetDealCardRes($dealCardInfo);
        $this->dealCard = $dealCardInfo['dealCard'];
        $this->windRaiseList = $dealCardInfo['windRaiseList'];
        $this->scattrInfo = $dealCardInfo['scattrInfo'];
        $this->jackpotInfo = $dealCardInfo['jackpotInfo'];
    }


    //读取免费次数
    public function getScattrInfo(): array
    {
        return $this->scattrInfo;
    }


    //读取是否中奖jackpot
    public function getJackpotInfo(): array
    {
        return $this->jackpotInfo;
    }


    //无需操作
    protected function setPool($cardList)
    {
        // TODO: Implement setPool() method.
    }
    //=========================================================


    //只要奖池有亏空 就执行系统强杀
    protected function getSysKillerRes(): bool
    {
        $killerType = $this->getRoomKillConfigType();
        $killerRate = $this->sysKillerConfig['room_type_killer'][$killerType] ?? 0;
        $this->sysKillerRate = $killerRate;
        Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, '系统原始强杀：' . json_encode($killerRate), Common::getGameTypeTextList($this->cls) . Common::getSysKillerType($this->killerType));
        if ($this->sysKillerWaveRate > 0) {
            return true;
        } else {
            return false;
        }
    }
}
