<?php
/**
 * @todo ExplorerSlots发牌
 */
namespace common\AiExplorerSlots;

use lib\Log;

abstract class ExplorerSlotsDealCardHundred extends \common\AiHundred\AiHundredDealCard
{
    //获取ScattrInfo  ExplorerSlots玩法
    public function getScattrInfo(): array
    {
        return [];
    }

    //获取JackpotInfo  ExplorerSlots玩法
    public function getJackpotInfo(): array
    {
        return [];
    }


    //获取wild框高亮 以及顶部 步数信息  ExplorerSlots玩法
    public function getWildAndTopStepInfo(): array
    {
        return [];
    }


}
