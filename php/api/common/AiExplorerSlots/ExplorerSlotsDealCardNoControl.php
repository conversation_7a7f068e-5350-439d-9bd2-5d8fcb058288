<?php
/**
 * @todo ExplorerSlots发牌
 */
namespace common\AiExplorerSlots;

use lib\Log;
use common\Common;

class ExplorerSlotsDealCardNoControl extends ExplorerSlotsDealCard
{

    //执行发牌
    protected function dealCardRun()
    {
        $this->winPointKillerRes = false;
        $this->sysKillerRes = false;
        $this->isFreeSpin = $this->isFree;
        $this->setDealCardRes($this->dealCardOnce());
    }

}
