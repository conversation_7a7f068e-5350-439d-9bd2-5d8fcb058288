<?php
//获取玩家跳场保护次数
use common\Common;

time();

//通过一张牌获取相同点数不同花色的组合
function get_card_by_color_num($color, $num)
{
    $list = [
        '红桃' => HONG_TAO_LIST,
        '方块' => FANG_KUAI_LIST,
        '黑桃' => HEI_TAO_LIST,
        '梅花' => MEI_HUA_LIST,
    ];
    if (!isset($list[$color])) {
        return 0;
    }
    return isset($list[$color][$num - 1]) ? $list[$color][$num - 1] : 0;
}


//随机获取大小王
function get_big_or_small_joker()
{
    $list = [G_CARD, G_SMALL_CARD];
    shuffle($list);
    return array_pop($list);
}



//根据赖子牌 获取所有鬼牌
function get_all_gui_lai_cards_by_card($card)
{
    //求出 枚举的鬼癞子牌
    if ($card == G_CARD) {
        $cards[] = G_CARD;
    } else {
        $cards = get_all_color_cards_by_one_card($card);
        $cards[] = G_CARD;
    }
    return $cards;
}

function get_all_xcard_by_cls($cls): array
{
    if ($cls == Common::GAME_TYPE_TEEN_PATTI_AK47) {
        return get_ak47_all_xcard();
    }
    if ($cls == Common::GAME_TYPE_TEEN_PATTI_JOKER) {
        return get_joker_all_xcard();
    }
    return [];
}


//获取ak47的所有癞子哎
function get_ak47_all_xcard(): array
{
    //获取ak47所有癞子牌
    $allXcardList = [];
    $xcardList = [1, 13, 4, 7];
    foreach ($xcardList as $xcard) {
        $allXcardList = array_merge($allXcardList, get_all_color_cards_by_one_card($xcard));
    }
    return $allXcardList;
}


//获取joker玩法的所有癞子
function get_joker_all_xcard(): array
{
    return [G_CARD];
}

//通过一张牌的编号获取相同点数不同花色的组合
function get_all_color_cards_by_one_card($card)
{
    $cardPool = init_card_pool(1);
    if (!in_array($card, $cardPool)) {
        return [];
    }
    if ($card == G_CARD) {
        return [G_CARD];
    } else {
        $l_card_num = get_card_info($card)['num'];
        $cards[] = FANG_KUAI_LIST[$l_card_num - 1];
        $cards[] = MEI_HUA_LIST[$l_card_num - 1];
        $cards[] = HONG_TAO_LIST[$l_card_num - 1];
        $cards[] = HEI_TAO_LIST[$l_card_num - 1];
        return $cards;
    }
}

//json 转换
function json_to_arr($a)
{
    if (!$a) {
        return array();
    }
    return json_decode($a, 1);
}

//对象转数组
function object_to_array($obj)
{
    return [];
    $obj = (array) $obj;
    foreach ($obj as $k => $v) {
        if (gettype($v) == 'resource') {
            return;
        }
        if (gettype($v) == 'object' || gettype($v) == 'array') {
            $obj[$k] = (array) object_to_array($v);
        }
    }

    return $obj;
}

//抽奖概率算法 权重操作
function get_rand1($proArr)
{
    $randList = array();
    foreach ($proArr as $k => $v) {
        if ($v <= 0) {
            continue;
        }
        $tmp = array_fill(0, $v, $k);
        $randList = array_merge($randList, $tmp);
    }
    if (!$randList) {
        return -1;
    }
    shuffle($randList);
    $result = array_shift($randList);
    return $result;
}


//抽奖概率算法 $proArr = array("id"=>"概率")
function get_rand($proArr)
{
    // 若参数不是数组，返回 -1
    if (!is_array($proArr)) {
        return -1;
    }

    // 计算总概率精度
    $proSum = array_sum($proArr);

    // 如果总概率为0或负数，返回 -1
    if ($proSum <= 0) {
        return -1;
    }

    // 生成一个在 0 到 proSum 之间的随机浮点数
    $randValue = (rand() / (getrandmax() + 1)) * $proSum;

    // 遍历概率数组
    foreach ($proArr as $key => $proCur) {
        // 若随机数小于或等于当前概率，返回对应的 key
        if ($randValue <= $proCur) {
            return $key;
        }

        // 否则，从随机数中减去当前概率，并继续循环
        $randValue -= $proCur;
    }

    // 若没有匹配到任何 key，返回数组中的一个 key
    foreach ($proArr as $key => $proCur) {
        return $key;
    }
    // 若没有匹配到任何 key，返回 -1
    return -1;
}


//按照牌的点数排序
function sort_rymmy_cards_asc($a)
{
    if (!is_array($a) || empty($a)) {
        return $a;
    }
    //获取手牌点数
    $newa = array_map('get_card_info', $a);
    $sort_arr = array_column($newa, 'num');
    array_multisort($sort_arr, SORT_ASC, $newa);
    $rtn = array_column($newa, 'card');
    return $rtn;
}

//按照花色分组
function group_rummy_cards_by_color($a)
{
    $rtnList = [];
    $cardInfoList = array_map('get_card_info', $a);
    foreach ($cardInfoList as $val) {
        $rtnList[$val['text_type']][] = $val['card'];
    }
    foreach ($rtnList as $key => $val) {
        $rtnList[$key] = sort_rymmy_cards_asc($val);
    }
    return $rtnList;
}

//按照牌的点数排序
function sort_teen_patty_cards_asc($a)
{
    if (!is_array($a) || empty($a)) {
        return $a;
    }
    //获取手牌点数
    $newa = array_map('get_card_info', $a);
    $sort_arr = array_column($newa, 'num');
    foreach ($sort_arr as $k => $v) {
        $sort_arr[$k] = BASE_ZJ_DIANSHU_SCORE[$v];
    }
    array_multisort($sort_arr, SORT_ASC, $newa);
    $rtn = array_column($newa, 'card');
    foreach ($rtn as $key => $val) {
        $rtn[$key] = (int) $val;
    }
    return $rtn;
}

//初始化总牌池  $num  初始化n副牌
function init_card_pool($repeat_num = 2, $has_gui_card = true)
{
    $cardPool = array_merge(FANG_KUAI_LIST, MEI_HUA_LIST, HONG_TAO_LIST, HEI_TAO_LIST);
    if ($has_gui_card) {
        $cardPool = array_merge($cardPool, array(G_CARD));
    }
    $rtnCardPool = [];
    for ($i = 1; $i <= $repeat_num; $i++) {
        $rtnCardPool = array_merge($rtnCardPool, $cardPool);
    }
    //以上设置2副牌
    //执行乱序
    shuffle($rtnCardPool);
    return $rtnCardPool;
}

//通过数字转化扑克牌
function get_card_info($num = '')
{
    $base_poker_card = init_card_pool(1);
    $base_poker_type = [0x00 => 'fk', 0x10 => 'mh', 0x20 => "hht", 0x30 => 'ht', 0x40 => 'w'];
    $base_poker_test_type = [0x00 => '方块', 0x10 => '梅花', 0x20 => "红桃", 0x30 => '黑桃', 0x40 => '鬼牌'];
    if (!$num || !in_array($num, $base_poker_card)) {
        $rtn['type'] = '';
        $rtn['text_type'] = '';
        $rtn['num'] = '';
        $rtn['card'] = '';
    } else {
        $rtn['type'] = $base_poker_type[($num & 0xF0)];
        $rtn['text_type'] = $base_poker_test_type[($num & 0xF0)];
        $rtn['num'] = ($num & 0x0F);
        $rtn['card'] = $num;
    }
    return $rtn;
}

//比较两个一维数组 A 和 B  取消A里同时在B里出现的元素 （如果A里面存在2个或2个以上同一个值相同的元素  只取消一个）
function del_b_arr_from_a_arr($a, $b)
{
    if (empty($a) || empty($b) || !is_array($a) || !is_array($b)) {
        return $a;
    }
    foreach ($b as $v) {
        $key = array_search($v, $a);
        if ($key !== false) {
            unset($a[$key]);
        }
    }
    return $a;
}

//计算二位数组的所有元素个数
function count_two_dimensional_array($a)
{
    if (!is_array($a)) {
        return 0;
    }
    $i = 0;
    foreach ($a as $k => $v) {
        $i += count_one_dimensional_array(array_filter($v));
    }
    return $i;
}

//计算一维数组元素个数
function count_one_dimensional_array($a)
{
    if (is_array($a) && !empty($a)) {
        return count(array_filter($a));
    } else {
        return 0;
    }
}



//去重一维数组函数
function array_unique_one_dimensional($a)
{
    $canRun = true;
    foreach ($a as $key => $val) {
        if (is_array($val)) {
            $canRun = false;
            break;
        }
    }
    if (!$canRun) {
        return $a;
    }
    if (is_array($a) && !empty($a)) {
        return array_unique($a);
    } else {
        return [];
    }
}

//合并数组
function array_merge_one_dimensional_array($a, $b)
{
    if (!is_array($a) || !is_array($b)) {
        return array();
    } else {
        return array_merge($a, $b);
    }
}

//获取左边相邻的牌
function get_adjacent_left_card($card)
{
    return get_adjacent_card($card);
}

//获取右边相邻的牌
function get_adjacent_right_card($card)
{
    return get_adjacent_card($card, true);
}

//给指定的牌 返回相邻的牌
function get_adjacent_card($card, $isBig = false)
{
    $adjacent_card = '';
    if ($card >= 1 && $card <= 13) {
        //方块中查找
        $base_card_list = FANG_KUAI_LIST;
    }
    if ($card >= 17 && $card <= 29) {
        //梅花中查找
        $base_card_list = MEI_HUA_LIST;
    }
    if ($card >= 33 && $card <= 45) {
        //红桃中查找
        $base_card_list = HONG_TAO_LIST;
    }
    if ($card >= 49 && $card <= 61) {
        //黑桃中查找
        $base_card_list = HEI_TAO_LIST;
    }
    if (empty($base_card_list)) {
        return $adjacent_card;
    }
    //查找给定的牌的下标
    $key = array_search($card, $base_card_list);
    if ($key === false) {
        return $adjacent_card;
    }
    //求出相邻的牌
    if ($isBig) {
        //取+1的牌
        $adjacent_card = $key == 12 ? $base_card_list[0] : $base_card_list[$key + 1];
    } else {
        //取-1的牌
        $adjacent_card = $key == 0 ? $base_card_list[12] : $base_card_list[$key - 1];
    }
    return $adjacent_card;
}

//根据房间规则解析 房间数据
function parse_room_type($roomType)
{
    //Log::console_log(__FUNCTION__,$roomType);
    $info = explode('_', $roomType);
    $roomInfo['cls'] = $info[0];
    $roomInfo['currency'] = $info[1];
    $roomInfo['max'] = $info[2];
    $roomInfo['base'] = $info[3];
    if ($roomInfo['cls'] == \common\Common::GAME_TYPE_RUMMY_POOL) {
        $roomInfo['pool'] = $info[4];
    }
    if ($roomInfo['cls'] == \common\Common::GAME_TYPE_RUMMY_DEALS) {
        $roomInfo['round'] = $info[4];
    }
    return $roomInfo;
}





//post 请求助手
function post_helper($finalUrl, $data)
{
    try {
        $headers = array(
            'Content-Type: application/json',
            'x-yyl-ctx:' . \lbase\Log::marshal_ctx(),
        );
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_URL, $finalUrl);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        if (!is_null($data))
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        $r = curl_exec($ch);
        if (curl_errno($ch)) {
            throw new \Exception(curl_errno($ch));
        }
        curl_close($ch);
        return json_decode($r, true);
    } catch (\Exception $e) {
        return $e->getMessage();
    }
}




//判断奇数，是返回TRUE，否返回FALSE
function is_odd($num)
{
    return (is_numeric($num) & ($num & 1));
}




/**
 * 根据飞行时间获取大火箭的赔付倍数
 * @deprecated 统一用 RobotBetLogic::getSpaceXBaseXByRunTime
 */
function getSpaceXBaseXByRunTime($second)
{
    return round(exp(($second - 0.0228) / 16.609), 2);
}




/**
 * 二分查找（Binary Search）算法，也叫折半查找算法。二分查找的思想非常简单，有点类似分治的思想。
 * 二分查找针对的是一个有序的数据集合，每次都通过跟区间的中间元素对比，
 * 将待查找的区间缩小为之前的一半，直到找到要查找的元素，或者区间被缩小为 0。
 */
//查找从小到大的
function binarySearch($array, $findVal, $notResReturnNear = false, $order = "desc")
{
    $indexList = [];
    // 非数组或者数组为空，直接返回-1
    if (!is_array($array) || empty($array)) {
        return $indexList;
    }
    // 查找区间，起点和终点
    $start = 0;
    $end = count($array) - 1;
    while ($start <= $end) {
        // 以中间点作为参照点比较，取整数
        $middle = intval(($start + $end) / 2);
        if ($array[$middle] < $findVal) {
            // 查找数比参照点小，则要查找的数在左半边
            // 因为 $middle 已经比较过了，这里需要减1
            if ($order == "desc") {
                $end = $middle - 1;
            } else {
                $start = $middle + 1;
            }
        } elseif ($array[$middle] > $findVal) {
            // 查找数比参照点大，则要查找的数在右半边
            // 因为 $middle 已经比较过了，这里需要加1
            if ($order == "desc") {
                $start = $middle + 1;
            } else {
                $end = $middle - 1;
            }
        } else {
            // 查找数与参照点相等，则找到返回
            $indexList[] = $middle;
            $i = $middle;
            while (1) {
                $i--;
                if (isset($array[$i]) && $array[$middle] == $array[$i]) {
                    $indexList[] = $i;
                } else {
                    break;
                }
            }
            $i = $middle;
            while (1) {
                $i++;
                if (isset($array[$i]) && $array[$middle] == $array[$i]) {
                    $indexList[] = $i;
                } else {
                    break;
                }
            }
            sort($indexList);
            return $indexList;
        }
    }
    if ($notResReturnNear) {
        if ($end < 0) {
            $indexList[] = 0;
        } elseif ($start >= count($array)) {
            $indexList[] = count($array) - 1;
        } else {
            $leftSub = $array[$end] - $findVal;
            $rightSub = $findVal - $array[$start];
            $indexList[] = $leftSub < $rightSub ? $end : $start;
        }
    }
    return $indexList;
}


//导出CSV
function put_csv($list, $title = null, $file_name = null)
{
    $file_name = $file_name ?? "exam" . time();

    // 清除之前的输出缓冲内容
    ob_clean();

    header('Content-Type: text/csv');
    header('Content-Disposition: attachment;filename=' . $file_name . ".csv");
    header('Cache-Control: max-age=0');
    $file = fopen('php://output', "a");
    $limit = 1000;
    $calc = 0;

    if ($title) {
        fputcsv($file, $title);
    }

    if (!empty($list)) {
        foreach ($list as $v) {
            $calc++;
            if ($limit == $calc) {
                ob_flush();
                flush();
                $calc = 0;
            }
            fputcsv($file, $v);
        }
    }

    fclose($file);
    exit();
}

?>

