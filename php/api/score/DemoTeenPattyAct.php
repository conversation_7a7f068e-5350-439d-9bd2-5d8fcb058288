<?php
/**
 * @todo Ai 接口
 */

namespace score;

use common\AiTeenPatty\AiTeenPattyHandCardsScore;
use common\AiRummy\AutoGroup\AiRummyAutoGroupV2;
use common\Common;

class DemoTeenPattyAct
{
    //验证teenpatti 3张牌 发牌排序
    public static function dealCards()
    {
        die;
        /*$AiTeenPattyHandCardsScoreObj = new AiTeenPattyHandCardsScore(2,51,1);
        $score = $AiTeenPattyHandCardsScoreObj->getPrHandCardsScore();
        var_dump($score);die;*/
        /*$obj = new AiRummyAutoGroupV2();
        $obj->findSeqFromColor();die;*/

        $list = [];
        $a = init_card_pool(1, false);
        ;
        $b = self::getCombinationToString2($a, 3);
        foreach ($b as $val) {
            $info = explode(',', $val);
            $AiTeenPattyHandCardsScoreObj = new AiTeenPattyHandCardsScore($info[0], $info[1], $info[2]);
            $score = $AiTeenPattyHandCardsScoreObj->getPrHandCardsScore();
            $list[$score][] = $info;
        }
        echo json_encode($list);
        die;
        $html['actinfo'] = array('title' => 'AI-TeenPatti 3张 验证规则', 'logochevron' => 'checkZjGroup', );
        //模拟用户  3个AI  3个真人
        $ulist = [
            ['uid' => 1, 'utype' => Common::PLAYER_TYPE_PEOPLE],
            ['uid' => 2, 'utype' => Common::PLAYER_TYPE_AI],
            ['uid' => 3, 'utype' => Common::PLAYER_TYPE_PEOPLE],
            ['uid' => 4, 'utype' => Common::PLAYER_TYPE_AI],
            ['uid' => 5, 'utype' => Common::PLAYER_TYPE_PEOPLE],
            ['uid' => 6, 'utype' => Common::PLAYER_TYPE_AI],
        ];
        $obj = new AiTeenPattyPrDealCard($ulist, '');
        $html['group_list'] = $obj->getDealCards();
        $html['template'] = "Demo/demo_teenpatty_pr_dealcards.html";
        return $html;
    }


    private static function getCombinationToString2($arr, $m)
    {

        $result = array();
        if ($m == 1) {
            return $arr;
        }
        if ($m == count($arr)) {
            $result[] = implode(',', $arr);
            return $result;
        }
        $temp_firstelement = $arr[0];
        unset($arr[0]);
        $arr = array_values($arr);
        $temp_list1 = self::getCombinationToString2($arr, ($m - 1));
        foreach ($temp_list1 as $s) {
            $s = $temp_firstelement . ',' . $s;
            $result[] = $s;
        }
        unset($temp_list1);
        $temp_list2 = self::getCombinationToString2($arr, $m);
        foreach ($temp_list2 as $s) {
            $result[] = $s;
        }
        unset($temp_list2);
        return $result;
    }

}
