<?php
/**
 * @todo 大盘营收情况展示
 */

namespace score;

use common\Common;
use common\RedisOpt;

class OverallProfitAct
{
    //大盘盈利情况
    public static function overallProfitList()
    {
        $list = RedisOpt::getPrRoomWalletLogList();
        $html['actinfo'] = array('title' => '大盘盈利', 'logochevron' => '', );
        $html['list'] = $list;
        $html['capitalTypeList']['all'] = '总';
        $html['capitalTypeList'] += Common::getCapitalTypeTextList();
        $html['template'] = "Stat/overall_profit.html";
        return $html;
    }
















}
