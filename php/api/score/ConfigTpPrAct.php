<?php
/**
 * @todo Ai 盈利率模式下 配置和展示
 */
namespace score;

use common\Common;
use common\RedisOpt;
use lib\Log;
use lib\Request;

class ConfigTpPrAct
{
    ####
    #机器人相关配置
    ####

    //ai等级展示
    public static function prAiConfigList()
    {
        $rtn['actinfo'] = array('title' => 'pr TP Ai 相关配置', 'logochevron' => '', );
        $rtn['template'] = "ConfigTpPr/pr_tp_ai_config_list.html";
        $currency = Request::getValue('currency') ? Request::getValue('currency') : Common::CURRENCY_TYPE_CASH;
        //牌型配置表
        $roomGroupList = Common::getTpRoomGroupList($currency, Common::GAME_TYPE_TEEN_PATTI);
        $rtn['room_cnt_list_config'] = $roomGroupList['roomCntList'];
        $rtn['room_all_cnt_list_config'] = $roomGroupList['allCntList'];
        $rtn['high_room_list_config'] = isset($roomGroupList['roomList']['tp_high_jack_pool']) ? $roomGroupList['roomList']['tp_high_jack_pool'] : [];
        $rtn['lower_room_list_config'] = isset($roomGroupList['roomList']['tp_lower_jack_pool']) ? $roomGroupList['roomList']['tp_lower_jack_pool'] : [];
        unset($roomGroupList['roomList']['tp_high_jack_pool']);
        unset($roomGroupList['roomList']['tp_lower_jack_pool']);
        $rtn['middle_room_list_config'] = $roomGroupList['roomList'];
        //玩法
        $rtn['teenParty'] = Common::GAME_TYPE_TEEN_PATTI;

        //获取动态配置参数
        $rtn['profit_dynamic_config'] = RedisOpt::getPrTpDymaicOptConfigList($currency);

        //获取货币配置
        $rtn['currency_list'] = Common::getCurrencyTypeTextList();
        $rtn['currency'] = $currency;

        //获取流水类型列表
        $rtn['capitalTypeList'] = Common::getCapitalTypeTextList();
        //获取ai性格列表
        $rtn['tp_disposition_list'] = Common::getTpAiDispositionList();
        //获取ai性格配置
        $rtn['tp_ai_disposition_config'] = RedisOpt::getTpAiDispositionConfig();
        //获取ai性格分配比例
        $rtn['tp_ai_disposition'] = RedisOpt::getTpAiDispositionRateConfigList($currency);

        //预设收益比例
        $rtn['win_rate'] = RedisOpt::getPrWinRate($currency);

        $rtn['room_info']['cls'] = Common::GAME_TYPE_TEEN_PATTI;
        $rtn['room_info']['currency'] = $currency;

        //获取pr配置
        $rtn['pr_config'] = RedisOpt::getTpPrConfigList();
        return $rtn;
    }





    //设置ai的性格配置
    public static function editTpAiDisposition()
    {
        try {
            $tpAiDispositionConfig = Request::getValue('tp_ai_disposition_config');
            RedisOpt::setTpAiDispositionConfig($tpAiDispositionConfig);
            $rtn['code'] = 1;
            $rtn['message'] = '操作成功';
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }


    //设置ai的性格配置
    // bakhmut: 发现无人使用
    public static function editTpGroupBaseX()
    {
        try {
            $tpGroupBaseConfig = Request::getValue('tp_group_base_x');
            //计算出总的牌型数量
            $allCnt = 0;
            foreach ($tpGroupBaseConfig as $key => $val) {
                $allCnt += intval($val['cnt'] * $val['x']);
            }
            $newTpGroupBaseConfig = [];
            //计算数据
            $cnt = 0;
            $avg[2]['k'] = 0;
            $avg[2]['s'] = 1;
            $avg[2]['r'] = 0;

            $avg['2.5']['k'] = 0;
            $avg['2.5']['s'] = 1;
            $avg['2.5']['r'] = 0;

            $avg[3]['k'] = 0;
            $avg[3]['s'] = 1;
            $avg[3]['r'] = 0;

            $avg['3.5']['k'] = 0;
            $avg['3.5']['s'] = 1;
            $avg['3.5']['r'] = 0;

            $avg[4]['k'] = 0;
            $avg[4]['s'] = 1;
            $avg[4]['r'] = 0;

            $avg['4.5']['k'] = 0;
            $avg['4.5']['s'] = 1;
            $avg['4.5']['r'] = 0;

            $avg[5]['k'] = 0;
            $avg[5]['s'] = 1;
            $avg[5]['r'] = 0;

            $avg[6]['k'] = 0;
            $avg[6]['s'] = 1;
            $avg[6]['r'] = 0;
            foreach ($tpGroupBaseConfig as $key => $val) {
                $tmpCnt = intval($val['cnt'] * $val['x']);
                $rookieCnt = intval($val['cnt']) * $val['rookie_x'];
                $baseCnt = intval($val['cnt']) * $val['base_x'];

                $cnt += $tmpCnt;
                $newTpGroupBaseConfig[$key]['base'] = $cnt;
                $newTpGroupBaseConfig[$key]['cnt'] = $val['cnt'];
                $newTpGroupBaseConfig[$key]['xcnt'] = $tmpCnt;
                $newTpGroupBaseConfig[$key]['rookie_xcnt'] = $rookieCnt;
                $newTpGroupBaseConfig[$key]['base_xcnt'] = $baseCnt;
                $newTpGroupBaseConfig[$key]['group_name'] = $val['group_name'];
                $newTpGroupBaseConfig[$key]['baseWinRate'] = bcdiv(($allCnt - $cnt), $allCnt, 4);
                $newTpGroupBaseConfig[$key]['player2WinRate'] = bcmul(pow($newTpGroupBaseConfig[$key]['baseWinRate'], 1), 1, 4);
                $setup2 = abs(1 / 2 - $newTpGroupBaseConfig[$key]['player2WinRate']);
                if ($setup2 < $avg[2]['s']) {
                    $avg[2]['k'] = $key;
                    $avg[2]['s'] = $setup2;
                    $avg[2]['r'] = $newTpGroupBaseConfig[$key]['base'];
                }


                $newTpGroupBaseConfig[$key]['player2.5WinRate'] = bcmul(pow($newTpGroupBaseConfig[$key]['baseWinRate'], 1.5), 1, 4);
                $setup25 = abs(1 / 2.5 - $newTpGroupBaseConfig[$key]['player2.5WinRate']);
                if ($setup25 < $avg['2.5']['s']) {
                    $avg['2.5']['k'] = $key;
                    $avg['2.5']['s'] = $setup25;
                    $avg['2.5']['r'] = $newTpGroupBaseConfig[$key]['base'];
                }


                $newTpGroupBaseConfig[$key]['player3WinRate'] = bcmul(pow($newTpGroupBaseConfig[$key]['baseWinRate'], 2), 1, 4);
                $setup3 = abs(1 / 3 - $newTpGroupBaseConfig[$key]['player3WinRate']);
                if ($setup3 < $avg[3]['s']) {
                    $avg[3]['k'] = $key;
                    $avg[3]['s'] = $setup3;
                    $avg[3]['r'] = $newTpGroupBaseConfig[$key]['base'];
                }


                $newTpGroupBaseConfig[$key]['player3.5WinRate'] = bcmul(pow($newTpGroupBaseConfig[$key]['baseWinRate'], 2.5), 1, 4);
                $setup35 = abs(1 / 3.5 - $newTpGroupBaseConfig[$key]['player3.5WinRate']);
                if ($setup35 < $avg['3.5']['s']) {
                    $avg['3.5']['k'] = $key;
                    $avg['3.5']['s'] = $setup35;
                    $avg['3.5']['r'] = $newTpGroupBaseConfig[$key]['base'];
                }


                $newTpGroupBaseConfig[$key]['player4WinRate'] = bcmul(pow($newTpGroupBaseConfig[$key]['baseWinRate'], 3), 1, 4);
                $setup4 = abs(1 / 4 - $newTpGroupBaseConfig[$key]['player4WinRate']);
                if ($setup4 < $avg[4]['s']) {
                    $avg[4]['k'] = $key;
                    $avg[4]['s'] = $setup4;
                    $avg[4]['r'] = $newTpGroupBaseConfig[$key]['base'];
                }


                $newTpGroupBaseConfig[$key]['player4.5WinRate'] = bcmul(pow($newTpGroupBaseConfig[$key]['baseWinRate'], 3.5), 1, 4);
                $setup45 = abs(1 / 4.5 - $newTpGroupBaseConfig[$key]['player4.5WinRate']);
                if ($setup45 < $avg['4.5']['s']) {
                    $avg['4.5']['k'] = $key;
                    $avg['4.5']['s'] = $setup45;
                    $avg['4.5']['r'] = $newTpGroupBaseConfig[$key]['base'];
                }


                $newTpGroupBaseConfig[$key]['player5WinRate'] = bcmul(pow($newTpGroupBaseConfig[$key]['baseWinRate'], 4), 1, 4);
                $setup5 = abs(1 / 5 - $newTpGroupBaseConfig[$key]['player5WinRate']);
                if ($setup5 < $avg[5]['s']) {
                    $avg[5]['k'] = $key;
                    $avg[5]['s'] = $setup5;
                    $avg[5]['r'] = $newTpGroupBaseConfig[$key]['base'];
                }


                $newTpGroupBaseConfig[$key]['player6WinRate'] = bcmul(pow($newTpGroupBaseConfig[$key]['baseWinRate'], 5), 1, 4);
                $setup6 = abs(1 / 6 - $newTpGroupBaseConfig[$key]['player6WinRate']);
                if ($setup6 < $avg[6]['s']) {
                    $avg[6]['k'] = $key;
                    $avg[6]['s'] = $setup6;
                    $avg[6]['r'] = $newTpGroupBaseConfig[$key]['base'];
                }

                $newTpGroupBaseConfig[$key]['x'] = $val['x'];
                $newTpGroupBaseConfig[$key]['base_x'] = $val['base_x'];
                $newTpGroupBaseConfig[$key]['rookie_x'] = $val['rookie_x'];
            }

            //平均概率
            foreach ($avg as $key => $val) {
                $avgConfig[$key] = $val['r'];
            }

            $data['tpGroupBaseConfig'] = $newTpGroupBaseConfig;
            $data['tpAvgConfig'] = $avgConfig;
            $data['allCnt'] = $allCnt;
            RedisOpt::setTpGroupBaseXList($data);
            $rtn['code'] = 1;
            $rtn['message'] = '操作成功';
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }



    //设置ai动态参数
    public static function setProfitAiDynamicConfig()
    {
        try {
            $dynamic = Request::getValue('dynamic');
            $currency = Request::getValue('currency');
            foreach ($dynamic as $key => $val) {
                $dynamic[$key] = json_encode($val);
            }
            RedisOpt::setPrTpDymaicOptConfig($currency, $dynamic);
            $rtn['code'] = 1;
            $rtn['message'] = '操作成功';
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }

    //设置ai性格分布
    public static function setAiDispositionConfig()
    {
        try {
            //设置百分比分布
            $configList = Request::getValue('tp_ai_disposition');
            $currency = Request::getValue('currency');
            foreach ($configList as $key => $val) {
                $configList[$key] = json_encode($val);
            }
            RedisOpt::setTpAiDispositionRateConfig($currency, $configList);
            //清除之前已经分配得到的性格属性
            RedisOpt::clearTpAiAssignDisposition();
            $rtn['code'] = 1;
            $rtn['message'] = '操作成功';
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }


    ###
    #真人相关配置
    ###
    //修改保存PR配置
    public static function editTpPrConfig()
    {
        try {
            $prConfig = Request::getValue('pr_config');
            RedisOpt::setTpPrConfig($prConfig);
            $rtn = [
                'code' => 1,
                'message' => '保存成功'
            ];
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }


}
