<?php
/**
 * @todo Ai 接口
 */

namespace score;

use lib\Request;

class AjaxAct
{
    //ajax操作唯一入口
    public static function run()
    {
        //设置不需要验证登录的操作
        $op = Request::getValue('act');
        //判断当前类的方法是否存在
        if (method_exists(__CLASS__, $op)) {
            if (!self::checkLogin($op)) {
                $output = array('code' => 0, 'message' => '请登录后再操作！', 'data' => array('jumpUrl' => \lib\Url::getUrl('login')));
            } else {
                $output = self::$op();
            }
            Request::returnApi($output);
        } else {
            Request::returnApi(array('code' => 0, 'message' => '无效的请求！'));
        }
        exit;
    }

    //检测是否登录
    private static function checkLogin($act)
    {
        $notCheckLoginActArr = ['login'];
        if (in_array($act, $notCheckLoginActArr)) {
            return true;
        } else {
            if (isset($_SESSION['admin_info']) && !empty($_SESSION['admin_info'])) {
                return true;
            } else {
                return false;
            }
        }
    }

    //ajax 登录
    private static function login()
    {
        $rtn = array(
            'code' => -1,
            'message' => '登录失败',
        );
        $username = Request::getValue('username');
        $password = Request::getValue('password');
        $login_user_list = \lconfig\AddrAdminConfig::Instance()->aiadmin_account_password_map;
        if (isset($login_user_list[$username]) && $password == $login_user_list[$username]) {
            //登录成功
            $rtn['code'] = 1;
            $rtn['message'] = '登录成功';

            $_SESSION['admin_info']['user_name'] = $username;
            $_SESSION['admin_info']['login_time'] = time();
        }
        return $rtn;
    }


    /**
     * 玩法数据模拟 导出csv表格
     * 由ajax请求转发到这里
     * let url = "<?php echo \lib\Url::getAjaxUrl('simulation');?>";
     * @return void
     */
    private static function simulation()
    {
        $info = Request::getJsonBody();
        $infoArr = json_decode($info, 1);

        $cls = intval($infoArr['cls'] ?? 0);
        $num = intval($infoArr['num'] ?? 0);
        $bet_fee = intval($infoArr['bet_fee'] ?? 0);
        $extra_param = strval($infoArr['extra_param'] ?? "");
        return DataSimulationAct::Export($cls, $bet_fee, $num, $extra_param);
    }






    ####################################################################################################################
    //[盈利率] 设置动态参数
    private static function setPrAiDynamicConfig()
    {
        return ConfigPrAct::setProfitAiDynamicConfig();
    }

    //[盈利率] 设置动态参数
    private static function setPrTpAiDynamicConfig()
    {
        return ConfigTpPrAct::setProfitAiDynamicConfig();
    }

    //[盈利率] 设置动态参数
    private static function setAiDispositionConfig()
    {
        return ConfigTpPrAct::setAiDispositionConfig();
    }



    //[盈利率] 真人 修改ai房间配置
    private static function editPrRoomPlan()
    {
        return ConfigPrAct::editRoomPlan();
    }

    //[盈利率] 真人 修改ai牌型配置
    private static function editPrAiLevel()
    {
        return ConfigPrAct::edieAiLevelConfig();
    }

    //[盈利率] 真人 PR配置
    private static function editPrConfig()
    {
        return ConfigPrAct::editPrConfig();
    }

    //[盈利率] TP 真人 PR配置
    private static function editTpPrConfig()
    {
        return ConfigTpPrAct::editTpPrConfig();
    }

    //[盈利率] 真人 幸运配置
    private static function editLuckyConfig()
    {
        return ConfigPrAct::editLuckyConfig();
    }

    //[盈利率] 真人 霉运配置
    private static function editUnLuckyConfig()
    {
        return ConfigPrAct::editUnLuckyConfig();
    }

    //[盈利率] 真人 13card发牌配置
    private static function editZDeal13CardRateConfig()
    {
        return ConfigPrAct::editZDeal13CardRateConfig();
    }

    //[盈利率] 真人 10card发牌配置
    // bakhmut: 发现无人使用
    private static function editZDeal10CardRateConfig()
    {
        return ConfigPrAct::editZDeal10CardRateConfig();
    }

    //[盈利率] 真人 暗牌癞子概率
    private static function editDarkConfig()
    {
        return ConfigPrAct::editDarkConfig();
    }

    //[盈利率] 真人 Ai 新手保护牌型配置
    private static function editZDealCardRookieBuffConfig()
    {
        return ConfigPrAct::editZDealCardRookieBuffConfig();
    }








    //[盈利率] 设置每个底注的预期盈利收益
    private static function setWinRate()
    {
        return ConfigPrAct::setWinRate();
    }

    //保存金骰子配置
    private static function setGoldDiceConfig()
    {
        return ConfigPrAct::setGoldDiceConfig();
    }

    //[盈利率] 设置每个底注的语气盈利收益
    private static function setRoomConfig()
    {
        return ConfigPrAct::setRoomConfig();
    }


    //[盈利率] 修改ai携带bounus数量比例
    private static function setRobotBounusRate()
    {
        return ConfigPrAct::setRobotBounusRate();
    }



    //tp ai 性格配置
    private static function editTpAiDisposition()
    {
        return ConfigTpPrAct::editTpAiDisposition();
    }

    //tp 牌型倍数翻倍
    // bakhmut: 发现无人使用
    private static function editTpGroupBaseX()
    {
        return ConfigTpPrAct::editTpGroupBaseX();
    }





}
