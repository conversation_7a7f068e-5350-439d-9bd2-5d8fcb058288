<?php
/**
 * @todo Ai 接口
 */

namespace score;

use common\AiRummy\AiRummyDisCard;
use common\AiRummy\AiRummyGroupCard;
use common\AiRummy\AutoGroup\AiRummyAutoGroup;
use common\AiRummy\AutoGroup\AiRummyAutoGroupV1;
use common\AiRummy\AiRummyGap;
use common\AiTeenPatty\AiTeenPattyHandCardsScore;
use common\AiTeenPatty\AiTeenPattyPrDealCadWeight;
use common\AiTeenPatty\AiTeenPattyXCardReplace;
use common\Common;
use common\ErrorCode;
use lib\Request;
use think\Debug;
use Exception;

class CardCodeComparisonTableAct
{
    public static function ttt()
    {
        return;
    }
    //牌码对照表
    public static function comparisonTable()
    {
        //        $test = [];
//        for($i=1;$i<=13;$i++){
//            $x = $i-1;
//            $cardIndexList = [
//                [$x, $x + 1, $x + 2],
//                [$x - 1, $x, $x + 1],
//                [$x - 1, $x - 2, $x],
//            ];
//
//            foreach ($cardIndexList as $group) {
//                if(array_sum($group)==0){
//                    continue;
//                }
//                $checkString = [];
//                foreach ($group as $key => $index) {
//                    //不符合要求
//                    if ($index > 13 || $index < -2) {
//                        $checkString = [];
//                        break;
//                    }
//                    if ($index <= 0) {
//                        $index = $index + 13;
//                    }
//                    $index = $index % 13;
//
//                    $checkString[] = $index+1;
//                }
//                if(!$checkString){
//                    continue;
//                }
//                sort($checkString);
//                $test[] = implode(',',$checkString);
//            }
//        }
//
//        var_dump(array_unique($test));die;




        $html['actinfo'] = array('title' => 'AI-模拟-初始化', 'logochevron' => 'ai-plan-list', );
        $html['template'] = "Demo/comparison_card_code.html";
        $basePokerCardList = [FANG_KUAI_LIST, MEI_HUA_LIST, HONG_TAO_LIST, HEI_TAO_LIST, [G_CARD, G_CARD]];
        $html['data'] = $basePokerCardList;
        return $html;
    }


    //验证rummy组排规则
    public static function checkRummyGroup()
    {
        $html['actinfo'] = array('title' => 'AI-验证规则', 'logochevron' => 'ai-plan-list', );
        $html['template'] = "Demo/demo_rummy_dealcards.html";
        $html['hupai'] = 1;
        $dealCards = Request::getValue('hand');
        $dealCards = $dealCards ? explode(',', $dealCards) : [];
        $lCard = Request::getValue('l_card');
        //执行组排
//        $obj = new AiRummyDisCard($dealCards,$lCard,count($dealCards));
//        //$obj = new AiRummyGroupCard($dealCards,$lCard,count($dealCards));
//        $groupList = $obj->getGroupList();
//        $disCard = $obj->getDisCard();
//        $finishRes = $obj->getCanFinishRes();
//        $html['group_list'] = DemoRummyAct::getGroupShow($groupList);
        if (count($dealCards) >= Common::GAME_TYPE_13_CARD) {
            $gameType = Common::GAME_TYPE_13_CARD;
        } else {
            $gameType = Common::GAME_TYPE_10_CARD;
        }
        $obj = new AiRummyGroupCard($dealCards, $lCard, $gameType);
        $declearCard = $obj->getDeclearCards();
        foreach ($declearCard as $key => $val) {
            $html['group_list']['declear_card' . $key][] = $val;
        }
        $disObj = new AiRummyDisCard($dealCards, $lCard, $gameType);
        $canFinish = $disObj->getCanFinishRes();
        $disCard = $disObj->getDisCard();
        //$score = $obj->getHandCardsScore();
        $html['t_card']['l_card'] = array('name' => '癞子', 'card' => $lCard);
        $html['ai_text'] = '';
        if ($canFinish) {
            $html['ai_text'] .= '胡牌了' . PHP_EOL;
        }
        $disCardInfo = get_card_info($disCard);
        $html['ai_text'] .= '执行打牌:' . $disCardInfo['text_type'] . $disCardInfo['num'] . PHP_EOL;
        return $html;
    }

    //根据真实的牌获取对应表的牌

    public static function checkTeenPattyGroupScore()
    {
        $html['actinfo'] = array('title' => 'AI-TeenPatti 3张 验证规则', 'logochevron' => '', );
        $dealCards = Request::getValue('t_hand');
        $dealCards = explode(',', $dealCards);

        $obj = new AiTeenPattyHandCardsScore($dealCards[0], $dealCards[1], $dealCards[2], Common::GAME_TYPE_TEEN_PATTI);
        $groupName = $obj->getGroupName();
        $groupScore = $obj->getHandCardsScore();
        $maxColor = $obj->getTpWarMaxCardColor($dealCards, $groupName);

        $colorList = [
            4 => '黑桃',
            3 => '红桃',
            2 => '梅花',
            1 => '方块',
        ];
        //展示数据模板
        $html['group_list'] = [
            [
                'uid' => 1,
                'utype' => Common::PLAYER_TYPE_PEOPLE,
                'use_ucards_num' => 0,
                'status' => false,
                'ucards' => [
                    [
                        'cards' => $dealCards,
                        'cards_score' => $groupScore,
                        'group_name' => $groupName,
                        'max_color' => $colorList[$maxColor],
                        'rank' => 1
                    ]
                ]
            ],
        ];
        $html['template'] = "Demo/demo_teenpatty_dealcards.html";
        return $html;
    }

    //验证teenpatty 牌组算分

    private static function getTableCardByRealCard($realCard)
    {
        return ceil(max($realCard & 0xF0, 12) / 12) * 100 + ($realCard & 0x0F);
    }
}
