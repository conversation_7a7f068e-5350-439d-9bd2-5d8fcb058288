<?php
/**
 * @todo Ai 盈利率模式下 配置和展示
 */
namespace score;

use common\Common;
use common\RedisOpt;
use lib\Log;
use lib\Request;

class ConfigPrAct
{
    ####
    #机器人相关配置
    ####

    //ai等级展示
    public static function prAiConfigList()
    {
        $rtn['actinfo'] = array('title' => 'pr Ai 相关配置', 'logochevron' => '', );
        $rtn['template'] = "ConfigPr/pr_ai_config_list.html";
        $currency = Request::getValue('currency') ? Request::getValue('currency') : Common::CURRENCY_TYPE_CASH;
        //基础发牌概率配置表
        $rtn['ai_level_10'] = RedisOpt::getPrAiLevelConfigList(10);
        $rtn['ai_level_13'] = RedisOpt::getPrAiLevelConfigList(13);
        //牌组名称列表
        $rtn['pr_group_name_list'] = Common::getProfitGroupNameList();
        //牌型配置表
        $roomGroupList = Common::getRoomGroupList($currency);
        $rtn['room_cnt_list_config'] = $roomGroupList['roomCntList'];
        $rtn['room_all_cnt_list_config'] = $roomGroupList['allCntList'];
        $rtn['high_room_list_config'] = isset($roomGroupList['roomList']['high_jack_pool']) ? $roomGroupList['roomList']['high_jack_pool'] : [];
        $rtn['lower_room_list_config'] = isset($roomGroupList['roomList']['lower_jack_pool']) ? $roomGroupList['roomList']['lower_jack_pool'] : [];
        unset($roomGroupList['roomList']['high_jack_pool']);
        unset($roomGroupList['roomList']['lower_jack_pool']);
        $rtn['middle_room_list_config'] = $roomGroupList['roomList'];

        //玩法
        $rtn['point'] = Common::GAME_TYPE_RUMMY_POINTS;
        $rtn['teen_card'] = Common::GAME_TYPE_RUMMY_10_CARD;
        $rtn['pool'] = Common::GAME_TYPE_RUMMY_POOL;
        $rtn['deals'] = Common::GAME_TYPE_RUMMY_DEALS;

        //获取aiplan 配置
        $rtn['plan_info'] = RedisOpt::getPrPlanInfoList(Common::PLAYER_TYPE_AI, $currency);

        //获取货币配置
        $rtn['currency_list'] = Common::getCurrencyTypeTextList();

        //获取动态配置参数
        $rtn['profit_dynamic_config'] = RedisOpt::getPrDymaicOptConfigList($currency);

        //获取ai带有bounus标识的比例
        $rtn['ai_bounus_rate_config'] = RedisOpt::getPrRobotBounusRate($currency);
        //获取流水类型列表
        $rtn['capitalTypeList'] = Common::getCapitalTypeTextList();
        //预设收益比例
        $rtn['win_rate'] = RedisOpt::getPrWinRate($currency);

        $rtn['currency'] = $currency;

        $rtn['room_info']['cls'] = Common::GAME_TYPE_RUMMY_POINTS;
        $rtn['room_info']['currency'] = $currency;
        return $rtn;
    }

    //批量设置pr的发牌方案配置
    public static function editRoomPlan()
    {
        try {
            $planInfo = Request::getValue('plan_info');
            $currency = Request::getValue('currency');
            if (!is_array($planInfo) || empty($planInfo) || !$currency) {
                throw new \Exception('请填写正确的参数');
            }
            $data = [];
            foreach ($planInfo as $key => $val) {
                $allRate = (int) ($val[1] + $val[2] + $val[3]);
                if ($val[1] < 0 || $val[2] < 0 || $val[3] < 0 || $allRate !== 100) {
                    throw new \Exception('小白 普通 高手 不能小于0 且 总和必须等于100');
                }
                //批量保存方案
                $data[$key] = json_encode($val);
            }
            //添加到redis中
            RedisOpt::setPrPlanInfo(Common::PLAYER_TYPE_AI, $currency, $data);
            $rtn['code'] = 1;
            $rtn['message'] = '操作成功';
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }

    //修改ai等级配置
    public static function edieAiLevelConfig()
    {
        try {
            $aiLevel10 = Request::getValue('ai_level_10');
            $aiLevel13 = Request::getValue('ai_level_13');
            //添加到redis中
            if ($aiLevel10) {
                RedisOpt::setPrAiLevelConfig(10, $aiLevel10);
            }
            if ($aiLevel13) {
                RedisOpt::setPrAiLevelConfig(13, $aiLevel13);
            }
            $rtn['code'] = 1;
            $rtn['message'] = '操作成功';
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }

    //设置ai动态参数
    public static function setProfitAiDynamicConfig()
    {
        try {
            $dynamic = Request::getValue('dynamic');
            $currency = Request::getValue('currency');
            foreach ($dynamic as $key => $val) {
                $dynamic[$key] = json_encode($val);
            }
            RedisOpt::setPrDymaicOptConfig($currency, $dynamic);
            $rtn['code'] = 1;
            $rtn['message'] = '操作成功';
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }

    //setWinRate



    ####################################################################################################################

    ###
    #真人相关配置
    ###

    //获取pr配置
    public static function prZConfigList()
    {
        $html['actinfo'] = array('title' => 'pr真人相关配置', 'logochevron' => '', );
        $html['template'] = "ConfigPr/pr_z_config_list.html";
        //获取pr配置
        $html['pr_config'] = RedisOpt::getPrConfigList();
        //获取幸运配置配置
        $html['lucky_config'] = RedisOpt::getPrLuckyConfig();
        //获取霉运配置
        $html['unlucky_config'] = RedisOpt::getPrUnLuckyConfig();
        //牌型配置表
        $html['pr_group_name_list'] = Common::getProfitGroupNameList();
        //牌型配置表
        $html['pr_group_name_all_list'] = Common::getProfitGroupNameAllList();

        //10 真人 发牌配置
        $html['group_10_profit_rate_config'] = RedisOpt::getPrCardsGroupConfigByMaxCardNum(10, false);
        //13 真人  发牌配置
        $html['group_13_profit_rate_config'] = RedisOpt::getPrCardsGroupConfigByMaxCardNum(13, false);
        //暗牌配置
        $html['dark_config'] = RedisOpt::getDarkRateConfigList();

        //获取新手保护发牌配置
        $html['pr_rookie_buff_deal_card_config'] = RedisOpt::getPrRookieBuffDealCardConfig();
        return $html;
    }

    //修改保存PR配置
    public static function editPrConfig()
    {
        try {
            $prConfig = Request::getValue('pr_config');
            RedisOpt::setPrConfig($prConfig);
            $rtn = [
                'code' => 1,
                'message' => '保存成功'
            ];
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }

    //修改保存PR 幸运配置
    public static function editLuckyConfig()
    {
        try {
            $luckyConfig = Request::getValue('lucky_config');
            RedisOpt::setPrLuckyConfig($luckyConfig);
            $rtn = [
                'code' => 1,
                'message' => '保存成功'
            ];
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }

    //修改保存PR 霉运配置
    public static function editUnLuckyConfig()
    {
        try {
            $unLuckyConfig = Request::getValue('unlucky_config');
            RedisOpt::setPrUnLuckyConfig($unLuckyConfig);
            $rtn = [
                'code' => 1,
                'message' => '保存成功'
            ];
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }

    //修改真人10 card 发牌概率配置
    // bakhmut: 发现无人使用
    public static function editZDeal10CardRateConfig()
    {
        try {
            $deal10CardRateConfig = Request::getValue('group_10_profit_rate_config');
            RedisOpt::setPrCardsGroupConfigByMaxCardNum(10, $deal10CardRateConfig);
            $rtn = [
                'code' => 1,
                'message' => '保存成功'
            ];
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }

    //修改真人13 card 发牌概率配置
    public static function editZDeal13CardRateConfig()
    {
        try {
            $deal13CardRateConfig = Request::getValue('group_13_profit_rate_config');
            RedisOpt::setPrCardsGroupConfigByMaxCardNum(13, $deal13CardRateConfig);
            $rtn = [
                'code' => 1,
                'message' => '保存成功'
            ];
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }

    //修改摸暗牌配置
    public static function editDarkConfig()
    {
        try {
            $darkConfig = Request::getValue('dark_config');
            RedisOpt::setDarkRateConfig($darkConfig);
            $rtn = [
                'code' => 1,
                'message' => '保存成功'
            ];
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }

    //设置新手保护发牌方案
    public static function editZDealCardRookieBuffConfig()
    {
        try {
            $rookieBuffDealCardConfig = Request::getValue('pr_rookie_buff_deal_card_config');
            RedisOpt::setPrRookieBuffDealCardConfig($rookieBuffDealCardConfig);
            $rtn = [
                'code' => 1,
                'message' => '保存成功'
            ];
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }



    //修改ai携带bounus数量比例
    public static function setRobotBounusRate()
    {
        try {
            $data = Request::getValue('jack_pool_bonus_rate');
            $currency = Request::getValue('currency');
            //添加到redis中
            RedisOpt::setPrRobotBounusRate($currency, $data);
            $rtn['code'] = 1;
            $rtn['message'] = '操作成功';
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }







    //奖池预设收益比例
    public static function setWinRate()
    {
        try {
            $data = Request::getValue('win_rate');
            $currency = Request::getValue('currency');
            //添加到redis中
            RedisOpt::setPrWinRate($currency, $data);
            $rtn['code'] = 1;
            $rtn['message'] = '操作成功';
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }


    //保存金筛子配置
    public static function setGoldDiceConfig()
    {
        try {
            $data = Request::getValue('goldDice');
            //添加到redis中
            RedisOpt::setGoldDiceConfig($data);
            $rtn['code'] = 1;
            $rtn['message'] = '操作成功';
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;
    }


    //保存房间配置
    public static function setRoomConfig()
    {
        try {
            $data = Request::getValue('room_config');
            $currency = Request::getValue('currency');
            //添加到redis中
            RedisOpt::setPrRoomConfig($currency, $data);
            $rtn['code'] = 1;
            $rtn['message'] = '操作成功';
        } catch (\Exception $e) {
            $rtn['code'] = 0;
            $rtn['message'] = $e->getMessage();
        }
        return $rtn;

    }

    ####################################################################################################################
}
