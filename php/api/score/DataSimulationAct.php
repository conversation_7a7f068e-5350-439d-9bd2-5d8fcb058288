<?php
/**
 * @todo 游戏玩法数据模拟
 */
declare(strict_types=1);
namespace score;


class DataSimulationAct
{
    /**
     * 玩法数据模拟 api后台页面
     * @return array
     */
    public static function Main()
    {
        $html['game_list'] = \llogic\game\GameEnum::ClsToGameStdName;
        $html['template'] = "Simulation/main.html";
        return $html;
    }


    /**
     * 生成数据模拟表格
     * @param int $cls
     * @param int $bet_fee
     * @param int $num
     * @param string $extra_param
     * @return void
     */
    public static function Export(int $cls, int $bet_fee, int $num, string $extra_param)
    {
        if (empty($cls)) {
            return;
        }

        self::processCls($cls, $bet_fee, $num, $extra_param);
    }


    /**
     * 根据玩法枚举 分发数据模拟类
     * @param int $cls
     * @param int $num
     * @param string $extra_param
     * @return void
     */
    private static function processCls(int $cls, int $bet_fee, int $num, string $extra_param)
    {
        switch ($cls) {
            // SuperSixerSlots
            case \llogic\game\GameEnum::GAME_TYPE_SUPER_SIXER_SLOTS:
                $uid = 123456;
                $slots_user = [];
                \lbase\GetRedis::Master()->Pipeline(\wm\games\SuperSixerSlots\Keys::KeyUserInfo($uid)->GET($slots_user));
                $obj = new \common\AiSuperSixerSlots\SuperSixerSlotsDealCardReport($uid, $slots_user);
                $obj->Main(intval($extra_param), $bet_fee, $num, 1);


            default:
                // 其他玩法类型
                break;
        }
    }













}
