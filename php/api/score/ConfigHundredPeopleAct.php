<?php
/**
 * @todo Ai 百人场游戏 大盘控配置
 */
namespace score;

use common\Common;
use common\RedisOpt;
use lib\Request;

class ConfigHundredPeopleAct
{
    ####
    #机器人相关配置
    ####
    //ai等级展示
    public static function prAiConfigList()
    {
        $rtn['actinfo'] = array('title' => '百人场 Ai 相关配置', 'logochevron' => '', );
        $rtn['template'] = "ConfigHp/pr_hp_ai_config_list.html";
        $currency = Request::getValue('currency') ? Request::getValue('currency') : Common::CURRENCY_TYPE_CASH;
        //获取流水类型列表
        $rtn['capitalTypeList'] = Common::getCapitalTypeTextList();

        //获取货币配置
        $rtn['currency_list'] = Common::getCurrencyTypeTextList();
        $rtn['currency'] = $currency;
        $rtn['roomList'] = Common::getAbRoomGroupList($currency);
        //预设收益比例
        $rtn['win_rate'] = RedisOpt::getPrWinRate($currency);

        //获取金骰子配置
        $goldDiceCOnfig = RedisOpt::getGoldDiceConfig();
        $rtn['gold_dice_config'] = $goldDiceCOnfig ? $goldDiceCOnfig : GOLD_DICE;
        return $rtn;
    }

}
