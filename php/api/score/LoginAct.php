<?php
/**
 * @todo Ai 接口
 */

namespace score;

use lib\Url;

class LoginAct
{

    //管理后台登录界面
    public static function login()
    {
        $html['actinfo'] = array('title' => 'AI-管理', 'logochevron' => 'login', );
        $html['template'] = "Login/login.html";
        //判定登录信息
        if (!isset($_SESSION['admin_info']) || empty($_SESSION['admin_info'])) {
            //登录页面
            return $html;
        } else {
            header('Location: ' . Url::getUrl('comparison-card-code'));
        }
    }


    //管理后台退出功能
    public static function logout()
    {
        unset($_SESSION['admin_info']);
        header('Location: ' . Url::getUrl('login'));
    }


}
