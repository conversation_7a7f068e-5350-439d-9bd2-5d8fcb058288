<?php
/**
 * @todo Ai 接口
 */

namespace score;

use common\AiRummy\AiRummyDisCard;
use common\AiRummy\AiRummyDrawCard;
use common\AiRummy\AiRummyDrawDarkCard;
use common\AiRummy\AiRummyDrop;
use common\AiRummy\AiRummyGroupCard;
use common\Common;
use lib\Request;
use lib\Url;

class DemoRummyAct
{
    //执行总排序
    public static function dealCards()
    {
        $html['actinfo'] = array('title' => 'AI-模拟-初始化', 'logochevron' => 'ai-plan-list', );
        $html['template'] = "Demo/demo_rummy_dealcards.html";
        $html['hupai'] = 0;
        $html['start'] = 1;
        unset($_SESSION['demoRummy']);
        //获取当前需要配置的类型
        $max = (int) Request::getValue('max');
        $d = (int) Request::getValue('d');
        $x = (int) Request::getValue('x');
        //初始化牌池
        $cardPool = init_card_pool(2);

        if ($x) {
            $lCard = $x;
        } else {
            //初始化癞子
            $lCard = '';
            self::initLcard($cardPool, $lCard);
        }

        //初始化明牌
        $mCard = '';
        self::initMcard($cardPool, $mCard);
        //初始化暗牌
        $aCard = '';
        self::initAcard($cardPool, $aCard);
        if ($d) {
            $dealCards = [2, 36, 49, 56, 26, 8, 19, 2, 56, 28, 50, 50, 51];
        } else {
            //初始化首牌
            $dealCards = [];
            self::initDealCards($cardPool, $dealCards, $max);
        }
        //        $dealCards = [29,23,18,52,9,11,23,17,24,33,22,21,60];
//        $lCard = 3;
        $dealCards[] = $mCard;
        //校验是否drop
        $obj = new AiRummyDrop($dealCards, $lCard, '1_1_2_50', 2, 0, 0);
        $dropRes = $obj->getDropRes();
        $gapScore = $obj->getGapScore();
        $gapList = $obj->getGapList();

        $_SESSION['demoRummy']['card_pool'] = $cardPool;
        $_SESSION['demoRummy']['hand'] = $dealCards;

        //执行组排
        $obj = new AiRummyGroupCard($dealCards, $lCard, $max);
        $groupList = $obj->getGroupList();
        $html['ai_text'] = '手牌：' . implode(",", $dealCards) . "<br>";
        $html['ai_text'] .= '癞子：' . $lCard . "<br>";
        $html['ai_text'] .= '缺口分数：' . $gapScore . "<br>";

        $html['ai_text'] .= '缺口牌：';
        foreach ($gapList as $c) {
            $cInfo = get_card_info($c);
            $html['ai_text'] .= "[ " . $cInfo['text_type'] . $cInfo['num'] . " ]---";
        }
        $html['ai_text'] .= '<br>';

        $html['first_13_card'] = $dealCards;
        $html['t_card']['l_card'] = array('name' => '癞子', 'card' => $lCard);
        $html['t_card']['a_card'] = array('name' => '暗牌', 'card' => $aCard);
        $html['t_card']['m_card'] = array('name' => '明牌', 'card' => $mCard);
        $html['group_list'] = self::getGroupShow($groupList);
        if ($dropRes) {
            $html['ai_text'] .= '<b style="color: red">符合drop条件 执行了drop</b>';
        }
        return $html;
    }



    //ai模拟摸牌打牌
    public static function drawAndDisCard()
    {
        $html['actinfo'] = array('title' => 'AI-模拟-初始化', 'logochevron' => 'ai-plan-list', );
        $html['template'] = "Demo/demo_rummy_dealcards.html";
        $html['hupai'] = 0;
        $html['start'] = 0;
        if (!isset($_SESSION['demoRummy'])) {
            //没有出实牌  执行发牌
            header('Location: ' . Url::getUrl('demo-rummy-dealcards'));
            die;
        }
        $lCard = $_SESSION['demoRummy']['lcard'];
        $mCard = $_SESSION['demoRummy']['mcard'];
        $aCard = $_SESSION['demoRummy']['acard'];
        $hand = $_SESSION['demoRummy']['hand'];
        //var_dump(implode(',',$hand));die;
        $maxCard = count($hand);
        $cardPool = $_SESSION['demoRummy']['card_pool'];

        if (!isset($_SESSION['demoRummy']['canFinish']) || !$_SESSION['demoRummy']['canFinish']) {
            $aiText = '';
            $minfo = get_card_info($mCard);
            $ainfo = get_card_info($aCard);
            $aiText .= '<p style="color: #0BB20C">明牌：' . $minfo['text_type'] . $minfo['num'] . '&nbsp;&nbsp;&nbsp;&nbsp;暗牌：' . $ainfo['text_type'] . $ainfo['num'] . '<br></p>';
            //执行摸明摸暗判定
            $obj = new AiRummyDrawCard($hand, $lCard, $maxCard, $mCard, $aCard);
            $drawCardRes = $obj->getDrawCardRes();
            $handScore = $obj->getHandScore();
            $drawMCardScore = $obj->getDrawMScore();

            $aiText .= '<p style="color: #0BB20C">手牌分数：' . $handScore . '<br></p>';
            $aiText .= '<p style="color: #0BB20C">摸明后分数：' . $drawMCardScore . '<br></p>';

            if ($drawCardRes == Common::TAKE_CARD_TYPE_M) {
                //执行摸明牌
                $hand[] = $mCard;
                $aiText .= '<p style="color: #0BB20C">AI 摸了明牌：' . $minfo['text_type'] . $minfo['num'] . '<br></p>';

            } else {
                //执行摸暗牌

                //执行摸暗牌之前是否触发换牌
                $obj = new AiRummyDrawDarkCard(1, $hand, [], '1_1_2_50', Common::PLAYER_TYPE_AI, $lCard, $cardPool, 3);
                $darkCard = $obj->getDarkCard();
                if ($darkCard) {
                    $cardPool = del_b_arr_from_a_arr($cardPool, array($darkCard));
                    $cardPool[] = $aCard;
                    $hand[] = $darkCard;
                    $ainfo = get_card_info($darkCard);
                    $aiText .= '<p style="color: #0BB20C">AI 摸暗牌 触发换牌机制：' . $ainfo['text_type'] . $ainfo['num'] . '<br></p>';
                } else {
                    $hand[] = $aCard;
                }
                $aiText .= '<p style="color: #0BB20C">AI 摸了暗牌：' . $ainfo['text_type'] . $ainfo['num'] . '<br></p>';
            }

            $disObj = new AiRummyDisCard($hand, $lCard, $maxCard);
            $canFinish = $disObj->getCanFinishRes();
            $declearCard = $disObj->getDeclearCards();
            $disCard = $disObj->getDisCard();


            $sinfo = get_card_info($disCard);
            $aiText .= '<p style="color: #0BB20C">AI 打了：' . $sinfo['text_type'] . $sinfo['num'] . '<br></p>';

            if ($canFinish) {
                $aiText .= '<p style="color:red">AI 胡牌了<br></p>';
                $html['hupai'] = 1;
                $_SESSION['demoRummy']['canFinish'] = true;
                $_SESSION['demoRummy']['aiText'] = $aiText;
            }

            //初始化明牌
            $mCard = '';
            self::initMcard($cardPool, $mCard);
            //初始化暗牌
            $aCard = '';
            self::initAcard($cardPool, $aCard);

            $_SESSION['demoRummy']['hand'] = del_b_arr_from_a_arr($hand, array($disCard));
            $_SESSION['demoRummy']['card_pool'] = $cardPool;
            foreach ($declearCard as $key => $val) {
                $html['group_list']['declear_card' . $key][] = $val;
            }
            $_SESSION['demoRummy']['group_list'] = $html['group_list'];
            $html['ai_text'] = $aiText;

        } else {
            $html['group_list'] = $_SESSION['demoRummy']['group_list'];
            $html['ai_text'] = $_SESSION['demoRummy']['aiText'];
        }

        $html['t_card']['l_card'] = array('name' => '癞子', 'card' => $lCard);
        $html['t_card']['a_card'] = array('name' => '暗牌', 'card' => $aCard);
        $html['t_card']['m_card'] = array('name' => '明牌', 'card' => $mCard);


        return $html;
    }




    //设置一张癞子牌
    public static function initLcard(&$cardPool, &$lcard)
    {
        $lcard = self::randCards($cardPool, 1)[0];
        $_SESSION['demoRummy']['lcard'] = $lcard;
    }

    //设置一张名牌
    public static function initMcard(&$cardPool, &$mcard)
    {
        $mcard = self::randCards($cardPool, 1)[0];
        $_SESSION['demoRummy']['mcard'] = $mcard;
    }

    //设置一张暗牌
    public static function initAcard(&$cardPool, &$acard)
    {
        $acard = self::randCards($cardPool, 1)[0];
        $_SESSION['demoRummy']['acard'] = $acard;
    }


    //初始化首牌
    public static function initDealCards(&$cardPool, &$dealCards, $num)
    {
        $dealCards = self::randCards($cardPool, $num);
        $_SESSION['demoRummy']['hand'] = $dealCards;
    }

    //随机一张摸牌
    public static function randCards($cardPool, $num = 1)
    {
        $cards = array();
        for ($j = 1; $j <= $num; $j++) {
            shuffle($cardPool);
            $cards[] = array_pop($cardPool);
        }
        //设置牌池
        $_SESSION['demoRummy']['zongpai'] = $cardPool;
        return $cards;
    }


    //展示数据组合
    public static function getGroupShow($groupList)
    {
        $newGroupList = [];
        if (!empty($groupList['group_san_pai'])) {
            $sanPai = $groupList['group_san_pai'];
            unset($groupList['group_san_pai']);
            $newGroupList['group_san_pai'][] = $sanPai;
        }
        if (!empty($groupList['group_laizi_guipai'])) {
            $guiLai = $groupList['group_laizi_guipai'];
            unset($groupList['group_laizi_guipai']);
            $newGroupList['group_laizi_guipai'][] = $guiLai;
        }
        foreach ($groupList as $key => $val) {
            if (empty($val)) {
                continue;
            }
            foreach ($val as $k => $v) {
                if (empty($v)) {
                    continue;
                }
                $newGroupList[$key] = isset($newGroupList[$key]) ? $newGroupList[$key] : [];
                $newGroupList[$key] = array_merge($newGroupList[$key], $v);
            }
        }
        return $newGroupList;
    }
}
