<?php

$dbnum_game = \lconfig\AddrRedisConfig::Instance()->simple_redis_dbnum;
$dbnum_gm = \lconfig\AddrRedisConfig::Instance()->simple_redis_dbnum;
// 这个看起来是 api 私有的 Redis
$dbnum_ai = \lconfig\AddrRedisConfig::Instance()->simple_redis_dbnum;

return [
    //redis_配置
    'redis_config' => \lconfig\AddrRedisConfig::Instance()->get_simple_redis_conf_for_ai(),

    // bakhmut: 统一不要前缀了
    'game_redis_prefix' => '',
    'web_redis_prefix' => '',


    //-------------------------------------------------------------------------
    // 发现无人使用的
    //-------------------------------------------------------------------------

    //奖池金额
    'jackpot_db' => $dbnum_ai,

    // 初始化奖池操作 --> setScoreJackpot --> setJackpotAmountOne
    'jackpot_key' => 'jackpot_list',
    // bakhmut: 发现无人使用
    // 批量设置盈利率奖池金额 --> setPrJackpotAmount
    'profit_jackpot_key' => 'profit_jackpot_list',
    'profit_jackpot_rate_key' => 'profit_jackpot_rate_list',
    'profit_jackpot_all_amount_key' => 'profit_jackpot_all_amount_key',
    'profit_init_jackpot_log' => 'profit_init_jackpot_log',


    //tp奖池

    // bakhmut: 发现无人使用
    'tp_jackpot_key' => 'tp_jackpot_list',

    // bakhmut: 发现无人使用
    // 批量设置盈利率奖池金额 --> setPrTpJackpotAmount --> tp_profit_jackpot_list
    'tp_profit_jackpot_key' => 'tp_profit_jackpot_list',

    // bakhmut: 发现无人使用
    // 批量设置盈利率奖池分配比例 --> setPrTpJackpotRate --> tp_profit_jackpot_rate_list
    'tp_profit_jackpot_rate_key' => 'tp_profit_jackpot_rate_list',

    // bakhmut: 发现无人使用
    // 设置盈利率初始化总奖池金额 --> setPrTpJackpotInitAllAmount --> tp_profit_jackpot_all_amount_key
    'tp_profit_jackpot_all_amount_key' => 'tp_profit_jackpot_all_amount_key',

    // bakhmut: 发现无人使用
    // 盈利率设置初始化奖池log --> setPrTpJackpotInitAllAmountLog --> tp_profit_init_jackpot_log
    'tp_profit_init_jackpot_log' => 'tp_profit_init_jackpot_log',


    //web使用的redis key=========================
    //牌型配置库
    'ai_rule_db' => $dbnum_ai,

    // bakhmut: 发现无人使用
    // ConfigScoreAct.addAiRule --> addRuleById --> ai_rule
    'ai_rule_key' => 'ai_rule',

    //方案配置库
    'ai_plan_db' => $dbnum_ai,

    // bakhmut: 发现无人使用
    // ConfigScoreAct.editPlan --> setPlanInfoByPlanId --> ai_plan
    'ai_plan_key' => 'ai_plan',

    //房间配置库
    'room_plan_db' => $dbnum_ai,

    // bakhmut: 发现无人使用
    // ConfigScoreAct.editRoomPlan --> setRoomPlanInfoOne --> ai_room_plan
    'room_plan_key' => 'ai_room_plan',


    //奖池补贴每日限额设置
    // bakhmut: 发现无人使用
    // hIncrByPrJackpotFillAmount --> fill_pool_day_config
    'fill_pool_day_config' => 'fill_pool_day_config',


    //ab ai携带金额段位数量
    // bakhmut: 发现无人使用
    'ab_ai_blance_amount_region_id_cnt' => 'ab_ai_blance_amount_region_id_cnt',

    //tp 牌型出现概率翻倍列表
    // bakhmut: 发现无人使用
    // editTpGroupBaseX --> setTpGroupBaseXList --> tp_group_base_conf
    'tp_group_base_conf' => 'tp_group_base_conf',


    // bakhmut: 发现无人使用，在后台界面被注掉了
    // "Rummy-真人 好运配置" --> editLuckyConfig --> setPrLuckyConfig --> ai_lucky_config
    'ai_lucky_config_key' => 'ai_lucky_config',

    // bakhmut: 发现无人使用，在后台界面被注掉了
    // "Rummy-真人 霉运配置" --> editUnLuckyConfig --> setPrUnLuckyConfig --> ai_unlucky_config
    'ai_unlucky_config_key' => 'ai_unlucky_config',

    // bakhmut: 发现无人使用
    'z_deal_10card_rate_config_key' => 'z_deal_10card_rate_config_key',


    //-------------------------------------------------------------------------
    // AI 头像昵称
    //-------------------------------------------------------------------------

    // 添加AI -> pr-robot-list -> "RobotPrAct.getRobotList 通过状态获取AI机器人列表"

    //游戏使用的redis key=========================
    //ai 列表配置
    'ai_list_db' => $dbnum_ai,

    // "score_robot_list.html 刷新头像昵称" --> "[净胜分] [清空AI] 'score-raobot-flush'" --> RobotScoreAct.flushRobot --> setScoreAiInfoById --> robot_
    'ai_robot_key' => 'robot_',

    // 添加AI按钮打开的二级页面
    // "pr_robot_list.html 刷新头像昵称" --> "[盈利率] [清空AI] 'pr-robot-flush'" --> 刷新用户的头像跟昵称 --> RobotPrAct.flushRobot --> setProfitAiInfoById --> robot_data_list
    'ai_robot_profit_key' => 'robot_data_list',


    //-------------------------------------------------------------------------
    // Rummy
    //-------------------------------------------------------------------------


    //盈利率配置
    'ai_profit_rate_db' => $dbnum_ai,

    // "Rummy-真人 PR配置" --> editPrConfig --> setPrConfig --> ai_profit_rate_config
    // {"1":{"p":"100","d_limit":"0.1","d_begin":"0.6","u_begin":"0.9","u_limit":"1.6","ed":"1","eu":"1"},"2":{"p":"150","d_limit":"0.2","d_begin":"0.4","u_begin":"1.4","u_limit":"1.8","ed":"1","eu":"1"},"3":
    'ai_profit_rate_config_key' => 'ai_profit_rate_config',

    // "Rummy-真人 13card发牌配置" --> editZDeal13CardRateConfig --> setPrCardsGroupConfigByMaxCardNum --> z_deal_13card_rate_config_key
    // {"group_name_3lv":{"normal":"45","up":"0.8","down":"-1","novice":"1","recharge":"0.4"},"group_name_2lv":{"normal":"183","up":"0.5","down":"-0.6","novice":"0.4","recharge":"0.3"},"group_name_1lv":{"normal":"140","up":"-1","down":"0.8","novice":"0","recharge":"-0.5"},"group_name_nolv_yeslai":{"normal":"451","up":"0","down":"-0.45","novice":"0","recharge":"0"},"group_name_nolv_nolai":{"normal":"182","up":"-1","down":"1.1","novice":"-0.75","recharge":"-0.5"}}
    // group_name_3lv 3绿及以上
    // group_name_2lv 2绿
    // group_name_1lv 1绿
    // group_name_nolv_yeslai 0绿有癞
    // group_name_nolv_nolai 0绿无癞
    // 基础权重 扶植系数 抑制系数 新手权重 新充权重
    // normal  up       down    novice  recharge
    'z_deal_13card_rate_config_key' => 'z_deal_13card_rate_config_key',

    // "Rummy-真人 新手保护发牌配置" --> editZDealCardRookieBuffConfig --> setPrRookieBuffDealCardConfig --> z_rookie_buff_dealcard_config_key
    // {"group_name_3lv":{"z13":"12","a13":"5","z10":"0","a10":"0"},"group_name_2lv":{"z13":"20","a13":"20","z10":"0","a10":"0"},"group_name_1lv":{"z13":"8","a13":"25","z10":"20","a10":"10"},"group_name_nolv_yeslai":{"z13":"50","a13":"50","z10":"60","a10":"70"},"group_name_nolv_nolai":{"z13":"10","a13":"0","z10":"20","a10":"20"}}
    // [13]真人 [13]AI [10]真人 [10]AI
    // z13      a13    z10      a10
    'z_rookie_buff_dealcard_config_key' => 'z_rookie_buff_dealcard_config_key',

    // bukhmut: 这个后台配置界面和代码不匹配，和给的 Redis 数据不匹配，有问题的！
    // "Rummy-真人 摸牌控制概率" --> editDarkConfig --> setDarkRateConfig --> dark_config_key
    // [{"dark_joker_rate":"0"},{"dark_joker_rate":"0"},{"dark_joker_rate":"0"},{"dark_joker_rate":"0"}]
    'dark_config_key' => 'dark_config_key',


    // bakhmut: 这个没给 Redis 数据
    //ai等级配置
    'ai_level_db' => $dbnum_ai,
    // "Rummy-AI 13card AI牌型配置" --> editPrAiLevel --> edieAiLevelConfig --> setPrAiLevelConfig --> ai_level
    // "Rummy-AI 10card AI牌型配置" --> editPrAiLevel --> edieAiLevelConfig --> setPrAiLevelConfig --> ai_level
    'ai_level_key' => 'ai_level',


    //rummy类 奖池预设收益比例
    // HASH(10_tp_jack_pool => {"1":"-0.5","2":"0.02","4":"0.1","5":"0.05","6":"0.075","7":"0.07","8":"0.045","9":"0.07","10":"0.07","11":"0.135","12":"0.09","13":"0.11","14":"0.11"})
    // "Rummy-AI 预设收益比例" --> setWinRate --> setPrWinRate
    // "TP-AI 预设收益比例" --> setWinRate --> setPrWinRate
    // "百人场-AI 预设收益比例" --> setWinRate --> setPrWinRate
    // getSysKillerTypeByPlayerIndex 会计算出 14 种用户杀率类型，和有效充值次数多少有关
    'ai_win_rate_config_key' => 'ai_new_win_rate_config_key',

    // 设置机器人携带bounus比例
    // HASH(50_jack_pool => 3)
    // "Rummy-AI 保存Bounus数量百分比" --> setRobotBounusRate --> setPrRobotBounusRate --> ai_bounus_rate_config_key
    'ai_bounus_rate_config_key' => 'ai_bounus_rate_config_key',

    // HASH(50_jack_pool => {"a_control_rate":"","a_control_coefficient":"","z_control_coefficient":"","finish_rate":"0.1","intervene_rate":"2"})
    // HASH(all => {"fill_limit_point":""})
    // "Rummy-AI 保存干预参数" --> setPrAiDynamicConfig --> setProfitAiDynamicConfig --> setPrDymaicOptConfig --> ai_dynamic_config_key
    // <th>A喂牌率</th> a_control_rate
    // <th>A控牌系数</th> a_control_coefficient
    // <th>Z控牌系数</th> z_control_coefficient
    // -<th>b百分比</th> jack_pool_bonus_rate
    // <th>胡牌干预</th> finish_rate
    // <th>房间控系数</th> intervene_rate
    // -<th>小白概率</th> plan_info
    // -<th>普通概率</th> plan_info
    // -<th>高手概率</th> plan_info
    'ai_dynamic_config_key' => 'ai_dynamic_config_key',

    // HASH(50_jack_pool => {"1":"30","2":"35","3":"35"})
    // 小白概率 普通概率 高手概率
    // "Rummy-AI 保存AI配置" --> editPrRoomPlan -> ConfigPrAct.editRoomPlan --> setPrPlanInfo --> profit_room_plan_key
    'profit_room_plan_key' => 'profit_ai_room_plan',



    //-------------------------------------------------------------------------
    // TP
    //-------------------------------------------------------------------------

    // 1-真金 2-筹码 10-金币

    // 根据 p 查 d_limit_d_begin u_begin _ulimit 用的
    // {"1":{"p":"100","d_limit":"0.3","d_begin":"0.7","u_begin":"1.05","u_limit":"2","ed":"1","eu":"1"},"2":{"p":"150","d_limit":"0.1","d_begin":"0.3","u_begin":"1.5","u_limit":"1.8","ed":"1","eu":"1"},"3":
    // "TP PR配置" --> editTpPrConfig --> setTpPrConfig --> tp_ai_profit_rate_config
    'tp_ai_profit_rate_config_key' => 'tp_ai_profit_rate_config',

    // AI性格配置
    // {"STEADY":{"initStuffyRate":"0.35","initRaiseRate":"0.3","raiseDecayRate":"0.3","avgWinRateDynamic":"0.8","lowestRaiseRate":"0","followDynamic":"0.9","followDynamicPow":"0.81","raiseDynamic":"0.78","raiseDynamicPow":"0.81","aa2":"0.5","bb1":"1.2","cc1":"2","dd2":"0.15","a1":"0.9","a2":"0.4","b1":"0.1","b2":"0.04","c1":"0.95","d3":"0.13","e3":"0.65","e4":"0.1","f1":"2","g3":"0.15","h1":"0.97"},
    // "TP AI性格配置" --> editTpAiDisposition --> setTpAiDispositionConfig --> tp_ai_disposition_config_key
    'tp_ai_disposition_config_key' => 'tp_ai_disposition_config_key',

    // 和奖池有关系，计算换牌概率会使用
    // HASH(base_cls_jack_pool => {"change_card_init_rate":"0.1","deal_rate":"0.02","sys_killer_limit_rate":"0.33","intervene_rate":"0.5"})
    // change_card_init_rate 初始换牌杀率（小数）
    // deal_rate 房间控初始（小数）
    // sys_killer_limit_rate 房间控上限
    // intervene_rate 房间控系数
    // "TP-AI 保存干预参数" --> setPrTpAiDynamicConfig --> setProfitAiDynamicConfig --> setPrTpDymaicOptConfig
    'tp_ai_dynamic_config_key' => 'tp_ai_dynamic_config_key',

    // AI性格分配比例
    // HASH(1000_tp_jack_pool => {"STEADY":"45","STEADY2":"10","CUNNING":"35","EXCITED":"15","JOKER_STEADY":"0","JOKER_STEADY2":"0","JOKER_CUNNING":"0","JOKER_EXCITED":"0","AK47_STEADY":"0","AK47_STEADY2":"0","AK47_CUNNING":"0","AK47_EXCITED":"0"})
    // "TP-AI 保存AI人格比例" --> setAiDispositionConfig --> setTpAiDispositionRateConfig --> tp_ai_disposition_rate_config_key
    'tp_ai_disposition_rate_config_key' => 'tp_ai_disposition_rate_config_key',


    //-------------------------------------------------------------------------
    // 百人场
    //-------------------------------------------------------------------------

    //系统强杀干预系数
    // HASH(1000_ab_jack_pool => {"room_type_killer":["0.02","0.1","0.15"],"buff_coefficient":["15","30","45"],"win_score_killer_base":"3000","intervene_rate":"0.16","initial_rate":"0.0","tolerance_factor":"0.5","float_rate":"0.33"})
    // 普通房 room_type_killer[0]
    // Buff系数 buff_coefficient[0]
    // 高手Buff room_type_killer[1]
    // Buff系数 buff_coefficient[1]
    /// 大户Buff room_type_killer[2]
    // Buff系数 buff_coefficient[2]
    // 强杀下注额 win_score_killer_base
    // 房间控系数 intervene_rate
    // 初始概率 initial_rate
    // 容忍系数 tolerance_factor
    // 浮动概率 float_rate
    // "百人场-AI 保存配置" --> setRoomConfig --> setPrRoomConfig --> ai_room_config_key
    'ai_room_config_key' => 'ai_room_config_key',

    //金骰子概率
    // [{"rate":"0.03"},{"rate":"0.025"},{"rate":"0.025"}]
    // 普通房 高手房 大户房
    // "百人场-AI 7_Up_Down 保存金骰子配置" --> setGoldDiceConfig --> ai_gold_dice_config_key
    'ai_gold_dice_config_key' => 'ai_gold_dice_config_key',



    //-------------------------------------------------------------------------
    // 动态
    //-------------------------------------------------------------------------


    //用户充值次数
    'recharge_all_cnt_db' => $dbnum_gm,
    'recharge_all_cnt' => 'recharge_all_cnt',


    // 按机器人ID绑定的AI性格
    // HASH(uid => (disposition, expires_time))
    // 保存AI人格比例 --> setAiDispositionConfig -- 清除旧的绑定 --> clearTpAiAssignDisposition --> tp_ai_assign_disposition_key
    'tp_ai_assign_disposition_key' => 'tp_ai_assign_disposition_key',


    //龙虎牌池 8副牌不带王
    // HASH(room_id => pool)
    // setLongHuPool --> long_hu_pool
    'long_hu_pool' => 'long_hu_pool',


    //Ab牌池
    // HASH(room_id => pool)
    // setAbClassicPool --> ab_classic_pool
    'ab_classic_pool' => 'ab_classic_pool',


    //-------------------------------------------------------------------------
    // 统计
    //-------------------------------------------------------------------------

    //不同类型的房间 流水的输赢佣金统计
    'room_wallet_log_stat_db' => $dbnum_game,

    // 获取大盘营收列表 --> getPrRoomWalletLogList
    // "根据奖池名称获取 房间流水 输赢佣金 统计金额" --> getPrRoomWalletLogStatV2
    // const AI_ROOM_DATA_RC = 'rc'; //AI总输赢
    // const AI_ROOM_DATA_RW = 'rw'; //真人总赢
    // const AI_ROOM_DATA_RL = 'rl'; //真人总输
    // const AI_ROOM_DATA_TB = 'tb'; //真人总下注
    // const AI_ROOM_DATA_RTB = 'rtb'; //新手真人总下注
    // update_room_data_detail --> 修改 --> AI_ROOM_DATA_W
    // 这个是玩家扣除佣金后赢的钱，
    // const AI_ROOM_DATA_W = 'w'; //总赢
    // update_room_data_detail --> 修改 --> AI_ROOM_DATA_L
    // 这个是玩家输的钱
    // const AI_ROOM_DATA_L = 'l'; //总输
    // insert_user_wallet_log --> update_room_data_detail -- 修改 --> AI_ROOM_DATA_C
    // robot_loser_process --> insert_user_wallet_log
    // robot_winner_process --> insert_user_wallet_log
    // AB.dealer_history --> insert_user_wallet_log
    // WalletLogic.reduce_currency --> insert_user_wallet_log
    // WalletLogic.init_wallet_currency --> insert_user_wallet_log
    // WalletLogic.inc_wallet_balance --> insert_user_wallet_log
    // 从意图上看，这个是系统盈亏，但某些情况的计算规则好像有问题：TP 有多个玩家时某个玩家赢了
    // const AI_ROOM_DATA_C = 'c'; //总营
    // HASH ai_room_data_20211020(玩法_货币_最大人数_底注点数_杀率类型_字段名 => 金额)
    'room_wallet_log_stat_key_prefix' => 'ai_room_data_',
    // 后面连接日期 ai_room_data_20211020





];
