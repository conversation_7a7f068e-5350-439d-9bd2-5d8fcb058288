<?php
//筹码面值
const LUCKY_DICE_CHIP_FACE_VALUE_LIST = [10, 50, 200, 1000, 5000];

//other下注机器人个数
const LUCKY_DICE_OTHER_BET_ROBOT_CNT = 100;

//lucky7骰子定义
const LUCKY_DICE_CNT = 6;

//lucky7骰子定义
const LUCKY_DICE = [
    0 => 'parrot',
    1 => 'snake',
    2 => 'monkey',
    3 => 'squirrel',
    4 => 'elephant',
    5 => 'peafowl',
];

//桌子配置
const LUCKY_DICE_TABLE = [
    'min_amount' => 100,
    //下注携带的最小金额
    'max_amount' => 20000, //单局下注的最大金额
];

//下注选项
const LUCKY_DICE_OPTION = [
    0 => ['2x' => 3.2, '3x' => 5.3, '4x' => 10.5, '5x' => 21, '6x' => 105, 'bet_amount_x' => 3.8],
    1 => ['2x' => 3.2, '3x' => 5.3, '4x' => 10.5, '5x' => 21, '6x' => 105, 'bet_amount_x' => 3.8],
    2 => ['2x' => 3.2, '3x' => 5.3, '4x' => 10.5, '5x' => 21, '6x' => 105, 'bet_amount_x' => 3.8],
    3 => ['2x' => 3.2, '3x' => 5.3, '4x' => 10.5, '5x' => 21, '6x' => 105, 'bet_amount_x' => 3.8],
    4 => ['2x' => 3.2, '3x' => 5.3, '4x' => 10.5, '5x' => 21, '6x' => 105, 'bet_amount_x' => 3.8],
    5 => ['2x' => 3.2, '3x' => 5.3, '4x' => 10.5, '5x' => 21, '6x' => 105, 'bet_amount_x' => 3.8],
];

// ####################################################################################################
const LUCKY_DICE_BET_SECOND = 15;
//排行榜下注时间权重以及下注金额倍数
const LUCKY_DICE_BET_WEIGHT = [
    //1-5秒
    [
        'min_second' => 1,
        'max_second' => 5,
        'other_bet_rate' => 0.05,
        //0.035
        'rank_bet_rate' => 0.45,
        'bet_amount_region' => [
            ['min' => 0, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [50, 50, 40, 40, 25, 25, 20, 20, 15, 15, 15, 10, 10],
        'min_bet_amount_rate' => 0.3,
        'max_bet_amount_rate' => 0.9,
    ],
    //6-10秒
    [
        'min_second' => 6,
        'max_second' => 10,
        'other_bet_rate' => 0.03,
        'rank_bet_rate' => 0.3,
        'bet_amount_region' => [
            ['min' => 0, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.1],
            ['min' => 0.1, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
        ],
        'bet_amount_region_weight' => [50, 50, 40, 40, 30, 25, 25, 15, 10],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.5,
    ],
    //11-15秒
    [
        'min_second' => 11,
        'max_second' => 15,
        'other_bet_rate' => 0.015,
        'rank_bet_rate' => 0.3,
        'bet_amount_region' => [
            ['min' => 0, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.1],
            ['min' => 0.1, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
        ],
        'bet_amount_region_weight' => [60, 50, 40, 40, 35, 25],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.5,
    ],
];
//牌桌AI 本局初始化信息 ###################################################################################

//ai本局是否下注
const LUCKY_DICE_BET_RATE = [
    'other' => 0.95,
    'rank' => 0.7
];

//ai本局下注总钱数概率
const LUCKY_DICE_BET_AMOUNT_RATE = [
    'other' => [
        'min' => 0.35,
        'max' => 0.35,
    ],
    'rank' => [
        'min' => 0.1,
        'max' => 0.31
    ]
];

//互斥选项  根据配置获取下下注选项
const LUCKY_DICE_MUTEX_LIST = [
    'other' => [
        [
            'option' => [0 => 166, 1 => 166, 2 => 166, 3 => 166, 4 => 166, 5 => 166],
            'select_option_cnt' => 3
        ]
    ],
    'rank' => [
        [
            'option' => [0 => 166, 1 => 166, 2 => 166, 3 => 166, 4 => 166, 5 => 166],
            'select_option_cnt' => 3
        ]
    ]
];


//AI 账户余额 重置参数配置################################################################################
//ai 携带金额权重区间配置 原始金额 不*100 在区间随机结束后再*100操作  保证区间出来的数在最终计算时有一位小数
const LUCKY_DICE_BALANCE_AMOUNT = [
    'amount_region' => [
        1 => ['min' => 100, 'max' => 5000],
    ],
    'weight' => [1 => 1]
];



?>

