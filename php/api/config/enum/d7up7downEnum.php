<?php
//7up7down

//筹码面值  以及出现的权重
const D_7UP_7DOWN_CHIP_FACE_VALUE_LIST = [10, 50, 200, 1000, 5000];

//other下注机器人个数
const D_7UP_7DOWN_OTHER_RAISE_ROBOT_CNT = 100;

//定义筛子个数
const D_7UP_7DOWN_IDCE_CNT = 2;

//7上7下筛子定义
const D_7UP_7DOWN_IDCE = [1 => 'red', 2 => 'black', 3 => 'black', 4 => 'red', 5 => 'black', 6 => 'black',];

//桌子配置
const D_7UP_7DOWN_TABLE = [
    'min_amount' => 100,
    //下注携带的最小金额
    'max_amount' => 20000, //单局下注的最大金额
];

//金骰子概率
const GOLD_DICE = [
    //普通房
    0 => [
        'rate' => 0.03,
        'weight' => [3 => 10, 4 => 10, 5 => 10],
    ],
    //高手房
    1 => [
        'rate' => 0.025,
        'weight' => [3 => 10, 4 => 5, 5 => 2],
    ],
    //大户房
    2 => [
        'rate' => 0.025,
        'weight' => [3 => 10, 4 => 10, 5 => 5],
    ],
];


//下注选项
//@注意  修改参数的时候  同步修改 游戏逻辑服配置
//sum   2个骰子 点数之和
//color 2个骰子 点数的颜色
const D_7UP_7DOWN_OPTION = [
    0 => ['x' => 2.3, 'sum' => [2, 3, 4, 5, 6], 'bet_amount_x' => 1.5],
    1 => ['x' => 5.7, 'sum' => [7], 'bet_amount_x' => 1.3],
    2 => ['x' => 2.3, 'sum' => [8, 9, 10, 11, 12], 'bet_amount_x' => 1.5],
];


//枚举所有筛子之和的情况
const D_7UP_7DOWN_OPTION_RESULT_LIST = [
    0 => [
        [1, 1],
        //和2
        [1, 2],
        [2, 1],
        //和3
        [2, 2],
        [1, 3],
        [3, 2],
        //和4
        [1, 4],
        [4, 1],
        [2, 3],
        [3, 2],
        //和5
        [1, 5],
        [5, 1],
        [2, 4],
        [4, 2],
        [3, 3],
        //和6
    ],
    1 => [
        [1, 6],
        [2, 5],
        [3, 4] //和7
    ],
    2 => [
        [2, 6],
        [6, 2],
        [3, 5],
        [5, 3],
        [4, 4],
        //和8
        [3, 6],
        [6, 3],
        [4, 5],
        [5, 4],
        //和9
        [4, 6],
        [6, 4],
        [5, 5],
        //和10
        [5, 6],
        [6, 5],
        //和11
        [6, 6] //和12
    ],
];

// ####################################################################################################
const D_7UP_7DOWN_SECOND = 15;
//排行榜下注时间权重以及下注金额倍数
const D_7UP_7DOWN_BET_WEIGHT = [
    //1-5秒
    [
        'min_second' => 1,
        'max_second' => 5,
        'other_bet_rate' => 0.05,
        //0.035
        'rank_bet_rate' => 0.45,
        'bet_amount_region' => [
            ['min' => 0, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [50, 50, 40, 40, 25, 25, 20, 20, 15, 15, 15, 10, 10],
        'min_bet_amount_rate' => 0.3,
        'max_bet_amount_rate' => 0.9,
    ],
    //6-10秒
    [
        'min_second' => 6,
        'max_second' => 10,
        'other_bet_rate' => 0.03,
        'rank_bet_rate' => 0.3,
        'bet_amount_region' => [
            ['min' => 0, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.1],
            ['min' => 0.1, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
        ],
        'bet_amount_region_weight' => [50, 50, 40, 40, 30, 25, 25, 15, 10],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.5,
    ],
    //11-15秒
    [
        'min_second' => 11,
        'max_second' => 15,
        'other_bet_rate' => 0.015,
        'rank_bet_rate' => 0.3,
        'bet_amount_region' => [
            ['min' => 0, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.1],
            ['min' => 0.1, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
        ],
        'bet_amount_region_weight' => [60, 50, 40, 40, 35, 25],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.5,
    ],
];


//牌桌AI 本局初始化信息 ###################################################################################
//ai本局是否下注
const D_7UP_7DOWN_BET_RATE = [
    'other' => 0.95,
    'rank' => 0.7
];

//ai本局下注总钱数概率
const D_7UP_7DOWN_BET_AMOUNT_RATE = [
    'other' => [
        'min' => 0.35,
        'max' => 0.35,
    ],
    'rank' => [
        'min' => 0.1,
        'max' => 0.31
    ]
];

//互斥选项  根据配置获取下下注选项
const D_7UP_7DOWN_MUTEX_LIST = [
    'other' => [
        [
            'option' => [0 => 600, 2 => 600],
            'select_option_cnt' => 1
        ],
        [
            'option' => [1 => 200],
            'select_option_cnt' => 1
        ]
    ],
    'rank' => [
        [
            'option' => [0 => 600, 2 => 600],
            'select_option_cnt' => 1
        ],
        [
            'option' => [1 => 200],
            'select_option_cnt' => 1
        ]
    ]
];



//AI 账户余额 重置参数配置################################################################################
//ai 携带金额权重区间配置 原始金额 不*100 在区间随机结束后再*100操作  保证区间出来的数在最终计算时有一位小数
const D_7UP_7DOWN_BALANCE_AMOUNT = [
    'amount_region' => [
        1 => ['min' => 100, 'max' => 5000],
    ],
    'weight' => [1 => 1]
];

?>

