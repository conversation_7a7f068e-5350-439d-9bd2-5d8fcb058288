<?php
//7up7down

//筹码面值
const BENZ_BMW_CHIP_FACE_VALUE_LIST = [10, 50, 200, 1000, 5000];

//other下注机器人个数
const BENZ_BMW_OTHER_RAISE_ROBOT_CNT = 100;

//桌子配置
const BENZ_BMW_TABLE = [
    'min_amount' => 10,
    //下注携带的最小金额
    'max_amount' => 2000, //单局下注的最大金额
];

//@注意  修改参数的时候  同步修改 游戏逻辑服配置
const BENZ_BMW_OPTION = [
    0 => ['x' => 42, 'name' => 'Ferrari', 'key_list' => [17], 'bet_amount_x' => 4],
    1 => ['x' => 26, 'name' => 'Lamborghini', 'key_list' => [5], 'bet_amount_x' => 0.8],
    2 => ['x' => 16, 'name' => 'Porsche', 'key_list' => [23], 'bet_amount_x' => 4],
    3 => ['x' => 10.6, 'name' => 'Mercedes', 'key_list' => [11], 'bet_amount_x' => 4],
    4 => ['x' => 5.3, 'name' => 'BMW', 'key_list' => [0, 4, 9, 14, 19], 'bet_amount_x' => 4],
    5 => ['x' => 5.3, 'name' => 'Audi', 'key_list' => [1, 6, 10, 15, 20], 'bet_amount_x' => 4],
    6 => ['x' => 5.3, 'name' => 'Mahindra', 'key_list' => [2, 7, 12, 16, 21], 'bet_amount_x' => 4],
    7 => ['x' => 5.3, 'name' => 'TATA', 'key_list' => [3, 8, 13, 18, 22], 'bet_amount_x' => 4],
];


//开牌权重配置
const BENZ_BMW_WIN_OPTION_WEIGHT = [0 => 15, 1 => 24, 2 => 40, 3 => 60, 4 => 120, 5 => 120, 6 => 120, 7 => 120];

//龙虎下注秒数
const BENZ_BMW_BET_SECOND = 15;
// ####################################################################################################
//排行榜下注时间权重以及下注金额倍数
const BENZ_BMW_BET_WEIGHT = [
    //1-5秒
    [
        'min_second' => 1,
        'max_second' => 5,
        'other_bet_rate' => 0.05,
        //0.035
        'rank_bet_rate' => 0.45,
        'bet_amount_region' => [
            ['min' => 0, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [60, 60, 50, 50, 40, 40, 30, 30, 20, 20, 10, 10, 10],
        'min_bet_amount_rate' => 0.3,
        'max_bet_amount_rate' => 0.9,
    ],
    //6-10秒
    [
        'min_second' => 6,
        'max_second' => 10,
        'other_bet_rate' => 0.03,
        'rank_bet_rate' => 0.3,
        'bet_amount_region' => [
            ['min' => 0, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.1],
            ['min' => 0.1, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
        ],
        'bet_amount_region_weight' => [50, 50, 40, 40, 30, 25, 25, 10, 10],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.5,
    ],
    //11-15秒
    [
        'min_second' => 11,
        'max_second' => 15,
        'other_bet_rate' => 0.015,
        'rank_bet_rate' => 0.2,
        'bet_amount_region' => [
            ['min' => 0, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.1],
            ['min' => 0.1, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
        ],
        'bet_amount_region_weight' => [60, 50, 40, 40, 35, 30],
        'min_bet_amount_rate' => 0.05,
        'max_bet_amount_rate' => 0.3,
    ],
];

//牌桌AI 本局初始化信息 ###################################################################################
//ai本局是否下注
const BENZ_BMW_BET_RATE = [
    'other' => 0.95,
    'rank' => 0.7
];


//ai本局下注总钱数概率
const BENZ_BMW_BET_AMOUNT_RATE = [
    'other' => [
        'min' => 0.35,
        'max' => 0.35,
    ],
    'rank' => [
        'min' => 0.1,
        'max' => 0.31
    ]
];

//互斥选项  根据配置获取下下注选项
const BENZ_BMW_MUTEX_LIST = [
    'other' => [
        [
            'option' => [0 => 70, 1 => 80, 2 => 90, 3 => 100, 4 => 160, 5 => 150, 6 => 160, 7 => 150],
            'select_option_cnt' => 3
        ]
    ],
    'rank' => [
        [
            'option' => [0 => 70, 1 => 80, 2 => 90, 3 => 100, 4 => 160, 5 => 150, 6 => 160, 7 => 150],
            'select_option_cnt' => 3
        ]
    ]
];

//AI 账户余额 重置参数配置################################################################################
//ai 携带金额权重区间配置 原始金额 不*100 在区间随机结束后再*100操作  保证区间出来的数在最终计算时有一位小数
const BENZ_BMW_BALANCE_AMOUNT = [
    'amount_region' => [
        1 => ['min' => 10, 'max' => 500],
    ],
    'weight' => [1 => 1]
];


?>

