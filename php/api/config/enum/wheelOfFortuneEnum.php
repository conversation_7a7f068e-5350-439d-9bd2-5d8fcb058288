<?php
//幸运转盘

//筹码面值
const WHEEL_CHIP_FACE_VALUE_LIST = [10, 50, 200, 1000, 5000];

//other下注机器人个数
const WHEEL_OTHER_RAISE_ROBOT_CNT = 100;

//蓝色池子赢
const WHEEL_BLUE_WIN = 0;
//黄色池子赢
const WHEEL_YELLOW_WIN = 2;
//红色池子赢（和赢）
const WHEEL_RED_HE = 1;


//转盘抽奖权重配置
const WHEEL_COLOR_WEIGHT = [
    //BULE
    WHEEL_BLUE_WIN => 45,
    //RED
    WHEEL_RED_HE => 10,
    //YELLOW
    WHEEL_YELLOW_WIN => 45,
];


//转盘色块标号配置  一共20个色块 对应不同的奖池
//客户端拿到标号后 演示出结果
const WHEEL_COLOR_BLOCK = [
    0 => WHEEL_YELLOW_WIN,
    1 => WHEEL_BLUE_WIN,
    2 => WHEEL_YELLOW_WIN,
    3 => WHEEL_RED_HE,
    4 => WHEEL_BLUE_WIN,
    5 => WHEEL_YELLOW_WIN,
    6 => WHEEL_BLUE_WIN,
    7 => WHEEL_YELLOW_WIN,
    8 => WHEEL_BLUE_WIN,
    9 => WHEEL_YELLOW_WIN,
    10 => WHEEL_BLUE_WIN,
    11 => WHEEL_YELLOW_WIN,
    12 => WHEEL_BLUE_WIN,
    13 => WHEEL_RED_HE,
    14 => WHEEL_YELLOW_WIN,
    15 => WHEEL_BLUE_WIN,
    16 => WHEEL_YELLOW_WIN,
    17 => WHEEL_BLUE_WIN,
    18 => WHEEL_YELLOW_WIN,
    19 => WHEEL_BLUE_WIN,
];


//桌子配置
const WHEEL_TABLE = [
    'min_amount' => 100,
    //下注携带的最小金额
    'max_amount' => 20000,
    //单局下注的最大金额
];

//@注意  修改参数的时候  同步修改 游戏逻辑服配置
//压中了和 蓝色池子 和 黄色池子 各赔0.5
const WHEEL_OPTION = [
    0 => ['x' => 2, 'name' => '蓝色池子', 'bet_amount_x' => 4],
    1 => ['x' => 9, 'name' => '红色池子（和）', 'bet_amount_x' => 0.8],
    2 => ['x' => 2, 'name' => '黄色池子', 'bet_amount_x' => 4],
];

//下注秒数
const WHEEL_BET_SECOND = 15;
// ####################################################################################################
//排行榜下注时间权重以及下注金额倍数
const WHEEL_BET_WEIGHT = [
    //1-5秒
    [
        'min_second' => 1,
        'max_second' => 5,
        'other_bet_rate' => 0.05,
        //0.035
        'rank_bet_rate' => 0.45,
        'bet_amount_region' => [
            ['min' => 0, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [50, 50, 40, 40, 25, 25, 20, 20, 15, 15, 15, 10, 10],
        'min_bet_amount_rate' => 0.3,
        'max_bet_amount_rate' => 0.9,
    ],
    //6-10秒
    [
        'min_second' => 6,
        'max_second' => 10,
        'other_bet_rate' => 0.03,
        'rank_bet_rate' => 0.3,
        'bet_amount_region' => [
            ['min' => 0, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.1],
            ['min' => 0.1, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
        ],
        'bet_amount_region_weight' => [50, 50, 40, 40, 30, 25, 25, 15, 10],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.5,
    ],
    //11-15秒
    [
        'min_second' => 11,
        'max_second' => 15,
        'other_bet_rate' => 0.015,
        'rank_bet_rate' => 0.3,
        'bet_amount_region' => [
            ['min' => 0, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.1],
            ['min' => 0.1, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
        ],
        'bet_amount_region_weight' => [60, 50, 40, 40, 35, 25],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.5,
    ],
];

//牌桌AI 本局初始化信息 ###################################################################################
//ai本局是否下注
const WHEEL_BET_RATE = [
    'other' => 0.95,
    'rank' => 0.7
];


//ai本局下注总钱数概率
const WHEEL_BET_AMOUNT_RATE = [
    'other' => [
        'min' => 0.35,
        'max' => 0.35,
    ],
    'rank' => [
        'min' => 0.1,
        'max' => 0.31
    ]
];

//互斥选项  根据配置获取下下注选项
const WHEEL_MUTEX_LIST = [
    'other' => [
        [
            'option' => [0 => 462, 2 => 462],
            'select_option_cnt' => 1
        ],
        [
            'option' => [1 => 50],
            'select_option_cnt' => 1
        ]
    ],
    'rank' => [
        [
            'option' => [0 => 462, 2 => 462],
            'select_option_cnt' => 1
        ],
        [
            'option' => [1 => 50],
            'select_option_cnt' => 1
        ]
    ]
];

//AI 账户余额 重置参数配置################################################################################
//ai 携带金额权重区间配置 原始金额 不*100 在区间随机结束后再*100操作  保证区间出来的数在最终计算时有一位小数
const WHEEL_BALANCE_AMOUNT = [
    'amount_region' => [
        1 => ['min' => 100, 'max' => 5000],
    ],
    'weight' => [1 => 1]
];


?>

