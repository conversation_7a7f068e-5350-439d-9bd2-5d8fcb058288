<?php
//ab游戏 下注 区域和 x赢的倍数

//筹码面值  以及出现的权重
const AB_CHIP_FACE_VALUE_LIST = [10, 50, 200, 1000, 5000];

//other下注机器人个数
const AB_OTHER_RAISE_ROBOT_CNT = 100;

const AB_TABLE = [
    'min_amount' => 100,
    //下注携带的最小金额
    'max_amount' => 20000, //单局下注的最大金额
];

//@注意  修改参数的时候  同步修改 游戏逻辑服配置
const AB_OPTION = [
    0 => ['x' => 1.98, 'bet_amount_x' => 2],
    //奇数
    1 => ['x' => 2.08, 'bet_amount_x' => 2],
    //偶数
    2 => ['x' => 3.7, 'min' => 1, 'max' => 5, 'bet_amount_x' => 0.6],
    3 => ['x' => 4.7, 'min' => 6, 'max' => 10, 'bet_amount_x' => 0.6],
    4 => ['x' => 5.9, 'min' => 11, 'max' => 15, 'bet_amount_x' => 0.6],
    5 => ['x' => 4.7, 'min' => 16, 'max' => 25, 'bet_amount_x' => 0.6],
    6 => ['x' => 16, 'min' => 26, 'max' => 30, 'bet_amount_x' => 0.2],
    7 => ['x' => 27, 'min' => 31, 'max' => 35, 'bet_amount_x' => 0.2],
    8 => ['x' => 54, 'min' => 36, 'max' => 40, 'bet_amount_x' => 0.2],
    9 => ['x' => 128, 'min' => 41, 'max' => 49, 'bet_amount_x' => 0.2],
];

//@注意  修改参数的时候  同步修改 游戏逻辑服配置
const AB_OPTION_WIN_NUM = [
    1 => [1, 3, 5],
    2 => [2, 4],

    6 => [6, 8, 10],
    7 => [7, 9],

    11 => [11, 13, 15],
    12 => [12, 14],

    16 => [16, 18, 20, 22, 24],
    17 => [17, 19, 21, 23, 25],

    26 => [26, 28, 30],
    27 => [26, 29],

    31 => [31, 33, 35],
    32 => [32, 34],

    36 => [36, 38, 40],
    37 => [37, 39],

    41 => [41, 43, 45],
    42 => [42, 44, 46],
];
// ####################################################################################################
//排行榜下注时间权重以及下注金额倍数
const AB_BET_SECOND = 15;
const AB_BET_WEIGHT = [
    //1-5秒
    [
        'min_second' => 1,
        'max_second' => 5,
        'other_bet_rate' => 0.05,
        //0.035
        'rank_bet_rate' => 0.45,
        'bet_amount_region' => [
            ['min' => 0, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [50, 50, 40, 40, 25, 25, 20, 20, 15, 15, 15, 10, 10],
        'min_bet_amount_rate' => 0.3,
        'max_bet_amount_rate' => 0.9,
    ],
    //6-10秒
    [
        'min_second' => 6,
        'max_second' => 10,
        'other_bet_rate' => 0.03,
        'rank_bet_rate' => 0.3,
        'bet_amount_region' => [
            ['min' => 0, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.1],
            ['min' => 0.1, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
        ],
        'bet_amount_region_weight' => [50, 50, 40, 40, 30, 25, 25, 15, 10],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.5,
    ],
    //11-15秒
    [
        'min_second' => 11,
        'max_second' => 15,
        'other_bet_rate' => 0.015,
        'rank_bet_rate' => 0.3,
        'bet_amount_region' => [
            ['min' => 0, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.1],
            ['min' => 0.1, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
        ],
        'bet_amount_region_weight' => [60, 50, 40, 40, 35, 25],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.5,
    ],
];


//牌桌AI 本局初始化信息 ###################################################################################
//ai本局是否下注
const AB_BET_RATE = [
    'other' => 0.95,
    'rank' => 0.7
];

//ai本局下注总钱数概率
const AB_BET_AMOUNT_RATE = [
    'other' => [
        'min' => 0.35,
        'max' => 0.35,
    ],
    'rank' => [
        'min' => 0.1,
        'max' => 0.31
    ]
];

//互斥选项  每个列表保留1个空位置
const AB_MUTEX_LIST = [
    'other' => [
        [
            'option' => [0 => 410, 1 => 440],
            'select_option_cnt' => 1
        ],
        [
            'option' => [2 => 100, 3 => 90, 4 => 80, 5 => 90, 6 => 65, 7 => 65, 8 => 55, 9 => 55],
            'select_option_cnt' => 4
        ]
    ],
    'rank' => [
        [
            'option' => [0 => 470, 1 => 500],
            'select_option_cnt' => 1
        ],
        [
            'option' => [2 => 100, 3 => 90, 4 => 80, 5 => 90, 6 => 50, 7 => 50, 8 => 45, 9 => 45],
            'select_option_cnt' => 4
        ]
    ]
];


//AI 账户余额 重置参数配置################################################################################
//ai 携带金额权重区间配置 原始金额 在区间随机结束后再操作  保证区间出来的数在最终计算时有一位小数
const AB_BALANCE_AMOUNT = [
    'amount_region' => [
        1 => ['min' => 100, 'max' => 5000],
    ],
    'weight' => [1 => 1]
];


?>

