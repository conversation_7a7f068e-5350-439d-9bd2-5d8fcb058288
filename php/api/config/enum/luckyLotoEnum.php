<?php

//筹码面值
const LUCKY_LOTO_CHIP_FACE_VALUE_LIST = [10, 50, 200, 1000, 5000];

//other下注机器人个数
const LUCKY_LOTO_OTHER_RAISE_ROBOT_CNT = 100;


//桌子配置
const LUCKY_LOTO_TABLE = [
    'min_amount' => 100,
    //下注携带的最小金额
    'max_amount' => 20000, //单局下注的最大金额
];

//ab游戏 下注 区域和 x赢的倍数
//@注意  修改参数的时候  同步修改 游戏逻辑服配置
const LUCKY_LOTO_OPTION = [
    0 => ['x' => 0.2, 'group_name' => 'ZJ_GROUP_NAME_BAOZI', 'bet_amount_x' => 3],
    //奖池
    1 => ['x' => 10.6, 'group_name' => 'ZJ_GROUP_NAME_TONGHUA_SHUNZI', 'bet_amount_x' => 2],
    //同花顺
    2 => ['x' => 6.4, 'group_name' => 'ZJ_GROUP_NAME_SHUNZI', 'bet_amount_x' => 3],
    //顺子
    3 => ['x' => 5.3, 'group_name' => 'ZJ_GROUP_NAME_TONGHUA', 'bet_amount_x' => 3],
    //同花
    4 => ['x' => 4.2, 'group_name' => 'ZJ_GROUP_NAME_DUIZI', 'bet_amount_x' => 4],
    //对子
    5 => ['x' => 3.2, 'group_name' => 'ZJ_GROUP_NAME_SANPAI', 'bet_amount_x' => 4], //散牌
];

const LUCKY_LOTO_BET_SECOND = 30;

//排行榜下注时间权重以及下注金额倍数
const LUCKY_LOTO_BET_WEIGHT = [
    //1-5秒
    [
        'min_second' => 1,
        'max_second' => 5,
        'other_bet_rate' => 1,
        'rank_bet_rate' => 1,
        'bet_amount_region' => [
            ['min' => 0, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [250, 50, 40, 40, 25, 25, 0, 0, 0, 0, 0, 0, 0],
        'min_bet_amount_rate' => 0.3,
        'max_bet_amount_rate' => 0.9,
    ],
    //6-10秒
    [
        'min_second' => 6,
        'max_second' => 10,
        'other_bet_rate' => 1,
        'rank_bet_rate' => 1,
        'bet_amount_region' => [
            ['min' => 0, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.1],
            ['min' => 0.1, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
        ],
        'bet_amount_region_weight' => [150, 50, 40, 40, 30, 0, 0, 0, 0],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.5,
    ],
    //11-30秒
    [
        'min_second' => 11,
        'max_second' => 30,
        'other_bet_rate' => 1,
        'rank_bet_rate' => 1,
        'bet_amount_region' => [
            ['min' => 0, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.1],
            ['min' => 0.1, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
        ],
        'bet_amount_region_weight' => [160, 50, 40, 40, 0, 0],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.5,
    ],

];


//牌桌AI 本局初始化信息 ###################################################################################
//ai本局是否下注
const LUCKY_LOTO_BET_RATE = [
    'other' => 0.95,
    'rank' => 0.7
];


//ai本局下注总钱数概率
const LUCKY_LOTO_BET_AMOUNT_RATE = [
    'other' => [
        'min' => 0.35,
        'max' => 0.35,
    ],
    'rank' => [
        'min' => 0.1,
        'max' => 0.31
    ]
];
//互斥选项  根据配置获取下下注选项
const LUCKY_LOTO_MUTEX_LIST = [
    'other' => [
        [
            'option' => [0 => 100, 1 => 135, 2 => 166, 3 => 201, 4 => 233, 5 => 265],
            'select_option_cnt' => 3
        ],
    ],
    'rank' => [
        [
            'option' => [0 => 100, 1 => 135, 2 => 166, 3 => 201, 4 => 233, 5 => 265],
            'select_option_cnt' => 3
        ],
    ],
];


//开牌权重配置
const LUCKY_LOTO_WIN_OPTION_WEIGHT = [0 => 25, 1 => 1890, 2 => 3150, 3 => 3780, 4 => 4730, 5 => 6310];


//开牌权重动态权重系数
const LUCKY_LOTO_WIN_OPTION_WEIGHT_RATE = [0 => 1, 1 => 1, 2 => 1, 3 => 1, 4 => 1, 5 => 1];

//AI 账户余额 重置参数配置################################################################################
//ai 携带金额权重区间配置 原始金额 不*100 在区间随机结束后再*100操作  保证区间出来的数在最终计算时有一位小数
const LUCKY_LOTO_BALANCE_AMOUNT = [
    'amount_region' => [
        1 => ['min' => 100, 'max' => 5000],
    ],
    'weight' => [1 => 1]
];

?>

