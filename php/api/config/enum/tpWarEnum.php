<?php

//筹码面值
const TP_WAR_CHIP_FACE_VALUE_LIST = [10, 50, 200, 1000, 5000];

//other下注机器人个数
const TP_WAR_OTHER_RAISE_ROBOT_CNT = 100;


//最大牌 花色分值
const TP_WAR_MAX_CARD_COLOR_SCORE = [
    'ht' => 4,
    //黑桃
    'hht' => 3,
    //红桃
    'mh' => 2,
    //梅花
    'fk' => 1, //方块
];

//桌子配置
const TP_WAR_TABLE = [
    'min_amount' => 100,
    //下注携带的最小金额
    'max_amount' => 200000, //单局下注的最大金额
];

//ab游戏 下注 区域和 x赢的倍数
//@注意  修改参数的时候  同步修改 游戏逻辑服配置
const TP_WAR_OPTION = [
    0 => ['x' => 2.05, 'bet_amount_x' => 1.5],
    //queen 赢
    1 => ['x' => 2.05, 'bet_amount_x' => 1.5],
    //king 赢
    2 => ['x' => 10.5, 'group_name' => 'ZJ_GROUP_NAME_BAOZI', 'bet_amount_x' => 1.3],
    //豹子
    3 => ['x' => 6.2, 'group_name' => 'ZJ_GROUP_NAME_TONGHUA_SHUNZI', 'bet_amount_x' => 1.3],
    //同花顺
    4 => ['x' => 4.1, 'group_name' => 'ZJ_GROUP_NAME_SHUNZI', 'bet_amount_x' => 1.3],
    //顺子
    5 => ['x' => 3.1, 'group_name' => 'ZJ_GROUP_NAME_TONGHUA', 'bet_amount_x' => 1.3],
    //同花
    6 => ['x' => 2.05, 'group_name' => 'ZJ_GROUP_NAME_DUIZI', 'min_score' => 160002, 'bet_amount_x' => 1.3], //对子
];


//发牌权重
const TP_WAR_DEAL_GROUP_WEIGHT = [
    'BASE_ZJ_BAOZI' => 109,
    'BASE_ZJ_TONGHUA_SHUNZI' => 101,
    'BASE_ZJ_SHUNZI' => 1512,
    'BASE_ZJ_TONGHUA' => 2301,
    'BASE_ZJ_DUIZI' => 7862,
    'ZJ_GROUP_NAME_SCORE_REGION' => 16440,
];

//散牌区间
const TP_WAR_DEAL_GROUP_REGION = [
    'min' => 532,
    'max' => 3341
];


const TP_WAR_BET_SECOND = 15;


//排行榜下注时间权重以及下注金额倍数
const TP_WAR_BET_WEIGHT = [
    //1-5秒
    [
        'min_second' => 1,
        'max_second' => 5,
        'other_bet_rate' => 0.05,
        //0.035
        'rank_bet_rate' => 0.45,
        'bet_amount_region' => [
            ['min' => 0, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [50, 50, 40, 40, 25, 25, 20, 20, 15, 15, 15, 10, 10],
        'min_bet_amount_rate' => 0.3,
        'max_bet_amount_rate' => 0.9,
    ],
    //6-10秒
    [
        'min_second' => 6,
        'max_second' => 10,
        'other_bet_rate' => 0.03,
        'rank_bet_rate' => 0.3,
        'bet_amount_region' => [
            ['min' => 0, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.1],
            ['min' => 0.1, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
        ],
        'bet_amount_region_weight' => [50, 50, 40, 40, 30, 25, 25, 15, 10],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.5,
    ],
    //11-15秒
    [
        'min_second' => 11,
        'max_second' => 15,
        'other_bet_rate' => 0.015,
        'rank_bet_rate' => 0.3,
        'bet_amount_region' => [
            ['min' => 0, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.1],
            ['min' => 0.1, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
        ],
        'bet_amount_region_weight' => [60, 50, 40, 40, 35, 25],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.5,
    ],

];


//牌桌AI 本局初始化信息 ###################################################################################
//ai本局是否下注
const TP_WAR_BET_RATE = [
    'other' => 0.95,
    'rank' => 0.7
];


//ai本局下注总钱数概率
const TP_WAR_BET_AMOUNT_RATE = [
    'other' => [
        'min' => 0.35,
        'max' => 0.35,
    ],
    'rank' => [
        'min' => 0.1,
        'max' => 0.31
    ]
];

//互斥选项  根据配置获取下下注选项
const TP_WAR_MUTEX_LIST = [
    'other' => [
        [
            'option' => [0 => 140, 1 => 140],
            'select_option_cnt' => 1
        ],
        [
            'option' => [2 => 110],
            'select_option_cnt' => 1
        ]
    ],
    'rank' => [
        [
            'option' => [0 => 140, 1 => 140],
            'select_option_cnt' => 1
        ],
        [
            'option' => [2 => 110],
            'select_option_cnt' => 1
        ]
    ],
];

//AI 账户余额 重置参数配置################################################################################
//ai 携带金额权重区间配置 原始金额 不*100 在区间随机结束后再*100操作  保证区间出来的数在最终计算时有一位小数
const TP_WAR_BALANCE_AMOUNT = [
    'amount_region' => [
        1 => ['min' => 100, 'max' => 5000],
    ],
    'weight' => [1 => 1]
];

?>

