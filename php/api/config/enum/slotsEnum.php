<?php

const SLOTS_X = 5;
const SLOTS_Y = 3;
//获胜计算路线
const SLOTS_WIN_WAY = [
    //获胜路线图
    1 => [1, 1, 1, 1, 1],
    2 => [2, 2, 2, 2, 2],
    3 => [0, 0, 0, 0, 0],
    4 => [2, 1, 0, 1, 2],
    5 => [0, 1, 2, 1, 0],
    6 => [1, 2, 2, 2, 1],
    7 => [1, 0, 0, 0, 1],
    8 => [2, 2, 1, 0, 0],
    9 => [0, 0, 1, 2, 2],
    10 => [1, 0, 1, 2, 1],
    11 => [1, 2, 1, 0, 1],
    12 => [0, 1, 1, 1, 0],
    13 => [2, 1, 1, 1, 2],
    14 => [2, 1, 2, 1, 2],
    15 => [0, 1, 0, 1, 0],
    16 => [1, 1, 2, 1, 1],
    17 => [1, 1, 0, 1, 1],
    18 => [0, 0, 2, 0, 0],
    19 => [2, 2, 0, 2, 2],
    20 => [2, 0, 0, 0, 2],
    21 => [0, 2, 2, 2, 0],
    22 => [2, 0, 2, 0, 2],
    23 => [0, 2, 0, 2, 0],
    24 => [1, 2, 0, 2, 1],
    25 => [1, 0, 2, 0, 1],
];

//获胜路线连续个数 以及赔率
const SLOTS_X_OPTION = [
    1 => [3 => 5, 4 => 30, 5 => 80],
    2 => [3 => 5, 4 => 35, 5 => 85],
    3 => [3 => 5, 4 => 40, 5 => 95],
    4 => [3 => 5, 4 => 45, 5 => 105],
    5 => [3 => 10, 4 => 80, 5 => 155],
    6 => [3 => 10, 4 => 95, 5 => 180],
    7 => [3 => 15, 4 => 125, 5 => 210],
    8 => [3 => 20, 4 => 140, 5 => 270],
    9 => [3 => 50, 4 => 250, 5 => 420],
    10 => [3 => 14, 4 => 21, 5 => 28],
    11 => [1 => 1, 2 => 1, 3 => 1, 4 => 1, 5 => 1],
    12 => [1 => 2, 2 => 4, 3 => 8, 4 => 16, 5 => 32],
    //    13 => [1=>3,2=>9,3=>27,4=>81,5=>243],
];

//奖励次数
const SLOTS_FREE_SPIN = [
    3 => 10,
    4 => 15,
    5 => 20
];

//免费旋转元素
const SLOTS_SCATTR = 10;

//最高赔率的元素
const SLOTS_MAX_SCORE_ELEMENT = 9;
//百搭元素
const SLOTS_WILD_LIST = [
    11,
    12
];






//发牌权重
const SLOTS_DEAL_CARDS_WEIGHT = [
    1 => 126,
    2 => 125,
    3 => 124,
    4 => 123,
    5 => 120,
    6 => 88,
    7 => 83,
    8 => 83,
    9 => 81,
    10 => 48,
    11 => 38,
];

//发牌权重
const SOTS_FREE_DEAL_CARDS_WEIGHT = [
    1 => 130,
    2 => 130,
    3 => 130,
    4 => 130,
    5 => 105,
    6 => 105,
    7 => 91,
    8 => 80,
    9 => 80,
    11 => 33,
    12 => 25,
];

//复仇杀状态下发牌权重 
const SOTS_WIN_POINT_KILLER_DEAL_CARDS_WEIGHT = [
    1 => 126,
    2 => 125,
    3 => 124,
    4 => 123,
    5 => 120,
    6 => 88,
    7 => 83,
    8 => 83,
    9 => 81,
    10 => 48,
    11 => 5,
];





?>

