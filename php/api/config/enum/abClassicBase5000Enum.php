<?php
//ab游戏 下注 区域和 x赢的倍数

//筹码面值  以及出现的权重
const AB_CLASSIC_CHIP_FACE_VALUE_LIST_5000 = [
    5000 => [5, 10, 20, 40, 80, 160, 320, 640, 1280, 2560],
];

const AB_CLASSIC_TABLE_5000 = [
    'min_amount' => 100,
    //下注携带的最小金额
    'max_amount' => 20000, //单局下注的最大金额
];

//@注意  修改参数的时候  同步修改 游戏逻辑服配置
const AB_CLASSIC_OPTION_5000 = [
    //第一张是开牌结果时的倍数
    1 => [
        0 => ['x' => 1.27, 'bet_amount_x' => 1],
        //奇数
        1 => ['x' => 1.27, 'bet_amount_x' => 1],
        //偶数
    ],
    //一张以上的时候的开牌倍数
    2 => [
        0 => ['x' => 2.08, 'bet_amount_x' => 1],
        //奇数
        1 => ['x' => 2.08, 'bet_amount_x' => 1],
        //偶数
    ],

];

// ####################################################################################################
//排行榜下注时间权重以及下注金额倍数
const AB_CLASSIC_BET_SECOND_5000 = 10;
//排行榜下注时间权重以及下注金额倍数
const AB_CLASSIC_BET_WEIGHT_5000 = [
    //1秒
    [
        'min_second' => 1,
        'max_second' => 1,
        'other_bet_rate' => 0.05,
        'rank_bet_rate' => 0.1,
        'bet_amount_region' => [
            ['min' > 0.001, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [100, 200, 350, 350, 350, 300, 250, 30, 30, 30, 30, 30, 30],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.9,
    ],
    [
        'min_second' => 2,
        'max_second' => 2,
        'other_bet_rate' => 0.05,
        'rank_bet_rate' => 0.1111,
        'bet_amount_region' => [
            ['min' > 0.001, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [100, 200, 350, 350, 350, 300, 250, 30, 30, 30, 30, 30, 30],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.9,
    ],
    [
        'min_second' => 3,
        'max_second' => 3,
        'other_bet_rate' => 0.06,
        'rank_bet_rate' => 0.125,
        'bet_amount_region' => [
            ['min' > 0.001, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [100, 200, 350, 350, 350, 300, 250, 30, 30, 30, 30, 30, 30],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.9,
    ],
    [
        'min_second' => 4,
        'max_second' => 4,
        'other_bet_rate' => 0.07,
        'rank_bet_rate' => 0.1428,
        'bet_amount_region' => [
            ['min' > 0.001, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [100, 200, 350, 350, 350, 300, 250, 30, 30, 30, 30, 30, 30],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.9,
    ],
    [
        'min_second' => 5,
        'max_second' => 5,
        'other_bet_rate' => 0.08,
        'rank_bet_rate' => 0.1666,
        'bet_amount_region' => [
            ['min' > 0.001, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [100, 200, 350, 350, 350, 300, 250, 30, 30, 30, 30, 30, 30],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.9,
    ],
    [
        'min_second' => 6,
        'max_second' => 6,
        'other_bet_rate' => 0.1,
        'rank_bet_rate' => 0.2,
        'bet_amount_region' => [
            ['min' > 0.001, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [100, 200, 350, 350, 350, 300, 250, 30, 30, 30, 30, 30, 30],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.9
    ],
    [
        'min_second' => 7,
        'max_second' => 7,
        'other_bet_rate' => 0.12,
        'rank_bet_rate' => 0.25,
        'bet_amount_region' => [
            ['min' > 0.001, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [100, 200, 350, 350, 350, 300, 250, 30, 30, 30, 30, 30, 30],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.9,
    ],
    [
        'min_second' => 8,
        'max_second' => 8,
        'other_bet_rate' => 0.16,
        'rank_bet_rate' => 0.3333,
        'bet_amount_region' => [
            ['min' > 0.001, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [100, 200, 350, 350, 350, 300, 250, 30, 30, 30, 30, 30, 30],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.9,
    ],
    [
        'min_second' => 9,
        'max_second' => 9,
        'other_bet_rate' => 0.25,
        'rank_bet_rate' => 0.5,
        'bet_amount_region' => [
            ['min' > 0.001, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [100, 200, 350, 350, 350, 300, 250, 30, 30, 30, 30, 30, 30],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.9,
    ],
    [
        'min_second' => 10,
        'max_second' => 10,
        'other_bet_rate' => 0.5,
        'rank_bet_rate' => 1,
        'bet_amount_region' => [
            ['min' > 0.001, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [100, 200, 350, 350, 350, 300, 250, 30, 30, 30, 30, 30, 30],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.9,
    ],


    //1秒
    [
        'min_second' => 11,
        'max_second' => 11,
        'other_bet_rate' => 0.1,
        'rank_bet_rate' => 0.1,
        'bet_amount_region' => [
            ['min' > 0.001, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [100, 200, 350, 350, 350, 300, 250, 30, 30, 30, 30, 30, 30],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.9,
    ],
    [
        'min_second' => 12,
        'max_second' => 12,
        'other_bet_rate' => 0.1111,
        'rank_bet_rate' => 0.1111,
        'bet_amount_region' => [
            ['min' > 0.001, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [100, 200, 350, 350, 350, 300, 250, 30, 30, 30, 30, 30, 30],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.9,
    ],
    [
        'min_second' => 13,
        'max_second' => 13,
        'other_bet_rate' => 0.125,
        'rank_bet_rate' => 0.125,
        'bet_amount_region' => [
            ['min' > 0.001, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [100, 200, 350, 350, 350, 300, 250, 30, 30, 30, 30, 30, 30],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.9,
    ],
    [
        'min_second' => 14,
        'max_second' => 14,
        'other_bet_rate' => 0.1428,
        'rank_bet_rate' => 0.1428,
        'bet_amount_region' => [
            ['min' > 0.001, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [100, 200, 350, 350, 350, 300, 250, 30, 30, 30, 30, 30, 30],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.9,
    ],
    [
        'min_second' => 15,
        'max_second' => 15,
        'other_bet_rate' => 0.1666,
        'rank_bet_rate' => 0.1666,
        'bet_amount_region' => [
            ['min' > 0.001, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [100, 200, 350, 350, 350, 300, 250, 30, 30, 30, 30, 30, 30],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.9,
    ],
    [
        'min_second' => 16,
        'max_second' => 16,
        'other_bet_rate' => 0.2,
        'rank_bet_rate' => 0.2,
        'bet_amount_region' => [
            ['min' > 0.001, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [100, 200, 350, 350, 350, 300, 250, 30, 30, 30, 30, 30, 30],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.9
    ],
    [
        'min_second' => 17,
        'max_second' => 17,
        'other_bet_rate' => 0.25,
        'rank_bet_rate' => 0.25,
        'bet_amount_region' => [
            ['min' > 0.001, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [100, 200, 350, 350, 350, 300, 250, 30, 30, 30, 30, 30, 30],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.9,
    ],
    [
        'min_second' => 18,
        'max_second' => 18,
        'other_bet_rate' => 0.3333,
        'rank_bet_rate' => 0.3333,
        'bet_amount_region' => [
            ['min' > 0.001, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [100, 200, 350, 350, 350, 300, 250, 30, 30, 30, 30, 30, 30],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.9,
    ],
    [
        'min_second' => 19,
        'max_second' => 19,
        'other_bet_rate' => 0.5,
        'rank_bet_rate' => 0.5,
        'bet_amount_region' => [
            ['min' > 0.001, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [100, 200, 350, 350, 350, 300, 250, 30, 30, 30, 30, 30, 30],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.9,
    ],
    [
        'min_second' => 20,
        'max_second' => 20,
        'other_bet_rate' => 1,
        'rank_bet_rate' => 1,
        'bet_amount_region' => [
            ['min' > 0.001, 'max' => 0.002],
            ['min' => 0.002, 'max' => 0.005],
            ['min' => 0.005, 'max' => 0.02],
            ['min' => 0.02, 'max' => 0.05],
            ['min' => 0.05, 'max' => 0.15],
            ['min' => 0.15, 'max' => 0.2],
            ['min' => 0.2, 'max' => 0.3],
            ['min' => 0.3, 'max' => 0.4],
            ['min' => 0.4, 'max' => 0.5],
            ['min' => 0.5, 'max' => 0.6],
            ['min' => 0.6, 'max' => 0.7],
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [100, 200, 350, 350, 350, 300, 250, 30, 30, 30, 30, 30, 30],
        'min_bet_amount_rate' => 0.1,
        'max_bet_amount_rate' => 0.9,
    ],
];


//牌桌AI 本局初始化信息 ###################################################################################
//ai本局是否下注
const AB_CLASSIC_BET_RATE_5000 = [
    'other' => 0.15,
    'rank' => 0.85
];

//ai本局下注总钱数概率
const AB_CLASSIC_BET_AMOUNT_RATE_5000 = [
    'other' => [
        'min' => 0.35,
        'max' => 0.35,
    ],
    'rank' => [
        'min' => 0.35,
        'max' => 0.35
    ]
];

//互斥选项  每个列表保留1个空位置
const AB_CLASSIC_MUTEX_LIST_5000 = [
    'other' => [
        [
            'option' => [100 => 0],
            'select_option_cnt' => 1
        ],
        [
            'option' => [0 => 50, 1 => 50],
            'select_option_cnt' => 2
        ]
    ],
    'rank' => [
        [
            'option' => [100 => 0],
            'select_option_cnt' => 1
        ],
        [
            'option' => [0 => 50, 1 => 50],
            'select_option_cnt' => 4
        ]
    ]
];


//可变选项权重
const AB_CLASSIC_BET_OPTION_CHANGE_WEIGHT_5000 = [
    'other' => [
        //第一轮下注概率
        100 => [-1 => 0, 0 => 50, 1 => 50],
        //如果第一轮下了0选项  第二轮权重
        0 => [-1 => 500, 0 => 95, 1 => 5],
        //如果第一轮下了1选项  第二轮权重
        1 => [-1 => 500, 0 => 5, 1 => 95],
    ],
    'rank' => [
        //第一轮下注概率
        100 => [-1 => 0, 0 => 50, 1 => 50],
        //如果第一轮下了0选项  第二轮权重
        0 => [-1 => 44, 0 => 95, 1 => 5],
        //如果第一轮下了1选项  第二轮权重
        1 => [-1 => 44, 0 => 5, 1 => 95],
    ]
];


//AI 账户余额 重置参数配置################################################################################
//ai 携带金额权重区间配置 原始金额 在区间随机结束后再操作  保证区间出来的数在最终计算时有一位小数
const AB_CLASSIC_BALANCE_AMOUNT_5000 = [
    'amount_region' => [
        1 => ['min' => 500, 'max' => 10000],
    ],
    'weight' => [1 => 1]
];


?>

