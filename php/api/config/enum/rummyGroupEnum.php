<?php
//大王
const G_CARD = 0x42;
//小王
const G_SMALL_CARD = 0x41;
//鬼牌和癞子的统一标识
const GL_CARD = 0x51;

//定义基础牌值 方块
const FANG_KUAI_LIST = [0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D];

//定义基础牌值 梅花
const MEI_HUA_LIST = [0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D];

//定义基础牌值 红桃
const HONG_TAO_LIST = [0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C, 0x2D];

//定义基础牌值 黑桃
const HEI_TAO_LIST = [0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C, 0x3D];

//定义基础牌型数量
const BASE_GROUP_CARD_NUM = [
    'group_chun_shun_3_lian' => 3,
    'group_chun_3_tiao' => 3,
    'group_bian_jia' => 2,
    'group_2_lian' => 2,
    'group_dui' => 2,
    'group_san_pai' => 1,
    'group_hun' => 1,
];
//定义基础牌型数量
const BASE_GROUP_CARD_NAME = [
    'group_chun_shun_3_lian' => '纯顺',
    'group_chun_3_tiao' => '三条',
    'group_bian_jia' => '边夹',
    'group_2_lian' => '两连',
    'group_dui' => '对',
    'group_san_pai' => '单牌',
    'group_hun' => '混',
    'group_no_hun' => '没有混',
];

//基础纯顺3连牌型
const BASE_CHUN_SHUN_3_LIAN_LIST = [
    [0X0C, 0X0D, 0X01],
    [0X3C, 0X3D, 0X31],
    [0X2C, 0X2D, 0X21],
    [0X1C, 0X1D, 0X11],
    //Q K A
    [0X0B, 0X0C, 0X0D],
    [0X3B, 0X3C, 0X3D],
    [0X2B, 0X2C, 0X2D],
    [0X1B, 0X1C, 0X1D],
    //J Q K
    [0X1A, 0X1B, 0X1C],
    [0X2A, 0X2B, 0X2C],
    [0X0A, 0X0B, 0X0C],
    [0X3A, 0X3B, 0X3C],
    //10 J Q
    [0X09, 0X0A, 0X0B],
    [0X39, 0X3A, 0X3B],
    [0X29, 0X2A, 0X2B],
    [0X19, 0X1A, 0X1B],
    //9 10 J
    [0X08, 0X09, 0X0A],
    [0X38, 0X39, 0X3A],
    [0X28, 0X29, 0X2A],
    [0X18, 0X19, 0X1A],
    //8 9 10
    [0X07, 0X08, 0X09],
    [0X37, 0X38, 0X39],
    [0X27, 0X28, 0X29],
    [0X17, 0X18, 0X19],
    //7 8 9
    [0X06, 0X07, 0X08],
    [0X36, 0X37, 0X38],
    [0X26, 0X27, 0X28],
    [0X16, 0X17, 0X18],
    //6 7 8
    [0X05, 0X06, 0X07],
    [0X35, 0X36, 0X37],
    [0X25, 0X26, 0X27],
    [0X15, 0X16, 0X17],
    //5 6 7
    [0X04, 0X05, 0X06],
    [0X34, 0X35, 0X36],
    [0X24, 0X25, 0X26],
    [0X14, 0X15, 0X16],
    //4 5 6
    [0X03, 0X04, 0X05],
    [0X33, 0X34, 0X35],
    [0X23, 0X24, 0X25],
    [0X13, 0X14, 0X15],
    //3 4 5
    [0X02, 0X03, 0X04],
    [0X32, 0X33, 0X34],
    [0X22, 0X23, 0X24],
    [0X12, 0X13, 0X14],
    //2 3 4
    [0X01, 0X02, 0X03],
    [0X31, 0X32, 0X33],
    [0X21, 0X22, 0X23],
    [0X11, 0X12, 0X13], //A 2 3
];
//基础纯顺4连牌型
const BASE_CHUN_SHUN_4_LIAN_LIST = [
    [0X3B, 0X3C, 0X3D, 0X31],
    [0X2B, 0X2C, 0X2D, 0X21],
    [0X0B, 0X0C, 0X0D, 0X01],
    [0X1B, 0X1C, 0X1D, 0X11],
    //J Q K A
    [0X0A, 0X0B, 0X0C, 0X0D],
    [0X1A, 0X1B, 0X1C, 0X1D],
    [0X2A, 0X2B, 0X2C, 0X2D],
    [0X3A, 0X3B, 0X3C, 0X3D],
    //10 J Q K
    [0X09, 0X0A, 0X0B, 0X0C],
    [0X19, 0X1A, 0X1B, 0X1C],
    [0X29, 0X2A, 0X2B, 0X2C],
    [0X39, 0X3A, 0X3B, 0X3C],
    //9 10 J Q
    [0X08, 0X09, 0X0A, 0X0B],
    [0X18, 0X19, 0X1A, 0X1B],
    [0X28, 0X29, 0X2A, 0X2B],
    [0X38, 0X39, 0X3A, 0X3B],
    //8 9 10 J
    [0X07, 0X08, 0X09, 0X0A],
    [0X17, 0X18, 0X19, 0X1A],
    [0X27, 0X28, 0X29, 0X2A],
    [0X37, 0X38, 0X39, 0X3A],
    //7 8 9 10
    [0X06, 0X07, 0X08, 0X09],
    [0X16, 0X17, 0X18, 0X19],
    [0X26, 0X27, 0X28, 0X29],
    [0X36, 0X37, 0X38, 0X39],
    //6 7 8 9
    [0X05, 0X06, 0X07, 0X08],
    [0X15, 0X16, 0X17, 0X18],
    [0X25, 0X26, 0X27, 0X28],
    [0X35, 0X36, 0X37, 0X38],
    //5 6 7 8
    [0X04, 0X05, 0X06, 0X07],
    [0X14, 0X15, 0X16, 0X17],
    [0X24, 0X25, 0X26, 0X27],
    [0X34, 0X35, 0X36, 0X37],
    //4 5 6 7
    [0X03, 0X04, 0X05, 0X06],
    [0X13, 0X14, 0X15, 0X16],
    [0X23, 0X24, 0X25, 0X26],
    [0X33, 0X34, 0X35, 0X36],
    //3 4 5 6
    [0X02, 0X03, 0X04, 0X05],
    [0X12, 0X13, 0X14, 0X15],
    [0X22, 0X23, 0X24, 0X25],
    [0X32, 0X33, 0X34, 0X35],
    //2 3 4 5
    [0X01, 0X02, 0X03, 0X04],
    [0X11, 0X12, 0X13, 0X14],
    [0X21, 0X22, 0X23, 0X24],
    [0X31, 0X32, 0X33, 0X34], //A 2 3 4
];
//基础纯顺5连牌型
const BASE_CHUN_SHUN_5_LIAN_LIST = [
    [0X0A, 0X0B, 0X0C, 0X0D, 0X01],
    [0X1A, 0X1B, 0X1C, 0X1D, 0X11],
    [0X2A, 0X2B, 0X2C, 0X2D, 0X21],
    [0X3A, 0X3B, 0X3C, 0X3D, 0X31],
    //10 J Q K A
    [0X09, 0X0A, 0X0B, 0X0C, 0X0D],
    [0X19, 0X1A, 0X1B, 0X1C, 0X1D],
    [0X29, 0X2A, 0X2B, 0X2C, 0X2D],
    [0X39, 0X3A, 0X3B, 0X3C, 0X3D],
    //9 10 J Q K
    [0X08, 0X09, 0X0A, 0X0B, 0X0C],
    [0X18, 0X19, 0X1A, 0X1B, 0X1C],
    [0X28, 0X29, 0X2A, 0X2B, 0X2C],
    [0X38, 0X39, 0X3A, 0X3B, 0X3C],
    //8 9 10 J Q
    [0X07, 0X08, 0X09, 0X0A, 0X0B],
    [0X17, 0X18, 0X19, 0X1A, 0X1B],
    [0X27, 0X28, 0X29, 0X2A, 0X2B],
    [0X37, 0X38, 0X39, 0X3A, 0X3B],
    //7 8 9 10 J
    [0X06, 0X07, 0X08, 0X09, 0X0A],
    [0X16, 0X17, 0X18, 0X19, 0X1A],
    [0X26, 0X27, 0X28, 0X29, 0X2A],
    [0X36, 0X37, 0X38, 0X39, 0X3A],
    //6 7 8 9 10
    [0X05, 0X06, 0X07, 0X08, 0X09],
    [0X15, 0X16, 0X17, 0X18, 0X19],
    [0X25, 0X26, 0X27, 0X28, 0X29],
    [0X35, 0X36, 0X37, 0X38, 0X39],
    //5 6 7 8 9
    [0X04, 0X05, 0X06, 0X07, 0X08],
    [0X14, 0X15, 0X16, 0X17, 0X18],
    [0X24, 0X25, 0X26, 0X27, 0X28],
    [0X34, 0X35, 0X36, 0X37, 0X38],
    //4 5 6 7 8
    [0X03, 0X04, 0X05, 0X06, 0X07],
    [0X13, 0X14, 0X15, 0X16, 0X17],
    [0X23, 0X24, 0X25, 0X26, 0X27],
    [0X33, 0X34, 0X35, 0X36, 0X37],
    //3 4 5 6 7
    [0X02, 0X03, 0X04, 0X05, 0X06],
    [0X12, 0X13, 0X14, 0X15, 0X16],
    [0X22, 0X23, 0X24, 0X25, 0X26],
    [0X32, 0X33, 0X34, 0X35, 0X36],
    //2 3 4 5 6
    [0X01, 0X02, 0X03, 0X04, 0X05],
    [0X11, 0X12, 0X13, 0X14, 0X15],
    [0X21, 0X22, 0X23, 0X24, 0X25],
    [0X31, 0X32, 0X33, 0X34, 0X35], //A 2 3 4 5
];
//基础牌型混顺3连
const BASE_HUN_SHUN_3_LIAN_LIST = [
    //混中间张 混3连
    [0X0C, 0X51, 0X01],
    [0X1C, 0X51, 0X11],
    [0X2C, 0X51, 0X21],
    [0X3C, 0X51, 0X31],
    //Q 癞子 A
    [0X0B, 0X51, 0X0D],
    [0X1B, 0X51, 0X1D],
    [0X2B, 0X51, 0X2D],
    [0X3B, 0X51, 0X3D],
    //J 癞子 K
    [0X0A, 0X51, 0X0C],
    [0X1A, 0X51, 0X1C],
    [0X2A, 0X51, 0X2C],
    [0X3A, 0X51, 0X3C],
    //10 癞子 Q
    [0X09, 0X51, 0X0B],
    [0X19, 0X51, 0X1B],
    [0X29, 0X51, 0X2B],
    [0X39, 0X51, 0X3B],
    //9 癞子 J
    [0X08, 0X51, 0X0A],
    [0X18, 0X51, 0X1A],
    [0X28, 0X51, 0X2A],
    [0X38, 0X51, 0X3A],
    //8 癞子 10
    [0X07, 0X51, 0X09],
    [0X17, 0X51, 0X19],
    [0X27, 0X51, 0X29],
    [0X37, 0X51, 0X39],
    //7 癞子 9
    [0X06, 0X51, 0X08],
    [0X16, 0X51, 0X18],
    [0X26, 0X51, 0X28],
    [0X36, 0X51, 0X38],
    //6 癞子 8
    [0X05, 0X51, 0X07],
    [0X15, 0X51, 0X17],
    [0X25, 0X51, 0X27],
    [0X35, 0X51, 0X37],
    //5 癞子 7
    [0X04, 0X51, 0X06],
    [0X14, 0X51, 0X16],
    [0X24, 0X51, 0X26],
    [0X34, 0X51, 0X36],
    //4 癞子 6
    [0X03, 0X51, 0X05],
    [0X13, 0X51, 0X15],
    [0X23, 0X51, 0X25],
    [0X33, 0X51, 0X35],
    //3 癞子 5
    [0X02, 0X51, 0X04],
    [0X12, 0X51, 0X14],
    [0X22, 0X51, 0X24],
    [0X32, 0X51, 0X34],
    //2 癞子 4
    [0X01, 0X51, 0X03],
    [0X11, 0X51, 0X13],
    [0X21, 0X51, 0X23],
    [0X31, 0X51, 0X33],
    //A 癞子 3


    //混两边各一张 混3连
    [0X51, 0X0D, 0X01],
    [0X51, 0X1D, 0X11],
    [0X51, 0X2D, 0X21],
    [0X51, 0X3D, 0X31],
    //癞子 K A
    [0X51, 0X0C, 0X0D],
    [0X51, 0X1C, 0X1D],
    [0X51, 0X2C, 0X2D],
    [0X51, 0X3C, 0X3D],
    //癞子 Q K
    [0X51, 0X0B, 0X0C],
    [0X51, 0X1B, 0X1C],
    [0X51, 0X2B, 0X2C],
    [0X51, 0X3B, 0X3C],
    //癞子 J Q
    [0X51, 0X0A, 0X0B],
    [0X51, 0X1A, 0X1B],
    [0X51, 0X2A, 0X2B],
    [0X51, 0X3A, 0X3B],
    //癞子 10 J
    [0X51, 0X09, 0X0A],
    [0X51, 0X19, 0X1A],
    [0X51, 0X29, 0X2A],
    [0X51, 0X39, 0X3A],
    //癞子 9 10
    [0X51, 0X08, 0X09],
    [0X51, 0X18, 0X19],
    [0X51, 0X28, 0X29],
    [0X51, 0X38, 0X39],
    //癞子 8 9
    [0X51, 0X07, 0X08],
    [0X51, 0X17, 0X18],
    [0X51, 0X27, 0X28],
    [0X51, 0X37, 0X38],
    //癞子 7 8
    [0X51, 0X06, 0X07],
    [0X51, 0X16, 0X17],
    [0X51, 0X26, 0X27],
    [0X51, 0X36, 0X37],
    //癞子 6 7
    [0X51, 0X05, 0X06],
    [0X51, 0X15, 0X16],
    [0X51, 0X25, 0X26],
    [0X51, 0X35, 0X36],
    //癞子 5 6
    [0X51, 0X04, 0X05],
    [0X51, 0X14, 0X15],
    [0X51, 0X24, 0X25],
    [0X51, 0X34, 0X35],
    //癞子 4 5
    [0X51, 0X03, 0X04],
    [0X51, 0X13, 0X14],
    [0X51, 0X23, 0X24],
    [0X51, 0X33, 0X34],
    //癞子 3 4
    [0X51, 0X02, 0X03],
    [0X51, 0X12, 0X13],
    [0X51, 0X22, 0X23],
    [0X51, 0X32, 0X33],
    //癞子 2 3
    [0X51, 0X02, 0X01],
    [0X51, 0X12, 0X11],
    [0X51, 0X22, 0X21],
    [0X51, 0X32, 0X31], //癞子 2 A
];
//基础牌型混顺4连
const BASE_HUN_SHUN_4_LIAN_LIST = [
    [0X0B, 0X51, 0X0D, 0X01],
    [0X1B, 0X51, 0X1D, 0X11],
    [0X2B, 0X51, 0X2D, 0X21],
    [0X3B, 0X51, 0X3D, 0X31],
    //J 癞子 K A
    [0X0B, 0X0C, 0X51, 0X01],
    [0X1B, 0X1C, 0X51, 0X11],
    [0X2B, 0X2C, 0X51, 0X21],
    [0X3B, 0X3C, 0X51, 0X31],
    //J Q 癞子 A
    [0X0A, 0X51, 0X0C, 0X0D],
    [0X1A, 0X51, 0X1C, 0X1D],
    [0X2A, 0X51, 0X2C, 0X2D],
    [0X3A, 0X51, 0X3C, 0X3D],
    //10 癞子 Q K
    [0X0A, 0X0B, 0X51, 0X0D],
    [0X1A, 0X1B, 0X51, 0X1D],
    [0X2A, 0X2B, 0X51, 0X2D],
    [0X3A, 0X3B, 0X51, 0X3D],
    //10 J 癞子 K
    [0X09, 0X51, 0X0B, 0X0C],
    [0X19, 0X51, 0X1B, 0X1C],
    [0X29, 0X51, 0X2B, 0X2C],
    [0X39, 0X51, 0X3B, 0X3C],
    //9 癞子 J Q
    [0X09, 0X0A, 0X51, 0X0C],
    [0X19, 0X1A, 0X51, 0X1C],
    [0X29, 0X2A, 0X51, 0X2C],
    [0X39, 0X3A, 0X51, 0X3C],
    //9 10 癞子 Q
    [0X08, 0X51, 0X0A, 0X0B],
    [0X18, 0X51, 0X1A, 0X1B],
    [0X28, 0X51, 0X2A, 0X2B],
    [0X38, 0X51, 0X3A, 0X3B],
    //8 癞子 10 J
    [0X08, 0X09, 0X51, 0X0B],
    [0X18, 0X19, 0X51, 0X1B],
    [0X28, 0X29, 0X51, 0X2B],
    [0X38, 0X39, 0X51, 0X3B],
    //8 9 癞子 J
    [0X07, 0X51, 0X09, 0X0A],
    [0X17, 0X51, 0X19, 0X1A],
    [0X27, 0X51, 0X29, 0X2A],
    [0X37, 0X51, 0X39, 0X3A],
    //7 癞子 9 10
    [0X07, 0X08, 0X51, 0X0A],
    [0X17, 0X18, 0X51, 0X1A],
    [0X27, 0X28, 0X51, 0X2A],
    [0X37, 0X38, 0X51, 0X3A],
    //7 8 癞子 10
    [0X06, 0X51, 0X08, 0X09],
    [0X16, 0X51, 0X18, 0X19],
    [0X26, 0X51, 0X28, 0X29],
    [0X36, 0X51, 0X38, 0X39],
    //6 癞子 8 9
    [0X06, 0X07, 0X51, 0X09],
    [0X16, 0X17, 0X51, 0X19],
    [0X26, 0X27, 0X51, 0X29],
    [0X36, 0X37, 0X51, 0X39],
    //6 7 癞子 9
    [0X05, 0X51, 0X07, 0X08],
    [0X15, 0X51, 0X17, 0X18],
    [0X25, 0X51, 0X27, 0X28],
    [0X35, 0X51, 0X37, 0X38],
    //5 癞子 7 8
    [0X05, 0X06, 0X51, 0X08],
    [0X15, 0X16, 0X51, 0X18],
    [0X25, 0X26, 0X51, 0X28],
    [0X35, 0X36, 0X51, 0X38],
    //5 6 癞子 8
    [0X04, 0X51, 0X06, 0X07],
    [0X14, 0X51, 0X16, 0X17],
    [0X24, 0X51, 0X26, 0X27],
    [0X34, 0X51, 0X36, 0X37],
    //4 癞子 6 7
    [0X04, 0X05, 0X51, 0X07],
    [0X14, 0X15, 0X51, 0X17],
    [0X24, 0X25, 0X51, 0X27],
    [0X34, 0X35, 0X51, 0X37],
    //4 5 癞子 7
    [0X03, 0X51, 0X05, 0X06],
    [0X13, 0X51, 0X15, 0X16],
    [0X23, 0X51, 0X25, 0X26],
    [0X33, 0X51, 0X35, 0X36],
    //3 癞子 5 6
    [0X03, 0X04, 0X51, 0X06],
    [0X13, 0X14, 0X51, 0X16],
    [0X23, 0X24, 0X51, 0X26],
    [0X33, 0X34, 0X51, 0X36],
    //3 4 癞子 6
    [0X02, 0X51, 0X04, 0X05],
    [0X12, 0X51, 0X14, 0X15],
    [0X22, 0X51, 0X24, 0X25],
    [0X32, 0X51, 0X34, 0X35],
    //2 癞子 4 5
    [0X02, 0X03, 0X51, 0X05],
    [0X12, 0X13, 0X51, 0X15],
    [0X22, 0X23, 0X51, 0X25],
    [0X32, 0X33, 0X51, 0X35],
    //2 3 癞子 5
    [0X51, 0X03, 0X04, 0X01],
    [0X51, 0X13, 0X14, 0X11],
    [0X51, 0X23, 0X24, 0X21],
    [0X51, 0X33, 0X34, 0X31],
    //癞子 3 4 A
    [0X02, 0X51, 0X04, 0X01],
    [0X12, 0X51, 0X14, 0X11],
    [0X22, 0X51, 0X24, 0X21],
    [0X32, 0X51, 0X34, 0X31], //2 癞子 4 A
];
//基础牌型混顺5连
const BASE_HUN_SHUN_5_LIAN_LIST = [
    [0X01, 0X02, 0X51, 0X04, 0X05],
    [0X11, 0X12, 0X51, 0X14, 0X15],
    [0X21, 0X22, 0X51, 0X24, 0X25],
    [0X31, 0X32, 0X51, 0X34, 0X35],
    //A 2 4 5
    [0X0A, 0X0B, 0X51, 0X0D, 0X01],
    [0X1A, 0X1B, 0X51, 0X1D, 0X11],
    [0X2A, 0X2B, 0X51, 0X2D, 0X21],
    [0X3A, 0X3B, 0X51, 0X3D, 0X31],
    //10 J K A
    [0X09, 0X0A, 0X51, 0X0C, 0X0D],
    [0X19, 0X1A, 0X51, 0X1C, 0X1D],
    [0X29, 0X2A, 0X51, 0X2C, 0X2D],
    [0X39, 0X3A, 0X51, 0X3C, 0X3D],
    //9 10 Q K
    [0X08, 0X09, 0X51, 0X0B, 0X0C],
    [0X18, 0X19, 0X51, 0X1B, 0X1C],
    [0X28, 0X29, 0X51, 0X2B, 0X2C],
    [0X38, 0X39, 0X51, 0X3B, 0X3C],
    //8 9 J Q
    [0X07, 0X08, 0X51, 0X0A, 0X0B],
    [0X17, 0X18, 0X51, 0X1A, 0X1B],
    [0X27, 0X28, 0X51, 0X2A, 0X2B],
    [0X37, 0X38, 0X51, 0X3A, 0X3B],
    ///7 8 10 J
    [0X06, 0X07, 0X51, 0X09, 0X0A],
    [0X16, 0X17, 0X51, 0X19, 0X1A],
    [0X26, 0X27, 0X51, 0X29, 0X2A],
    [0X36, 0X37, 0X51, 0X39, 0X3A],
    //6 7 9 10
    [0X05, 0X06, 0X51, 0X08, 0X09],
    [0X15, 0X16, 0X51, 0X18, 0X19],
    [0X25, 0X26, 0X51, 0X28, 0X29],
    [0X35, 0X36, 0X51, 0X38, 0X39],
    //5 6 8 9
    [0X04, 0X05, 0X51, 0X07, 0X08],
    [0X14, 0X15, 0X51, 0X17, 0X18],
    [0X24, 0X25, 0X51, 0X27, 0X28],
    [0X34, 0X35, 0X51, 0X37, 0X38],
    //4 5 7 8
    [0X03, 0X04, 0X51, 0X06, 0X07],
    [0X13, 0X14, 0X51, 0X16, 0X17],
    [0X23, 0X24, 0X51, 0X26, 0X27],
    [0X33, 0X34, 0X51, 0X36, 0X37],
    //3 4 6 7
    [0X02, 0X03, 0X51, 0X05, 0X06],
    [0X12, 0X14, 0X51, 0X15, 0X16],
    [0X22, 0X23, 0X51, 0X25, 0X26],
    [0X32, 0X33, 0X51, 0X35, 0X36], //2 3 5 6
];
//基础纯三条牌型
const BASE_CHUN_3_TIAO_LIST = [
    [0X01, 0X11, 0X21],
    [0X01, 0X31, 0X21],
    [0X01, 0X31, 0X11],
    [0X11, 0X31, 0X21],
    //A A A
    [0X0D, 0X1D, 0X2D],
    [0X0D, 0X3D, 0X2D],
    [0X0D, 0X3D, 0X1D],
    [0X1D, 0X3D, 0X2D],
    //K K K
    [0X0C, 0X1C, 0X2C],
    [0X0C, 0X3C, 0X2C],
    [0X0C, 0X3C, 0X1C],
    [0X1C, 0X3C, 0X2C],
    //Q Q Q
    [0X0B, 0X1B, 0X2B],
    [0X0B, 0X3B, 0X2B],
    [0X0B, 0X3B, 0X1B],
    [0X1B, 0X3B, 0X2B],
    //J J J
    [0X0A, 0X1A, 0X2A],
    [0X0A, 0X3A, 0X2A],
    [0X0A, 0X3A, 0X1A],
    [0X1A, 0X3A, 0X2A],
    //10 10 10 10
    [0X09, 0X19, 0X29],
    [0X09, 0X39, 0X29],
    [0X09, 0X39, 0X19],
    [0X19, 0X39, 0X29],
    //9 9 9
    [0X08, 0X18, 0X28],
    [0X08, 0X38, 0X28],
    [0X08, 0X38, 0X18],
    [0X18, 0X38, 0X28],
    //8 8 8
    [0X07, 0X17, 0X27],
    [0X07, 0X37, 0X27],
    [0X07, 0X37, 0X17],
    [0X17, 0X37, 0X27],
    //7 7 7
    [0X06, 0X16, 0X26],
    [0X06, 0X36, 0X26],
    [0X06, 0X36, 0X16],
    [0X16, 0X36, 0X26],
    //6 6 6
    [0X05, 0X15, 0X25],
    [0X05, 0X35, 0X25],
    [0X05, 0X35, 0X15],
    [0X15, 0X35, 0X25],
    //5 5 5
    [0X04, 0X14, 0X24],
    [0X04, 0X34, 0X24],
    [0X04, 0X34, 0X14],
    [0X14, 0X34, 0X24],
    //4 4 4
    [0X03, 0X13, 0X23],
    [0X03, 0X33, 0X23],
    [0X03, 0X33, 0X13],
    [0X13, 0X33, 0X23],
    //3 3 3
    [0X02, 0X12, 0X22],
    [0X02, 0X32, 0X22],
    [0X02, 0X32, 0X12],
    [0X12, 0X32, 0X22], //2 2 2
];
//基础纯4条牌型
const BASE_CHUN_4_TIAO_LIST = [
    [0X01, 0X11, 0X21, 0X31],
    //A A A
    [0X0D, 0X1D, 0X2D, 0X3D],
    //K K K
    [0X0C, 0X1C, 0X2C, 0X3C],
    //Q Q Q
    [0X0B, 0X1B, 0X2B, 0X3B],
    //J J J
    [0X0A, 0X1A, 0X2A, 0X3A],
    //10 10 10
    [0X09, 0X19, 0X29, 0X39],
    //9 9 9
    [0X08, 0X18, 0X28, 0X38],
    //8 8 8
    [0X07, 0X17, 0X27, 0X37],
    //7 7 7
    [0X06, 0X16, 0X26, 0X36],
    //6 6 6
    [0X05, 0X15, 0X25, 0X35],
    //5 5 5
    [0X04, 0X14, 0X24, 0X34],
    //4 4 4
    [0X03, 0X13, 0X23, 0X33],
    //3 3 3
    [0X02, 0X12, 0X22, 0X32], //2 2 2
];
//基础混三条牌型
const BASE_HUN_3_TIAO_LIST = [
    [0X51, 0X01, 0X11],
    [0X51, 0X01, 0X21],
    [0X51, 0X01, 0X31],
    [0X51, 0X11, 0X21],
    [0X51, 0X11, 0X31],
    [0X51, 0X21, 0X31],
    // 癞子 A A
    [0X51, 0X0D, 0X1D],
    [0X51, 0X0D, 0X2D],
    [0X51, 0X0D, 0X3D],
    [0X51, 0X1D, 0X2D],
    [0X51, 0X1D, 0X3D],
    [0X51, 0X2D, 0X3D],
    // 癞子 K K
    [0X51, 0X0C, 0X1C],
    [0X51, 0X0C, 0X2C],
    [0X51, 0X0C, 0X3C],
    [0X51, 0X1C, 0X2C],
    [0X51, 0X1C, 0X3C],
    [0X51, 0X2C, 0X3C],
    // 癞子 Q Q
    [0X51, 0X0B, 0X1B],
    [0X51, 0X0B, 0X2B],
    [0X51, 0X0B, 0X3B],
    [0X51, 0X1B, 0X2B],
    [0X51, 0X1B, 0X3B],
    [0X51, 0X2B, 0X3B],
    // 癞子 J J
    [0X51, 0X0A, 0X1A],
    [0X51, 0X0A, 0X2A],
    [0X51, 0X0A, 0X3A],
    [0X51, 0X1A, 0X2A],
    [0X51, 0X1A, 0X3A],
    [0X51, 0X2A, 0X3A],
    // 癞子 10 10
    [0X51, 0X09, 0X19],
    [0X51, 0X09, 0X29],
    [0X51, 0X09, 0X39],
    [0X51, 0X19, 0X29],
    [0X51, 0X19, 0X39],
    [0X51, 0X29, 0X39],
    // 癞子 9 9
    [0X51, 0X08, 0X18],
    [0X51, 0X08, 0X28],
    [0X51, 0X08, 0X38],
    [0X51, 0X18, 0X28],
    [0X51, 0X18, 0X38],
    [0X51, 0X28, 0X38],
    // 癞子 8 8
    [0X51, 0X07, 0X17],
    [0X51, 0X07, 0X27],
    [0X51, 0X07, 0X37],
    [0X51, 0X17, 0X27],
    [0X51, 0X17, 0X37],
    [0X51, 0X27, 0X37],
    // 癞子 7 7
    [0X51, 0X06, 0X16],
    [0X51, 0X06, 0X26],
    [0X51, 0X06, 0X36],
    [0X51, 0X16, 0X26],
    [0X51, 0X16, 0X36],
    [0X51, 0X26, 0X36],
    // 癞子 6 6
    [0X51, 0X05, 0X15],
    [0X51, 0X05, 0X25],
    [0X51, 0X05, 0X35],
    [0X51, 0X15, 0X25],
    [0X51, 0X15, 0X35],
    [0X51, 0X25, 0X35],
    // 癞子 5 5
    [0X51, 0X04, 0X14],
    [0X51, 0X04, 0X24],
    [0X51, 0X04, 0X34],
    [0X51, 0X14, 0X24],
    [0X51, 0X14, 0X34],
    [0X51, 0X24, 0X34],
    // 癞子 4 4
    [0X51, 0X03, 0X13],
    [0X51, 0X03, 0X23],
    [0X51, 0X03, 0X33],
    [0X51, 0X13, 0X23],
    [0X51, 0X13, 0X33],
    [0X51, 0X23, 0X33],
    // 癞子 3 3
    [0X51, 0X02, 0X12],
    [0X51, 0X02, 0X22],
    [0X51, 0X02, 0X32],
    [0X51, 0X12, 0X22],
    [0X51, 0X12, 0X32],
    [0X51, 0X22, 0X32], // 癞子 2 2
];
//基础对牌型
const BASE_DUI_LIST = [
    [0X01, 0X11],
    [0X01, 0X21],
    [0X01, 0X31],
    [0X11, 0X21],
    [0X11, 0X31],
    [0X21, 0X31],
    //A A
    [0X0D, 0X1D],
    [0X0D, 0X2D],
    [0X0D, 0X3D],
    [0X1D, 0X2D],
    [0X1D, 0X3D],
    [0X2D, 0X3D],
    //K K
    [0X0C, 0X1C],
    [0X0C, 0X2C],
    [0X0C, 0X3C],
    [0X1C, 0X2C],
    [0X1C, 0X3C],
    [0X2C, 0X3C],
    //Q Q
    [0X0B, 0X1B],
    [0X0B, 0X2B],
    [0X0B, 0X3B],
    [0X1B, 0X2B],
    [0X1B, 0X3B],
    [0X2B, 0X3B],
    //J J
    [0X0A, 0X1A],
    [0X0A, 0X2A],
    [0X0A, 0X3A],
    [0X1A, 0X2A],
    [0X1A, 0X3A],
    [0X2A, 0X3A],
    //10 10
    [0X09, 0X19],
    [0X09, 0X29],
    [0X09, 0X39],
    [0X19, 0X29],
    [0X19, 0X39],
    [0X29, 0X39],
    //9 9
    [0X08, 0X18],
    [0X08, 0X28],
    [0X08, 0X38],
    [0X18, 0X28],
    [0X18, 0X38],
    [0X28, 0X38],
    //8 8
    [0X07, 0X17],
    [0X07, 0X27],
    [0X07, 0X37],
    [0X17, 0X27],
    [0X17, 0X37],
    [0X27, 0X37],
    //7 7
    [0X06, 0X16],
    [0X06, 0X26],
    [0X06, 0X36],
    [0X16, 0X26],
    [0X16, 0X36],
    [0X26, 0X36],
    //6 6
    [0X05, 0X15],
    [0X05, 0X25],
    [0X05, 0X35],
    [0X15, 0X25],
    [0X15, 0X35],
    [0X25, 0X35],
    //5 5
    [0X04, 0X14],
    [0X04, 0X24],
    [0X04, 0X34],
    [0X14, 0X24],
    [0X14, 0X34],
    [0X24, 0X34],
    //4 4
    [0X03, 0X13],
    [0X03, 0X23],
    [0X03, 0X33],
    [0X13, 0X23],
    [0X13, 0X33],
    [0X23, 0X33],
    //3 3
    [0X02, 0X12],
    [0X02, 0X22],
    [0X02, 0X32],
    [0X12, 0X22],
    [0X12, 0X32],
    [0X22, 0X32], //2 2
];
//基础2连牌型
const BASE_2_LIAN_LIST = [
    [0X0C, 0X0D],
    [0X1C, 0X1D],
    [0X2C, 0X2D],
    [0X3C, 0X3D],
    //Q K
    [0X0B, 0X0C],
    [0X1B, 0X1C],
    [0X2B, 0X2C],
    [0X3B, 0X3C],
    //J Q
    [0X0A, 0X0B],
    [0X1A, 0X1B],
    [0X2A, 0X2B],
    [0X3A, 0X3B],
    //10 J
    [0X09, 0X0A],
    [0X19, 0X1A],
    [0X29, 0X2A],
    [0X39, 0X3A],
    //9 10
    [0X08, 0X09],
    [0X18, 0X19],
    [0X28, 0X29],
    [0X38, 0X39],
    //8 9
    [0X07, 0X08],
    [0X17, 0X18],
    [0X27, 0X28],
    [0X37, 0X38],
    //7 8
    [0X06, 0X07],
    [0X16, 0X17],
    [0X26, 0X27],
    [0X36, 0X37],
    //6 7
    [0X05, 0X06],
    [0X15, 0X16],
    [0X25, 0X26],
    [0X35, 0X36],
    //5 6
    [0X04, 0X05],
    [0X14, 0X15],
    [0X24, 0X25],
    [0X34, 0X35],
    //4 5
    [0X03, 0X04],
    [0X13, 0X14],
    [0X23, 0X24],
    [0X33, 0X34],
    //3 4
    [0X02, 0X03],
    [0X12, 0X13],
    [0X22, 0X23],
    [0X32, 0X33], //2 3
];
//基础边夹牌型
const BASE_BIANJIA_LIST = [
    [0X0D, 0X01],
    [0X1D, 0X11],
    [0X2D, 0X21],
    [0X3D, 0X31],
    //K A
    [0X0C, 0X01],
    [0X1C, 0X11],
    [0X2C, 0X21],
    [0X3C, 0X31],
    //Q A
    [0X0B, 0X0D],
    [0X1B, 0X1D],
    [0X2B, 0X2D],
    [0X3B, 0X3D],
    //J K
    [0X0A, 0X0C],
    [0X1A, 0X1C],
    [0X2A, 0X2C],
    [0X3A, 0X3C],
    //10 Q
    [0X09, 0X0B],
    [0X19, 0X1B],
    [0X29, 0X2B],
    [0X39, 0X3B],
    //9 J
    [0X08, 0X0A],
    [0X18, 0X1A],
    [0X28, 0X2A],
    [0X38, 0X3A],
    //8 10
    [0X03, 0X01],
    [0X13, 0X11],
    [0X23, 0X21],
    [0X33, 0X31],
    //3 A
    [0X02, 0X01],
    [0X12, 0X11],
    [0X22, 0X21],
    [0X32, 0X31],
    //2 A
    [0X07, 0X09],
    [0X17, 0X19],
    [0X27, 0X29],
    [0X37, 0X39],
    //7 9
    [0X06, 0X08],
    [0X16, 0X18],
    [0X26, 0X28],
    [0X36, 0X38],
    //6 8
    [0X05, 0X07],
    [0X15, 0X17],
    [0X25, 0X27],
    [0X35, 0X37],
    //5 7
    [0X04, 0X06],
    [0X14, 0X16],
    [0X24, 0X26],
    [0X34, 0X36],
    //4 6
    [0X03, 0X05],
    [0X13, 0X15],
    [0X23, 0X25],
    [0X33, 0X35],
    //3 5
    [0X02, 0X04],
    [0X12, 0X14],
    [0X22, 0X24],
    [0X32, 0X34], //2 4
];
//基础两连对牌型
const BASE_2_LIAN_DUI_LIST = [
    [0X0C, 0X0D, 0X1D],
    [0X0C, 0X0D, 0X2D],
    [0X0C, 0X0D, 0X3D],
    [0X1C, 0X1D, 0X0D],
    [0X1C, 0X1D, 0X2D],
    [0X1C, 0X1D, 0X3D],
    [0X2C, 0X2D, 0X1D],
    [0X2C, 0X2D, 0X0D],
    [0X2C, 0X2D, 0X3D],
    [0X3C, 0X3D, 0X1D],
    [0X3C, 0X3D, 0X2D],
    [0X3C, 0X3D, 0X0D],
    //Q K K

    [0X0C, 0X1C, 0X1D],
    [0X0C, 0X3C, 0X3D],
    [0X0C, 0X2C, 0X2D],
    [0X1C, 0X0C, 0X0D],
    [0X1C, 0X2C, 0X2D],
    [0X1C, 0X3C, 0X3D],
    [0X2C, 0X1C, 0X1D],
    [0X2C, 0X0C, 0X0D],
    [0X2C, 0X3C, 0X3D],
    [0X3C, 0X1C, 0X1D],
    [0X3C, 0X0C, 0X0D],
    [0X3C, 0X2C, 0X2D],
    //Q Q K

    [0X0B, 0X0C, 0X1C],
    [0X0B, 0X0C, 0X2C],
    [0X0B, 0X0C, 0X3C],
    [0X1B, 0X1C, 0X0C],
    [0X1B, 0X1C, 0X2C],
    [0X1B, 0X1C, 0X3C],
    [0X2B, 0X2C, 0X1C],
    [0X2B, 0X2C, 0X0C],
    [0X2B, 0X2C, 0X3C],
    [0X3B, 0X3C, 0X1C],
    [0X3B, 0X3C, 0X2C],
    [0X3B, 0X3C, 0X0C],
    //J Q Q

    [0X0B, 0X1B, 0X1C],
    [0X0B, 0X3B, 0X3C],
    [0X0B, 0X2B, 0X2C],
    [0X1B, 0X0B, 0X0C],
    [0X1B, 0X2B, 0X2C],
    [0X1B, 0X3B, 0X3C],
    [0X2B, 0X1B, 0X1C],
    [0X2B, 0X0B, 0X0C],
    [0X2B, 0X3B, 0X3C],
    [0X3B, 0X1B, 0X1C],
    [0X3B, 0X2B, 0X2C],
    [0X3B, 0X0B, 0X0C],
    //J J Q

    [0X0A, 0X0B, 0X1B],
    [0X0A, 0X0B, 0X2B],
    [0X0A, 0X0B, 0X3B],
    [0X1A, 0X1B, 0X0B],
    [0X1A, 0X1B, 0X2B],
    [0X1A, 0X1B, 0X3B],
    [0X2A, 0X2B, 0X1B],
    [0X2A, 0X2B, 0X0B],
    [0X2A, 0X2B, 0X3B],
    [0X3A, 0X3B, 0X1B],
    [0X3A, 0X3B, 0X2B],
    [0X3A, 0X3B, 0X0B],
    //10 J J

    [0X0A, 0X2A, 0X2B],
    [0X0A, 0X1A, 0X1B],
    [0X0A, 0X3A, 0X3B],
    [0X1A, 0X0A, 0X0B],
    [0X1A, 0X2A, 0X2B],
    [0X1A, 0X3A, 0X3B],
    [0X2A, 0X0A, 0X0B],
    [0X2A, 0X1A, 0X1B],
    [0X2A, 0X3A, 0X3B],
    [0X3A, 0X0A, 0X0B],
    [0X3A, 0X1A, 0X1B],
    [0X3A, 0X2A, 0X2B],
    //10 10 J

    [0X09, 0X0A, 0X1A],
    [0X09, 0X0A, 0X2A],
    [0X09, 0X0A, 0X3A],
    [0X19, 0X1A, 0X0A],
    [0X19, 0X1A, 0X2A],
    [0X19, 0X1A, 0X3A],
    [0X29, 0X2A, 0X1A],
    [0X29, 0X2A, 0X0A],
    [0X29, 0X2A, 0X3A],
    [0X39, 0X3A, 0X1A],
    [0X39, 0X3A, 0X2A],
    [0X39, 0X3A, 0X0A],
    //9 10 10

    [0X09, 0X19, 0X1A],
    [0X09, 0X29, 0X2A],
    [0X09, 0X39, 0X3A],
    [0X19, 0X09, 0X0A],
    [0X19, 0X29, 0X2A],
    [0X19, 0X39, 0X3A],
    [0X29, 0X09, 0X0A],
    [0X29, 0X19, 0X1A],
    [0X29, 0X39, 0X3A],
    [0X39, 0X09, 0X0A],
    [0X39, 0X19, 0X1A],
    [0X39, 0X29, 0X2A],
    //9 9 10

    [0X08, 0X09, 0X19],
    [0X08, 0X09, 0X29],
    [0X08, 0X09, 0X39],
    [0X18, 0X19, 0X09],
    [0X18, 0X19, 0X29],
    [0X18, 0X19, 0X39],
    [0X28, 0X29, 0X19],
    [0X28, 0X29, 0X09],
    [0X28, 0X29, 0X39],
    [0X38, 0X39, 0X19],
    [0X38, 0X39, 0X29],
    [0X38, 0X39, 0X09],
    //8 9 9

    [0X08, 0X18, 0X19],
    [0X08, 0X28, 0X29],
    [0X08, 0X38, 0X39],
    [0X18, 0X08, 0X09],
    [0X18, 0X28, 0X29],
    [0X18, 0X38, 0X39],
    [0X28, 0X08, 0X09],
    [0X28, 0X18, 0X19],
    [0X28, 0X38, 0X39],
    [0X38, 0X08, 0X09],
    [0X38, 0X18, 0X19],
    [0X38, 0X28, 0X29],
    //8 8 9

    [0X07, 0X08, 0X18],
    [0X07, 0X08, 0X28],
    [0X07, 0X08, 0X38],
    [0X17, 0X18, 0X08],
    [0X17, 0X18, 0X28],
    [0X17, 0X18, 0X38],
    [0X27, 0X28, 0X18],
    [0X27, 0X28, 0X08],
    [0X27, 0X28, 0X38],
    [0X37, 0X38, 0X18],
    [0X37, 0X38, 0X28],
    [0X37, 0X38, 0X08],
    //7 8 8

    [0X07, 0X17, 0X18],
    [0X07, 0X27, 0X28],
    [0X07, 0X37, 0X38],
    [0X17, 0X37, 0X38],
    [0X17, 0X07, 0X08],
    [0X17, 0X27, 0X28],
    [0X27, 0X07, 0X08],
    [0X27, 0X17, 0X18],
    [0X27, 0X37, 0X38],
    [0X37, 0X07, 0X08],
    [0X37, 0X17, 0X18],
    [0X37, 0X27, 0X28],
    //7 7 8

    [0X06, 0X07, 0X17],
    [0X06, 0X07, 0X27],
    [0X06, 0X07, 0X37],
    [0X16, 0X17, 0X07],
    [0X16, 0X17, 0X27],
    [0X16, 0X17, 0X37],
    [0X26, 0X27, 0X17],
    [0X26, 0X27, 0X07],
    [0X26, 0X27, 0X37],
    [0X36, 0X37, 0X17],
    [0X36, 0X37, 0X27],
    [0X36, 0X37, 0X07],
    //6 7 7

    [0X06, 0X16, 0X17],
    [0X06, 0X26, 0X27],
    [0X06, 0X36, 0X37],
    [0X16, 0X36, 0X37],
    [0X16, 0X06, 0X07],
    [0X16, 0X26, 0X27],
    [0X26, 0X06, 0X07],
    [0X26, 0X16, 0X17],
    [0X26, 0X36, 0X37],
    [0X36, 0X06, 0X07],
    [0X36, 0X16, 0X17],
    [0X36, 0X26, 0X27],
    //6 6 7

    [0X05, 0X06, 0X16],
    [0X05, 0X06, 0X26],
    [0X05, 0X06, 0X36],
    [0X15, 0X16, 0X06],
    [0X15, 0X16, 0X26],
    [0X15, 0X16, 0X36],
    [0X25, 0X26, 0X16],
    [0X25, 0X26, 0X06],
    [0X25, 0X26, 0X36],
    [0X35, 0X36, 0X16],
    [0X35, 0X36, 0X26],
    [0X35, 0X36, 0X06],
    //5 6 6


    [0X05, 0X15, 0X16],
    [0X05, 0X25, 0X26],
    [0X05, 0X35, 0X36],
    [0X15, 0X35, 0X36],
    [0X15, 0X05, 0X06],
    [0X15, 0X25, 0X26],
    [0X25, 0X05, 0X06],
    [0X25, 0X15, 0X16],
    [0X25, 0X35, 0X36],
    [0X35, 0X05, 0X06],
    [0X35, 0X15, 0X16],
    [0X35, 0X25, 0X26],
    //5 5 6


    [0X04, 0X05, 0X15],
    [0X04, 0X05, 0X25],
    [0X04, 0X05, 0X35],
    [0X14, 0X15, 0X05],
    [0X14, 0X15, 0X25],
    [0X14, 0X15, 0X35],
    [0X24, 0X25, 0X15],
    [0X24, 0X25, 0X05],
    [0X24, 0X25, 0X35],
    [0X34, 0X35, 0X15],
    [0X34, 0X35, 0X25],
    [0X34, 0X35, 0X05],
    //4 5 5

    [0X04, 0X14, 0X15],
    [0X04, 0X24, 0X25],
    [0X04, 0X34, 0X35],
    [0X14, 0X34, 0X35],
    [0X14, 0X04, 0X05],
    [0X14, 0X24, 0X25],
    [0X24, 0X04, 0X05],
    [0X24, 0X14, 0X15],
    [0X24, 0X34, 0X35],
    [0X34, 0X04, 0X05],
    [0X34, 0X14, 0X15],
    [0X34, 0X24, 0X25],
    //4 4 5

    [0X03, 0X04, 0X14],
    [0X03, 0X04, 0X24],
    [0X03, 0X04, 0X34],
    [0X13, 0X14, 0X04],
    [0X13, 0X14, 0X24],
    [0X13, 0X14, 0X34],
    [0X23, 0X24, 0X14],
    [0X23, 0X24, 0X04],
    [0X23, 0X24, 0X34],
    [0X33, 0X34, 0X14],
    [0X33, 0X34, 0X24],
    [0X33, 0X34, 0X04],
    //3 4 4

    [0X03, 0X13, 0X14],
    [0X03, 0X23, 0X24],
    [0X03, 0X33, 0X34],
    [0X13, 0X33, 0X34],
    [0X13, 0X03, 0X04],
    [0X13, 0X23, 0X24],
    [0X23, 0X03, 0X04],
    [0X23, 0X13, 0X14],
    [0X23, 0X33, 0X34],
    [0X33, 0X03, 0X04],
    [0X33, 0X13, 0X14],
    [0X33, 0X23, 0X24],
    //3 3 4

    [0X02, 0X03, 0X13],
    [0X02, 0X03, 0X23],
    [0X02, 0X03, 0X33],
    [0X12, 0X13, 0X03],
    [0X12, 0X13, 0X23],
    [0X12, 0X13, 0X33],
    [0X22, 0X23, 0X13],
    [0X22, 0X23, 0X03],
    [0X22, 0X23, 0X33],
    [0X32, 0X33, 0X13],
    [0X32, 0X33, 0X23],
    [0X32, 0X33, 0X03],
    //2 3 3

    [0X02, 0X12, 0X13],
    [0X02, 0X22, 0X23],
    [0X02, 0X32, 0X33],
    [0X12, 0X32, 0X33],
    [0X12, 0X02, 0X03],
    [0X12, 0X22, 0X23],
    [0X22, 0X02, 0X03],
    [0X22, 0X12, 0X13],
    [0X22, 0X32, 0X33],
    [0X32, 0X02, 0X03],
    [0X32, 0X12, 0X13],
    [0X32, 0X22, 0X23], //2 2 3
];
//基础夹边对牌型
const BASE_BIAN_JIA_DUI_LIST = [
    [0X0D, 0X01, 0X11],
    [0X0D, 0X01, 0X21],
    [0X0D, 0X01, 0X31],
    [0X1D, 0X11, 0X01],
    [0X1D, 0X11, 0X21],
    [0X1D, 0X11, 0X31],
    [0X2D, 0X21, 0X11],
    [0X2D, 0X21, 0X01],
    [0X2D, 0X21, 0X31],
    [0X3D, 0X31, 0X11],
    [0X3D, 0X31, 0X21],
    [0X3D, 0X31, 0X01],
    //K A A

    [0X0D, 0X1D, 0X11],
    [0X0D, 0X2D, 0X21],
    [0X0D, 0X3D, 0X31],
    [0X1D, 0X0D, 0X01],
    [0X1D, 0X2D, 0X21],
    [0X1D, 0X3D, 0X31],
    [0X2D, 0X0D, 0X01],
    [0X2D, 0X1D, 0X11],
    [0X2D, 0X3D, 0X31],
    [0X3D, 0X0D, 0X01],
    [0X3D, 0X1D, 0X11],
    [0X3D, 0X2D, 0X21],
    //K K A

    [0X0C, 0X01, 0X11],
    [0X0C, 0X01, 0X21],
    [0X0C, 0X01, 0X31],
    [0X1C, 0X11, 0X01],
    [0X1C, 0X11, 0X21],
    [0X1C, 0X11, 0X31],
    [0X2C, 0X21, 0X01],
    [0X2C, 0X21, 0X11],
    [0X2C, 0X21, 0X31],
    [0X3C, 0X31, 0X01],
    [0X3C, 0X11, 0X11],
    [0X3C, 0X31, 0X21],
    //Q A A

    [0X0C, 0X1C, 0X11],
    [0X0C, 0X2C, 0X21],
    [0X0C, 0X3C, 0X31],
    [0X1C, 0X0C, 0X01],
    [0X1C, 0X2C, 0X21],
    [0X1C, 0X3C, 0X31],
    [0X2C, 0X0C, 0X01],
    [0X2C, 0X1C, 0X11],
    [0X2C, 0X3C, 0X31],
    [0X3C, 0X0C, 0X01],
    [0X3C, 0X1C, 0X11],
    [0X3C, 0X2C, 0X21],
    //Q Q A

    [0X0B, 0X0D, 0X1D],
    [0X0B, 0X0D, 0X2D],
    [0X0B, 0X0D, 0X3D],
    [0X1B, 0X1D, 0X0D],
    [0X1B, 0X1D, 0X2D],
    [0X1B, 0X1D, 0X3D],
    [0X2B, 0X2D, 0X0D],
    [0X2B, 0X2D, 0X1D],
    [0X2B, 0X2D, 0X3D],
    [0X3B, 0X3D, 0X0D],
    [0X3B, 0X3D, 0X1D],
    [0X3B, 0X3D, 0X2D],
    //J K K

    [0X0B, 0X1B, 0X1D],
    [0X0B, 0X2B, 0X2D],
    [0X0B, 0X3B, 0X3D],
    [0X1B, 0X0B, 0X0D],
    [0X1B, 0X2B, 0X2D],
    [0X1B, 0X3B, 0X3D],
    [0X2B, 0X0B, 0X0D],
    [0X2B, 0X1B, 0X1D],
    [0X2B, 0X3B, 0X3D],
    [0X3B, 0X0B, 0X0D],
    [0X3B, 0X1B, 0X1D],
    [0X3B, 0X2B, 0X2D],
    //J J K

    [0X0A, 0X0C, 0X1C],
    [0X0A, 0X0C, 0X2C],
    [0X0A, 0X0C, 0X3C],
    [0X1A, 0X1C, 0X0C],
    [0X1A, 0X1C, 0X2C],
    [0X1A, 0X1C, 0X3C],
    [0X2A, 0X2C, 0X0C],
    [0X2A, 0X2C, 0X1C],
    [0X2A, 0X2C, 0X3C],
    [0X3A, 0X3C, 0X0C],
    [0X3A, 0X3C, 0X1C],
    [0X3A, 0X3C, 0X2C],
    //10 Q Q

    [0X0A, 0X1A, 0X1C],
    [0X0A, 0X2A, 0X2C],
    [0X0A, 0X3A, 0X3C],
    [0X1A, 0X0A, 0X0C],
    [0X1A, 0X2A, 0X2C],
    [0X1A, 0X3A, 0X3C],
    [0X2A, 0X0A, 0X0C],
    [0X2A, 0X1A, 0X1C],
    [0X2A, 0X3A, 0X3C],
    [0X3A, 0X0A, 0X0C],
    [0X3A, 0X1A, 0X1C],
    [0X3A, 0X2A, 0X2C],
    //10 10 Q

    [0X09, 0X0B, 0X1B],
    [0X09, 0X0B, 0X2B],
    [0X09, 0X0B, 0X3B],
    [0X19, 0X1B, 0X0B],
    [0X19, 0X1B, 0X2B],
    [0X19, 0X1B, 0X3B],
    [0X29, 0X2B, 0X0B],
    [0X29, 0X2B, 0X1B],
    [0X29, 0X2B, 0X3B],
    [0X39, 0X3B, 0X0B],
    [0X39, 0X3B, 0X1B],
    [0X39, 0X3B, 0X2B],
    //9 J J

    [0X09, 0X19, 0X1B],
    [0X09, 0X29, 0X2B],
    [0X09, 0X39, 0X3B],
    [0X19, 0X09, 0X0B],
    [0X19, 0X29, 0X2B],
    [0X19, 0X39, 0X3B],
    [0X29, 0X09, 0X0B],
    [0X29, 0X19, 0X1B],
    [0X29, 0X39, 0X3B],
    [0X39, 0X09, 0X0B],
    [0X39, 0X19, 0X1B],
    [0X39, 0X29, 0X2B],
    //9 9 J

    [0X08, 0X0A, 0X1A],
    [0X08, 0X0A, 0X2A],
    [0X08, 0X0A, 0X3A],
    [0X18, 0X1A, 0X0A],
    [0X18, 0X1A, 0X2A],
    [0X18, 0X1A, 0X3A],
    [0X28, 0X2A, 0X0A],
    [0X28, 0X2A, 0X1A],
    [0X28, 0X2A, 0X3A],
    [0X38, 0X3A, 0X0A],
    [0X38, 0X3A, 0X1A],
    [0X38, 0X3A, 0X2A],
    //8 10 10

    [0X08, 0X18, 0X1A],
    [0X08, 0X28, 0X2A],
    [0X08, 0X38, 0X3A],
    [0X18, 0X08, 0X0A],
    [0X18, 0X28, 0X2A],
    [0X18, 0X38, 0X3A],
    [0X28, 0X08, 0X0A],
    [0X28, 0X18, 0X1A],
    [0X28, 0X38, 0X3A],
    [0X38, 0X08, 0X0A],
    [0X38, 0X18, 0X1A],
    [0X38, 0X28, 0X2A],
    //8 8 10

    [0X07, 0X09, 0X19],
    [0X07, 0X09, 0X29],
    [0X07, 0X09, 0X39],
    [0X17, 0X19, 0X09],
    [0X17, 0X19, 0X29],
    [0X17, 0X19, 0X39],
    [0X27, 0X29, 0X09],
    [0X27, 0X29, 0X19],
    [0X27, 0X29, 0X39],
    [0X37, 0X39, 0X09],
    [0X37, 0X39, 0X19],
    [0X37, 0X39, 0X29],
    //7 9 9

    [0X07, 0X17, 0X19],
    [0X07, 0X27, 0X29],
    [0X07, 0X37, 0X39],
    [0X17, 0X07, 0X09],
    [0X17, 0X27, 0X29],
    [0X17, 0X37, 0X39],
    [0X27, 0X07, 0X09],
    [0X27, 0X17, 0X19],
    [0X27, 0X37, 0X39],
    [0X37, 0X07, 0X09],
    [0X37, 0X17, 0X19],
    [0X37, 0X27, 0X29],
    //7 7 9

    [0X06, 0X08, 0X18],
    [0X06, 0X08, 0X28],
    [0X06, 0X08, 0X38],
    [0X16, 0X18, 0X08],
    [0X16, 0X18, 0X28],
    [0X16, 0X18, 0X38],
    [0X26, 0X28, 0X08],
    [0X26, 0X28, 0X18],
    [0X26, 0X28, 0X38],
    [0X36, 0X38, 0X08],
    [0X36, 0X38, 0X18],
    [0X36, 0X38, 0X28],
    //6 8 8

    [0X06, 0X16, 0X18],
    [0X06, 0X26, 0X28],
    [0X06, 0X36, 0X38],
    [0X16, 0X06, 0X08],
    [0X16, 0X26, 0X28],
    [0X16, 0X36, 0X38],
    [0X26, 0X06, 0X08],
    [0X26, 0X16, 0X18],
    [0X26, 0X36, 0X38],
    [0X36, 0X06, 0X08],
    [0X36, 0X16, 0X18],
    [0X36, 0X26, 0X28],
    //6 6 8

    [0X05, 0X07, 0X17],
    [0X05, 0X07, 0X27],
    [0X05, 0X07, 0X37],
    [0X15, 0X17, 0X07],
    [0X15, 0X17, 0X27],
    [0X15, 0X17, 0X37],
    [0X25, 0X27, 0X07],
    [0X25, 0X27, 0X17],
    [0X25, 0X27, 0X37],
    [0X35, 0X37, 0X07],
    [0X35, 0X37, 0X17],
    [0X35, 0X37, 0X27],
    //5 7 7

    [0X05, 0X15, 0X17],
    [0X05, 0X25, 0X27],
    [0X05, 0X35, 0X37],
    [0X15, 0X05, 0X07],
    [0X15, 0X25, 0X27],
    [0X15, 0X35, 0X37],
    [0X25, 0X05, 0X07],
    [0X25, 0X15, 0X17],
    [0X25, 0X35, 0X37],
    [0X35, 0X05, 0X07],
    [0X35, 0X15, 0X17],
    [0X35, 0X25, 0X27],
    //5 5 7

    [0X04, 0X06, 0X16],
    [0X04, 0X06, 0X26],
    [0X04, 0X06, 0X36],
    [0X14, 0X16, 0X06],
    [0X14, 0X16, 0X26],
    [0X14, 0X16, 0X36],
    [0X24, 0X26, 0X06],
    [0X24, 0X26, 0X16],
    [0X24, 0X26, 0X36],
    [0X34, 0X36, 0X06],
    [0X34, 0X36, 0X16],
    [0X34, 0X36, 0X26],
    //4 6 6

    [0X04, 0X14, 0X16],
    [0X04, 0X24, 0X26],
    [0X04, 0X34, 0X36],
    [0X14, 0X04, 0X06],
    [0X14, 0X24, 0X26],
    [0X14, 0X34, 0X36],
    [0X24, 0X04, 0X06],
    [0X24, 0X14, 0X16],
    [0X24, 0X34, 0X36],
    [0X34, 0X04, 0X06],
    [0X34, 0X14, 0X16],
    [0X34, 0X24, 0X26],
    //4 4 6

    [0X03, 0X05, 0X15],
    [0X03, 0X05, 0X25],
    [0X03, 0X05, 0X35],
    [0X13, 0X15, 0X05],
    [0X13, 0X15, 0X25],
    [0X13, 0X15, 0X35],
    [0X23, 0X25, 0X05],
    [0X23, 0X25, 0X15],
    [0X23, 0X25, 0X35],
    [0X33, 0X35, 0X05],
    [0X33, 0X35, 0X15],
    [0X33, 0X35, 0X25],
    //3 5 5

    [0X03, 0X13, 0X15],
    [0X03, 0X23, 0X25],
    [0X03, 0X33, 0X35],
    [0X13, 0X03, 0X05],
    [0X13, 0X23, 0X25],
    [0X13, 0X33, 0X35],
    [0X23, 0X03, 0X05],
    [0X23, 0X13, 0X15],
    [0X23, 0X33, 0X35],
    [0X33, 0X03, 0X05],
    [0X33, 0X13, 0X15],
    [0X33, 0X23, 0X25],
    //3 3 5

    [0X02, 0X12, 0X14],
    [0X02, 0X22, 0X24],
    [0X02, 0X32, 0X34],
    [0X12, 0X02, 0X04],
    [0X12, 0X22, 0X24],
    [0X12, 0X32, 0X34],
    [0X22, 0X02, 0X04],
    [0X22, 0X12, 0X14],
    [0X22, 0X32, 0X34],
    [0X32, 0X02, 0X04],
    [0X32, 0X12, 0X14],
    [0X32, 0X22, 0X24],
    //2 2 4

    [0X02, 0X04, 0X14],
    [0X02, 0X04, 0X24],
    [0X02, 0X04, 0X34],
    [0X12, 0X14, 0X04],
    [0X12, 0X14, 0X24],
    [0X12, 0X14, 0X34],
    [0X22, 0X24, 0X04],
    [0X22, 0X24, 0X14],
    [0X22, 0X24, 0X34],
    [0X32, 0X34, 0X04],
    [0X32, 0X34, 0X14],
    [0X32, 0X34, 0X24],
    //2 4 4


    [0X13, 0X01, 0X11],
    [0X23, 0X01, 0X21],
    [0X33, 0X01, 0X31],
    [0X03, 0X11, 0X01],
    [0X23, 0X11, 0X21],
    [0X33, 0X11, 0X31],
    [0X03, 0X21, 0X01],
    [0X13, 0X21, 0X11],
    [0X33, 0X21, 0X31],
    [0X03, 0X31, 0X01],
    [0X13, 0X31, 0X11],
    [0X23, 0X31, 0X21],
    //3 A A

    [0X03, 0X13, 0X11],
    [0X03, 0X23, 0X21],
    [0X03, 0X33, 0X31],
    [0X13, 0X03, 0X01],
    [0X13, 0X23, 0X21],
    [0X13, 0X33, 0X31],
    [0X23, 0X03, 0X01],
    [0X23, 0X13, 0X11],
    [0X23, 0X33, 0X31],
    [0X33, 0X03, 0X01],
    [0X33, 0X13, 0X11],
    [0X33, 0X23, 0X21],
    //3 3 A

    [0X12, 0X01, 0X11],
    [0X22, 0X01, 0X21],
    [0X32, 0X01, 0X31],
    [0X02, 0X11, 0X01],
    [0X22, 0X11, 0X21],
    [0X32, 0X11, 0X31],
    [0X02, 0X21, 0X01],
    [0X12, 0X21, 0X11],
    [0X32, 0X21, 0X31],
    [0X02, 0X31, 0X01],
    [0X12, 0X31, 0X11],
    [0X22, 0X31, 0X21],
    //A A 2

    [0X02, 0X12, 0X01],
    [0X02, 0X22, 0X01],
    [0X02, 0X32, 0X01],
    [0X12, 0X02, 0X11],
    [0X12, 0X22, 0X11],
    [0X12, 0X32, 0X11],
    [0X22, 0X12, 0X21],
    [0X22, 0X02, 0X21],
    [0X22, 0X32, 0X21],
    [0X32, 0X12, 0X31],
    [0X32, 0X22, 0X31],
    [0X32, 0X02, 0X31], //2 2 A

];
//基础两连两对牌型
const BASE_2_LIAN_2_DUI_LIST = [
    [0x0D, 0x0C, 0x1D, 0x1C],
    [0x0D, 0x0C, 0x2D, 0x2C],
    [0x0D, 0x0C, 0x3D, 0x3C],
    [0x1D, 0x1C, 0x2D, 0x2C],
    [0x1D, 0x1C, 0x3D, 0x3C],
    [0x2D, 0x2C, 0x3D, 0x3C],
    //Q Q K K
    [0x0B, 0x0C, 0x1B, 0x1C],
    [0x0B, 0x0C, 0x2B, 0x2C],
    [0x0B, 0x0C, 0x3B, 0x3C],
    [0x1B, 0x1C, 0x2B, 0x2C],
    [0x1B, 0x1C, 0x3B, 0x3C],
    [0x2B, 0x2C, 0x3B, 0x3C],
    //J J Q Q
    [0x0B, 0x0A, 0x1B, 0x1A],
    [0x0B, 0x0A, 0x2B, 0x2A],
    [0x0B, 0x0A, 0x3B, 0x3A],
    [0x1B, 0x1A, 0x2B, 0x2A],
    [0x1B, 0x1A, 0x3B, 0x3A],
    [0x2B, 0x2A, 0x3B, 0x3A],
    //10 10 J J
    [0x09, 0x0A, 0x19, 0x1A],
    [0x09, 0x0A, 0x29, 0x2A],
    [0x09, 0x0A, 0x39, 0x3A],
    [0x19, 0x1A, 0x29, 0x2A],
    [0x19, 0x1A, 0x39, 0x3A],
    [0x29, 0x2A, 0x39, 0x3A],
    //9 9 10 10
    [0x09, 0x08, 0x19, 0x18],
    [0x09, 0x08, 0x29, 0x28],
    [0x09, 0x08, 0x39, 0x38],
    [0x19, 0x18, 0x29, 0x28],
    [0x19, 0x18, 0x39, 0x38],
    [0x29, 0x28, 0x39, 0x38],
    //8 8 9 9
    [0x07, 0x08, 0x17, 0x18],
    [0x07, 0x08, 0x27, 0x28],
    [0x07, 0x08, 0x37, 0x38],
    [0x17, 0x18, 0x27, 0x28],
    [0x17, 0x18, 0x37, 0x38],
    [0x27, 0x28, 0x37, 0x38],
    //7 7 8 8
    [0x06, 0x07, 0x16, 0x17],
    [0x06, 0x07, 0x26, 0x27],
    [0x06, 0x07, 0x36, 0x37],
    [0x16, 0x17, 0x26, 0x27],
    [0x16, 0x17, 0x36, 0x37],
    [0x26, 0x27, 0x36, 0x37],
    //6 6 7 7
    [0x05, 0x06, 0x15, 0x16],
    [0x05, 0x06, 0x25, 0x26],
    [0x05, 0x06, 0x35, 0x36],
    [0x15, 0x16, 0x25, 0x26],
    [0x15, 0x16, 0x35, 0x36],
    [0x25, 0x26, 0x35, 0x36],
    //5 5 6 6
    [0x04, 0x05, 0x14, 0x15],
    [0x04, 0x05, 0x24, 0x25],
    [0x04, 0x05, 0x34, 0x35],
    [0x14, 0x15, 0x24, 0x25],
    [0x14, 0x15, 0x34, 0x35],
    [0x24, 0x25, 0x34, 0x35],
    //4 4 5 5
    [0x03, 0x04, 0x13, 0x14],
    [0x03, 0x04, 0x23, 0x24],
    [0x03, 0x04, 0x33, 0x34],
    [0x13, 0x14, 0x23, 0x24],
    [0x13, 0x14, 0x33, 0x34],
    [0x23, 0x24, 0x33, 0x34],
    //3 3 4 4
    [0x02, 0x03, 0x12, 0x13],
    [0x02, 0x03, 0x22, 0x23],
    [0x02, 0x03, 0x32, 0x33],
    [0x12, 0x13, 0x22, 0x23],
    [0x12, 0x13, 0x32, 0x33],
    [0x22, 0x23, 0x32, 0x33], //2 2 3 3
];
//基础边夹两对牌型
const BASE_BIAN_JIA_2_DUI_LIST = [
    [0x0D, 0x01, 0x1D, 0x11],
    [0x0D, 0x01, 0x2D, 0x21],
    [0x0D, 0x01, 0x3D, 0x31],
    [0x1D, 0x11, 0x2D, 0x21],
    [0x1D, 0x11, 0x3D, 0x31],
    [0x2D, 0x21, 0x3D, 0x31],
    // K K A A
    [0x0C, 0x01, 0x1C, 0x11],
    [0x0C, 0x01, 0x2C, 0x21],
    [0x0C, 0x01, 0x3C, 0x31],
    [0x1C, 0x11, 0x2C, 0x21],
    [0x1C, 0x11, 0x3C, 0x31],
    [0x2C, 0x21, 0x3C, 0x31],
    // Q Q A A
    [0x0B, 0x0D, 0x1B, 0x1D],
    [0x0B, 0x0D, 0x2B, 0x2D],
    [0x0B, 0x0D, 0x3B, 0x3D],
    [0x1B, 0x1D, 0x2B, 0x2D],
    [0x1B, 0x1D, 0x3B, 0x3D],
    [0x2B, 0x2D, 0x3B, 0x3D],
    // J J K K
    [0x0A, 0x0C, 0x1A, 0x1C],
    [0x0A, 0x0C, 0x2A, 0x2C],
    [0x0A, 0x0C, 0x3A, 0x3C],
    [0x1A, 0x1C, 0x2A, 0x2C],
    [0x1A, 0x1C, 0x3A, 0x3C],
    [0x2A, 0x2C, 0x3A, 0x3C],
    //10 10 Q Q
    [0x0B, 0x09, 0x1B, 0x19],
    [0x0B, 0x09, 0x2B, 0x29],
    [0x0B, 0x09, 0x3B, 0x39],
    [0x1B, 0x19, 0x2B, 0x29],
    [0x1B, 0x19, 0x3B, 0x39],
    [0x2B, 0x29, 0x3B, 0x39],
    //9 9 J J
    [0x08, 0x0A, 0x18, 0x1A],
    [0x08, 0x0A, 0x28, 0x2A],
    [0x08, 0x0A, 0x38, 0x3A],
    [0x18, 0x1A, 0x28, 0x2A],
    [0x18, 0x1A, 0x38, 0x3A],
    [0x28, 0x2A, 0x38, 0x3A],
    //8 8 10 10
    [0x09, 0x07, 0x19, 0x17],
    [0x09, 0x07, 0x29, 0x27],
    [0x09, 0x07, 0x39, 0x37],
    [0x19, 0x17, 0x29, 0x27],
    [0x19, 0x17, 0x39, 0x37],
    [0x29, 0x27, 0x39, 0x37],
    //7 7 9 9
    [0x06, 0x08, 0x16, 0x18],
    [0x06, 0x08, 0x26, 0x28],
    [0x06, 0x08, 0x36, 0x38],
    [0x16, 0x18, 0x26, 0x28],
    [0x16, 0x18, 0x36, 0x38],
    [0x26, 0x28, 0x36, 0x38],
    //6 6 8 8
    [0x05, 0x07, 0x15, 0x17],
    [0x05, 0x07, 0x25, 0x27],
    [0x05, 0x07, 0x35, 0x37],
    [0x15, 0x17, 0x25, 0x27],
    [0x15, 0x17, 0x35, 0x37],
    [0x25, 0x27, 0x35, 0x37],
    //5 5 7 7
    [0x04, 0x06, 0x14, 0x16],
    [0x04, 0x06, 0x24, 0x26],
    [0x04, 0x06, 0x34, 0x36],
    [0x14, 0x16, 0x24, 0x26],
    [0x14, 0x16, 0x34, 0x36],
    [0x24, 0x26, 0x34, 0x36],
    //4 4 6 6
    [0x03, 0x05, 0x13, 0x15],
    [0x03, 0x05, 0x23, 0x25],
    [0x03, 0x05, 0x33, 0x35],
    [0x13, 0x15, 0x23, 0x25],
    [0x13, 0x15, 0x33, 0x35],
    [0x23, 0x25, 0x33, 0x35],
    //3 3 5 5
    [0x02, 0x04, 0x12, 0x14],
    [0x02, 0x04, 0x22, 0x24],
    [0x02, 0x04, 0x32, 0x34],
    [0x12, 0x14, 0x22, 0x24],
    [0x12, 0x14, 0x32, 0x34],
    [0x22, 0x24, 0x32, 0x34],
    //2 2 4 4
    [0x03, 0x01, 0x13, 0x11],
    [0x03, 0x01, 0x23, 0x21],
    [0x03, 0x01, 0x33, 0x31],
    [0x13, 0x11, 0x23, 0x21],
    [0x13, 0x11, 0x33, 0x31],
    [0x23, 0x21, 0x33, 0x31],
    //A A 3 3
    [0x02, 0x01, 0x12, 0x11],
    [0x02, 0x01, 0x22, 0x21],
    [0x02, 0x01, 0x32, 0x31],
    [0x12, 0x11, 0x22, 0x21],
    [0x12, 0x11, 0x32, 0x31],
    [0x22, 0x21, 0x32, 0x31], //A A 2 2
];




//花色索引
const RUMMY_COLOR_INDEX = ['fk' => 0, 'mh' => 1, 'hht' => 2, 'ht' => 3];

//rummy所有纯顺模板
const RUMMY_REAL_SEQ_TMP = [
    '0000000000111',
    '0000000001110',
    '0000000011100',
    '0000000111000',
    '0000001110000',
    '0000011100000',
    '0000111000000',
    '0001110000000',
    '0011100000000',
    '0111000000000',
    '1110000000000',
    '1000000000011',
    '0000000001111',
    '0000000011110',
    '0000000111100',
    '0000001111000',
    '0000011110000',
    '0000111100000',
    '0001111000000',
    '0011110000000',
    '0111100000000',
    '1111000000000',
    '1000000000111',
    '0000000011111',
    '0000000111110',
    '0000001111100',
    '0000011111000',
    '0000111110000',
    '0001111100000',
    '0011111000000',
    '0111110000000',
    '1111100000000',
    '1000000001111',
];

//rummy所有混顺模板  一个混  混3
const RUMMY_FAKE_SEQ_1XCARD_3SEQ_TMP = [
    '0000000000110',
    '0000000000101',
    '0000000000011',
    '0000000001100',
    '0000000001010',
    '0000000010100',
    '0000000110000',
    '0000000011000',
    '0000001100000',
    '0000001010000',
    '0000011000000',
    '0000000101000',
    '0000101000000',
    '0000010100000',
    '0001100000000',
    '0001010000000',
    '0000110000000',
    '0011000000000',
    '0010100000000',
    '1000000000010',
    '0110000000000',
    '0101000000000',
    '1100000000000',
    '1010000000000',
    '1000000000001',
];

//rummy所有混顺模板  一个混  混4 混5
const RUMMY_FAKE_SEQ_1XCARD_TMP = [
    '0000000001011',
    '0000000001101',
    '0000000010110',
    '0000000011010',
    '1000000001110',
    '0000000101100',
    '0000000110100',
    '0000001011000',
    '0000001101000',
    '0000010110000',
    '0000011010000',
    '0000101100000',
    '0000110100000',
    '0001011000000',
    '0001101000000',
    '0010110000000',
    '0011010000000',
    '0101100000000',
    '0110100000000',
    '1011000000000',
    '1101000000000',
    '1000000000110',
    '1000000000101',
    '0000000010111',
    '0000000011011',
    '0000000011101',
    '0000000101110',
    '0000000110110',
    '0000000111010',
    '0000001011100',
    '0000001101100',
    '0000001110100',
    '0000010111000',
    '0000011011000',
    '0000011101000',
    '0000101110000',
    '0000110110000',
    '0000111010000',
    '0001011100000',
    '0001101100000',
    '0001110100000',
    '0010111000000',
    '0011011000000',
    '0011101000000',
    '0101110000000',
    '0110110000000',
    '0111010000000',
    '1011100000000',
    '1101100000000',
    '1110100000000',
    '1000000001011',
    '1000000001101',
];

//rummy所有混顺模板  2个混  混3 混4 混5
const RUMMY_FAKE_SEQ_2XCARD_TMP = [
    '0000000000001',
    '0000000000010',
    '0000000000100',
    '0000000001000',
    '0000000010000',
    '0000000100000',
    '0000001000000',
    '0000010000000',
    '0000100000000',
    '0001000000000',
    '0010000000000',
    '0100000000000',
    '1000000000000',
    '0000000001001',
    '0000000010010',
    '0000000100100',
    '0000001001000',
    '0000010010000',
    '0000100100000',
    '1000000001010',
    '0001001000000',
    '0010010000000',
    '0100100000000',
    '1001000000000',
    '1000000000100',
    '0000000010011',
    '0000000011001',
    '0000000010101',
    '0000000100110',
    '0000000110010',
    '0000000101010',
    '0000001001100',
    '0000001100100',
    '0000001010100',
    '0000010011000',
    '0000011001000',
    '0000010101000',
    '0000100110000',
    '0000110010000',
    '0000101010000',
    '0001001100000',
    '0001100100000',
    '0001010100000',
    '0010011000000',
    '0011001000000',
    '0010101000000',
    '0100110000000',
    '0110010000000',
    '0101010000000',
    '1001100000000',
    '1100100000000',
    '1010100000000',
    '1000000001001',
    '1000000001100',
];

//rummy所有真set模板
const RUMMY_REAL_SET_TMP = [
    '0111',
    '1011',
    '1101',
    '1110',
    '1111',
];

//rummy所有混set模板
const RUMMY_FAKE_SET_1XCARD_TMP = [
    '1100',
    '0110',
    '0011',
    '1010',
    '0101',
    '1001',
];

//rummy所有混set模板
const RUMMY_FAKE_SET_2XCARD_TMP = [
    '1000',
    '0100',
    '0001',
];

const POKER = [
    // 方块 1 - k
    // 1,2,3,4,5,6,7,8,9,10,11,12,13
    FANG_KUAI_LIST,
    // 梅花 1 - k
    // 17 18 19 20 21 22 23 24 25 26 27 28 29
    MEI_HUA_LIST,
    // 红桃 1 - k
    // 33 34 35 36 37 38 39 40 41 42 43 44 45
    HONG_TAO_LIST,
    // 黑桃 1 - k
    // 49 50 51 52 53 54 55 56 57 58 59 60 61
    HEI_TAO_LIST,
    // 79 79 大小王一致
    [G_CARD, G_CARD]
];


//rummy 缺口分值
const RUMMY_GAP_SCORE = [
    //真顺数量 对应分值
    'realSeqScore' => [
        3 => 20,
        4 => 25,
        5 => 30,
    ],
    //已使用的癞子  每张4分
    'usedXcardScore' => 5,
    //未使用的癞子  每张10分
    'notUsedXcardScore' => 10,
    //普通缺口牌 每张2分
    'normalCardScore' => 2.5,
];

?>

