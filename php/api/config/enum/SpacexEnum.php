<?php
//筹码面值
const SPACE_X_CHIP_FACE_VALUE_LIST = [10, 50, 200, 1000, 5000];


//other下注机器人个数
const PACE_X_OTHER_RAISE_ROBOT_CNT = 100;
//桌子配置
const SPACE_X_TABLE = [
    'min_amount' => 100,
    //下注携带的最小金额
    'max_amount' => 20000, //单局下注的最大金额
];

//下注选项
const SPACE_X_OPTION = [
    0 => ['x' => 0, 'bet_amount_x' => 6],
];

// ####################################################################################################
//龙虎下注秒数
const SPACE_X_BET_SECOND = 100;
//排行榜下注时间权重以及下注金额倍数
const SPACE_X_BET_WEIGHT = [
    //1-5秒
    [
        'min_second' => 1,
        'max_second' => 5,
        'other_bet_rate' => 0.05,
        //0.035
        'rank_bet_rate' => 0.45,
        'bet_amount_region' => [
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [10, 10],
        'min_bet_amount_rate' => 0.8,
        'max_bet_amount_rate' => 1,
    ],
    //6-10秒
    [
        'min_second' => 6,
        'max_second' => 10,
        'other_bet_rate' => 0.03,
        'rank_bet_rate' => 0.3,
        'bet_amount_region' => [
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [10, 10],
        'min_bet_amount_rate' => 0.8,
        'max_bet_amount_rate' => 1,
    ],
    //11-15秒
    [
        'min_second' => 11,
        'max_second' => 100,
        'other_bet_rate' => 0.015,
        'rank_bet_rate' => 0.3,
        'bet_amount_region' => [
            ['min' => 0.7, 'max' => 0.8],
            ['min' => 0.8, 'max' => 0.9],
        ],
        'bet_amount_region_weight' => [10, 10],
        'min_bet_amount_rate' => 0.8,
        'max_bet_amount_rate' => 1,
    ],
];
//牌桌AI 本局初始化信息 ###################################################################################

//ai本局是否下注
const SPACE_X_BET_RATE = [
    'other' => 0.95,
    'rank' => 0.7
];

//ai本局下注总钱数概率
const SPACE_X_BET_AMOUNT_RATE = [
    'other' => [
        'min' => 0.35,
        'max' => 0.35,
    ],
    'rank' => [
        'min' => 0.1,
        'max' => 0.31
    ]
];

//互斥选项  根据配置获取下下注选项
const SPACE_X_MUTEX_LIST = [
    'other' => [
        [
            'option' => [0 => 100],
            'select_option_cnt' => 1
        ]
    ],
    'rank' => [
        [
            'option' => [0 => 100],
            'select_option_cnt' => 1
        ]
    ]
];


//AI 账户余额 重置参数配置################################################################################
//ai 携带金额权重区间配置 原始金额 不*100 在区间随机结束后再*100操作  保证区间出来的数在最终计算时有一位小数
const SPACE_X_BALANCE_AMOUNT = [
    'amount_region' => [
        1 => ['min' => 100, 'max' => 5000],
    ],
    'weight' => [1 => 1]
];



?>

