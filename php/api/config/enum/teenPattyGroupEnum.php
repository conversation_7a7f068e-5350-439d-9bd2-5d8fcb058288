<?php

//每个AI进场时，会在配置的范围内随机到一个加倍心理上限
const BASE_ZJ_AI_RAISE_CNT_CONFIG = [
    '10' => [1 => 3, 2 => 13, 3 => 34, 4 => 34, 5 => 13, 6 => 3],
    '50' => [1 => 3, 2 => 20, 3 => 38, 4 => 27, 5 => 10, 6 => 2],
    '100' => [1 => 3, 2 => 13, 3 => 34, 4 => 34, 5 => 13, 6 => 3],
    '500' => [1 => 3, 2 => 13, 3 => 34, 4 => 34, 5 => 13, 6 => 3],
    '1000' => [1 => 3, 2 => 13, 3 => 34, 4 => 34, 5 => 13, 6 => 3],
    '2000' => [1 => 3, 2 => 13, 3 => 37, 4 => 34, 5 => 10, 6 => 3],
    '5000' => [1 => 3, 2 => 16, 3 => 39, 4 => 30, 5 => 10, 6 => 2],
    '10000' => [1 => 3, 2 => 16, 3 => 41, 4 => 30, 5 => 9, 6 => 1],
    '20000' => [1 => 3, 2 => 16, 3 => 42, 4 => 30, 5 => 8, 6 => 1],
];

//定义牌型牌力值
const BASE_ZJ_TYPE_POWER_SCORE = [
    'ZJ_GROUP_NAME_BAOZI' => 10000000000,
    'ZJ_GROUP_NAME_TONGHUA_SHUNZI' => 1000000000,
    'ZJ_GROUP_NAME_SHUNZI' => 100000000,
    'ZJ_GROUP_NAME_TONGHUA' => 100000,
    'ZJ_GROUP_NAME_DUIZI' => 10000,
    'ZJ_GROUP_NAME_SANPAI' => 1,
];

//定义单个牌力值
const BASE_ZJ_DIANSHU_SCORE = [
    1 => 32,
    2 => 2,
    3 => 3,
    4 => 4,
    5 => 5,
    6 => 6,
    7 => 7,
    8 => 8,
    9 => 9,
    10 => 10,
    11 => 11,
    12 => 12,
    13 => 13,
];

//最后一个ai加倍配置
const LAST_AI_RAISE_RATE = [
    1 => 0.95,
    2 => 0.5,
    3 => 0.1
];

?>

