<?php
/**
 * @todo Ai 接口
 */

namespace api;

use llogic\common\Struct;

use common\AiTeenPatty\AiTeenPattyChangeCards;
use common\AiTeenPatty\AiTeenPattyPrBetSequence;
use common\AiTeenPatty\AiTeenPattyPrControlDealCardV2;
use common\AiTeenPatty\AiTeenPattyPrRookieBuffDealCardV2;
use common\AiTeenPatty\AiTeenPattyPrStuffy;
use common\AiTeenPatty\AiTeenPattyPrSee;
use common\ErrorCode;
use Exception;
use lib\Log;
use lib\Request;

class ApiTeenPattyPrAct
{
    //服务器 grou牌组
    public static function dealCards(): array
    {
        $rtn = [
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => [
                'ulist' => [],
            ]
        ];
        try {
            $info = Request::getJsonBody();
            if (!$info) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['POST_EMPTY_ERR'], ErrorCode::ERR_CODE['POST_EMPTY_ERR']);
            }
            $infoArr = json_decode($info, 1);
            if (!isset($infoArr['d']['ulist']) || !$infoArr['d']['ulist'] || !is_array($infoArr['d']['ulist'])) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['ULIST_ERROR'], ErrorCode::ERR_CODE['ULIST_ERROR']);
            }
            if (!is_string($infoArr['d']['rtype'] ?? null) || !$infoArr['d']['rtype']) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['R_ROOM_TYPE_ERR'], ErrorCode::ERR_CODE['R_ROOM_TYPE_ERR']);
            }
            if (!isset($infoArr['d']['rid']) || !$infoArr['d']['rid']) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['R_ROOM_TYPE_ERR'], ErrorCode::ERR_CODE['R_ROOM_TYPE_ERR']);
            }

            $room_type = $infoArr['d']['rtype'];
            Struct::room_type_to_game_name_base_rupee_type($room_type, $game_name, $base_rupee);
            $ulist = $infoArr['d']['ulist'];
            $player_count = count($ulist); // 人数
            // 统计 ulist 中 utype 字段为 Common::PLAYER_TYPE_PEOPLE 的个数，即真人数
            $human_count = 0;
            foreach ($ulist as $u) {
                if ($u['utype'] == \common\Common::PLAYER_TYPE_PEOPLE) {
                    $human_count++;
                }
            }
            $robot_count = $player_count - $human_count; // 机器人数
            $branchName = "未指定";

            //如果有新手保护 则认定全场都是新手保护发牌方式
            $rookieBuffList = array_column($ulist, 'rookie_buff');
            $cryUp = false;

            $isChangeCard = false;
            $changeRate = 0;
            $handler = "";
            $capitalNames = [];

            if (in_array(1, $rookieBuffList)) {
                if (!isset($infoArr['d']['cry_up'])) {
                    $infoArr['d']['cry_up'] = false;
                }
                $AiTeenPattyDealCardObj = new AiTeenPattyPrRookieBuffDealCardV2($ulist, $room_type, $infoArr['d']['rid'], $infoArr['d']['cry_up']);
                $cryUp = $AiTeenPattyDealCardObj->getCryUp();
                $branchName = $AiTeenPattyDealCardObj->calcBranchName();
                $handler = "AiTeenPattyPrRookieBuffDealCardV2";
            } else {
                $AiTeenPattyDealCardObj = new AiTeenPattyPrControlDealCardV2($ulist, $room_type, $infoArr['d']['rid']);
                $branchName = $AiTeenPattyDealCardObj->calcBranchName();
                $isChangeCard = $AiTeenPattyDealCardObj->isChangeCard;
                $changeRate = $AiTeenPattyDealCardObj->changeRate;
                $handler = "AiTeenPattyPrControlDealCardV2";
                $capitalNames = $AiTeenPattyDealCardObj->capitalNames;
            }
            $rtn['res']['ulist'] = $AiTeenPattyDealCardObj->getDealCards();
            $rtn['res']['cry_up'] = $cryUp;

            \lbase\metric\PushAPIDeal::yyl_api_deal_total($game_name, $base_rupee, $branchName);

            \lbase\Log::info("TpApi-dealCards", [
                'd' => $infoArr['d'],
                'res' => $rtn['res'],
                "handler" => $handler,
                "game_name" => $game_name,
                "base_rupee" => $base_rupee,
                "branchName" => $branchName,
                "isChangeCard" => $isChangeCard,
                "changeRate" => $changeRate,
                "capitalNames" => $capitalNames,
            ]);

        } catch (Exception $e) {
            $rtn['code'] = $e->getCode();
            $rtn['message'] = $e->getMessage();
        }

        return $rtn;
    }

    //获取ai操作
    public static function aiOpt(): array
    {
        $rtn = [
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => []
        ];
        try {
            $info = Request::getJsonBody();

            if (!$info) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['POST_EMPTY_ERR'], ErrorCode::ERR_CODE['POST_EMPTY_ERR']);
            }
            $infoArr = json_decode($info, 1);
            if (!isset($infoArr['d']['ulist']) || !$infoArr['d']['ulist'] || !is_array($infoArr['d']['ulist'])) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['ULIST_ERROR'], ErrorCode::ERR_CODE['ULIST_ERROR']);
            }
            if (!isset($infoArr['d']['rtype']) || !$infoArr['d']['rtype']) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['R_ROOM_TYPE_ERR'], ErrorCode::ERR_CODE['R_ROOM_TYPE_ERR']);
            }
            if (!isset($infoArr['d']['circle'])) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['R_NUM_ROUNDS_ERR'], ErrorCode::ERR_CODE['R_NUM_ROUNDS_ERR']);
            }
            if (!isset($infoArr['d']['curr_uid']) || !$infoArr['d']['curr_uid']) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['UID_ERROR'], ErrorCode::ERR_CODE['UID_ERROR']);
            }
            if (!isset($infoArr['d']['rid']) || !$infoArr['d']['rid']) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['R_ROOM_TYPE_ERR'], ErrorCode::ERR_CODE['R_ROOM_TYPE_ERR']);
            }
            if (!isset($infoArr['d']['cry_up'])) {
                $infoArr['d']['cry_up'] = false;
            }
            //关闭抬叫局
            //$infoArr['d']['cry_up'] = false;
            $aiTeenPattyObj = new AiTeenPattyPrStuffy($infoArr['d']['ulist'], $infoArr['d']['curr_uid'], $infoArr['d']['rtype'], $infoArr['d']['circle'], $infoArr['d']['rid'], $infoArr['d']['cry_up']);
            $isSee = $aiTeenPattyObj->getIsSee();
            if ($isSee) {
                $aiTeenPattyObj = new AiTeenPattyPrSee($infoArr['d']['ulist'], $infoArr['d']['curr_uid'], $infoArr['d']['rtype'], $infoArr['d']['circle'], $infoArr['d']['rid'], $infoArr['d']['cry_up']);
            }
            $rtn['res']['see'] = $isSee; //是否看牌
            $rtn['res']['drop'] = $aiTeenPattyObj->getIsDrop(); //是否弃牌
            $rtn['res']['add'] = $aiTeenPattyObj->getIsRaise(); //是否加注（封顶不能加注，直接结算）
            $rtn['res']['compare'] = $aiTeenPattyObj->getIsInitiativeCheck(); //主动比牌（下家未看牌，不能主动发起比牌）（未封顶情况下双方点数一样，则发起方输；否则平局）
            $rtn['res']['accept'] = $aiTeenPattyObj->getIsPassiveCheck(); //是否接受比牌（未看牌，别人不能给AI发起比牌）
            //是否抬轿局 //是否局内充值   抬轿局中 不drop 不接受比牌 也不主动比牌
            if (isset($infoArr['d']['cry_up']) && $infoArr['d']['cry_up'] && isset($infoArr['d']['inside_recharge']) && !$infoArr['d']['inside_recharge']) {
                $rtn['res']['drop'] = false;
                $rtn['res']['compare'] = false;
                $rtn['res']['accept'] = false;
            }

        } catch (Exception $e) {
            $rtn['code'] = $e->getCode();
            $rtn['message'] = $e->getMessage();
        }

        return $rtn;
    }




    //中途换牌
    public static function changeCards(): array
    {
        $rtn = [
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => []
        ];
        try {
            $info = Request::getJsonBody();
            if (!$info) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['POST_EMPTY_ERR'], ErrorCode::ERR_CODE['POST_EMPTY_ERR']);
            }
            $infoArr = json_decode($info, 1);
            if (!isset($infoArr['d']['ulist']) || !$infoArr['d']['ulist'] || !is_array($infoArr['d']['ulist'])) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['ULIST_ERROR'], ErrorCode::ERR_CODE['ULIST_ERROR']);
            }
            if (!isset($infoArr['d']['rtype']) || !$infoArr['d']['rtype']) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['R_ROOM_TYPE_ERR'], ErrorCode::ERR_CODE['R_ROOM_TYPE_ERR']);
            }
            if (!isset($infoArr['d']['rid']) || !$infoArr['d']['rid']) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['R_ROOM_TYPE_ERR'], ErrorCode::ERR_CODE['R_ROOM_TYPE_ERR']);
            }
            if (!isset($infoArr['d']['uid']) || !$infoArr['d']['uid']) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['R_ROOM_TYPE_ERR'], ErrorCode::ERR_CODE['R_ROOM_TYPE_ERR']);
            }
            $obj = new AiTeenPattyChangeCards($infoArr['d']['ulist'], $infoArr['d']['rtype'], $infoArr['d']['rid'], $infoArr['d']['uid']);
            $rtn['res'] = $obj->getChangeCardsRes();

        } catch (Exception $e) {
            $rtn['code'] = $e->getCode();
            $rtn['message'] = $e->getMessage();
        }

        return $rtn;
    }


    //服务器 本次操作玩家的牌的顺位预估
    public static function betSequence(): array
    {
        $rtn = [
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => []
        ];
        try {
            $info = Request::getJsonBody();
            if (!$info) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['POST_EMPTY_ERR'], ErrorCode::ERR_CODE['POST_EMPTY_ERR']);
            }
            $infoArr = json_decode($info, 1);

            if (!isset($infoArr['d']['uid']) || !$infoArr['d']['uid']) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['UID_ERROR'], ErrorCode::ERR_CODE['UID_ERROR']);
            }
            if (!isset($infoArr['d']['opt']) || !$infoArr['d']['opt']) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['UID_ERROR'], ErrorCode::ERR_CODE['UID_ERROR']);
            }
            //            if(!isset($infoArr['d']['raise_cnt'])){
//                throw new Exception(ErrorCode::ERR_TEXT_EN['UID_ERROR'],ErrorCode::ERR_CODE['UID_ERROR']);
//            }
            if (!isset($infoArr['d']['rid']) || !$infoArr['d']['rid']) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['R_ROOM_TYPE_ERR'], ErrorCode::ERR_CODE['R_ROOM_TYPE_ERR']);
            }
            if (!isset($infoArr['d']['ulist']) || !$infoArr['d']['ulist'] || !is_array($infoArr['d']['ulist'])) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['ULIST_ERROR'], ErrorCode::ERR_CODE['ULIST_ERROR']);
            }
            $AiTeenPattyDealCardObj = new AiTeenPattyPrBetSequence($infoArr['d']['uid'], $infoArr['d']['ulist'], $infoArr['d']['opt'], $infoArr['d']['rid']);
            $rtn['res'] = $AiTeenPattyDealCardObj->getPostBackInfo();
        } catch (Exception $e) {
            $rtn['code'] = $e->getCode();
            $rtn['message'] = $e->getMessage();
        }

        return $rtn;
    }



}
