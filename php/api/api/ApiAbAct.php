<?php
/**
 * @todo Ai 组牌 打牌 drop 接口
 */
namespace api;

use llogic\common\Struct;

use common\AiCommon\AiDealerGameBalanceAmount;
use common\AiHundred\AiABDealCard;
use common\AiCommon\AiDealerGameBetV2 as AiDealerGameBet;
use common\AiCommon\AiDealerGameInit;
use common\Common;
use common\ErrorCode;
use Exception;
use lib\Log;
use lib\Request;

class ApiAbAct implements ApiHundredInterfaceAct
{

    //盈利率 AB玩法 结算发牌信息
    public static function dealCards(): array
    {
        $rtn = [
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => [
                'cards' => [],
                'win_raise_id' => [],
                'start' => 0,
            ]
        ];
        try {
            $info = Request::getJsonBody();

            if (!$info) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['POST_EMPTY_ERR'], ErrorCode::ERR_CODE['POST_EMPTY_ERR']);
            }
            $infoArr = json_decode($info, 1);

            if (!isset($infoArr['d']['rtype']) || !$infoArr['d']['rtype']) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['R_ROOM_TYPE_ERR'], ErrorCode::ERR_CODE['R_ROOM_TYPE_ERR']);
            }
            if (!isset($infoArr['d']['xcard']) || !$infoArr['d']['xcard']) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['L_CARD_ERR'], ErrorCode::ERR_CODE['L_CARD_ERR']);
            }
            if (!isset($infoArr['d']['raise_list'])) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['RAISE_LIST_ERROR'], ErrorCode::ERR_CODE['RAISE_LIST_ERROR']);
            }
            if (!isset($infoArr['d']['bet_uid_list'])) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['RAISE_LIST_ERROR'], ErrorCode::ERR_CODE['RAISE_LIST_ERROR']);
            }
            if (!isset($infoArr['d']['rutype'])) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['RAISE_LIST_ERROR'], ErrorCode::ERR_CODE['RAISE_LIST_ERROR']);
            }
            if (!isset($infoArr['d']['all_win_amount'])) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['RAISE_LIST_ERROR'], ErrorCode::ERR_CODE['RAISE_LIST_ERROR']);
            }
            if (!isset($infoArr['d']['all_bet_amount'])) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['RAISE_LIST_ERROR'], ErrorCode::ERR_CODE['RAISE_LIST_ERROR']);
            }

            $room_type = $infoArr['d']['rtype'];
            Struct::room_type_to_game_name_base_rupee_type($room_type, $game_name, $base_rupee);

            //buff房间  用户的余额
            $balanceAmount = $infoArr['d']['balance_amount'] ?? 0;

            //获取最终的发牌
            $dealCardsObj = new AiABDealCard($infoArr['d']['xcard'], $infoArr['d']['rtype'], $infoArr['d']['bet_uid_list'], $infoArr['d']['raise_list'], $infoArr['d']['rutype'], $infoArr['d']['all_win_amount'], $infoArr['d']['all_bet_amount'], $infoArr['d']['raise_user'], $balanceAmount);
            $rtn['res']['cards'] = $dealCardsObj->getDealCards();
            $rtn['res']['win_raise_id'] = $dealCardsObj->getWindRaiseList();
            //获取发牌开始位置
            $rtn['res']['start'] = $dealCardsObj->getDealCardsStartKey();


            $dealCardsObj->SendMetrics();
        } catch (Exception $e) {
            $rtn['code'] = $e->getCode();
            $rtn['message'] = $e->getMessage();
        }

        return $rtn;
    }



    // ai模拟下注
    public static function betAmount(): array
    {
        $rtn = [
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => [
                'bet_list' => [],
            ]
        ];
        try {
            $info = Request::getJsonBody();
            //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__,'接受到的参数：'.$info);
            if (!$info) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['POST_EMPTY_ERR'], ErrorCode::ERR_CODE['POST_EMPTY_ERR']);
            }
            $infoArr = json_decode($info, 1);
            if (!isset($infoArr['d']['ulist'])) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['RAISE_LIST_ERROR'], ErrorCode::ERR_CODE['RAISE_LIST_ERROR']);
            }
            if (!isset($infoArr['d']['cur_second'])) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['RAISE_LIST_ERROR'], ErrorCode::ERR_CODE['RAISE_LIST_ERROR']);
            }
            if (!isset($infoArr['d']['type'])) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['RAISE_LIST_ERROR'], ErrorCode::ERR_CODE['RAISE_LIST_ERROR']);
            }
            if (!isset($infoArr['d']['invented_ai_cnt'])) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['RAISE_LIST_ERROR'], ErrorCode::ERR_CODE['RAISE_LIST_ERROR']);
            }
            //获取下注结果
            $obj = new AiDealerGameBet();
            //设置下注筹码面值列表
            $obj->setChipFaceValueList(AB_CHIP_FACE_VALUE_LIST);
            //设置下注配置
            $obj->setBetConfig(AB_BET_WEIGHT);
            //设置下注选项
            $obj->setBetOption(AB_OPTION);
            //设置下注时间
            $obj->setBetMaxSecond(AB_BET_SECOND);
            //设置当前游戏进行到多少秒
            $obj->setCurSecond($infoArr['d']['cur_second']);
            //设置虚拟ai的数量
            $obj->setInventedAiCnt($infoArr['d']['invented_ai_cnt']);
            //设置下注区域互斥
            $obj->setMutexList(AB_MUTEX_LIST);
            foreach ($infoArr['d']['ulist'] as $key => $val) {
                //设置下注的ai类型
                $obj->setAiType($key);
                //设置需要下注的ai列表
                $obj->setAiList($val);
                $rtn['res']['bet_list'][$key] = $obj->getBetList();
            }
        } catch (Exception $e) {
            $rtn['code'] = $e->getCode();
            $rtn['message'] = $e->getMessage();
        }
        //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__,'返回的参数：'.json_encode($rtn));
        return $rtn;
    }


    //初始化ai属性
    public static function initAiTableInfo(): array
    {

        $rtn = [
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => [
                'ulist' => [],
            ]
        ];
        try {
            $info = Request::getJsonBody();
            //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__,'接受到的参数：'.$info);
            if (!$info) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['POST_EMPTY_ERR'], ErrorCode::ERR_CODE['POST_EMPTY_ERR']);
            }
            $infoArr = json_decode($info, 1);
            if (!isset($infoArr['d']['ulist'])) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['RAISE_LIST_ERROR'], ErrorCode::ERR_CODE['RAISE_LIST_ERROR']);
            }
            if (!isset($infoArr['d']['type'])) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['RAISE_LIST_ERROR'], ErrorCode::ERR_CODE['RAISE_LIST_ERROR']);
            }
            $obj = new AiDealerGameInit();
            $obj->setTableConfig(AB_TABLE);
            $obj->setBetAmountRate(AB_BET_AMOUNT_RATE);
            $obj->setBetOptionList(AB_MUTEX_LIST);
            $obj->setBetRate(AB_BET_RATE);
            //rank or other
            $obj->setAiType($infoArr['d']['type']);
            $obj->setAiList($infoArr['d']['ulist']);
            $rtn['res']['ulist'] = $obj->getAiInitList();
        } catch (Exception $e) {
            $rtn['code'] = $e->getCode();
            $rtn['message'] = $e->getMessage();
        }
        //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__,'返回的参数：'.json_encode($rtn));
        return $rtn;


    }


    //ai携带金额 初始化 重置
    public static function balanceAmountList(): array
    {
        $rtn = [
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => [
                'ulist' => [],
            ]
        ];
        try {
            $info = Request::getJsonBody();
            //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__,'接受到的参数：'.$info);
            if (!$info) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['POST_EMPTY_ERR'], ErrorCode::ERR_CODE['POST_EMPTY_ERR']);
            }
            $infoArr = json_decode($info, 1);
            if (!isset($infoArr['d']['ulist']) || !$infoArr['d']['ulist']) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['RAISE_LIST_ERROR'], ErrorCode::ERR_CODE['RAISE_LIST_ERROR']);
            }
            //获取下注结果
            $obj = new AiDealerGameBalanceAmount($infoArr['d']['ulist']);
            $obj->setBalanceAmountConfig(AB_BALANCE_AMOUNT);
            $obj->setTableConfig(AB_TABLE);

            $obj->setAiList($infoArr['d']['ulist']);
            $rtn['res']['ulist'] = $obj->getAiListBalance();
        } catch (Exception $e) {
            $rtn['code'] = $e->getCode();
            $rtn['message'] = $e->getMessage();
        }
        //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__,'返回的参数：'.json_encode($rtn));
        return $rtn;
    }

}
