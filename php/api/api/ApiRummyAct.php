<?php
/**
 * @todo Ai 组牌 打牌 drop 接口
 */

namespace api;

use llogic\common\Struct;

use common\AiCommon\AiDealerGameBalanceAmount;
use common\AiRummy\AutoGroup\AiRummyAutoGroup;
use common\AiRummy\AiRummyDealCardBat;
use common\AiRummy\AiRummyDealCardProfitToAi;
use common\AiRummy\AiRummyDealCardProfitToPlayer;
use common\AiRummy\AiRummyDisCard;
use common\AiRummy\AiRummyDrawCard;
use common\AiRummy\AiRummyDrawDarkCard;
use common\AiRummy\AiRummyDrop;
use common\AiRummy\AiRummyFinishRate;
use common\AiRummy\AiRummyGroupCard;
use common\Common;
use common\ErrorCode;
use common\RedisOpt;
use lib\Log;
use lib\Request;
use lib\Config;

class ApiRummyAct
{

    //autoSort
    public static function autoSortCards(): array
    {
        $rtn = array(
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => [
                'declear' => array(),
            ]
        );
        return $rtn;
        try {
            $info = Request::getJsonBody();

            if (!$info) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['POST_EMPTY_ERR'], ErrorCode::ERR_CODE['POST_EMPTY_ERR']);
            }
            $infoArr = json_decode($info, 1);
            //获取需要group的一维数组手牌
            if (!isset($infoArr['d']['hand']) || !is_array($infoArr['d']['hand'])) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['HAND_CARD_NUM_ERR'], ErrorCode::ERR_CODE['HAND_CARD_NUM_ERR']);
            }
            //获取本次group的癞子牌
            if (!isset($infoArr['d']['xcard']) || $infoArr['d']['xcard'] <= 0) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['L_CARD_ERR'], ErrorCode::ERR_CODE['L_CARD_ERR']);
            }
            //获取房间类型
            if (!isset($infoArr['d']['rtype'])) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['R_ROOM_TYPE_ERR'], ErrorCode::ERR_CODE['R_ROOM_TYPE_ERR']);
            }
            //房间类型解析为数组信息
            $roomInfo = parse_room_type($infoArr['d']['rtype']);
            if ($roomInfo['cls'] == Common::GAME_TYPE_RUMMY_10_CARD) {
                $gameType = Common::GAME_TYPE_10_CARD;
            } else {
                $gameType = Common::GAME_TYPE_13_CARD;
            }
            //进行分组 获取dealCards
            $obj = new AiRummyAutoGroup($infoArr['d']['hand'], $infoArr['d']['xcard'], $gameType);
            $rtn['res']['declear'] = $obj->getDeclearCards();

        } catch (\Exception $e) {
            $rtn['code'] = $e->getCode();
            $rtn['message'] = $e->getMessage();
        }

        return $rtn;
    }

    //服务器 grou牌组
    public static function initGroupCards(): array
    {
        $rtn = array(
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => [
                'declear' => array(),
            ]
        );
        try {
            $info = Request::getJsonBody();

            if (!$info) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['POST_EMPTY_ERR'], ErrorCode::ERR_CODE['POST_EMPTY_ERR']);
            }
            $infoArr = json_decode($info, 1);
            //获取需要group的一维数组手牌
            if (!isset($infoArr['d']['hand']) || !is_array($infoArr['d']['hand'])) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['HAND_CARD_NUM_ERR'], ErrorCode::ERR_CODE['HAND_CARD_NUM_ERR']);
            }
            //获取本次group的癞子牌
            if (!isset($infoArr['d']['xcard']) || $infoArr['d']['xcard'] <= 0) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['L_CARD_ERR'], ErrorCode::ERR_CODE['L_CARD_ERR']);
            }
            //进行分组 获取dealCards
            $obj = new AiRummyGroupCard($infoArr['d']['hand'], $infoArr['d']['xcard'], count($infoArr['d']['hand']));
            $rtn['res']['declear'] = $obj->getDeclearCards();

        } catch (\Exception $e) {
            $rtn['code'] = $e->getCode();
            $rtn['message'] = $e->getMessage();
        }

        return $rtn;
    }

    //手牌批量分组
    public static function iniGroupCardsBat(): array
    {
        $rtn = array(
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => [
                'declear' => array(),
            ]
        );
        try {
            $info = Request::getJsonBody();

            if (!$info) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['POST_EMPTY_ERR'], ErrorCode::ERR_CODE['POST_EMPTY_ERR']);
            }
            $infoArr = json_decode($info, 1);

            //获取需要group的二维数组手牌
            if (!isset($infoArr['d']['hand']) || !is_array($infoArr['d']['hand'])) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['HAND_CARD_NUM_ERR'], ErrorCode::ERR_CODE['HAND_CARD_NUM_ERR']);
            }
            //获取本次group的癞子牌
            if (!isset($infoArr['d']['xcard']) || $infoArr['d']['xcard'] <= 0) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['L_CARD_ERR'], ErrorCode::ERR_CODE['L_CARD_ERR']);
            }
            $declearList = array();
            foreach ($infoArr['d']['hand'] as $key => $val) {
                if (!is_array($val) || empty($val)) {
                    $declearList[$key] = [];
                    continue;
                }
                //进行分组 获取dealCards
                $obj = new AiRummyGroupCard($val, $infoArr['d']['xcard'], count($val));
                $declearList[$key] = $obj->getDeclearCards();
            }
            $rtn['res']['declear'] = $declearList;
        } catch (\Exception $e) {
            $rtn['code'] = $e->getCode();
            $rtn['message'] = $e->getMessage();
        }

        return $rtn;
    }

    //服务器判定 摸明 摸暗
    public static function drawCard(): array
    {
        $rtn = array(
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => [
                'type' => Common::TAKE_CARD_TYPE_A,
            ]
        );
        try {
            $info = Request::getJsonBody();

            if (!$info) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['POST_EMPTY_ERR'], ErrorCode::ERR_CODE['POST_EMPTY_ERR']);
            }
            $infoArr = json_decode($info, 1);
            //获取需要group的一维数组手牌
            if (!isset($infoArr['d']['hand']) || !is_array($infoArr['d']['hand'])) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['HAND_CARD_NUM_ERR'], ErrorCode::ERR_CODE['HAND_CARD_NUM_ERR']);
            }
            //获取本次group的癞子牌
            if (!isset($infoArr['d']['xcard']) || $infoArr['d']['xcard'] <= 0) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['L_CARD_ERR'], ErrorCode::ERR_CODE['L_CARD_ERR']);
            }
            //获取本次group的明牌
            if (!isset($infoArr['d']['mcard']) || $infoArr['d']['mcard'] <= 0) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['M_CARD_ERR'], ErrorCode::ERR_CODE['M_CARD_ERR']);
            }
            //            //摸牌牌池
//            if(!isset($infoArr['d']['pool']) || $infoArr['d']['pool']<=0){
//                throw new \Exception(ErrorCode::ERR_TEXT_EN['M_CARD_ERR'],ErrorCode::ERR_CODE['M_CARD_ERR']);
//            }
            //进行摸牌决策
            $obj = new AiRummyDrawCard($infoArr['d']['hand'], $infoArr['d']['xcard'], count($infoArr['d']['hand']), $infoArr['d']['mcard']);
            $drawCardRes = $obj->getDrawCardRes();

            if ($drawCardRes) {
                $rtn['res']['type'] = Common::TAKE_CARD_TYPE_M;
            }
        } catch (\Exception $e) {
            $rtn['code'] = $e->getCode();
            $rtn['message'] = $e->getMessage();
        }

        return $rtn;
    }

    //服务器判定 需要打掉的牌
    public static function discardCard(): array
    {
        $rtn = array(
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => array(
                'card' => 0,
                'finish' => false,
                'declear' => [],
            )
        );
        try {
            $info = Request::getJsonBody();

            if (!$info) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['POST_EMPTY_ERR'], ErrorCode::ERR_CODE['POST_EMPTY_ERR']);
            }
            $infoArr = json_decode($info, 1);
            //获取需要group的一维数组手牌
            if (!isset($infoArr['d']['hand']) || !is_array($infoArr['d']['hand'])) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['HAND_CARD_NUM_ERR'], ErrorCode::ERR_CODE['HAND_CARD_NUM_ERR']);
            }
            //获取本次group的癞子牌
            if (!isset($infoArr['d']['xcard']) || $infoArr['d']['xcard'] <= 0) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['L_CARD_ERR'], ErrorCode::ERR_CODE['L_CARD_ERR']);
            }

            //执行打牌逻辑 并取回打牌结果
            $obj = new AiRummyDisCard($infoArr['d']['hand'], $infoArr['d']['xcard'], count($infoArr['d']['hand']) - 1);
            $rtn['res']['card'] = $obj->getDisCard();
            $rtn['res']['finish'] = $obj->getCanFinishRes();
            $rtn['res']['declear'] = $obj->getDeclearCards();

            //执行概率胡牌结果
            if (isset($infoArr['d']['rtype']) && isset($infoArr['d']['circle']) && $rtn['res']['finish']) {
                $finishRateObj = new AiRummyFinishRate($infoArr['d']['rtype'], $infoArr['d']['circle']);
                $rtn['res']['finish'] = $finishRateObj->getFinishRes();
            }

        } catch (\Exception $e) {
            $rtn['code'] = $e->getCode();
            $rtn['message'] = $e->getMessage();
        }

        return $rtn;
    }

    //是否需要drop
    public static function dropCard(): array
    {
        $rtn = array(
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => [
                'drop' => false,
            ]
        );
        try {
            $info = Request::getJsonBody();

            if (!$info) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['POST_EMPTY_ERR'], ErrorCode::ERR_CODE['POST_EMPTY_ERR']);
            }
            $infoArr = json_decode($info, 1);
            //获取需要group的一维数组手牌
            if (!isset($infoArr['d']['hand'])) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['HAND_CARD_NUM_ERR'], ErrorCode::ERR_CODE['HAND_CARD_NUM_ERR']);
            }
            //获取本次group的癞子牌
            if (!isset($infoArr['d']['xcard'])) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['L_CARD_ERR'], ErrorCode::ERR_CODE['L_CARD_ERR']);
            }
            if (!isset($infoArr['d']['pnum']) || $infoArr['d']['pnum'] <= 0) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['P_NUM_ERR'], ErrorCode::ERR_CODE['P_NUM_ERR']);
            }
            if (!isset($infoArr['d']['rnum']) || $infoArr['d']['rnum'] < 0) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['R_NUM_ROUNDS_ERR'], ErrorCode::ERR_CODE['R_NUM_ROUNDS_ERR']);
            }
            //当前玩家获得的分数
            if (!isset($infoArr['d']['rlscore'])) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['RLSCORE_ERROR'], ErrorCode::ERR_CODE['RLSCORE_ERROR']);
            }
            if (!isset($infoArr['d']['rtype'])) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['RTYPE_ERROR'], ErrorCode::ERR_CODE['RTYPE_ERROR']);
            }
            if (!isset($infoArr['d']['dnum'])) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['RTYPE_ERROR'], ErrorCode::ERR_CODE['RTYPE_ERROR']);
            }

            $obj = new AiRummyDrop($infoArr['d']['hand'], $infoArr['d']['xcard'], $infoArr['d']['rtype'], $infoArr['d']['pnum'], $infoArr['d']['dnum'], $infoArr['d']['rnum']);
            $rtn['res']['drop'] = $obj->getDropRes();
        } catch (\Exception $e) {
            $rtn['code'] = $e->getCode();
            $rtn['message'] = $e->getMessage();
        }

        return $rtn;

    }

    //盈利率模式 发牌
    public static function dealCardsByProfit(): array
    {
        $rtn = [
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => [
                'hand' => [],
                'pool' => [],
            ]
        ];
        try {
            $info = Request::getJsonBody();
            if (!$info) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['POST_EMPTY_ERR'], ErrorCode::ERR_CODE['POST_EMPTY_ERR']);
            }
            $infoArr = json_decode($info, 1);

            if (!isset($infoArr['d']['rtype']) || !$infoArr['d']['rtype']) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['R_ROOM_TYPE_ERR'], ErrorCode::ERR_CODE['R_ROOM_TYPE_ERR']);
            }
            if (!isset($infoArr['d']['mcard']) || !$infoArr['d']['mcard']) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['CARD_MAX_CARD_ERROR'], ErrorCode::ERR_CODE['CARD_MAX_CARD_ERROR']);
            }
            //获取需要group的一维数组手牌
            if (!isset($infoArr['d']['pool']) || !is_array($infoArr['d']['pool']) || count($infoArr['d']['pool']) < $infoArr['d']['mcard']) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['CARD_POOL_ERROR'], ErrorCode::ERR_CODE['CARD_POOL_ERROR']);
            }
            //获取本次group的癞子牌
            if (!isset($infoArr['d']['xcard']) || $infoArr['d']['xcard'] <= 0) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['L_CARD_ERR'], ErrorCode::ERR_CODE['L_CARD_ERR']);
            }
            //获取本次group的用户身份
            if (!isset($infoArr['d']['utype']) || $infoArr['d']['utype'] < 0) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['UTYPE_ERROR'], ErrorCode::ERR_CODE['UTYPE_ERROR']);
            }
            //获取本次group的用户id
            if (!isset($infoArr['d']['uid']) || $infoArr['d']['uid'] < 0) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['UID_ERROR'], ErrorCode::ERR_CODE['UID_ERROR']);
            }
            if ($infoArr['d']['utype'] == Common::PLAYER_TYPE_AI) {
                //AI发牌
                $obj = new AiRummyDealCardProfitToAi($infoArr['d']['uid'], $infoArr['d']['rtype'], $infoArr['d']['pool'], $infoArr['d']['xcard']);
            } else {
                //获取是否新手保护
                $rookieBuff = $infoArr['d']['rookie_buff'] ? $infoArr['d']['rookie_buff'] : 0;
                $user_wallet = isset($infoArr['d']['user_wallet']) ? $infoArr['d']['user_wallet'] : 0;
                //真人发牌
                $obj = new AiRummyDealCardProfitToPlayer($infoArr['d']['uid'], $infoArr['d']['rtype'], $infoArr['d']['pool'], $infoArr['d']['xcard'], $rookieBuff, $infoArr['d']['rid'], $user_wallet);
            }
            $rtn['res']['hand'] = $obj->getDealCards();
            $rtn['res']['pool'] = $obj->getCardPool();
        } catch (\Exception $e) {
            $rtn['code'] = $e->getCode();
            $rtn['message'] = $e->getMessage();
        }

        return $rtn;
    }

    //盈利率模式 批量发牌
    public static function dealCardsByProfitBat(): array
    {
        $rtn = [
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => [
                'hand_list' => [],
                'pool' => [],
            ]
        ];
        try {
            $info = Request::getJsonBody();
            if (!$info) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['POST_EMPTY_ERR'], ErrorCode::ERR_CODE['POST_EMPTY_ERR']);
            }
            $infoArr = json_decode($info, 1);

            if (!isset($infoArr['d']['rtype']) || !$infoArr['d']['rtype']) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['R_ROOM_TYPE_ERR'], ErrorCode::ERR_CODE['R_ROOM_TYPE_ERR']);
            }
            if (!isset($infoArr['d']['mcard']) || !$infoArr['d']['mcard']) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['CARD_MAX_CARD_ERROR'], ErrorCode::ERR_CODE['CARD_MAX_CARD_ERROR']);
            }
            //获取需要group的一维数组手牌
            if (!isset($infoArr['d']['pool']) || !is_array($infoArr['d']['pool']) || count($infoArr['d']['pool']) < $infoArr['d']['mcard']) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['CARD_POOL_ERROR'], ErrorCode::ERR_CODE['CARD_POOL_ERROR']);
            }
            //获取本次group的癞子牌
            if (!isset($infoArr['d']['xcard']) || $infoArr['d']['xcard'] <= 0) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['L_CARD_ERR'], ErrorCode::ERR_CODE['L_CARD_ERR']);
            }
            //获取本次批量发牌的用户id列表
            if (!isset($infoArr['d']['ulist']) || !is_array($infoArr['d']['ulist']) || empty($infoArr['d']['ulist'])) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['UID_ERROR'], ErrorCode::ERR_CODE['UID_ERROR']);
            }
            //挑选出真人类型的id
            $userTypeList = array_column($infoArr['d']['ulist'], 'uid', 'utype');
            if (!isset($userTypeList[Common::PLAYER_TYPE_PEOPLE])) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['UID_ERROR'], ErrorCode::ERR_CODE['UID_ERROR']);
            }
            //是否有新手保护存在
            $rookieBuffList = array_column($infoArr['d']['ulist'], 'rookie_buff');
            if (in_array(1, $rookieBuffList)) {
                $rookieBuff = true;
            } else {
                $rookieBuff = false;
            }
            Log::console_log_room_id(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__, $infoArr['d']['rid'], '是否有新手保护：' . json_encode($rookieBuff));
            if ($rookieBuff) {
                //执行新手保护批量发牌策略
                $dealList = AiRummyDealCardBat::dealCardsBatForRookieBuff($infoArr['d']['ulist'], $infoArr['d']['rtype'], $infoArr['d']['pool'], $infoArr['d']['xcard'], $infoArr['d']['rid']);
            } else {
                $dealList = AiRummyDealCardBat::dealCardsBatForNormal($infoArr['d']['ulist'], $infoArr['d']['rtype'], $infoArr['d']['pool'], $infoArr['d']['xcard'], $infoArr['d']['rid']);
            }
            $rtn['res']['hand_list'] = $dealList['userDealCardsList'];
            $rtn['res']['pool'] = $dealList['cardsPool'];
        } catch (\Exception $e) {
            $rtn['code'] = $e->getCode();
            $rtn['message'] = $e->getMessage();
            $rtn['res']['pool'] = $infoArr['d']['pool'];
        }

        return $rtn;
    }

    //盈利率模式 判定是否全部匹配Ai
    public static function matchAllAi(): array
    {
        $rtn = [
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => [
                'match_all_ai' => false,
            ]
        ];
        try {
            $info = Request::getJsonBody();
            //            Log::console_log(__FUNCTION__,'接受到的参数：'.$info);
            if (!$info) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['POST_EMPTY_ERR'], ErrorCode::ERR_CODE['POST_EMPTY_ERR']);
            }
            $infoArr = json_decode($info, 1);
            if (!isset($infoArr['d']['rtype']) || !$infoArr['d']['rtype']) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['R_ROOM_TYPE_ERR'], ErrorCode::ERR_CODE['R_ROOM_TYPE_ERR']);
            }
            //获取本次批量发牌的用户id列表
            if (!isset($infoArr['d']['uid']) || !$infoArr['d']['uid']) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['UID_ERROR'], ErrorCode::ERR_CODE['UID_ERROR']);
            }
            //获取是否新手保护  新手保护阶段不执行
            $rookieBuff = $infoArr['d']['rookie_buff'] ? $infoArr['d']['rookie_buff'] : 0;
            if ($rookieBuff) {
                //throw new \Exception(ErrorCode::ERR_TEXT_EN['API_SUCCESS'], ErrorCode::ERR_CODE['API_SUCCESS']);
                $rtn['res']['match_all_ai'] = true;
            }

        } catch (\Exception $e) {
            $rtn['code'] = $e->getCode();
            $rtn['message'] = $e->getMessage();
        }
        //        Log::console_log(__FUNCTION__,'返回的参数：'.json_encode($rtn));
        return $rtn;
    }

    //盈利率 获取奖池名称
    public static function getJackPoolNameByProfit(): array
    {
        $rtn = [
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => [
                'jack_list' => '',
                'jack_pool' => '',
                'jack_pool_ai' => '',
            ]
        ];


        /*
jack_pool:
"1000_sloats"
jack_pool_ai:
"1000_sloats_1"
jack_list:
"profit_jackpot_list_1"
*/

        try {
            $info = Request::getJsonBody();
            //            Log::console_log(__FUNCTION__,'接受到的参数：'.$info);
            if (!$info) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['POST_EMPTY_ERR'], ErrorCode::ERR_CODE['POST_EMPTY_ERR']);
            }
            $infoArr = json_decode($info, 1);
            if (!isset($infoArr['d']['rtype']) || !$infoArr['d']['rtype']) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['R_ROOM_TYPE_ERR'], ErrorCode::ERR_CODE['R_ROOM_TYPE_ERR']);
            }
            $roomInfo = parse_room_type($infoArr['d']['rtype']);
            if ($roomInfo['cls'] == Common::GAME_TYPE_TEEN_PATTI) {
                //此处需要去除游戏逻辑服redis前缀
                // tp_profit_jackpot_list_1
                $rtn['res']['jack_list'] = str_replace(Config::get('game_redis_prefix'), '', Config::get('tp_profit_jackpot_key')) . '_' . $roomInfo['currency'];
            } else {
                //此处需要去除游戏逻辑服redis前缀
                // profit_jackpot_list_1
                $rtn['res']['jack_list'] = str_replace(Config::get('game_redis_prefix'), '', Config::get('profit_jackpot_key')) . '_' . $roomInfo['currency'];
            }
            $rtn['res']['jack_pool'] = Common::getPoolNameByClsAndBase($roomInfo['cls'], $roomInfo['base']);
            $rtn['res']['jack_pool_ai'] = Common::getPoolNameByClsAndBase($roomInfo['cls'], $roomInfo['base']) . '_' . $roomInfo['currency'];
        } catch (\Exception $e) {
            $rtn['code'] = $e->getCode();
            $rtn['message'] = $e->getMessage();
        }
        //        Log::console_log(__FUNCTION__,'返回的参数：'.json_encode($rtn));
        return $rtn;
    }


    //盈利率 当前用户暗牌是否是癞子
    public static function DrawDarkCardByProfit(): array
    {
        //        $rtn = [
//            'code'=>ErrorCode::ERR_CODE['API_SUCCESS'],
//            'message'=>ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
//            'res'=>[
//                'dark_card' => '',
//            ]
//        ];
        $rtn = [
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => [
                'xcard' => '',
                'pool' => [],
            ]
        ];
        try {
            $info = Request::getJsonBody();

            if (!$info) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['POST_EMPTY_ERR'], ErrorCode::ERR_CODE['POST_EMPTY_ERR']);
            }
            $infoArr = json_decode($info, 1);

            if (!isset($infoArr['d']['rtype']) || !$infoArr['d']['rtype']) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['R_ROOM_TYPE_ERR'], ErrorCode::ERR_CODE['R_ROOM_TYPE_ERR']);
            }
            //获取本次group的用户身份
            if (!isset($infoArr['d']['utype']) || $infoArr['d']['utype'] < 0) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['UTYPE_ERROR'], ErrorCode::ERR_CODE['UTYPE_ERROR']);
            }
            //获取本次group的用户id
            if (!isset($infoArr['d']['pool']) || $infoArr['d']['pool'] < 0) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['CARD_POOL_ERROR'], ErrorCode::ERR_CODE['CARD_POOL_ERROR']);
            }
            //获取本次group的用户id
            if (!isset($infoArr['d']['xcard']) || $infoArr['d']['xcard'] < 0) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['L_CARD_ERR'], ErrorCode::ERR_CODE['L_CARD_ERR']);
            }
            //获取本次group的用户id
            if (!isset($infoArr['d']['uid']) || $infoArr['d']['uid'] < 0) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['UID_ERROR'], ErrorCode::ERR_CODE['UID_ERROR']);
            }
            //当前摸牌回合数
            if (!isset($infoArr['d']['circle']) || $infoArr['d']['circle'] < 0) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['CIRCLE_NUM_ERROR'], ErrorCode::ERR_CODE['CIRCLE_NUM_ERROR']);
            }
            //            $obj = new AiRummyDrawDarkCard(
//                $infoArr['d']['uid'],
//                $infoArr['d']['cards'],//新增
//                $infoArr['d']['ulist'],//新增
//                $infoArr['d']['rtype'],
//                $infoArr['d']['utype'],
//                $infoArr['d']['xcard'],
//                $infoArr['d']['pool'],
//                $infoArr['d']['circle']
//            );
//            $rtn['res']['dark_card'] = $obj->getDarkCard();
            $rtn['res']['xcard'] = 0;
            $rtn['res']['pool'] = $infoArr['d']['pool'];
        } catch (\Exception $e) {
            $rtn['code'] = $e->getCode();
            $rtn['message'] = $e->getMessage();
        }

        return $rtn;
    }



    //ai携带金额 初始化 重置
    public static function changeNameAndHeader(): array
    {
        $rtn = [
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => [
                'ulist' => [],
            ]
        ];
        try {
            $info = Request::getJsonBody();
            //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__,'接受到的参数：'.$info);
            if (!$info) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['POST_EMPTY_ERR'], ErrorCode::ERR_CODE['POST_EMPTY_ERR']);
            }
            $infoArr = json_decode($info, 1);
            if (!isset($infoArr['d']['ulist']) || !$infoArr['d']['ulist']) {
                throw new \Exception(ErrorCode::ERR_TEXT_EN['ULIST_ERROR'], ErrorCode::ERR_CODE['ULIST_ERROR']);
            }
            //获取下注结果
            $obj = new AiDealerGameBalanceAmount($infoArr['d']['ulist']);
            $obj->setAiList($infoArr['d']['ulist']);
            $rtn['res']['ulist'] = $obj->getAiListChangeNameAndHeader();
        } catch (\Exception $e) {
            $rtn['code'] = $e->getCode();
            $rtn['message'] = $e->getMessage();
        }
        //Log::console_log(substr(strrchr(__CLASS__, "\\"), 1) . '.' . __FUNCTION__,'返回的参数：'.json_encode($rtn));
        return $rtn;
    }


}
