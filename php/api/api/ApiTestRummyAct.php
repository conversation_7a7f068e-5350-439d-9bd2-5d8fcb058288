<?php
/**
 * @todo Ai 接口
 */

namespace api;

use llogic\common\Struct;

use common\AiRummy\AutoGroup\AiRummyAutoGroup;
use common\AiRummy\AiRummyDealCardScore;
use common\AiRummy\AiRummyDisCard;
use common\Common;
use common\ErrorCode;
use common\AiRummy\AiRummyDealCardProfit as AiRummyDealCardProfit;
use lib\Log;

class ApiTestRummyAct
{

    //盈利模式测试3绿 首轮胡牌情况
    public static function test3LvFinishRatePr(): array
    {
        $pool = init_card_pool(2);
        $xcard = array_pop($pool);
        $maxCardNum = 13;
        $obj = new AiRummyDealCardProfit($pool, $xcard, $maxCardNum);
        $groupName = Common::PROFIT_GROUP_NAME_NOLV_NOLAI;
        $dealCards = $obj->getDealCardsByGroup($groupName);

        $obj = new AiRummyAutoGroup($dealCards, $xcard);
        //http://172.21.0.190/declear_test.php
        $score = $obj->getHandCardsScore();
        $deal = $obj->getDeclearCards();

        $rtn = array(
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => [
                'hand' => $deal,
                'xcard' => $xcard,
                'score' => $score,
            ]
        );



        //$pool = $obj->getPool();
        //shuffle($pool);
        //$drawCard = array_pop($pool);
        //array_push($dealCards,$drawCard);

        //执行胡牌判定
        //$obj = new AiRummyDisCard($dealCards,$xcard,$maxCardNum);
        //$finishRes = $obj->getCanFinishRes();
//        $rtn = array(
//            'code'=>ErrorCode::ERR_CODE['API_SUCCESS'],
//            'message'=>ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
//            'res'=>[
//                'dealCard'=>$dealCards,
//                'xcard'=>$xcard,
//                //'finishRes' => $finishRes,
//            ]
//        );
//        if($finishRes){
//            Log::console_log(__FUNCTION__,'返回的参数：'.json_encode($rtn));
//        }
        return $rtn;

    }



    //净胜分模式测试3绿  首轮胡牌情况
    public static function test3LvFinishRateScore(): array
    {
        $pool = init_card_pool(2);
        $xcard = array_pop($pool);
        $maxCardNum = 13;
        $obj = new AiRummyDealCardScore($pool, $xcard, $maxCardNum);
        $groupName = Common::SCORE_GROUP_NAME_3_CHUN_SHUN;
        $dealCards = $obj->getDealCardsByGroup($groupName);
        $pool = $obj->getPool();
        shuffle($pool);
        $drawCard = array_pop($pool);
        array_push($dealCards, $drawCard);

        //执行胡牌判定
        $obj = new AiRummyDisCard($dealCards, $xcard, $maxCardNum);
        $finishRes = $obj->getCanFinishRes();
        $rtn = array(
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => [
                'dealCard' => $dealCards,
                'xcard' => $xcard,
                'finishRes' => $finishRes,
            ]
        );
        if ($finishRes) {

        }

        return $rtn;
    }



}
