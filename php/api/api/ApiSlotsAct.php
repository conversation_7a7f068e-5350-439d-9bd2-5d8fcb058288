<?php
/**
 * @todo slots 发牌接口
 */
namespace api;

use llogic\common\Struct;

use common\AiHundred\AiSlotsDealCard;
use common\Common;
use common\ErrorCode;
use Exception;
use lib\Log;
use lib\Request;


class ApiSlotsAct implements ApiHundredInterfaceAct
{

    //盈利率 AB玩法 结算发牌信息
    public static function dealCards(): array
    {
        $rtn = [
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => [
                'cards' => [],
                'win_raise_id' => [],
                'scattr_info' => [],
                'start' => 0,
            ]
        ];
        try {
            $info = Request::getJsonBody();
            if (!$info) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['POST_EMPTY_ERR'], ErrorCode::ERR_CODE['POST_EMPTY_ERR']);
            }
            $infoArr = json_decode($info, 1);
            if (!isset($infoArr['d']['rtype']) || !$infoArr['d']['rtype']) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['R_ROOM_TYPE_ERR'], ErrorCode::ERR_CODE['R_ROOM_TYPE_ERR']);
            }
            if (!isset($infoArr['d']['uid'])) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['RAISE_LIST_ERROR'], ErrorCode::ERR_CODE['RAISE_LIST_ERROR']);
            }
            if (!isset($infoArr['d']['rutype'])) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['RAISE_LIST_ERROR'], ErrorCode::ERR_CODE['RAISE_LIST_ERROR']);
            }
            if (!isset($infoArr['d']['all_win_amount'])) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['RAISE_LIST_ERROR'], ErrorCode::ERR_CODE['RAISE_LIST_ERROR']);
            }
            if (!isset($infoArr['d']['all_bet_amount'])) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['RAISE_LIST_ERROR'], ErrorCode::ERR_CODE['RAISE_LIST_ERROR']);
            }
            if (!isset($infoArr['d']['is_free'])) {
                throw new Exception(ErrorCode::ERR_TEXT_EN['RAISE_LIST_ERROR'], ErrorCode::ERR_CODE['RAISE_LIST_ERROR']);
            }
            if (!isset($infoArr['d']['wild_step_info'])) {
                $infoArr['d']['wild_step_info'] = [];
            }
            if (!isset($infoArr['d']['raise_user_wallet_list'])) {
                $infoArr['d']['raise_user_wallet_list'] = [];
            }

            $room_type = $infoArr['d']['rtype'];
            Struct::room_type_to_game_name_base_rupee_type($room_type, $game_name, $base_rupee);

            //获取最终的发牌
            $dealCardsObj = AiSlotsDealCard::create(
                $infoArr['d']['rtype'],
                $infoArr['d']['is_free'],
                $infoArr['d']['wild_step_info'],
                $infoArr['d']['uid'],
                $infoArr['d']['bet_amount'],
                $infoArr['d']['rutype'],
                $infoArr['d']['all_win_amount'],
                $infoArr['d']['all_bet_amount'],
                $infoArr['d']['raise_user_wallet_list'],
            );
            $rtn['res']['cards'] = $dealCardsObj->getDealCards();
            $rtn['res']['win_raise_id'] = $dealCardsObj->getWindRaiseList();
            $rtn['res']['scattr_info'] = $dealCardsObj->getScattrInfo();
            $rtn['res']['wild_step_info'] = $dealCardsObj->getWildAndTopStepInfo();
            //获取发牌开始位置
            $rtn['res']['start'] = $dealCardsObj->getDealCardsStartKey();


            $dealCardsObj->SendMetrics();
        } catch (Exception $e) {
            $rtn['code'] = $e->getCode();
            $rtn['message'] = $e->getMessage();
        }

        return $rtn;
    }

    public static function clientShowInfo(): array
    {
        $info = Request::getJsonBody();
        $roomType = "";
        if ($info) {
            $infoArr = json_decode($info, 1);
            $roomType = $infoArr['d']['rtype'];
        }
        $roomInfo = parse_room_type($roomType);
        if ($roomInfo['cls'] == Common::GAME_TYPE_SLOATS_LOTUS) {
            return self::clientShowInfoLotus();
        } else {
            return self::clientShowInfoGanesha();
        }
    }
    //客户端获取展示的赔率信息
    private static function clientShowInfoGanesha(): array
    {
        $rtn = [
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => [
                'client_show_info' => [],
            ]
        ];
        foreach (SLOTS_X_OPTION as $id => $info) {
            $tmpInfo = [];
            $tmpInfo['id'] = $id;
            if ($id == SLOTS_SCATTR) {
                $tmpInfo['type'] = 2;
                $info = SLOTS_FREE_SPIN;
            } elseif ($id > SLOTS_SCATTR) {
                $tmpInfo['type'] = 1;
            } else {
                $tmpInfo['type'] = 0;
            }
            foreach ($info as $cnt => $x) {
                $tmpInfo['odds'][] = ['n' => $cnt, 'o' => $x];
            }

            $rtn['res']['client_show_info'][] = $tmpInfo;
        }
        return $rtn;
    }


    //客户端获取展示的赔率信息
    private static function clientShowInfoLotus(): array
    {
        $rtn = [
            'code' => ErrorCode::ERR_CODE['API_SUCCESS'],
            'message' => ErrorCode::ERR_TEXT_EN['API_SUCCESS'],
            'res' => [
                'client_show_info' => [],
            ]
        ];
        $optionList = SLOTS_LOUTS_X_OPTION;
        foreach (SLOTS_LOUTS_WILD_LIST as $id) {
            $optionList[$id] = [];
        }
        foreach ($optionList as $id => $info) {
            $tmpInfo = [];
            $tmpInfo['id'] = $id;
            if (in_array($id, SLOTS_LOUTS_WILD_LIST)) {
                $tmpInfo['type'] = 1;
            } else {
                $tmpInfo['type'] = 0;
            }
            $tmpInfo['odds'] = [];
            foreach ($info as $cnt => $x) {
                $tmpInfo['odds'][] = ['n' => $cnt, 'o' => $x];
            }

            $rtn['res']['client_show_info'][] = $tmpInfo;
        }
        return $rtn;
    }


    // ai模拟下注
    public static function betAmount(): array
    {
        // TODO: Implement betAmount() method.
        return [];
    }


    public static function initAiTableInfo(): array
    {
        // TODO: Implement initAiTableInfo() method.
        return [];
    }


    public static function balanceAmountList(): array
    {
        // TODO: Implement balanceAmountList() method.
        return [];
    }

}
