<?php error_reporting(0);//不会弹出警告，只适合当前页?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <title>AI-PROFIT-RATE-CONFIG</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="A fully featured admin theme which can be used to build CRM, CMS, etc." name="description" />
    <meta content="Coderthemes" name="author" />
    <!-- App favicon -->
    <link rel="shortcut icon" href="/resource/assets/images/favicon.ico">

    <!-- App css -->
    <link href="/resource/assets/css/icons.min.css" rel="stylesheet" type="text/css" />
    <link href="/resource/assets/css/app.min.css?v=11" rel="stylesheet" type="text/css" />
    <style>
        .actives{
            color: #727cf5;
            border-bottom: 2px solid #727cf5;
        }
        .table td, .table th {
            padding:0px;
        }
        .form-control {
            display: block;
            width: 100%;
            height: 20px;
            padding: .45rem .9rem;
            font-size: .875rem;
            font-weight: 400;
            line-height: 0.5;
            color: #6c757d;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #dee2e6;
            border-radius: .25rem;
            -webkit-transition: border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
            transition: border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
            transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
            transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
        }
    </style>
</head>
<body class="bg">
<div class="container-fluid">

    <!-- Begin page -->
    <div class="wrapper">
        <!-- ========== Left Sidebar Start ========== -->
        <?php require_once realpath(dirname(__FILE__). '/../').'/Public/nav.html';?>
        <!-- Left Sidebar End -->

        <div class="content-page">

            <div class="content">

                <div class="row">

                    <div class="col-lg-12" style="border: 1px solid #e3eaef;padding: 20px;">
                        <h4>PR配置</h4>
                        <?php $edit_pr_url =  \lib\Url::getAjaxUrl('editPrConfig');?>
                        <button type="button" class="btn btn-outline-success" style="margin-bottom: 10px;" onclick="ajax_post_form_submit(this,'profit_rate_config_form','<?php echo $edit_pr_url;?>');"> <i class="mdi mdi-content-save"></i> <span>保存</span> </button>
                        <form id="profit_rate_config_form">
                            <table class="table table-hover table-centered mb-0">
                                <thead>
                                    <tr>
                                        <th>序号<br></th>
                                        <th>预设节点<br>P=0.5*Df+Dc</th>
                                        <th>扶植极值<br>d_limit</th>
                                        <th>扶植起点<br>d_begin</th>
                                        <th>限制起点<br>u_begin</th>
                                        <th>限制极值<br>u_limit</th>
                                        <th>扶植公式弧度参数<br>ed</th>
                                        <th>限制公式弧度参数<br>eu</th>
                                    </tr>
                                </thead>
                                <tbody>

                                <?php for($i=1;$i<=12;$i++){?>
                                    <?php if(isset($pr_config[$i])){?>
                                        <tr>
                                            <td><?php echo $i;?></td>
                                            <td title="P">          <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][p]"          value="<?php echo $pr_config[$i]['p'];?>">    </td>
                                            <td title="d_limit">    <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][d_limit]"    value="<?php echo $pr_config[$i]['d_limit'];?>">    </td>
                                            <td title="d_begin">    <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][d_begin]"    value="<?php echo $pr_config[$i]['d_begin'];?>">    </td>
                                            <td title="u_begin">    <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][u_begin]"    value="<?php echo $pr_config[$i]['u_begin'];?>">    </td>
                                            <td title="u_limit">    <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][u_limit]"    value="<?php echo $pr_config[$i]['u_limit'];?>">    </td>
                                            <td title="ed">         <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][ed]"         value="<?php echo $pr_config[$i]['ed'];?>">    </td>
                                            <td title="eu">         <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][eu]"         value="<?php echo $pr_config[$i]['eu'];?>">    </td>
                                        </tr>

                                    <?php }else{?>
                                        <tr>
                                            <td><?php echo $i;?></td>
                                            <td title="P">          <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][p]"          value="">    </td>
                                            <td title="d_limit">    <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][d_limit]"    value="">    </td>
                                            <td title="d_begin">    <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][d_begin]"    value="">    </td>
                                            <td title="u_begin">    <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][u_begin]"    value="">    </td>
                                            <td title="u_limit">    <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][u_limit]"    value="">    </td>
                                            <td title="ed">         <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][ed]"         value="">    </td>
                                            <td title="eu">         <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][eu]"         value="">    </td>
                                        </tr>
                                    <?php }?>
                                <?php }?>


                                </tbody>
                            </table>
                        </form>
                    </div>
                </div>

                <div class="row" >
                    <!--<div class="col-lg-4" style="border: 1px solid #e3eaef;padding: 20px;">

                        <?php $edit_lucky_url =  \lib\Url::getAjaxUrl('editLuckyConfig');?>

                        <form id="lucky_config_form">
                            <h4>好运配置</h4>
                            <button type="button" class="btn btn-outline-success" style="margin-bottom: 10px;" onclick="ajax_post_form_submit(this,'lucky_config_form','<?php echo $edit_lucky_url;?>');"> <i class="mdi mdi-content-save"></i> <span>保存</span> </button>
                            <table class="table table-hover table-centered mb-0">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>次数</th>
                                        <th>上限</th>
                                        <th>幸运触发阈值</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php for($i=1;$i<=6;$i++){?>
                                        <?php if(isset($lucky_config[$i])){?>
                                            <tr>
                                                <td><?php echo $i;?></td>
                                                <td><input type="text" class="form-control" name="lucky_config[<?php echo $i;?>][num]"         value="<?php echo isset($lucky_config[$i]['num'])?$lucky_config[$i]['num']:0;?>"></td>
                                                <td><input type="text" class="form-control" name="lucky_config[<?php echo $i;?>][pool_amount]" value="<?php echo isset($lucky_config[$i]['pool_amount'])?$lucky_config[$i]['pool_amount']:0;?>"></td>
                                                <td><input type="text" class="form-control" name="lucky_config[<?php echo $i;?>][threshold]"   value="<?php echo isset($lucky_config[$i]['threshold'])?$lucky_config[$i]['threshold']:0;?>"></td>
                                            </tr>
                                        <?php }else{ ?>
                                            <tr>
                                                <td><?php echo $i;?></td>
                                                <td><input type="text" class="form-control" name="lucky_config[<?php echo $i;?>][num]"         value=""></td>
                                                <td><input type="text" class="form-control" name="lucky_config[<?php echo $i;?>][pool_amount]" value=""></td>
                                                <td><input type="text" class="form-control" name="lucky_config[<?php echo $i;?>][threshold]" value=""></td>
                                            </tr>
                                        <?php }?>
                                    <?php }?>
                                </tbody>
                            </table>
                        </form>
                    </div>
                    <div class="col-lg-4" style="border: 1px solid #e3eaef;padding: 20px;">
                        <?php $edit_unlucky_url =  \lib\Url::getAjaxUrl('editUnLuckyConfig');?>

                        <form id="unlucky_config_form">
                            <h4>霉运配置</h4>
                            <button type="button" class="btn btn-outline-success" style="margin-bottom: 10px;" onclick="ajax_post_form_submit(this,'unlucky_config_form','<?php echo $edit_unlucky_url;?>');"> <i class="mdi mdi-content-save"></i> <span>保存</span> </button>
                            <table class="table table-hover table-centered mb-0">
                                <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>次数</th>
                                    <th>上限</th>
                                    <th>霉运触发阈值</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php for($i=1;$i<=6;$i++){?>
                                <?php if(isset($unlucky_config[$i])){?>
                                <tr>
                                    <td><?php echo $i;?></td>
                                    <td><input type="text" class="form-control" name="unlucky_config[<?php echo $i;?>][num]"         value="<?php echo isset($unlucky_config[$i]['num'])?$unlucky_config[$i]['num']:0;?>"></td>
                                    <td><input type="text" class="form-control" name="unlucky_config[<?php echo $i;?>][pool_amount]" value="<?php echo isset($unlucky_config[$i]['pool_amount'])?$unlucky_config[$i]['pool_amount']:0;?>"></td>
                                    <td><input type="text" class="form-control" name="unlucky_config[<?php echo $i;?>][threshold]"   value="<?php echo isset($unlucky_config[$i]['threshold'])?$unlucky_config[$i]['threshold']:0;?>"></td>
                                </tr>
                                <?php }else{ ?>
                                <tr>
                                    <td><?php echo $i;?></td>
                                    <td><input type="text" class="form-control" name="unlucky_config[<?php echo $i;?>][num]"         value=""></td>
                                    <td><input type="text" class="form-control" name="unlucky_config[<?php echo $i;?>][pool_amount]" value=""></td>
                                    <td><input type="text" class="form-control" name="unlucky_config[<?php echo $i;?>][threshold]"   value=""></td>
                                </tr>
                                <?php }?>
                                <?php }?>
                                </tbody>
                            </table>
                        </form>
                    </div>-->





                </div>


                <div class="row">
                    <div class="col-lg-4" style="border: 1px solid #e3eaef;padding: 20px;">
                        <h4>13card发牌配置</h4>
                        <?php $edit_pr_url =  \lib\Url::getAjaxUrl('editZDeal13CardRateConfig');?>
                        <button type="button" class="btn btn-outline-success" style="margin-bottom: 10px;" onclick="ajax_post_form_submit(this,'group_13_profit_rate_config','<?php echo $edit_pr_url;?>');"> <i class="mdi mdi-content-save"></i> <span>保存</span> </button>
                        <form id="group_13_profit_rate_config">
                            <table class="table table-hover table-centered mb-0">
                                <thead>
                                <tr>
                                    <th style="width: 22%">牌型<br></th>
                                    <th style="width: 13%">基础权重</th>
                                    <th style="width: 13%">扶植系数</th>
                                    <th style="width: 13%">抑制系数</th>
                                    <!--<th style="width: 13%">幸运权重</th>
                                    <th style="width: 13%">霉运权重</th>-->
                                    <th style="width: 13%">新手权重</th>
                                    <th style="width: 13%">新充权重</th>
                                </tr>
                                </thead>
                                <tbody>

                                <?php foreach($pr_group_name_list as $i=>$v){?>
                                <?php if(isset($group_13_profit_rate_config[$i])){?>
                                <tr>
                                    <td><?php echo $v;?></td>
                                    <td title="normal">     <input type="text" class="form-control" name="group_13_profit_rate_config[<?php echo $i;?>][normal]"      value="<?php echo $group_13_profit_rate_config[$i]['normal'];?>">     </td>
                                    <td title="up">         <input type="text" class="form-control" name="group_13_profit_rate_config[<?php echo $i;?>][up]"          value="<?php echo $group_13_profit_rate_config[$i]['up'];?>">         </td>
                                    <td title="down">       <input type="text" class="form-control" name="group_13_profit_rate_config[<?php echo $i;?>][down]"        value="<?php echo $group_13_profit_rate_config[$i]['down'];?>">       </td>
                                    <!--<td title="lucky">      <input type="text" class="form-control" name="group_13_profit_rate_config[<?php echo $i;?>][lucky]"       value="<?php echo $group_13_profit_rate_config[$i]['lucky'];?>">      </td>
                                    <td title="unlucky">    <input type="text" class="form-control" name="group_13_profit_rate_config[<?php echo $i;?>][unlucky]"     value="<?php echo $group_13_profit_rate_config[$i]['unlucky'];?>">-->    </td>
                                    <td title="novice">     <input type="text" class="form-control" name="group_13_profit_rate_config[<?php echo $i;?>][novice]"      value="<?php echo $group_13_profit_rate_config[$i]['novice'];?>">     </td>
                                    <td title="recharge">     <input type="text" class="form-control" name="group_13_profit_rate_config[<?php echo $i;?>][recharge]"      value="<?php echo $group_13_profit_rate_config[$i]['recharge'];?>">     </td>
                                </tr>

                                <?php }else{?>
                                <tr>
                                    <td><?php echo $v;?></td>
                                    <td title="normal">     <input type="text" class="form-control" name="group_13_profit_rate_config[<?php echo $i;?>][normal]" >    </td>
                                    <td title="up">         <input type="text" class="form-control" name="group_13_profit_rate_config[<?php echo $i;?>][up]"     >    </td>
                                    <td title="down">       <input type="text" class="form-control" name="group_13_profit_rate_config[<?php echo $i;?>][down]"   >    </td>
                                    <!--<td title="lucky">      <input type="text" class="form-control" name="group_13_profit_rate_config[<?php echo $i;?>][lucky]"  >    </td>
                                    <td title="unlucky">    <input type="text" class="form-control" name="group_13_profit_rate_config[<?php echo $i;?>][unlucky]">    </td>-->
                                    <td title="novice">     <input type="text" class="form-control" name="group_13_profit_rate_config[<?php echo $i;?>][novice]">    </td>
                                    <td title="recharge">     <input type="text" class="form-control" name="group_13_profit_rate_config[<?php echo $i;?>][recharge]">    </td>
                                </tr>
                                <?php }?>
                                <?php }?>
                                </tbody>
                            </table>
                        </form>
                    </div>


                    <div class="col-lg-4" style="border: 1px solid #e3eaef;padding: 20px;">
                        <h4>新手保护发牌配置</h4>
                        <?php $edit_pr_rookie_buff_url =  \lib\Url::getAjaxUrl('editZDealCardRookieBuffConfig');?>
                        <button type="button" class="btn btn-outline-success" style="margin-bottom: 10px;" onclick="ajax_post_form_submit(this,'pr_rookie_buff_deal_card_config','<?php echo $edit_pr_rookie_buff_url;?>');"> <i class="mdi mdi-content-save"></i> <span>保存</span> </button>
                        <form id="pr_rookie_buff_deal_card_config">
                            <table class="table table-hover table-centered mb-0">
                                <thead>
                                <tr>
                                    <th style="width: 22%">牌型<br></th>
                                    <th style="width: 13%">[13]真人</th>
                                    <th style="width: 13%">[13]AI</th>
                                    <th style="width: 13%">[10]真人</th>
                                    <th style="width: 13%">[10]AI</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php foreach($pr_group_name_list as $i=>$v){?>
                                <?php if(isset($pr_rookie_buff_deal_card_config[$i])){?>
                                <tr>
                                    <td><?php echo $v;?></td>
                                    <td title="z13">     <input type="text" class="form-control" name="pr_rookie_buff_deal_card_config[<?php echo $i;?>][z13]" value="<?php echo $pr_rookie_buff_deal_card_config[$i]['z13'];?>">  </td>
                                    <td title="a13">     <input type="text" class="form-control" name="pr_rookie_buff_deal_card_config[<?php echo $i;?>][a13]" value="<?php echo $pr_rookie_buff_deal_card_config[$i]['a13'];?>">  </td>
                                    <td title="z10">     <input type="text" class="form-control" name="pr_rookie_buff_deal_card_config[<?php echo $i;?>][z10]" value="<?php echo $pr_rookie_buff_deal_card_config[$i]['z10'];?>">  </td>
                                    <td title="a10">     <input type="text" class="form-control" name="pr_rookie_buff_deal_card_config[<?php echo $i;?>][a10]" value="<?php echo $pr_rookie_buff_deal_card_config[$i]['a10'];?>">  </td>
                                </tr>

                                <?php }else{?>
                                <tr>
                                    <td><?php echo $v;?></td>
                                    <td title="z13">     <input type="text" class="form-control" name="pr_rookie_buff_deal_card_config[<?php echo $i;?>][z13]">    </td>
                                    <td title="a13">     <input type="text" class="form-control" name="pr_rookie_buff_deal_card_config[<?php echo $i;?>][a13]">    </td>
                                    <td title="z10">     <input type="text" class="form-control" name="pr_rookie_buff_deal_card_config[<?php echo $i;?>][z10]">    </td>
                                    <td title="a10">     <input type="text" class="form-control" name="pr_rookie_buff_deal_card_config[<?php echo $i;?>][a10]">    </td>
                                </tr>
                                <?php }?>
                                <?php }?>


                                </tbody>
                            </table>
                        </form>
                    </div>

                    <div class="col-lg-4" style="border: 1px solid #e3eaef;padding: 20px;">
                        <h4>摸牌控制概率</h4>
                        <?php $edit_pr_rookie_buff_url =  \lib\Url::getAjaxUrl('editDarkConfig');?>
                        <button type="button" class="btn btn-outline-success" style="margin-bottom: 10px;" onclick="ajax_post_form_submit(this,'pr_control_draw_dark_card_config','<?php echo $edit_pr_rookie_buff_url;?>');"> <i class="mdi mdi-content-save"></i> <span>保存</span> </button>
                        <form id="pr_control_draw_dark_card_config ">
                            <table class="table table-hover table-centered mb-0">
                                <thead>
                                <tr>
                                    <th style="width: 22%">牌型<br></th>
                                    <th style="width: 13%">扶植概率</th>
                                    <th style="width: 13%">抑制概率</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php foreach($pr_group_name_all_list as $i=>$v){?>
                                <?php if(isset($pr_rookie_buff_deal_card_config[$i])){?>
                                <tr>
                                    <td><?php echo $v;?></td>
                                    <td title="up13">     <input type="text" class="form-control" name="pr_control_draw_duck_card_config[<?php echo $i;?>][up13]" value="<?php echo $pr_control_draw_duck_card_config[$i]['up13'];?>">  </td>
                                    <td title="down13">     <input type="text" class="form-control" name="pr_control_draw_duck_card_config[<?php echo $i;?>][down13]" value="<?php echo $pr_control_draw_duck_card_config[$i]['down13'];?>">  </td>
                                </tr>
                                <?php }else{?>
                                <tr>
                                    <td><?php echo $v;?></td>
                                    <td title="up13">     <input type="text" class="form-control" name="pr_control_draw_duck_card_config[<?php echo $i;?>][up13]">    </td>
                                    <td title="down13">     <input type="text" class="form-control" name="pr_control_draw_duck_card_config[<?php echo $i;?>][down13]">    </td>
                                </tr>
                                <?php }?>
                                <?php }?>


                                </tbody>
                            </table>
                        </form>
                    </div>

                    <!--<div class="col-lg-6" style="border: 1px solid #e3eaef;padding: 20px;">
                        <h4>10card发牌配置</h4>
                        <?php $edit_pr_url =  \lib\Url::getAjaxUrl('editZDeal10CardRateConfig');?>
                        <button type="button" class="btn btn-outline-success" style="margin-bottom: 10px;" onclick="ajax_post_form_submit(this,'group_10_profit_rate_config','<?php echo $edit_pr_url;?>');"> <i class="mdi mdi-content-save"></i> <span>保存</span> </button>
                        <form id="group_10_profit_rate_config">
                            <table class="table table-hover table-centered mb-0">
                                <thead>
                                <tr>
                                    <th style="width: 22%">牌型<br></th>
                                    <th style="width: 13%">基础权重</th>
                                    <th style="width: 13%">扶植系数</th>
                                    <th style="width: 13%">抑制系数</th>
                                    <th style="width: 13%">幸运权重</th>
                                    <th style="width: 13%">霉运权重</th>
                                    <th style="width: 13%">新手权重</th>
                                </tr>
                                </thead>
                                <tbody>

                                <?php foreach($pr_group_name_list as $i=>$v){?>
                                <?php if(isset($group_10_profit_rate_config[$i])){?>
                                    <tr>
                                        <td><?php echo $v;?></td>
                                        <td title="normal">     <input type="text" class="form-control" name="group_10_profit_rate_config[<?php echo $i;?>][normal]"      value="<?php echo $group_10_profit_rate_config[$i]['normal'];?>">     </td>
                                        <td title="up">         <input type="text" class="form-control" name="group_10_profit_rate_config[<?php echo $i;?>][up]"          value="<?php echo $group_10_profit_rate_config[$i]['up'];?>">         </td>
                                        <td title="down">       <input type="text" class="form-control" name="group_10_profit_rate_config[<?php echo $i;?>][down]"        value="<?php echo $group_10_profit_rate_config[$i]['down'];?>">       </td>
                                        <td title="lucky">      <input type="text" class="form-control" name="group_10_profit_rate_config[<?php echo $i;?>][lucky]"       value="<?php echo $group_10_profit_rate_config[$i]['lucky'];?>">      </td>
                                        <td title="unlucky">    <input type="text" class="form-control" name="group_10_profit_rate_config[<?php echo $i;?>][unlucky]"     value="<?php echo $group_10_profit_rate_config[$i]['unlucky'];?>">    </td>
                                        <td title="unlucky">    <input type="text" class="form-control" name="group_10_profit_rate_config[<?php echo $i;?>][novice]"     value="<?php echo $group_10_profit_rate_config[$i]['novice'];?>">    </td>
                                    </tr>

                                <?php }else{?>
                                    <tr>
                                        <td><?php echo $v;?></td>
                                        <td title="normal">     <input type="text" class="form-control" name="group_10_profit_rate_config[<?php echo $i;?>][normal]">       </td>
                                        <td title="up">         <input type="text" class="form-control" name="group_10_profit_rate_config[<?php echo $i;?>][up]" >          </td>
                                        <td title="down">       <input type="text" class="form-control" name="group_10_profit_rate_config[<?php echo $i;?>][down]">         </td>
                                        <td title="lucky">      <input type="text" class="form-control" name="group_10_profit_rate_config[<?php echo $i;?>][lucky]">        </td>
                                        <td title="unlucky">    <input type="text" class="form-control" name="group_10_profit_rate_config[<?php echo $i;?>][unlucky]">      </td>
                                        <td title="unlucky">    <input type="text" class="form-control" name="group_10_profit_rate_config[<?php echo $i;?>][novice]">       </td>
                                    </tr>
                                <?php }?>
                                <?php }?>


                                </tbody>
                            </table>
                        </form>
                    </div>-->
                </div>





                <div class="row">

                </div>



            </div> <!-- content -->

        </div> <!-- content-page -->

    </div> <!-- end wrapper-->

    <!-- ============================================================== -->
    <!-- End Page content -->
    <!-- ============================================================== -->
</div>
<!-- END Container -->
<div class="rightbar-overlay"></div>
<!-- /Right-bar -->
<!-- App js -->
<script src="/resource/assets/js/app.min.js"></script>
<script src="/resource/assets/js/common.js?v=11"></script>

</body>
</html>