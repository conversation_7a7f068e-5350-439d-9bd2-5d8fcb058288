<?php error_reporting(0);//不会弹出警告，只适合当前页?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>AI-PROFIT-RATE-CONFIG</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="A fully featured admin theme which can be used to build CRM, CMS, etc." name="description" />
    <meta content="Coderthemes" name="author" />
    <!-- App favicon -->
    <link rel="shortcut icon" href="/resource/assets/images/favicon.ico">

    <!-- App css -->
    <link href="/resource/assets/css/icons.min.css" rel="stylesheet" type="text/css" />
    <link href="/resource/assets/css/app.min.css?v=11" rel="stylesheet" type="text/css" />
    <style>
        .actives{
            color: #727cf5;
            border-bottom: 2px solid #727cf5;
        }
        .table td, .table th {
            padding:0px;
        }
        p{
            margin-bottom: 0;
        }
        .col-md-1 {
            -webkit-box-flex: 0;
            -ms-flex: 0 0 7.5%;
            flex: 0 0 7.5%;
            max-width: 7.5%;
        }

        .form-inline .form-control {
            display: inline-block;
            width: 100%;
            vertical-align: middle;
        }
        .form-control  {

            display: inline-block;
            /* width: auto; */
            vertical-align: middle;
            display: block;
            width: 100%;
            height: 25px;
            padding: .45rem .9rem;
            font-size: .875rem;
            font-weight: 400;
            line-height: 0.5;
            color: #6c757d;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #dee2e6;
            border-radius: .25rem;
            -webkit-transition: border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
            transition: border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
            transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
            transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
        }
    </style>
</head>
<body class="bg">
<div class="container-fluid">

    <!-- Begin page -->
    <div class="wrapper">
        <!-- ========== Left Sidebar Start ========== -->
        <?php require_once realpath(dirname(__FILE__). '/../').'/Public/nav.html';?>
        <!-- Left Sidebar End -->

        <div class="content-page">

            <div class="content">


                <div class="row">

                    <div class="col-lg-12" style="border: 1px solid #e3eaef;padding: 20px;">
                        <h4>TP PR配置</h4>
                        <?php $edit_pr_url =  \lib\Url::getAjaxUrl('editTpPrConfig');?>
                        <button type="button" class="btn btn-outline-success" style="margin-bottom: 10px;" onclick="ajax_post_form_submit(this,'profit_rate_config_form','<?php echo $edit_pr_url;?>');"> <i class="mdi mdi-content-save"></i> <span>保存</span> </button>
                        <form id="profit_rate_config_form">
                            <table class="table table-hover table-centered mb-0">
                                <thead>
                                <tr>
                                    <th>序号<br></th>
                                    <th>预设节点<br>P=0.5*Df+Dc</th>
                                    <th>扶植极值<br>d_limit</th>
                                    <th>扶植起点<br>d_begin</th>
                                    <th>限制起点<br>u_begin</th>
                                    <th>限制极值<br>u_limit</th>
                                    <th>扶植公式弧度参数<br>ed</th>
                                    <th>限制公式弧度参数<br>eu</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php for($i=1;$i<=12;$i++){?>
                                <?php if(isset($pr_config[$i])){?>
                                <tr>
                                    <td><?php echo $i;?></td>
                                    <td title="P">          <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][p]"          value="<?php echo $pr_config[$i]['p'];?>">    </td>
                                    <td title="d_limit">    <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][d_limit]"    value="<?php echo $pr_config[$i]['d_limit'];?>">    </td>
                                    <td title="d_begin">    <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][d_begin]"    value="<?php echo $pr_config[$i]['d_begin'];?>">    </td>
                                    <td title="u_begin">    <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][u_begin]"    value="<?php echo $pr_config[$i]['u_begin'];?>">    </td>
                                    <td title="u_limit">    <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][u_limit]"    value="<?php echo $pr_config[$i]['u_limit'];?>">    </td>
                                    <td title="ed">         <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][ed]"         value="<?php echo $pr_config[$i]['ed'];?>">    </td>
                                    <td title="eu">         <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][eu]"         value="<?php echo $pr_config[$i]['eu'];?>">    </td>
                                </tr>

                                <?php }else{?>
                                <tr>
                                    <td><?php echo $i;?></td>
                                    <td title="P">          <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][p]"          value="">    </td>
                                    <td title="d_limit">    <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][d_limit]"    value="">    </td>
                                    <td title="d_begin">    <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][d_begin]"    value="">    </td>
                                    <td title="u_begin">    <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][u_begin]"    value="">    </td>
                                    <td title="u_limit">    <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][u_limit]"    value="">    </td>
                                    <td title="ed">         <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][ed]"         value="">    </td>
                                    <td title="eu">         <input type="text" class="form-control" name="pr_config[<?php echo $i;?>][eu]"         value="">    </td>
                                </tr>
                                <?php }?>
                                <?php }?>

                                </tbody>
                            </table>
                        </form>
                    </div>
                </div>


                <div class="row">

                    <div class="col-lg-12" style="border: 1px solid #e3eaef;padding: 20px;">
                        <h4>TP AI性格配置</h4>
                        <button type="button" class="btn btn-outline-success" style="margin-bottom: 10px;" onclick="ajax_post_form_submit(this,'tp_ai_disposition_form','<?php echo \lib\Url::getAjaxUrl("editTpAiDisposition");?>');"> <i class="mdi mdi-content-save"></i> <span>保存</span> </button>
                        <form id="tp_ai_disposition_form">
                            <table class="table table-hover table-centered mb-0">
                                <thead>
                                <tr style="font-size: 10px;">
                                    <th style="width: 4%">性格<br></th>
                                    <th style="width: 4%">初始闷牌概率</th>
                                    <th style="width: 4%">初始加注概率</th>
                                    <th style="width: 4%">加注衰减系数</th>
                                    <th style="width: 4%">人均胜率系数</th>
                                    <th style="width: 4%">最低加注概率</th>
                                    <th style="width: 4%">平跟系数1</th>
                                    <th style="width: 4%">平跟系数2</th>
                                    <th style="width: 4%">加注系数1</th>
                                    <th style="width: 4%">加注系数2</th>
                                    <th style="width: 4%">aa2</th>
                                    <th style="width: 4%">bb1</th>
                                    <th style="width: 4%">cc1</th>
                                    <th style="width: 4%">dd2</th>
                                    <th style="width: 4%">a1</th>
                                    <th style="width: 4%">a2</th>
                                    <th style="width: 4%">b1</th>
                                    <th style="width: 4%">b2</th>
                                    <th style="width: 4%">c1</th>
                                    <th style="width: 4%">d3</th>
                                    <th style="width: 4%">e3</th>
                                    <th style="width: 4%">e4</th>
                                    <th style="width: 4%">f1</th>
                                    <th style="width: 4%">g3</th>
                                    <th style="width: 4%">h1</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php foreach($tp_disposition_list as $i=>$v){?>
                                <?php if(isset($tp_ai_disposition_config[$i])){?>
                                <tr>
                                    <td><?php echo $v;?></td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][initStuffyRate]"  value="<?php echo $tp_ai_disposition_config[$i]['initStuffyRate'];?>">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][initRaiseRate]"  value="<?php echo $tp_ai_disposition_config[$i]['initRaiseRate'];?>">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][raiseDecayRate]"  value="<?php echo $tp_ai_disposition_config[$i]['raiseDecayRate'];?>">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][avgWinRateDynamic]"  value="<?php echo $tp_ai_disposition_config[$i]['avgWinRateDynamic'];?>">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][lowestRaiseRate]"  value="<?php echo $tp_ai_disposition_config[$i]['lowestRaiseRate'];?>">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][followDynamic]"  value="<?php echo $tp_ai_disposition_config[$i]['followDynamic'];?>">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][followDynamicPow]"  value="<?php echo $tp_ai_disposition_config[$i]['followDynamicPow'];?>">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][raiseDynamic]"  value="<?php echo $tp_ai_disposition_config[$i]['raiseDynamic'];?>">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][raiseDynamicPow]"  value="<?php echo $tp_ai_disposition_config[$i]['raiseDynamicPow'];?>">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][aa2]"  value="<?php echo $tp_ai_disposition_config[$i]['aa2'];?>">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][bb1]"  value="<?php echo $tp_ai_disposition_config[$i]['bb1'];?>">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][cc1]"  value="<?php echo $tp_ai_disposition_config[$i]['cc1'];?>">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][dd2]"  value="<?php echo $tp_ai_disposition_config[$i]['dd2'];?>">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][a1]"  value="<?php echo $tp_ai_disposition_config[$i]['a1'];?>">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][a2]"  value="<?php echo $tp_ai_disposition_config[$i]['a2'];?>">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][b1]"  value="<?php echo $tp_ai_disposition_config[$i]['b1'];?>">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][b2]"  value="<?php echo $tp_ai_disposition_config[$i]['b2'];?>">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][c1]"  value="<?php echo $tp_ai_disposition_config[$i]['c1'];?>">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][d3]"  value="<?php echo $tp_ai_disposition_config[$i]['d3'];?>">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][e3]"  value="<?php echo $tp_ai_disposition_config[$i]['e3'];?>">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][e4]"  value="<?php echo $tp_ai_disposition_config[$i]['e4'];?>">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][f1]"  value="<?php echo $tp_ai_disposition_config[$i]['f1'];?>">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][g3]"  value="<?php echo $tp_ai_disposition_config[$i]['g3'];?>">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][h1]"  value="<?php echo $tp_ai_disposition_config[$i]['h1'];?>">  </td>

                                    <?php }else{?>
                                <tr>
                                    <td><?php echo $v;?></td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][initStuffyRate]">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][initRaiseRate]">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][raiseDecayRate]">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][avgWinRateDynamic]">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][lowestRaiseRate]">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][followDynamic]">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][followDynamicPow]">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][raiseDynamic]">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][raiseDynamicPow]">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][aa2]">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][bb1]">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][cc1]">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][dd2]">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][a1]">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][a2]">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][b1]">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][b2]">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][c1]">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][d3]">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][e3]">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][e4]">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][f1]">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][g3]">  </td>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition_config[<?php echo $i;?>][h1]">  </td>
                                </tr>
                                <?php }?>
                                <?php }?>
                                </tbody>
                            </table>
                        </form>
                    </div>




                    <div class="col-lg-12" style="border: 1px solid #e3eaef;padding: 20px;">
                        <h4>TP AI配置</h4>
                        <ul class="nav nav-tabs nav-justified nav-bordered mb-3">
                            <?php foreach($currency_list as $key=>$val){?>
                            <li class="nav-item">
                                <a href="<?php echo \lib\Url::getUrl('pr-tp-ai-config',array('currency'=>$key));?>"  class="nav-link <?php if($currency==$key){ echo 'active';}?>">
                                    <span class="d-none d-lg-block"><?php echo strip_tags($val);?></span>
                                </a>
                            </li>
                            <?php }?>

                        </ul>
                        <form id="ai_room_config" class="form-inline">
                            <input type="hidden" name="currency" value="<?php echo $currency;?>">
                            <?php $edit_pr_url =  \lib\Url::getUrl('edit-10gpr-config');?>
                            <div class="form-group" style="width: 10%">
                                <?php $obj = new \common\AiCommon\AiOverallWinRateV2(1,[],1);?>
                                [分钟：<?php echo $obj->getTodayMinutes();?>]<?php echo $obj->getTimeRate();?><br>
                            </div>
                            <div class="form-group" >
                                <button type="button" class="btn btn-danger"  onclick="ajax_post_form_submit(this,'ai_room_config','<?php echo \lib\Url::getAjaxUrl("setWinRate");?>');"> <i class="mdi mdi-content-save"></i> <span>预设收益比例</span> </button>
                                &nbsp;&nbsp;&nbsp;&nbsp;
                                <button type="button" class="btn btn-success"  onclick="ajax_post_form_submit(this,'ai_room_config','<?php echo \lib\Url::getAjaxUrl("setPrTpAiDynamicConfig");?>');"> <i class="mdi mdi-content-save"></i> <span>保存干预参数</span> </button>
                                &nbsp;&nbsp;&nbsp;&nbsp;
                                <button type="button" class="btn btn-warning"  onclick="ajax_post_form_submit(this,'ai_room_config','<?php echo \lib\Url::getAjaxUrl("setAiDispositionConfig");?>');"> <i class="mdi mdi-content-save"></i> <span>保存AI人格比例</span> </button>
                            </div>

                            <?php foreach($middle_room_list_config as $k=>$v){?>
                            <?php foreach($v as $key=>$val){?>


                            <table class="table table-hover table-centered" style="text-align: center;border: 1px solid #e3eaef;border-bottom:2px solid #0acf97;margin-bottom: 20px;">
                                <tr>
                                    <td>Teen Party   &nbsp;底注：<?php echo $key*0.001;?></td>
                                </tr>
                                <tr>
                                    <td colspan="10">
                                        <div class="row">
                                            <?php foreach($capitalTypeList as $capitalType=>$capitalTypeName){
                                            $obj = new \common\AiCommon\AiOverallWinRateV2($k,$room_info,$capitalType);
                                            $roomConfig = $obj->getRoomConfig();
                                            ?>
                                            <div class="col-md-1" style="padding:0px;font-size: 10px;">
                                                <table class="table table-centered table-hover mb-0">
                                                    <tr>
                                                        <th style="width: 10%"><?php echo $capitalTypeName;?></th>
                                                    </tr>
                                                    <tr>
                                                        <td style="width: 10%" width="10%"><input type="text" class="form-control" name="win_rate[<?php echo $k;?>][<?php echo $capitalType;?>]"  title="<?php echo $k;?>" value="<?php echo $win_rate[$k][$capitalType];?>"></td>
                                                    </tr>
                                                    <tr>
                                                        <td style="width: 20%">注:<?php echo bcmul($obj->getLogAmount(),0.001,2);?></td>
                                                    </tr>
                                                    <tr>
                                                        <td style="width: 20%">
                                                            原:<?php echo bcmul($obj->getOldTargetAmount(),0.001,2);?><br>

                                                            标:<?php echo bcmul($obj->getDynamicTargetAmount(),0.001,2);?>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td style="width: 20%">收:<?php echo bcmul($obj->getSystemAmount(),0.001,2);?></td>
                                                    </tr>
                                                    <tr>
                                                        <td style="width:20%;color:<?php if($obj->getRealWinAmount()>0){ echo 'red';}else{ echo 'green';}?>">缺:<?php echo bcmul($obj->getRealWinAmount(),0.001,2);?></td>
                                                    </tr>
                                                </table>
                                            </div>
                                            <?php }?>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><input type="text" class="form-control" name="dynamic[<?php echo $k;?>][change_card_init_rate]"  value="<?php echo isset($profit_dynamic_config[$k]['change_card_init_rate'])?$profit_dynamic_config[$k]['change_card_init_rate']:'';?>"></td>
                                    <td><input type="text" class="form-control" name="dynamic[<?php echo $k;?>][deal_rate]"  value="<?php echo isset($profit_dynamic_config[$k]['deal_rate'])?$profit_dynamic_config[$k]['deal_rate']:'';?>"></td>
                                    <td><input type="text" class="form-control" name="dynamic[<?php echo $k;?>][sys_killer_limit_rate]"  value="<?php echo isset($profit_dynamic_config[$k]['sys_killer_limit_rate'])?$profit_dynamic_config[$k]['sys_killer_limit_rate']:'';?>"></td>
                                    <td><input type="text" class="form-control" name="dynamic[<?php echo $k;?>][intervene_rate]"  value="<?php echo isset($profit_dynamic_config[$k]['intervene_rate'])?$profit_dynamic_config[$k]['intervene_rate']:'';?>"></td>
                                    <?php foreach($tp_disposition_list as $dk=>$dv){?>
                                    <td><input type="text" class="form-control" name="tp_ai_disposition[<?php echo $k;?>][<?php echo $dk?>]"  value="<?php echo isset($tp_ai_disposition[$k][$dk])?$tp_ai_disposition[$k][$dk]:'';?>"></td>
                                    <?php }?>
                                    <td>

                    </td>
                    </tr>

                    <tr>
                        <th style="width: 2%;">初始换牌杀率（小数）</th>
                        <th style="width: 2%;">房间控初始（小数）</th>
                        <th style="width: 2%;">房间控上限</th>
                        <th style="width: 2%;">房间控系数</th>
                        <?php foreach($tp_disposition_list as $v){?>
                        <th style="width: 2%;"><?php echo $v;?></th>
                        <?php }?>
                        <th style="width: 3%"> 操作</th>
                    </tr>

                    </table>
                    <?php }?>
                    <?php }?>

                    </form>
                </div>

            </div>

        </div> <!-- content -->

    </div> <!-- content-page -->

</div> <!-- end wrapper-->

<!-- ============================================================== -->
<!-- End Page content -->
<!-- ============================================================== -->
</div>
<!-- END Container -->
<div class="rightbar-overlay"></div>
<!-- /Right-bar -->
<!-- App js -->
<script src="/resource/assets/js/app.min.js"></script>
<script src="/resource/assets/js/common.js?v=11"></script>

</body>
</html>
