<?php error_reporting(0);//不会弹出警告，只适合当前页?>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
<meta content="yes" name="apple-mobile-web-app-capable" />
<meta content="black" name="apple-mobile-web-app-status-bar-style" />
<meta content="telephone=no" name="format-detection" />
<title>提示页面</title>
<link href="/static/css/common.css" rel="stylesheet" type="text/css">
<link href="/static/css/not.css" rel="stylesheet" type="text/css">
<meta name="keywords" content="">
<meta name="description" content="">
</head>
<body>
<!--<img src="/static/img/not.jpg" width="100%">-->
<?php if(isset($message)) {?>
<div class="not_txt"><?php echo($message); ?></div>
<?php }else{?>
<div class="not_txt"><?php echo($error); ?></div>
<?php }?>
<a href="<?php echo($jumpUrl); ?>" class="retry_a">跳转</a>
</body>
</html>