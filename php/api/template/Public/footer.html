<?php error_reporting(0);//不会弹出警告，只适合当前页?>
<script>
	//展示拥有权限的菜单
	var adminPurviewList = <{$adminPurviewList|json_encode}>;
	$("[purview_opt]").each(function(){

		var op = $(this).attr('purview_opt');
		var res = $.inArray(op,adminPurviewList);
		if(res==-1){
			//不存在
			$(this).hide();
		}else{
			//存在
			if($(this).parent().parent().attr('purview_opt')=='menu'){
				$(this).parent().parent().show();
			};

			if($(this).parent().parent().parent().parent().attr('purview_opt')=='menu'){
				$(this).parent().parent().parent().parent().show();
			};
			$(this).show();
		}
	});
</script>
<!-- Main Footer
<footer class="main-footer sticky footer-type-1">
	<div class="footer-inner">
		<div class="footer-text">
			&copy; 2017 <strong><a href="#" target="_blank">i66games（邵帅）</a></strong> 
		</div>
		<div class="go-up">
			<a href="#" rel="go-top"><i class="fa-angle-up"></i></a>
		</div>
	</div>
</footer> -->