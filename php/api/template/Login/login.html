<?php error_reporting(0);//不会弹出警告，只适合当前页?>
<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="utf-8" />
        <title>AI RULE</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta content="" name="description" />
        <meta content="ss" name="author" />
        <!-- App favicon -->
        <link rel="shortcut icon" href="/resource/assets/images/favicon.ico">

        <!-- App css -->
        <link href="/resource/assets/css/icons.min.css" rel="stylesheet" type="text/css" />
        <link href="/resource/assets/css/app.min.css" rel="stylesheet" type="text/css" />

    </head>
    <body class="auth-fluid-pages pb-0">

        <div class="auth-fluid">
            <!--Auth fluid left content -->
            <div class="auth-fluid-form-box" style="background-color:transparent">
                <div class="align-items-center d-flex h-100">
                    <div class="card-body">

                        <!-- Logo -->
                        <div class="auth-brand text-center text-lg-left">
                            <a href="#">
                                <span><img src="/resource/assets/images/logo-light.png" alt="" height="100"></span>
                            </a>
                        </div>
                        <!-- title-->
                        <h4 class="mt-0" style="color:#fff">Sign In</h4>
                        <p class="mb-4" id="info_show_box" style="color:#fff">Enter your sign name and password to access account.</p>
						<form class="needs-validation" novalidate id="login-form">
							<div class="form-group mb-3">
								<label for="Username" style="color:#fff">Sign name</label>
								<input type="text" class="form-control" id="Username"placeholder="Username"  required>
								<div class="valid-feedback">
									Looks good!
								</div>
							</div>
							<div class="form-group mb-3">
								<label for="Password" style="color:#fff">Password</label>
								<input type="password" class="form-control" id="Password" placeholder="Password"required>
								<div class="valid-feedback">
									Looks good!
								</div>
							</div>
							
							 <div class="form-group mb-3">
								<p class="font-16" style="color:#fff">Sign in with</p>
								<ul class="social-list list-inline mt-3">
									<li class="list-inline-item">
										<a href="javascript: void(0);" class="social-list-item border-primary text-primary"><i class="mdi mdi-facebook"></i></a>
									</li>
									<li class="list-inline-item">
										<a href="javascript: void(0);" class="social-list-item border-danger text-danger"><i class="mdi mdi-google"></i></a>
									</li>
									<li class="list-inline-item">
										<a href="javascript: void(0);" class="social-list-item border-info text-info"><i class="mdi mdi-twitter"></i></a>
									</li>
									<li class="list-inline-item">
										<a href="javascript: void(0);" class="social-list-item border-secondary text-secondary"><i class="mdi mdi-github-circle"></i></a>
									</li>
								</ul>
							</div>
					
							<button class="btn btn-primary" type="submit">Log In</button>
						</form>

                    </div> <!-- end .card-body -->
                </div> <!-- end .align-items-center.d-flex.h-100-->
            </div>
            <!-- end auth-fluid-form-box-->

            <!-- Auth fluid right content -->
            <div class="auth-fluid-right text-center">
                <div class="auth-user-testimonial">
                    <h2 class="mb-3">I can do everything for you!</h2>
                    <p class="lead"><i class="mdi mdi-format-quote-open"></i>  Create perfect artificial intelligence!<i class="mdi mdi-format-quote-close"></i>
                    </p>
                    <p>
                        - AI
                    </p>
                </div> <!-- end auth-user-testimonial-->
            </div>
            <!-- end Auth fluid right content -->
        </div>
        <!-- end auth-fluid-->

        <!-- App js -->
        <script src="/resource/assets/js/app.min.js"></script>
		<script>
        var ajaxUrl = "<?php echo \lib\Url::getAjaxUrl('login');?>";
		$('#login-form').submit(function (e) {
			if (e.isDefaultPrevented()) return
			//  PROCESS DATA ...
			   $.ajax({
						url: ajaxUrl,
						method: 'POST',
						dataType: 'json',
						data: {
							username:$("#Username").val(),
							password:$("#Password").val(),
						},
						success: function(res)
						{	if(res.code){
								window.location.href = "<?php echo \lib\Url::getUrl('comparison-card-code');?>";
							}else{
								$("#info_show_box").html("<span style='color:#ff0000'>"+res.message+"</span>");
							}
						}
					});
			return false;//阻止页面刷新
		});
				
		</script>
    </body>

</html>