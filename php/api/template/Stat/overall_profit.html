<?php error_reporting(0);//不会弹出警告，只适合当前页?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>大盘盈利log</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="A fully featured admin theme which can be used to build CRM, CMS, etc." name="description" />
    <meta content="Coderthemes" name="author" />
    <!-- App favicon -->
    <link rel="shortcut icon" href="/resource/assets/images/favicon.ico">

    <!-- App css -->
    <link href="/resource/assets/css/icons.min.css" rel="stylesheet" type="text/css" />
    <link href="/resource/assets/css/app.min.css?v=11" rel="stylesheet" type="text/css" />
    <style>
        .actives{
            color: #727cf5;
            border-bottom: 2px solid #727cf5;
        }
        .table td, .table th {
            padding:0px;
        }
        p{
            margin-bottom: 0;
        }

        .form-inline .form-control {
            display: inline-block;
            width: 100%;
            vertical-align: middle;
        }
        .form-control  {

            display: inline-block;
            /* width: auto; */
            vertical-align: middle;
            display: block;
            width: 100%;
            height: 25px;
            padding: .45rem .9rem;
            font-size: .875rem;
            font-weight: 400;
            line-height: 0.5;
            color: #6c757d;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #dee2e6;
            border-radius: .25rem;
            -webkit-transition: border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
            transition: border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
            transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
            transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
        }

        .table2{
            position: relative;
            clear: both;
            zoom: 1;
            overflow-x: auto;
            width: 3500px;
        }


        .table_left_m{
            left: -16px;
            position: sticky;
            background-color: #f1f3fa;
            width: 120px;
        }

        .table_left_m2{
            left: 104px;
            position: sticky;
            background-color: #f1f3fa;
            width: 50px;
        }
    </style>
</head>
<body class="bg">
<div class="container-fluid">

    <!-- Begin page -->
    <div class="wrapper">
        <!-- ========== Left Sidebar Start ========== -->
        <?php require_once realpath(dirname(__FILE__). '/../').'/Public/nav.html';?>
        <!-- Left Sidebar End -->

        <div class="content-page">

            <div class="content">

                <div class="row">

                    <div class="col-lg-12" style="width:200%;max-width:200%;overflow-x: auto;">
                        <h4 class="header-title">大盘盈利log</h4>
                        <p class="text-muted">
                            展示最近90天数据
                        </p>

                        <?php if($list){?>
                        <?php foreach($list as $k=>$v){?>
                        <table class="table table-sm table-centered table2" style="border: 1px solid #e3eaef;padding: 20px;">
                            <thead>
                            <tr>
                                <th colspan="44" style="border-top: 2px solid #0acf97">
                                    <?php echo '日期：'.$k;?>
                                </th>
                            </tr>
                            <tr>
                                <th class="table_left_m">名称</th>
                                <th class="table_left_m2" style="border-right: 1px solid #0acf97;">底注</th>

                                <?php foreach($capitalTypeList as $capitalType=>$capitalTypeName){?>
                                <th>[<?php echo $capitalTypeName?>]庄</th>
                                <th>比</th>
                                <th style="border-right: 1px solid #0acf97;">注</th>
                                <?php }?>
                            </tr>
                            </thead>
                            <tbody>
                            <?php foreach($v as $key=>$val){?>
                            <tr>
                                <td class="table_left_m"><?php  echo $val['cls'];?></td>
                                <td class="table_left_m2" style="border-right: 1px solid #0acf97"><?php  echo $val['base'];?></td>


                                <?php foreach($capitalTypeList as $capitalType=>$capitalTypeName){?>

                                <td style="<?php if($val[$capitalType]['c']<0){ echo 'color:red;';}?>">
                                    <?php echo bcmul($val[$capitalType]['c'],0.001,2);?>
                                </td>



                                <td style="<?php if($val[$capitalType]['c']<0){ echo 'color:red;';}?>">

                                    <?php
                                         if($val[$capitalType]['wl']==0 || $val[$capitalType]['c']==0){
                                            echo '+0.00';
                                         }else{
                                            $rate = bcdiv($val[$capitalType]['c'],$val[$capitalType]['wl'],4)*100;
                                            echo $rate>=0?'+'.$rate:$rate;
                                    }
                                    ?>
                                    %
                                </td>

                                <td style="border-right: 1px solid #0acf97"><?php echo bcmul($val[$capitalType]['wl'],0.001,2);?></td>
                                <?php }?>
                            </tr>
                            <?php }?>
                            </tbody>
                        </table>
                        <?php }}?>
                    </div> <!-- end custom accordions-->
                </div> <!-- end col -->


            </div>


        </div>
    </div> <!-- content-page -->

</div> <!-- end wrapper-->

<!-- ============================================================== -->
<!-- End Page content -->
<!-- ============================================================== -->
</div>
<!-- END Container -->
<div class="rightbar-overlay"></div>
<!-- /Right-bar -->
<!-- App js -->
<script src="/resource/assets/js/app.min.js"></script>
<script src="/resource/assets/js/common.js?v=11"></script>

</body>
</html>