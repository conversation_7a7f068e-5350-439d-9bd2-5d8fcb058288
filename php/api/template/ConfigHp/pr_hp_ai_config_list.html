<?php error_reporting(0);//不会弹出警告，只适合当前页?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>AI-PROFIT-RATE-CONFIG</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="A fully featured admin theme which can be used to build CRM, CMS, etc." name="description" />
    <meta content="Coderthemes" name="author" />
    <!-- App favicon -->
    <link rel="shortcut icon" href="/resource/assets/images/favicon.ico">

    <!-- App css -->
    <link href="/resource/assets/css/icons.min.css" rel="stylesheet" type="text/css" />
    <link href="/resource/assets/css/app.min.css?v=11" rel="stylesheet" type="text/css" />
    <style>
        .actives{
            color: #727cf5;
            border-bottom: 2px solid #727cf5;
        }
        .table td, .table th {
            padding:0px;
        }
        p{
            margin-bottom: 0;
        }

        .form-inline .form-control {
            display: inline-block;
            width: 100%;
            vertical-align: middle;
        }
        .col-md-1 {
            -webkit-box-flex: 0;
            -ms-flex: 0 0 7.5%;
            flex: 0 0 7.5%;
            max-width: 7.5%;
        }
        .form-control  {

            display: inline-block;
            /* width: auto; */
            vertical-align: middle;
            display: block;
            width: 100%;
            height: 25px;
            padding: .45rem .9rem;
            font-size: .875rem;
            font-weight: 400;
            line-height: 0.5;
            color: #6c757d;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #dee2e6;
            border-radius: .25rem;
            -webkit-transition: border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
            transition: border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
            transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
            transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
        }
    </style>
</head>
<body class="bg">
<div class="container-fluid">

    <!-- Begin page -->
    <div class="wrapper">
        <!-- ========== Left Sidebar Start ========== -->
        <?php require_once realpath(dirname(__FILE__). '/../').'/Public/nav.html';?>
        <!-- Left Sidebar End -->

        <div class="content-page">

            <div class="content">

                <div class="row">

                    <span class="col-lg-12" style="border: 1px solid #e3eaef;padding: 20px;">
                        <h4>百人场 控大盘配置</h4>
                         <ul class="nav nav-tabs nav-justified nav-bordered mb-3">
                            <?php foreach($currency_list as $key=>$val){?>
                                <li class="nav-item">
                                    <a href="<?php echo \lib\Url::getUrl('pr-hp-ai-config',array('currency'=>$key));?>"  class="nav-link <?php if($currency==$key){ echo 'active';}?>">
                                        <span class="d-none d-lg-block"><?php echo strip_tags($val);?></span>
                                    </a>
                                </li>
                             <?php }?>
                        </ul>


                        <form id="ai_room_config" class="form-horizontal form-inline">

                            <input type="hidden" name="currency" value="<?php echo $currency;?>">
                            <?php $edit_pr_url =  \lib\Url::getUrl('edit-10gpr-config');?>
                            <div class="form-group" >
                                <button type="button" class="btn btn-danger"  onclick="ajax_post_form_submit(this,'ai_room_config','<?php echo \lib\Url::getAjaxUrl("setWinRate");?>');"> <i class="mdi mdi-content-save"></i> <span>预设收益比例</span> </button>
                                &nbsp;&nbsp;&nbsp;&nbsp;
                                <button type="button" class="btn btn-success"  onclick="ajax_post_form_submit(this,'ai_room_config','<?php echo \lib\Url::getAjaxUrl("setRoomConfig");?>');"> <i class="mdi mdi-content-save"></i> <span>保存配置</span> </button>
                                <br><br><br><br><br><br>
                                <?php $obj = new \common\AiCommon\AiOverallWinRateV2(1,[],1);?>
                                 [分钟：<?php echo $obj->getTodayMinutes();?>]<?php echo $obj->getTimeRate();?><br>
                            </div>


                            <?php foreach($roomList as $k=>$v){?>
                                <table class="table table-hover table-centered" style="text-align: center;border: 1px solid #e3eaef;border-bottom:2px solid #0acf97;margin-bottom: 10px;">
                                    <tr>
                                        <th colspan="13" style="text-align: left">
                                            <?php echo $v['gameName'];?>
                                            <?php if($v['gameName'] == common\Common::getGameTypeTextList(common\Common::GAME_TYPE_SEVEN_UP_DOWN)){?>
                                            <img src="/resource/pai/dice_gold_5.png" data-toggle="modal" data-target="#signup-modal" style="width: 3%">
                                            <?php }?> &nbsp;<?php echo $v['base']*0.001;?>
                                        </th>
                                    </tr>

                                    <tr>
                                        <td colspan="13">
                                            <div class="row">
                                            <?php foreach($capitalTypeList as $capitalType=>$capitalTypeName){
                                                    $obj = new \common\AiCommon\AiOverallWinRateV2($v['jackportName'],$v,$capitalType);
                                                    $roomConfig = $obj->getRoomConfig();
                                                ?>
                                                <div class="col-md-1" style="padding:0px;font-size: 10px;">
                                                    <table class="table table-centered table-hover mb-0">
                                                        <tr>
                                                            <th style="width: 10%"><?php echo $capitalTypeName;?></th>
                                                        </tr>
                                                         <tr>
                                                            <td style="width: 10%" width="10%"><input type="text" class="form-control" name="win_rate[<?php echo $v['jackportName'];?>][<?php echo $capitalType;?>]"  title="<?php echo $v['jackportName'];?>" value="<?php echo $win_rate[$v['jackportName']][$capitalType];?>"></td>
                                                         </tr>
                                                         <tr>
                                                             <td style="width: 20%">注:<?php echo bcmul($obj->getLogAmount(),0.001,2);?></td>
                                                         </tr>
                                                        <tr>
                                                            <td style="width: 20%">
                                                                原:<?php echo bcmul($obj->getOldTargetAmount(),0.001,2);?><br>

                                                                标:<?php echo bcmul($obj->getDynamicTargetAmount(),0.001,2);?>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 20%">收:<?php echo bcmul($obj->getSystemAmount(),0.001,2);?></td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width:20%;color:<?php if($obj->getRealWinAmount()>0){ echo 'red';}else{ echo 'green';}?>">缺:<?php echo bcmul($obj->getRealWinAmount(),0.001,2);?></td>
                                                        </tr>
                                                    </table>
                                                </div>
                                            <?php }?>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><input type="text" class="form-control" name="room_config[<?php echo $v['jackportName'];?>][room_type_killer][0]"         value="<?php echo $roomConfig['room_type_killer'][0];?>"></td>
                                        <td><input type="text" class="form-control" name="room_config[<?php echo $v['jackportName'];?>][buff_coefficient][0]"            value="<?php echo $roomConfig['buff_coefficient'][0];?>"></td>
                                        <td><input type="text" class="form-control" name="room_config[<?php echo $v['jackportName'];?>][room_type_killer][1]"         value="<?php echo $roomConfig['room_type_killer'][1];?>"></td>
                                        <td><input type="text" class="form-control" name="room_config[<?php echo $v['jackportName'];?>][buff_coefficient][1]"            value="<?php echo $roomConfig['buff_coefficient'][1];?>"></td>
                                        <td><input type="text" class="form-control" name="room_config[<?php echo $v['jackportName'];?>][room_type_killer][2]"         value="<?php echo $roomConfig['room_type_killer'][2];?>"></td>
                                        <td><input type="text" class="form-control" name="room_config[<?php echo $v['jackportName'];?>][buff_coefficient][2]"            value="<?php echo $roomConfig['buff_coefficient'][2];?>"></td>
                                        <td><input type="text" class="form-control" name="room_config[<?php echo $v['jackportName'];?>][win_score_killer_base]"       value="<?php echo $roomConfig['win_score_killer_base'];?>"></td>
                                        <td><input type="text" class="form-control" name="room_config[<?php echo $v['jackportName'];?>][intervene_rate]"              value="<?php echo $roomConfig['intervene_rate'];?>"></td>
                                        <td><input type="text" class="form-control" name="room_config[<?php echo $v['jackportName'];?>][initial_rate]"                value="<?php echo $roomConfig['initial_rate'];?>"></td>
                                        <td><input type="text" class="form-control" name="room_config[<?php echo $v['jackportName'];?>][tolerance_factor]"            value="<?php echo $roomConfig['tolerance_factor'];?>"></td>
                                        <td><input type="text" class="form-control" name="room_config[<?php echo $v['jackportName'];?>][float_rate]"                  value="<?php echo $roomConfig['float_rate'];?>"></td>
                                    </tr>
                                    <tr>
                                        <th>普通房</th>
                                        <th>Buff系数</th>
                                        <th>高手Buff</th>
                                        <th>Buff系数</th>
                                        <th>大户Buff</th>
                                        <th>Buff系数</th>
                                        <th>强杀下注额</th>
                                        <th>房间控系数</th>
                                        <th>初始概率</th>
                                        <th>容忍系数</th>
                                        <th>浮动概率</th>
                                    </tr>

                                    </table>
                                <?php }?>

            </form>
        </div>
    </div>

</div> <!-- content -->

</div> <!-- content-page -->

</div> <!-- end wrapper-->

<!-- ============================================================== -->
<!-- End Page content -->
<!-- ============================================================== -->
</div>
<!-- END Container -->
<div class="rightbar-overlay"></div>
<!-- /Right-bar -->
<!-- App js -->

<!-- Signup modal content -->
<div id="signup-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">

            <div class="modal-body">
                <div class="text-center mt-2 mb-4">
                    <a href="index.html" class="text-success">
                        <span><img src="assets/images/logo-dark.png" alt="" height="18"></span>
                    </a>
                </div>

                <form class="pl-3 pr-3" id="gold_dice_config" action="#">

                    <div class="form-group">
                        <label>普通房</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">触发概率</span>
                            </div>
                            <input style="height: auto" type="text" class="form-control" name="goldDice[0][rate]" value = "<?php echo $gold_dice_config[0]['rate'];?>" >
                        </div>
                    </div>

                    <div class="form-group">
                        <label>高手房</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">触发概率</span>
                            </div>
                            <input style="height: auto" type="text" class="form-control" name="goldDice[1][rate]" value = "<?php echo $gold_dice_config[1]['rate'];?>" >
                        </div>
                    </div>

                    <div class="form-group">
                        <label>大户房</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">触发概率</span>
                            </div>
                            <input style="height: auto" type="text" class="form-control" name="goldDice[2][rate]" value = "<?php echo $gold_dice_config[2]['rate'];?>" >
                        </div>
                    </div>

                    <div class="form-group text-center">
                        <button type="button" class="btn btn-danger"  onclick="ajax_post_form_submit(this,'gold_dice_config','<?php echo \lib\Url::getAjaxUrl("setGoldDiceConfig");?>');"> <i class="mdi mdi-content-save"></i> <span>保存金骰子配置</span> </button>
                    </div>

                </form>

            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<script src="/resource/assets/js/app.min.js"></script>
<script src="/resource/assets/js/common.js?v=11"></script>

</body>
</html>
