<?php error_reporting(0);//不会弹出警告，只适合当前页?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>ai-牌码对照表</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="A fully featured admin theme which can be used to build CRM, CMS, etc." name="description" />
    <meta content="Coderthemes" name="author" />
    <!-- App favicon -->
    <link rel="shortcut icon" href="/resource/assets/images/favicon.ico">

    <!-- App css -->
    <link href="/resource/assets/css/icons.min.css" rel="stylesheet" type="text/css" />
    <link href="/resource/assets/css/app.min.css?v=11" rel="stylesheet" type="text/css" />
    <style>
        .modal-backdrop {
            filter: alpha(opacity=0)!important;
            opacity: 0!important;
        }
        .modal-sm {
            width: 50%;
            max-width: 50%;
        }

        .col-xl-1{
            flex: 0 0 5.8%;
            padding:0px;
        }

        .custom-control {
            position: relative;
            display: block;
            min-height: 1.3125rem;
            padding-left: 0rem;
        }

    </style>

</head>


<div class="container-fluid">

    <!-- Begin page -->
    <div class="wrapper">
        <!-- ========== Left Sidebar Start ========== -->
        <?php require_once realpath(dirname(__FILE__). '/../').'/Public/nav.html';?>
        <?php $lcard['num']='';?>
        <div class="content-page">
            <div class="content">
                <div class="row col-xl-12" id="lmacardbox">
                </div>
                <!-- end row-->
                <div class="row">
                    <table class="table table-striped table-centered mb-0" width="100%">
                        <?php foreach($data as $val){?>
                        <tr>
                           <td>
                               <div class="row" >
                                   <?php foreach($val as $one){?>


                                   <?php $spnum = get_card_info($one);?>
                                   <?php if($spnum['type']!='w' && $spnum['num']<=10){?>
                                   <div class="col-xl-1" onclick="oncheck(<?php echo $one;?>);">
                                       <img src="/resource/pai/dp.png" alt="" style="width:100%"/>
                                       <img style="position:absolute;z-index: 999;top: 6%;left:10%;height:16%"  src="/resource/pai/<?php echo $spnum['num'];?>.png" alt="" />
                                       <img style="position:absolute;z-index: 999;top: 26%;left:8%;height:14%"  src="/resource/pai/<?php echo $spnum['type'];?>.png" alt="" />
                                       <?php if($spnum['num'] == $lcard['num']){?>
                                       <img src="/resource/pai/dwr.png" alt="" style="position:absolute;z-index: 999;top: 48%;left:8%;height:18%"/>
                                       <?php }?>
                                       <img style="position:absolute;z-index: 999;bottom: 10%;right:12%;height:26%" src="/resource/pai/<?php echo $spnum['type'];?>.png" alt="" />
                                   </div>
                                   <?php }?>

                                   <?php if($spnum['type']!='w' && $spnum['num']>10){?>
                                   <div class="col-xl-1" style='width:20px;' onclick="oncheck(<?php echo $one;?>);">
                                       <img src="/resource/pai/dp.png" alt="" style="width:100%"/>
                                       <img src="/resource/pai/<?php echo $spnum['num'];?>r.png" alt="" style="position:absolute;z-index: 555;width:36%;bottom: 10%;right:12%"/>
                                       <img style="position:absolute;z-index: 999;top: 6%;left:10%;height:16%"  src="/resource/pai/<?php echo $spnum['num'];?>.png" alt="" />
                                       <?php if($spnum['num'] == $lcard['num']){?>
                                       <img src="/resource/pai/dwr.png" alt="" style="position:absolute;z-index: 999;top: 48%;left:8%;height:18%"/>
                                       <?php }?>
                                       <img style="position:absolute;z-index: 999;top: 26%;left:9%;height:14%"  src="/resource/pai/<?php echo $spnum['type'];?>.png" alt="" />
                                   </div>
                                   <?php }?>

                                   <?php if($spnum['type']=='w'){?>
                                   <div class="col-xl-1" style='width:20px;' onclick="oncheck(<?php echo $one;?>);">
                                       <img src="/resource/pai/dp.png" alt="" style="width:100%"/>
                                       <img src="/resource/pai/dwr.png" alt="" style="position:absolute;z-index: 555;width:36%;bottom: 10%;right:12%"/>
                                       <img style="position:absolute;z-index: 999;top: 6%;left:10%;height:50%"  src="/resource/pai/dab.png" alt="" />
                                   </div>
                                   <?php }?>

                                   <div class="custom-control custom-checkbox">
                                       <input type="checkbox" class="custom-control-input" id="customCheck<?php echo $one;?>" name="card" value="<?php echo $one;?>">
                                       <label class="custom-control-label" for="customCheck<?php echo $one;?>" id="label_<?php echo $one;?>"><?php echo $one;?></label>
                                   </div>

                                   <?php }?>
                               </div>
                           </td>
                        </tr>
                        <?php }?>
                        <tr>
                            <td>
                                <div class="form-group mb-3">
                                    <button class="btn btn-success btn-single" onclick="create_card()">生成手牌</button>
                                </div>&nbsp;&nbsp;

                                <form class="form-inline" method="post" action="<?php echo \lib\Url::getUrl('comparison-rummy-check-group');?>">
                                        <div class="form-group mb-3">
                                            <label for="hand">手牌</label>
                                            <input type="text" id="hand" class="form-control" name="hand">
                                        </div>&nbsp;&nbsp;

                                        <div class="form-group mb-3">
                                            <label for="l_card">癞子</label>
                                            <input type="text" id="l_card" class="form-control" name="l_card">
                                        </div>&nbsp;&nbsp;

                                        <div class="form-group mb-3">
                                            <button type="submit" class="btn btn-success btn-single">验证 摆牌</button>
                                        </div>&nbsp;&nbsp;
                                </form>

                                <form class="form-inline" method="post" action="<?php echo \lib\Url::getUrl('comparison-teenpatty-check-group-score');?>">
                                    <div class="form-group mb-3">
                                        <label for="hand">手牌</label>
                                        <input type="text" id="t_hand" class="form-control" name="t_hand">
                                    </div>&nbsp;&nbsp;

                                    <div class="form-group mb-3">
                                        <button type="submit" class="btn btn-success btn-single">验证 TeenPatti 分值</button>
                                    </div>&nbsp;&nbsp;
                                </form>
                            </td>
                        </tr>
                    </table>
                </div>
            </div> <!-- content -->
        </div> <!-- content-page -->
    </div> <!-- end wrapper-->

    <!-- ============================================================== -->
    <!-- End Page content -->
    <!-- ============================================================== -->


</div>
<!-- END Container -->
<div class="rightbar-overlay"></div>
<!-- /Right-bar -->
<script src="/resource/assets/js/app.min.js"></script>
<script>
    function oncheck(id){
        if($("#customCheck"+id).attr("checked")){
            $("#customCheck"+id).removeAttr("checked");
        }else{
            $("#customCheck"+id).attr("checked","true");
        }
    }

    //生成手牌
    function create_card() {
        var id_array=new Array();
        $('input[name="card"]:checked').each(function(){
            id_array.push($(this).val());//向数组中添加元素
        });
        var idstr=id_array.join(',');//将数组元素连接起来以构建一个字符串
        $("#hand").val(idstr);
        $("#t_hand").val(idstr);
    }
</script>
</body>
</html>