<?php error_reporting(0);//不会弹出警告，只适合当前页?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>ai-摸牌-打牌</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="A fully featured admin theme which can be used to build CRM, CMS, etc." name="description" />
    <meta content="Coderthemes" name="author" />
    <!-- App favicon -->
    <link rel="shortcut icon" href="/resource/assets/images/favicon.ico">

    <!-- App css -->
    <link href="/resource/assets/css/icons.min.css" rel="stylesheet" type="text/css" />
    <link href="/resource/assets/css/app.min.css?v=11" rel="stylesheet" type="text/css" />
    <style>
        .col-xl-1{
            flex: 0 0 7.33333%;
            padding:0px;
        }

    </style>

</head>


<div class="container-fluid">

    <!-- Begin page -->
    <div class="wrapper">
        <!-- ========== Left Sidebar Start ========== -->
        <?php require_once realpath(dirname(__FILE__). '/../').'/Public/nav.html';?>
        <?php $lcard = get_card_info($t_card['l_card']['card']);?>
        <div class="content-page">
            <div class="content">
                <div class="row col-xl-12" id="lmacardbox">
                </div>
                <!-- end row-->
                <div class="row">
                    <table class="table table-striped table-centered mb-0" width="100%">
                        <tr>
                            <td colspan="2">
                                <div class="row" >
                                <?php foreach($t_card as $card_one){?>
                                    <?php echo $card_one['name'];?>
                                    <?php $card_one = get_card_info($card_one['card']);?>
                                    <?php if($card_one['type']!='w' && $card_one['num']<=10){?>
                                    <div class="col-xl-1">
                                        <img src="/resource/pai/dp.png" alt="" style="width:100%"/>
                                        <img style="position:absolute;z-index: 999;top: 6%;left:10%;height:16%"  src="/resource/pai/<?php echo $card_one['num'];?>.png" alt="" />
                                        <img style="position:absolute;z-index: 999;top: 26%;left:8%;height:14%"  src="/resource/pai/<?php echo $card_one['type'];?>.png" alt="" />
                                        <?php if($card_one['num'] == $lcard['num'] && $lcard['type']!='w'){?>
                                        <img src="/resource/pai/dwr.png" alt="" style="position:absolute;z-index: 999;top: 48%;left:8%;height:18%"/>
                                        <?php }?>
                                        <img style="position:absolute;z-index: 999;bottom: 10%;right:12%;height:26%" src="/resource/pai/<?php echo $card_one['type'];?>.png" alt="" />
                                    </div>
                                    <?php }?>

                                    <?php if($card_one['type']!='w' && $card_one['num']>10){?>
                                    <div class="col-xl-1" style='width:20px;'>
                                        <img src="/resource/pai/dp.png" alt="" style="width:100%"/>
                                        <img src="/resource/pai/<?php echo $card_one['num'];?>r.png" alt="" style="position:absolute;z-index: 555;width:36%;bottom: 10%;right:12%"/>
                                        <img style="position:absolute;z-index: 999;top: 6%;left:10%;height:16%"  src="/resource/pai/<?php echo $card_one['num'];?>.png" alt="" />
                                        <?php if($card_one['num'] == $lcard['num'] && $lcard['type']!='w'){?>
                                        <img src="/resource/pai/dwr.png" alt="" style="position:absolute;z-index: 999;top: 48%;left:8%;height:18%"/>
                                        <?php }?>
                                        <img style="position:absolute;z-index: 999;top: 26%;left:9%;height:14%"  src="/resource/pai/<?php echo $card_one['type'];?>.png" alt="" />
                                    </div>
                                    <?php }?>

                                    <?php if($card_one['type']=='w'){?>
                                    <div class="col-xl-1" style='width:20px;'>
                                        <img src="/resource/pai/dp.png" alt="" style="width:100%"/>
                                        <img src="/resource/pai/dwr.png" alt="" style="position:absolute;z-index: 555;width:36%;bottom: 10%;right:12%"/>
                                        <img style="position:absolute;z-index: 999;top: 6%;left:10%;height:50%"  src="/resource/pai/dab.png" alt="" />
                                    </div>
                                    <?php }?>
                                <?php }?>

                                <?php if($hupai!=1){?>
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a class="btn btn-secondary btn-single" href="<?php echo \lib\Url::getUrl('demo-rummy-draw-dis-card');?>" target="_blank">执行摸牌</a>
                                <?php }?>

                                <?php if($start==1){?>


                                <?php }?>


                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2">
                                    <?php echo $ai_text;?>
                            </td>
                        </tr>
                        <?php foreach($group_list as $key=>$val){?>
                        <tr>
                            <td style="width: 10%">
                                <?php echo $key;?>
                            </td>
                            <td style="width: 90%">
                                <div class="row" >
                                    <?php foreach($val as $group_one){?>
                                    &nbsp;&nbsp;&nbsp;&nbsp;
                                    <?php foreach($group_one as $spnum){?>
                                    <?php $spnum = get_card_info($spnum);?>
                                    <?php if($spnum['type']!='w' && $spnum['num']<=10){?>
                                    <div class="col-xl-1">
                                        <img src="/resource/pai/dp.png" alt="" style="width:100%"/>
                                        <img style="position:absolute;z-index: 999;top: 6%;left:10%;height:16%"  src="/resource/pai/<?php echo $spnum['num'];?>.png" alt="" />
                                        <img style="position:absolute;z-index: 999;top: 26%;left:8%;height:14%"  src="/resource/pai/<?php echo $spnum['type'];?>.png" alt="" />
                                        <?php if($spnum['num'] == $lcard['num'] && $lcard['type']!='w'){?>
                                        <img src="/resource/pai/dwr.png" alt="" style="position:absolute;z-index: 999;top: 48%;left:8%;height:18%"/>
                                        <?php }?>
                                        <img style="position:absolute;z-index: 999;bottom: 10%;right:12%;height:26%" src="/resource/pai/<?php echo $spnum['type'];?>.png" alt="" />
                                    </div>
                                    <?php }?>

                                    <?php if($spnum['type']!='w' && $spnum['num']>10){?>
                                    <div class="col-xl-1" style='width:20px;'>
                                        <img src="/resource/pai/dp.png" alt="" style="width:100%"/>
                                        <img src="/resource/pai/<?php echo $spnum['num'];?>r.png" alt="" style="position:absolute;z-index: 555;width:36%;bottom: 10%;right:12%"/>
                                        <img style="position:absolute;z-index: 999;top: 6%;left:10%;height:16%"  src="/resource/pai/<?php echo $spnum['num'];?>.png" alt="" />
                                        <?php if($spnum['num'] == $lcard['num'] && $lcard['type']!='w'){?>
                                        <img src="/resource/pai/dwr.png" alt="" style="position:absolute;z-index: 999;top: 48%;left:8%;height:18%"/>
                                        <?php }?>
                                        <img style="position:absolute;z-index: 999;top: 26%;left:9%;height:14%"  src="/resource/pai/<?php echo $spnum['type'];?>.png" alt="" />
                                    </div>
                                    <?php }?>

                                    <?php if($spnum['type']=='w'){?>
                                    <div class="col-xl-1" style='width:20px;'>
                                        <img src="/resource/pai/dp.png" alt="" style="width:100%"/>
                                        <img src="/resource/pai/dwr.png" alt="" style="position:absolute;z-index: 555;width:36%;bottom: 10%;right:12%"/>
                                        <img style="position:absolute;z-index: 999;top: 6%;left:10%;height:50%"  src="/resource/pai/dab.png" alt="" />
                                    </div>
                                    <?php }?>
                                    <?php }?>
                                    <?php }?>
                                </div>
                            </td>
                        </tr>
                        <?php }?>
                    </table>
                </div>
            </div> <!-- content -->
        </div> <!-- content-page -->
    </div> <!-- end wrapper-->

    <!-- ============================================================== -->
    <!-- End Page content -->
    <!-- ============================================================== -->


</div>
<!-- END Container -->
<div class="rightbar-overlay"></div>
<!-- /Right-bar -->

</body>
</html>