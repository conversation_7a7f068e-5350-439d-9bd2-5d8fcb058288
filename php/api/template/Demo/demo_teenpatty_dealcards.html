<?php error_reporting(0);//不会弹出警告，只适合当前页?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>ai-TeenPatti 3张 模拟发牌</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="A fully featured admin theme which can be used to build CRM, CMS, etc." name="description" />
    <meta content="Coderthemes" name="author" />
    <!-- App favicon -->
    <link rel="shortcut icon" href="/resource/assets/images/favicon.ico">

    <!-- App css -->
    <link href="/resource/assets/css/icons.min.css" rel="stylesheet" type="text/css" />
    <link href="/resource/assets/css/app.min.css?v=11" rel="stylesheet" type="text/css" />
    <style>
        .col-xl-1{
            flex: 0 0 7.33333%;
            padding:0px;
        }

    </style>

</head>


<div class="container-fluid">

    <!-- Begin page -->
    <div class="wrapper">
        <!-- ========== Left Sidebar Start ========== -->
        <?php require_once realpath(dirname(__FILE__). '/../').'/Public/nav.html';?>
        <div class="content-page">
            <div class="content">
                <div class="row col-xl-12" id="lmacardbox">
                </div>
                <!-- end row-->
                <div class="row">
                    <table class="table table-striped table-centered mb-0" width="100%">
                        <tr>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>

                        <?php foreach($group_list as $k=>$v){?>
                        <tr>

                            <td style="width: 10%">
                                <?php echo '玩家'.$k;?>
                                <?php echo $v['utype']==0?'<span style="color:green">真人</span>':'<span style="color:red">AI</span>';?>
                            </td>
                            <td style="width: 90%">
                                <div class="row" >
                                    <?php foreach($v['ucards'] as $key=>$val){?>

                                    <div class="col-xl-1" style="display: flex;margin-right: 40px;">
                                        <div style="align-self: center;">
                                            <p style="margin: 0px;"><?php echo '分值：'.$val['cards_score'];?></p>
                                            <p style="margin: 0px;color: red"><?php echo '牌型：'.$val['group_name'];?></p>
                                            <p style="margin: 0px;"><?php echo '排名：'.$val['rank'];?></p>
                                            <p style="margin: 0px;"><?php echo '最大花色：'.$val['max_color'];?></p>
                                        </div>
                                    </div>

                                    <?php foreach($val['cards'] as $spnum){?>
                                    <?php $spnum = get_card_info($spnum);?>

                                    <?php if($spnum['type']!='w' && $spnum['num']<=10){?>
                                    <div class="col-xl-1" style="border-bottom: 3px solid #6c757d">
                                        <img src="/resource/pai/dp.png" alt="" style="width:100%"/>
                                        <img style="position:absolute;z-index: 999;top: 6%;left:10%;height:16%"  src="/resource/pai/<?php echo $spnum['num'];?>.png" alt="" />
                                        <img style="position:absolute;z-index: 999;top: 26%;left:8%;height:14%"  src="/resource/pai/<?php echo $spnum['type'];?>.png" alt="" />

                                        <img style="position:absolute;z-index: 999;bottom: 10%;right:12%;height:26%" src="/resource/pai/<?php echo $spnum['type'];?>.png" alt="" />
                                    </div>
                                    <?php }?>

                                    <?php if($spnum['type']!='w' && $spnum['num']>10){?>
                                    <div class="col-xl-1" style='width:20px;border-bottom: 3px solid #6c757d'>
                                        <img src="/resource/pai/dp.png" alt="" style="width:100%"/>
                                        <img src="/resource/pai/<?php echo $spnum['num'];?>r.png" alt="" style="position:absolute;z-index: 555;width:36%;bottom: 10%;right:12%"/>
                                        <img style="position:absolute;z-index: 999;top: 6%;left:10%;height:16%"  src="/resource/pai/<?php echo $spnum['num'];?>.png" alt="" />

                                        <img style="position:absolute;z-index: 999;top: 26%;left:9%;height:14%"  src="/resource/pai/<?php echo $spnum['type'];?>.png" alt="" />
                                    </div>
                                    <?php }?>
                                    <?php }?>

                                    <div class="col-xl-1" style="display: flex">

                                    </div>
                                    <?php }?>
                                </div>

                            </td>

                        </tr>
                        <?php }?>
                    </table>
                </div>
            </div> <!-- content -->
        </div> <!-- content-page -->
    </div> <!-- end wrapper-->

    <!-- ============================================================== -->
    <!-- End Page content -->
    <!-- ============================================================== -->


</div>
<!-- END Container -->
<div class="rightbar-overlay"></div>
<!-- /Right-bar -->

</body>
</html>