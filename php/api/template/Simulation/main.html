<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>数据模拟导出</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="A fully featured admin theme which can be used to build CRM, CMS, etc." name="description" />
    <meta content="Coderthemes" name="author" />
    <!-- App favicon -->
    <link rel="shortcut icon" href="/resource/assets/images/favicon.ico">

    <!-- App css -->
    <link href="/resource/assets/css/icons.min.css" rel="stylesheet" type="text/css" />
    <link href="/resource/assets/css/app.min.css?v=11" rel="stylesheet" type="text/css" />
    <style>
        /* 下拉框样式 */
        select {
            padding: 8px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: #fff;
            font-size: 14px;
            color: #333;
            margin-bottom: 10px;
        }

        /* 输入框样式 */
        input[type="text"] {
            padding: 8px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: #fff;
            font-size: 14px;
            color: #333;
        }

        /* 按钮样式 */
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            background-color: #007bff;
            color: #fff;
            font-size: 14px;
            cursor: pointer;
        }

        button:hover {
            background-color: #0056b3;
        }

        /* 标题样式 */
     .title {
            font-weight: bold;
            margin-bottom: 5px;
        }
    </style>

    <!-- 引入vue3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

    <!-- 引入arcoVue用来开发后台样式 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@arco-design/web-vue@2.56.2/dist/arco.min.css">
    <script src="https://cdn.jsdelivr.net/npm/@arco-design/web-vue@2.56.2/dist/arco-vue.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@arco-design/web-vue@2.56.2/dist/arco-vue-icon.min.js"></script>

</head>

<body class="bg">
    <div class="container-fluid">

        <!-- Begin page -->
        <div class="wrapper">
            <!-- ========== Left Sidebar Start ========== -->
            <?php require_once realpath(dirname(__FILE__). '/../').'/Public/nav.html';?>
            <!-- Left Sidebar End -->

            <div class="content-page">

                <div class="content">

                    <div class="row">

                        <div class="col-lg-12" style="width:200%;max-width:200%;overflow-x: auto;">
                            <div class="card">
                                <div class="card-body">
                                    <h2>数据模拟</h2><br>
                                    <div id="app">
                                        <div class="title">玩法列表</div>
                                        <select v-model="selectedGameId" @change="updateSelectedGame">
                                            <!-- 遍历处理后的游戏列表数组，同时绑定每个选项的id和值 -->
                                            <option v-for="(game, index) in processedGameList" :key="index" :value="game.id"
                                                    :text="game.name">{{ game.name }}</option>
                                        </select>
                                        <div class="title" style="margin-top: 15px;">下注金额₹</div>
                                        <input type="text" v-model="betFee" placeholder="默认20 选填" />
                                        <br>

                                        <div class="title" style="margin-top: 15px;">模拟次数</div>
                                        <input type="text" v-model="num" placeholder="默认10W 选填" />
                                        <br>

                                        <div class="title" style="margin-top: 15px;">可选参数</div>
                                        <input type="text" v-model="extraParam" placeholder="查看下方规则说明后填写" />
                                        <br><br>

                                        <button @click="confirmSubmit">确定</button>
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;

                                        <template v-if="isLoading">
                                            <a-spin :size="40" :tip="'正在加载...'" :delay="0"></a-spin>
                                        </template>

                                        <br><br><br>
                                        <a-collapse :bordered="false">
                                            <a-collapse-item header="规则说明" key="1">
                                                <pre>
                                                    {{ collapseItemContent }}
                                                </pre>
                                            </a-collapse-item>
                                        </a-collapse>

                                    </div>
                                </div>
                            </div>
                        </div> <!-- end custom accordions-->
                    </div> <!-- end col -->


                </div>


            </div>
        </div> <!-- content-page -->

    </div> <!-- end wrapper-->

    <!-- App js -->
    <script src="/resource/assets/js/app.min.js"></script>

    <script>
        const { createApp } = Vue;
        const { Collapse, CollapsePanel } = ArcoVue;
        const { Spin } = ArcoVue;

        const app = createApp({
            data() {
                return {
                    // 服务器发过来的玩法列表
                    gameList: [],
                    // 处理游戏列表数组
                    processedGameList: [],
                    // 下拉框选择的玩法
                    selectedGameId: null,
                    // 输入框填写的额外参数
                    extraParam: '',
                    // 输入框填写的模拟次数
                    num: '',
                    // 输入框填写的下注金额
                    betFee: '',
                    // 用于存储不同游戏玩法对应的规则说明内容
                    gameRules: {
                        // 游戏玩法的id作为键，对应的规则说明内容作为值
                        // 23 SuperSixer
                        '23': `
模拟规则如下：
只要当前积攒的奖励达到一定条件，就收奖，收奖后重置所有进度

收奖条件：
>= 2倍收奖                ：可选参数传2
>= 5倍收奖                ：可选参数传5
>= 10倍收奖               ：可选参数传10
>= 20倍收奖               ：可选参数传20
>= 50倍收奖               ：可选参数传50
>= 100倍收奖              ：可选参数传100
>= 300倍收奖              ：可选参数传300
获得Top Prize后收奖       ：可选参数传10000
获得1次超额奖后收奖       ：可选参数传10001
获得2次超额奖后收奖       ：可选参数传10002
获得3次超额奖后收奖       ：可选参数传10003`,
                    },
                    // 绑定到手风琴折叠面板显示规则说明的内容
                    collapseItemContent: '',
                    // 控制加载效果的显示与隐藏
                    isLoading: false
                };
            },
            mounted() {
                // 这里接收到的是原始游戏列表数组
                let game_list_str = '<?php echo json_encode($game_list);?>';
                this.gameList = JSON.parse(game_list_str);

                // 处理游戏列表数组，将其转换为包含id和名称的对象数组，以便正确展示在下拉列表中并获取id
                this.processedGameList = Object.entries(this.gameList).map(([id, name]) => ({ id: parseInt(id), name }));
            },
            methods: {
                updateSelectedGame() {
                    console.log(this.selectedGameId)
                    // 根据选择的游戏玩法id，从原始游戏列表中获取对应的玩法名称，更新selectedGame
                    this.selectedGame = this.gameList[this.selectedGameId];

                    // 根据选择的游戏玩法id，更新折叠面板的规则说明文字内容
                    if (this.gameRules[this.selectedGameId]) {
                        this.collapseItemContent = this.gameRules[this.selectedGameId];
                    }
                },
                confirmSubmit() {
                    // 设置为加载状态，显示加载动画
                    this.isLoading = true;
                    this.submitData();
                },
                submitData() {
                    const cls = this.selectedGameId;
                    const extra_param = this.extraParam;
                    const num = this.num;
                    const bet_fee = this.betFee;

                    // 使用fetch API发送POST请求
                    let url = "<?php echo \lib\Url::getAjaxUrl('simulation');?>";
                    fetch(url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            cls: cls,
                            extra_param: extra_param,
                            num: num,
                            bet_fee: bet_fee,
                        })
                    })
                 .then(response => {
                        /*
                          record by jingzhao:
                          虽然导出这个功能是在服务器实现的，但是使用fetch这样的库来请求下载接口，是无法唤起浏览器的下载弹窗的。
                          搜了搜资料，需要前端写代码处理一下服务器返回的流数据
                          大致的意思就是将流文件利用Blob构造函数，构造一个Blob对象，再利用URL的createObjectURL生成一个链接，最后将这个链接赋值给一个动态生成的a标签的 href属性，最后触发一下click事件。这样浏览器就能自动触发下载文件了。
                        */

                        // 请求完成，设置为非加载状态，隐藏加载动画
                        this.isLoading = false;
                        // 检查响应状态码是否在200 - 299之间，表示成功
                        if (!response.ok) {
                            throw new Error('网络请求失败，状态码：' + response.status);
                        }
                        // 获取响应的Content-Type头信息，判断是否为流数据（这里假设是常见的流数据类型，如application/octet-stream等）
                        const contentType = response.headers.get('Content-Type');
                        if (contentType && (contentType.includes('application/octet-stream') || contentType.includes('text/csv'))) {
                            // 创建一个新的Blob对象，将响应流数据作为参数传入
                            return response.blob();
                        } else {
                            throw new Error('返回的数据类型不支持下载');
                        }
                    })
                 .then(blob => {
                        // 创建一个URL对象，指向Blob对象
                        const url = window.URL.createObjectURL(blob);
                        // 创建一个 <a> 标签元素
                        const a = document.createElement('a');
                        // 设置 <a> 标签的 href 属性为Blob对象的URL
                        a.href = url;
                        // 设置 <a> 标签的 download 属性为服务器设置的文件名（这里从content-disposition头信息中获取，示例中为SuperSixer.csv）
                        a.download = 'SuperSixer.csv';
                        // 将 <a> 标签添加到文档 body 中
                        document.body.appendChild(a);
                        // 模拟点击 <a> 标签，触发浏览器下载
                        a.click();
                        // 移除 <a> 标签
                        document.body.removeChild(a);
                        // 解放创建的URL对象
                        window.URL.revokeObjectURL(url);
                    })
                 .catch(error => {
                        // 请求出错，设置为非加载状态，隐藏加载动画
                        this.isLoading = false;
                        console.error('请求出错：', error);
                    });
                }
            }
        });

        app.use(Collapse);
        app.component('a-collapse-panel', CollapsePanel);
        app.use(Spin);
        app.mount('#app');
    </script>

</body>

</html>
