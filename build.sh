


# https://stackoverflow.com/questions/29832037/how-to-get-script-directory-in-posix-sh
gScriptDir=$(CDPATH= cd -- "$(dirname -- "$0")" && pwd)
cd "$gScriptDir"

. "$gScriptDir/deploy/cppenv/cppenv_shbase.sh"

set -x



os_name=$(uname)
if [ "$os_name" == "Linux" ]; then
  export PATH=$PATH:/usr/local/go/bin
  export GOPROXY=https://mirrors.aliyun.com/goproxy/,https://goproxy.cn,direct
  export GOPROXY=https://goproxy.cn,direct
  export GOSUMDB=off
fi


# -race 多线程安全检测
GO_BUILD_RACE="-race"
GO_BUILD_RACE=""
# -N 禁用优化  -l 禁用内联
GO_BUILD_GCFLAGS="-N -l"
GO_BUILD_GCFLAGS=""
# gin 会用到 -tags=jsoniter
GO_BUILD_TAGS=jsoniter
GO_BUILD_LDFLAGS=""


echo "">pkg/buildinfo/buildinfo.txt
echo "GIT_COMMIT=`git rev-parse HEAD`">>pkg/buildinfo/buildinfo.txt
echo "GIT_BRANCH=`git rev-parse --abbrev-ref HEAD`">>pkg/buildinfo/buildinfo.txt
echo "BUILD_TIME=`date -R`">>pkg/buildinfo/buildinfo.txt
echo "GO_BUILD_RACE=$GO_BUILD_RACE">>pkg/buildinfo/buildinfo.txt
echo "GO_BUILD_GCFLAGS=$GO_BUILD_GCFLAGS">>pkg/buildinfo/buildinfo.txt
echo "GO_BUILD_TAGS=$GO_BUILD_TAGS">>pkg/buildinfo/buildinfo.txt
echo "GO_BUILD_LDFLAGS=$GO_BUILD_LDFLAGS">>pkg/buildinfo/buildinfo.txt
echo "GO_VERSION=`go version`">>pkg/buildinfo/buildinfo.txt



go mod download

mkdir -p bin

if [[ "$1" == "" ]] || [[ "$1" == "pushmetrics" ]]; then
CGO_ENABLED=0 go build $GO_BUILD_RACE -gcflags "$GO_BUILD_GCFLAGS" -tags="$GO_BUILD_TAGS" -ldflags "${GO_BUILD_LDFLAGS}" -o bin/ ./cmd/pushmetrics/
fi


if [[ "$1" == "" ]] || [[ "$1" == "dotserver" ]]; then
CGO_ENABLED=0 go build $GO_BUILD_RACE -gcflags "$GO_BUILD_GCFLAGS" -tags="$GO_BUILD_TAGS" -ldflags "${GO_BUILD_LDFLAGS}" -o bin/ ./cmd/dotserver/
fi


if [[ "$1" == "" ]] || [[ "$1" == "attributionserver" ]]; then
CGO_ENABLED=0 go build $GO_BUILD_RACE -gcflags "$GO_BUILD_GCFLAGS" -tags="$GO_BUILD_TAGS" -ldflags "${GO_BUILD_LDFLAGS}" -o bin/ ./cmd/attributionserver/
fi


if [[ "$1" == "" ]] || [[ "$1" == "chbuf" ]]; then
CGO_ENABLED=0 go build $GO_BUILD_RACE -gcflags "$GO_BUILD_GCFLAGS" -tags="$GO_BUILD_TAGS" -ldflags "${GO_BUILD_LDFLAGS}" -o bin/ ./cmd/chbuf/
fi


if [[ "$1" == "" ]] || [[ "$1" == "alertbot" ]]; then
CGO_ENABLED=0 go build $GO_BUILD_RACE -gcflags "$GO_BUILD_GCFLAGS" -tags="$GO_BUILD_TAGS" -ldflags "${GO_BUILD_LDFLAGS}" -o bin/ ./cmd/alertbot/
fi


if [[ "$1" == "" ]] || [[ "$1" == "sdserver" ]]; then
CGO_ENABLED=0 go build $GO_BUILD_RACE -gcflags "$GO_BUILD_GCFLAGS" -tags="$GO_BUILD_TAGS" -ldflags "${GO_BUILD_LDFLAGS}" -o bin/ ./cmd/sdserver/
fi

if [[ "$1" == "" ]] || [[ "$1" == "mybuf" ]]; then
CGO_ENABLED=0 go build $GO_BUILD_RACE -gcflags "$GO_BUILD_GCFLAGS" -tags="$GO_BUILD_TAGS" -ldflags "${GO_BUILD_LDFLAGS}" -o bin/ ./cmd/mybuf/
fi




