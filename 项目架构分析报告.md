# 项目架构分析报告

## 项目概述

这是一个基于 Go 和 PHP 的在线游戏平台，主要提供印度市场的卡牌游戏服务，包括 Rummy（拉米纸牌）、Teen Patti（三张牌）等多种游戏。项目采用微服务架构，Go 负责基础设施服务，PHP 负责游戏业务逻辑。

## 技术架构

### Go 微服务架构

#### 核心服务组件

1. **alertbot** (端口: 3305)
   - **功能**: 告警机器人服务，接收各种系统告警并转发到 Telegram
   - **技术栈**: Gin HTTP 框架
   - **主要功能**:
     - 接收 Prometheus Alertmanager 告警
     - 接收 AWS SNS 通知
     - 支持 Telegram Webhook
     - 告警消息聚合和限流

2. **attributionserver** (端口: 3303)
   - **功能**: 用户归因服务，处理 Adjust SDK 回调，记录用户来源
   - **技术栈**: Gin + MySQL + Redis
   - **主要功能**:
     - 处理 Adjust 广告归因回调
     - 记录用户安装来源（Facebook、Google、有机流量等）
     - 用户设备信息收集
     - 地理位置信息处理

3. **chbuf** (端口: 3320/3321)
   - **功能**: ClickHouse 缓冲写入服务
   - **技术栈**: TCP 服务器 + HTTP 监控
   - **主要功能**:
     - 按表名分组缓冲 ClickHouse 写入请求
     - 批量写入优化（超时或超量触发）
     - JSONEachRow 格式数据处理

4. **dotserver** (端口: 3302)
   - **功能**: 第三方打点服务，异步调用各种 SDK
   - **技术栈**: Redis 队列 + 多 SDK 集成
   - **主要功能**:
     - Adjust S2S API 调用
     - Facebook Conversions API
     - Google Analytics 4
     - Firebase Analytics
     - Roibest API
   - 下面把 dotserver 里提到的 5 个“第三方”分别用一句话说清“它是什么、干什么、为什么 dotserver 要调它”。
      Adjust S2S API 调用
      • 它是 Adjust 提供的“服务器-到-服务器”接口，用来把安装、付费等事件直接发到 Adjust，不走客户端 SDK。
      • dotserver 用它：当客户端没装 Adjust SDK 或想隐藏敏感数据时，后端代发事件，照样做归因和 ROI 分析。
      Facebook Conversions API（CAPI）
      • Facebook 的“服务器端像素”，把转化事件（购买、注册等）直接发到 Meta 的广告系统，与 Facebook Pixel 互为备份。
      • dotserver 用它：iOS 14+ 限制客户端数据，CAPI 通过服务器补全事件，提高广告匹配率和投放效果。
      Google Analytics 4（GA4）
      • Google 的新一代网站/APP 分析平台，用事件模型统计流量、留存、转化。
      • dotserver 用它：把后端业务事件（订单、关卡通过等）实时送进 GA4，补充前端埋点，做统一漏斗分析。
      Firebase Analytics（FA）
      • Google 专为移动 App 做的埋点&分析 SDK，数据直接进 Firebase 控制台和 BigQuery。
      • dotserver 用它：当客户端 Firebase SDK 缺事件或需要修正时，后端补发，保证 DAU、留存、LTV 指标完整。
      Roibest API
      • Roibest（若博斯特）是一家做“延迟深度链接 + 再营销归因”的 MMP（类似 Adjust/AppsFlyer）。
      • dotserver 用它：把安装、打开、付费等事件回传给 Roibest，用于做“一键拉起 App”和广告归因，尤其在国内安卓渠道常用。
      一句话总结：dotserver 扮演“统一打点网关”，把这 5 家第三方需要的事件数据，通过各自的服务器端 API 异步推过去，既解耦业务代码，又保证数据完整。

5. **mybuf** (端口: 3324/3325)
   - **功能**: MySQL 异步写入缓冲服务
   - **技术栈**: TCP 服务器 + MySQL 连接池
   - **主要功能**:
     - SQL 语句缓冲和批量执行
     - 按表名分组优化
     - 网络错误重试和熔断机制

6. **pushmetrics** (端口: 3300/3301)
   - **功能**: 指标收集服务，为 PHP 应用提供 Prometheus 指标导出
   - **技术栈**: TCP 服务器 + Prometheus 格式导出
   - **主要功能**:
     - Counter 和 Gauge 指标类型支持
     - 内存中指标聚合
     - 基数爆炸保护

7. **sdserver** (端口: 3322)
   - **功能**: 服务发现服务，为 Prometheus 提供动态服务发现
   - **技术栈**: Gin + Redis
   - **主要功能**:
     - 服务注册和发现
     - Prometheus HTTP SD 格式支持

#### Go 技术栈总结
- **Web 框架**: Gin
- **数据库**: MySQL (sqlx), Redis (go-redis)
- **监控**: Prometheus 指标导出
- **日志**: zerolog 结构化日志
- **配置**: 基于 Redis 的动态配置系统
- **通信**: TCP/HTTP 混合协议

### PHP 业务架构

#### 核心业务模块

1. **游戏服务器** (Workerman 架构)
   - **serverws.php**: WebSocket 网关服务
   - **serverbiz.php**: 业务逻辑处理服务
   - **serverreg.php**: 服务注册中心
   - **serverhttp.php**: HTTP API 服务

2. **游戏 API 服务**
   - **位置**: `php/api/`
   - **功能**: 游戏 AI 决策 API
   - **主要接口**:
     - Rummy 游戏 AI（组牌、摸牌、打牌、弃牌）
     - Teen Patti 游戏 AI（发牌、操作决策）
     - 盈利率控制系统

3. **客服系统**
   - **位置**: `php/kefu/`
   - **技术栈**: ThinkPHP 5 + Workerman
   - **功能**: 在线客服聊天系统

4. **支付系统**
   - **位置**: `php/pay/`
   - **技术栈**: ThinkPHP 5
   - **功能**: 第三方支付集成

5. **管理后台**
   - **位置**: `php/console/`
   - **技术栈**: ThinkPHP 5
   - **功能**: 游戏管理和数据分析

#### PHP 核心库结构

1. **lbase** - 基础工具库
   - 数据库连接管理 (MySQL, ClickHouse, Redis)
   - 网络通信工具
   - 日志系统
   - 指标收集

2. **lconfig** - 配置管理
   - 动态配置加载
   - 多环境配置支持
   - 支付配置管理

3. **ldb** - 数据访问层
   - 用户数据管理
   - 房间数据管理
   - 配置数据管理
   - Redis 脚本管理

4. **llogic** - 业务逻辑层
   - 游戏逻辑实现
   - 用户管理
   - 支付逻辑
   - 活动系统
   - 统计分析

5. **lproto** - 协议定义
   - 前端消息协议
   - 用户数据结构
   - 配置数据结构

#### PHP 技术栈总结
- **框架**: Workerman (高性能) + ThinkPHP 5 (管理后台)
- **数据库**: MySQL, ClickHouse, Redis
- **队列**: Redis 队列
- **WebSocket**: Workerman Gateway
- **依赖管理**: Composer
- **第三方集成**:
  - 阿里云 CDN/DNS
  - AWS SDK
  - GeoIP2
  - PhpSpreadsheet

## 业务架构

### 游戏类型

1. **Rummy Points** (拉米积分制)
2. **Teen Patti** (印度三张牌)
3. **AK47** (Teen Patti 变种)
4. **TP Joker** (带鬼牌的 Teen Patti)
5. **百人游戏**:
   - Andar Bahar (安达巴哈尔)
   - Dragon vs Tiger (龙虎斗)
   - Lucky Dice (幸运骰子)
   - Red vs Black (红黑大战)
   - 等多种快节奏博彩游戏

### 核心业务流程

1. **用户系统**
   - 用户注册/登录
   - KYC 身份验证
   - 钱包系统（现金/练习币）
   - 邀请推广系统

2. **游戏系统**
   - 房间匹配系统
   - AI 对手系统
   - 实时游戏状态同步
   - 游戏结算系统

3. **支付系统**
   - 多渠道充值
   - 提现管理
   - 交易记录
   - 风控系统

4. **运营系统**
   - 活动管理
   - 客服系统
   - 数据分析
   - 用户行为追踪

### AI 系统

项目包含复杂的 AI 系统：

1. **游戏 AI**
   - Rummy AI：智能组牌、摸牌决策、打牌策略
   - Teen Patti AI：发牌控制、下注决策、比牌策略

2. **盈利控制系统**
   - 基于用户画像的发牌控制
   - 新手保护机制
   - 大户控制策略
   - 盈利率动态调整

## 数据架构

### 数据存储

1. **MySQL**: 用户数据、游戏记录、交易数据
2. **Redis**: 缓存、会话、实时数据、队列
3. **ClickHouse**: 大数据分析、用户行为数据

### 数据流

1. **实时数据**: WebSocket → Workerman → Redis
2. **分析数据**: PHP → chbuf → ClickHouse
3. **持久化数据**: PHP → mybuf → MySQL
4. **指标数据**: PHP → pushmetrics → Prometheus

## 监控和运维

### 监控系统
- **Prometheus**: 指标收集
- **Grafana**: 数据可视化
- **Alertmanager**: 告警管理
- **VictoriaMetrics**: 时序数据存储

### 日志系统
- **结构化日志**: zerolog (Go), 自定义日志 (PHP)
- **日志聚合**: Vector
- **告警通知**: Telegram Bot

### 部署架构
- **容器化**: 支持 systemd 服务管理
- **负载均衡**: Nginx
- **服务发现**: 自研 sdserver
- **配置管理**: 基于 Redis 的动态配置

## 安全和合规

1. **地理位置限制**: 主要面向印度市场
2. **KYC 验证**: 身份认证系统
3. **风控系统**: 异常行为检测
4. **数据加密**: 敏感数据加密存储
5. **审计日志**: 完整的操作记录

## 项目特点

1. **高并发**: Workerman + Go 微服务架构
2. **实时性**: WebSocket 实时通信
3. **可扩展**: 微服务架构，易于水平扩展
4. **数据驱动**: 完整的数据收集和分析系统
5. **AI 驱动**: 智能游戏对手和盈利控制
6. **多语言**: 支持多种印度本地语言
7. **合规性**: 符合印度游戏法规要求

这个项目是一个成熟的在线游戏平台，技术架构合理，业务逻辑完整，具有很强的商业价值和技术参考价值。