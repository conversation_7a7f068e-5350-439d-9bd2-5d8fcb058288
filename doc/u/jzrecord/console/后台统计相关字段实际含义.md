$$ 此文档会把console后台,统计这块所有用英文缩写的字段名 ,解释一下是什么含义 $$
$$ 代码几乎没有注释,所以很多字段含义只能靠猜, 猜不出来的就不记录了 $$

# console
- 后台分类名称：console
- 服务端接口路径：console -> application -> index -> controller -> main.php -> index()
  

| 字段          | 含义                   | 服务器赋值的字段名（产品请忽略该列） |
| ------------- | ---------------------- | ---- |
| Reg  | 今日注册数量     | registerCount   |
| total Reg | 总注册数量     | registerCountTotal   |
| Dau-rt | 今日活跃   | activeCount   |
| RE num n/o      | 新客充值次数               | payCount_new   |
| Fee     | 今日佣金       | commissionCount   |
|  Total Fee     | 佣金总额     | commissionFeeTotal   |
| P-cost             | 看代码含义是：输了多少钱（目前不太确定）     | give_lost下的lost |
| P-gain        | 看代码含义是：赢了多少钱（目前不太确定）               | give_lost下的win   |
| Rev        | AI营收               | aiWin   |
| n/RE        | 充值金额（保留两位小数）/ 今日充值金额！！！！！！！               | payCount_new_amt / payCount   |
| RE(total)            | 总充值金额 |   rechargeAmountTotal   |
| W        | 今日提现金额               |   withCount |
| W(Total)            | 总提现金额 |withdrawAmountTotal|
| Bo-used        | 看代码含义应该是输钱返利的总额     | give_lost下的lost   |
| D-gift            | 看代码含义应该是注册+签到+拉新 总共送了多少现金 |give_lost下的give|
| F-RE-W            | 不清楚 |不清楚|
| RE/H            | 不清楚 |不清楚|
| REG/H            | 不清楚 |不清楚|
| ORDER            | 充值状态统计 ||
| Total            | 看代码含义是统计有多少人参与到了支付 无论失败与成功 都会统计 |rechargeStatus|
| wait            | 计算等待充值状态在总充值次数中所占的百分比，并将其四舍五入到最近的整数 |rechargeStatus|
| succ            | 计算充值成功状态在总充值次数中所占的百分比，并将其四舍五入到最近的整数 |rechargeStatus|
| Fail            | 计算充值失败状态在总充值次数中所占的百分比，并将其四舍五入到最近的整数 |rechargeStatus|








<br><br><br><br>
# 综合统计

## 新增用户留存
- 后台分类名称：新增用户留存
- 服务端接口路径：console -> application -> index -> controller -> stat.php -> userRetentionStat()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| Date  | 日期     |
| Reg | 该日期注册人数     | 
| D1  |   当天注册人数在一天内注册人数的占比   |
| D2  |   当天注册人数在两天内注册人数的占比   |
| D3  |   当天注册人数在三天内注册人数的占比  |
| D4  |   当天注册人数在四天内注册人数的占比  |
| D5-D180  |   以此类推  |






<br><br><br><br>
## 当日充值用户数据次日留存
- 后台分类名称：当日充值用户数据次日留存
- 服务端接口路径：console -> application -> index -> controller -> UserSecondStayData.php -> index()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| Date  | 日期     |
| ID | 用户id     | 
| Channel  |   用户渠道   |
| Retention?  |   是否登录（1：登录 2：未登录）   |
- 后面的字段都没有赋值 不知道为什么 可能是缺代码


<br><br><br><br>
## 货币统计
- 后台分类名称：货币统计
- 服务端接口路径：console -> application -> index -> controller -> UserBehaviorStatistics.php -> statisticsView()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| RE  | 充值金额总和     |
| RE fee | 充值金额乘以0.03，然后将结果四舍五入到小数点后两位后，再求总和     | 
| W  |   提现金额总和   |
| W fee?  |   提现手续费总和   |
| Bo-Used sum  |   返利金额总和   |
| D-gift sum  |   赠送金额总和   |
| Fee sum  |   佣金总和   |
| Date  |   添加时间   |
| RE  |   充值金额总和   |
| RE-credit  |   充值到账金额总和   |
| RE fee  |   充值金额乘以0.03，然后将结果四舍五入到小数点后两位后，再求总和   |
| W  |   提现金额总和   |
| W fee  |   提现手续费总和   |
| P-cost  |   输钱总和   |
| P-gain  |   赢钱总和   |
| matches  |   对局次数总和   |
| Bo-Used  |   返利金额总和   |
| D-gift  |   赠送金额总和   |
| Fee  |   佣金总和   |
| D-left  |   赠送淤积总和   |
| D-left Dau  |   活跃充值淤积总和   |
| W-left  |   余额淤积总和   |
| W-left Dau  |   当日活跃用户提现淤积总和   |
| Bo-left  |   返利淤积总和   |
| Bo-left Dau |   当日活跃返利淤积总和   |




<br><br><br><br>
## 房间统计
- 后台分类名称：房间统计
- 服务端接口路径：console -> application -> index -> controller -> UserBehaviorStatistics.php -> roomStatisticsView()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| Begin  | 开始时间     |
| End   | 结束时间    | 
| Type  |   玩法   |
| Currency |   币种   |
| Num of P  |   最大人数   |
| Point  |   底注   |
| Sort  |   按什么排序   |
| Total Room  |   房间数量总和   |
| Fee  |   当币种=1时，抽佣金额总和   |
| P-cost  |   输钱总和   |
| P-gain  |   赢钱总和   |
| Currency  |   币种   |
| Type  |   玩法   |
| Fee  |   抽佣金额总和   |
| Num of P  |   最大人数   |
| Point  |   底注乘以0.001，然后将结果四舍五入到小数点后两位   |
| matches  |   房间数量总和   |
| matches P*times  |   房间在线人数总和   |
| matches P  |   玩家和   |
| Participate rate  |   计算房间在线人数总和除以房间数量总和，然后将结果四舍五入到小数点后两位。   |
| P-cost match  |   赢钱总和   |
| P-gain match  |   输钱总和   |


<br><br><br><br>
## 新用户充值追踪
- 后台分类名称：新用户充值追踪 Type = Detail
- <a>注意！！！！！ 该分类里面会根据Type值的选择 有不同的统计页面 一个六个 我会在上面标注好type值↑</a>
- 服务端接口路径：console -> application -> index -> controller -> UserRechargeTrack.php -> indexView()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| Begin  | 开始筛选时间     |
| End   | 结束筛选时间    | 
| Type  |   一共六种类型 看代码看不出来每个类型叫什么名字   |
| &nbsp; | &nbsp;  |
| Date |   日期   |
| Reg  |   当日新增用户数量   |
| Y-Re  |   不确定 代码没有体现出来   |
| &nbsp; | &nbsp;  |
| Until-Y  |   &nbsp;    |
| RE Num  |   总充值人数   |
| RE  |  总充值金额    |
| W  |   总提现金额   |
| Fee  |   总佣金   |
| &nbsp; | &nbsp;  |
| D1  |      |
| RE Num  |   1天充值人数   |
| RE  |  1天充值金额    |
| W  |   1天提现金额   |
| Fee  |   1天佣金   |
| &nbsp; | &nbsp;  |
| D2  |      |
| RE Num  |   2天充值人数   |
| RE  |  2天充值金额    |
| W  |   2天提现金额   |
| Fee  |   2天佣金   |
| 往下以此类推  |   &nbsp;    |



<br><br><br><br>
- 后台分类名称：新用户充值追踪 Type = ROI
- <a>注意！！！！！ 该分类里面会根据Type值的选择 有不同的统计页面 一个六个 我会在上面标注好type值↑</a>
- 服务端接口路径：console -> application -> index -> controller -> UserRechargeTrack.php -> indexView()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| 代码看不出任何含义  |      |




<br><br><br><br>
- 后台分类名称：新用户充值追踪 Type = ΔRe/u	
- <a>注意！！！！！ 该分类里面会根据Type值的选择 有不同的统计页面 一个六个 我会在上面标注好type值↑</a>
- 服务端接口路径：console -> application -> index -> controller -> UserRechargeTrack.php -> indexView()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| Begin  | 开始筛选时间     |
| End   | 结束筛选时间    | 
| Type  |   一共六种类型 看代码看不出来每个类型叫什么名字   |
| &nbsp; | &nbsp;  |
| Date |   日期   |
| Reg  |   当日新增用户数量   |
| &nbsp; | &nbsp;  |
| Until-Y  |   &nbsp;    |
| ΔRe/u	  |   总充值金额 减去 总提现金额，然后除以当日新增用户数量，最后将结果四舍五入到小数点后两位   |
| RE  |  总充值金额    |
| W  |   总提现金额   |
| &nbsp; | &nbsp;  |
| D1  |      |
| Re  |   1天充值金额 减去 1天提现金额，然后除以当日新增用户数量，最后将结果四舍五入到小数点后两位   |
| &nbsp; | &nbsp;  |
| D2  |      |
| Re  |   2天充值金额 减去 2天提现金额，然后除以当日新增用户数量，最后将结果四舍五入到小数点后两位   |
| &nbsp; | &nbsp;  |
| D3 - D150  |   往下以此类推   |




<br><br><br><br>
- 后台分类名称：新用户充值追踪 Type = Re
- <a>注意！！！！！ 该分类里面会根据Type值的选择 有不同的统计页面 一个六个 我会在上面标注好type值↑</a>
- 服务端接口路径：console -> application -> index -> controller -> UserRechargeTrack.php -> indexView()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| Begin  | 开始筛选时间     |
| End   | 结束筛选时间    | 
| Type  |   一共六种类型 看代码看不出来每个类型叫什么名字   |
| &nbsp; | &nbsp;  |
| Date |   日期   |
| Reg  |   当日新增用户数量   |
| &nbsp; | &nbsp;  |
| Until-Y  |   &nbsp;    |
| Re/u  |   总充值金额 除以 当日新增用户数量，然后将结果四舍五入到小数点后两位   |
| RE  |  总充值金额    |
| W  |   总提现金额   |
| &nbsp; | &nbsp;  |
| D1  |      |
| Re  |   1天充值金额   |
| &nbsp; | &nbsp;  |
| D2  |      |
| Re  |   2天充值金额   |
| &nbsp; | &nbsp;  |
| D3 - D150  |   往下以此类推   |




<br><br><br><br>
- 后台分类名称：新用户充值追踪 Type = Re/u
- <a>注意！！！！！ 该分类里面会根据Type值的选择 有不同的统计页面 一个六个 我会在上面标注好type值↑</a>
- 服务端接口路径：console -> application -> index -> controller -> UserRechargeTrack.php -> indexView()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| Begin  | 开始筛选时间     |
| End   | 结束筛选时间    | 
| Type  |   一共六种类型 看代码看不出来每个类型叫什么名字   |
| &nbsp; | &nbsp;  |
| Date |   日期   |
| Reg  |   当日新增用户数量   |
| &nbsp; | &nbsp;  |
| Until-Y  |   &nbsp;    |
| Re/u  |   总充值金额 除以 当日新增用户数量，然后将结果四舍五入到小数点后两位   |
| RE  |  总充值金额    |
| W  |   总提现金额   |
| &nbsp; | &nbsp;  |
| D1  |      |
| Re/u  |   1天充值金额 除以 当日新增用户数量，然后将结果四舍五入到小数点后两位   |
| &nbsp; | &nbsp;  |
| D2  |      |
| Re/u  |   2天充值金额 除以 当日新增用户数量，然后将结果四舍五入到小数点后两位   |
| &nbsp; | &nbsp;  |
| D3 - D150  |   往下以此类推   |




<br><br><br><br>
- 后台分类名称：新用户充值追踪 Type = Free-gift
- <a>注意！！！！！ 该分类里面会根据Type值的选择 有不同的统计页面 一个六个 我会在上面标注好type值↑</a>
- 服务端接口路径：console -> application -> index -> controller -> UserRechargeTrack.php -> indexView()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| Begin  | 开始筛选时间     |
| End   | 结束筛选时间    | 
| Type  |   一共六种类型 看代码看不出来每个类型叫什么名字   |
| &nbsp; | &nbsp;  |
| Date |   日期   |
| Reg  |   当日新增用户数量   |
| &nbsp; | &nbsp;  |
| 再往下代码看不出任何含义了  |   &nbsp;    |




<br><br><br><br>
## 每日数据统计
- 后台分类名称：每日数据统计 
- <a>注意！！！！！ 这个功能有问题 我看代码里 它查的数据表我们现有的数据库里没有 查询的字段也没有 这个就不一行一行看代码了 具体字段含义参考导出excel的字段吧</a>
- 服务端接口路径：console -> application -> index -> controller -> UserBehaviorStatistics.php -> total()




<br><br><br><br>
## 综合查询
- 后台分类名称：综合查询 
- 服务端接口路径：console -> application -> index -> controller -> Search.php -> index()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| Date  | 日期     |
| Reg   | 在该日一共注册了多少人    | 
| Sign up  |   在该日一共有多少人签到  |
| Original match |   在该日有多少人进行了游戏（不确定 代码体现不出来）     |
| Plastic match |   代码看不出来是什么含义     |
| No match  |   在该日有多少人未开局游戏   |
| Match rate  |   代码看不出来是什么含义   |



<br><br><br><br>
## 充值行为统计
- 后台分类名称：充值行为统计
- 服务端接口路径：console -> application -> index -> controller -> RechargeBehaviorStatistics.php -> view()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| 代码看不出来是什么含义  |   |



<br><br><br><br>
## ACU APU
- 后台分类名称：ACU APU
- 服务端接口路径：console -> application -> index -> controller -> Stat.php -> acuPcuStat()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| 代码看不出来是什么含义  |   |



<br><br><br><br>
## 每小时充值注册统计
- 后台分类名称：每小时充值注册统计
- 服务端接口路径：console -> application -> index -> controller -> HourRechargeRegister.php -> index()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| Date  | 日期  |
| RE  | 充值金额  |
| Date  | 注册人数  |




<br><br><br><br>
## 付费用户留存
- 后台分类名称：付费用户留存
- 服务端接口路径：console -> application -> index -> controller -> stat.php -> rechargeUserRetentionStat()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| UID  | 用户id     |
| Info | 用户名/用户地址     | 
| D1  |   当天注册人数在一天内注册人数的占比   |
| D2  |   当天注册人数在两天内注册人数的占比   |
| D3  |   当天注册人数在三天内注册人数的占比  |
| D4  |   当天注册人数在四天内注册人数的占比  |
| D5-D180  |   以此类推  |



<br><br><br><br>
## 大R用户监测
- 后台分类名称：大R用户监测
- 服务端接口路径：console -> application -> index -> controller -> UserBehaviorStatistics.php -> rmbPlayer()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| Date  | 日期     |
| Reg | 该日期注册人数     | 
| Channel  |   渠道   |
| LastLog  |   上次登录时间   |
| Total  |   RE: 充值总额/ 1000 &nbsp;&nbsp;&nbsp; W: 提现总额 / 1000 |
| &nbsp;   |   &nbsp;  |
| Last1  |   &nbsp;  |
| data |   RE: 昨日充值总额/ 1000 &nbsp;&nbsp;&nbsp; W: 昨日提现总额 / 1000 |
| matches |   昨日玩了什么玩法，以及玩了多少局  |
| &nbsp;   |   &nbsp;  |
| Last2  |     |
| data |   RE: 两天内充值总额/ 1000 &nbsp;&nbsp;&nbsp; W: 两天内提现总额 / 1000  |
| matches |   两天内玩了什么玩法，以及玩了多少局  |
| &nbsp;   |   &nbsp;  |
| Last1-7  |     |
| data |   RE: 七天内充值总额/ 1000 &nbsp;&nbsp;&nbsp; W: 七天内提现总额 / 1000;  |
| matches |   七天内玩了什么玩法，以及玩了多少局  |



<br><br><br><br>
## bonus明细
- 后台分类名称：bonus明细
- 服务端接口路径：console -> application -> index -> controller -> BonusStatistics.php -> View()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| Date  | 日期     |
| System out | bonus总赠送     | 
| new reg  |   新用户赠送bonus   |
| RE act  |   充值活动赠送   |
| V Gift  |   vip礼包赠送 |
| V Pri  |   vip充值赠送  |
| Coupon  |   月卡赠送 |
| manual |   人工赠送  |
| Used   |  bonus抵扣  |
| Bo-left Dau  |  bonus淤积   |
| Bo-Have Dau |   bonus有效淤积  |
| Have rate |   有效Bonus拥有率的平均值 |


<br><br><br><br>
## 月卡统计
- 后台分类名称：月卡统计
- 服务端接口路径：console -> application -> index -> controller -> CardStatistics.php -> View()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| Date  | 日期     |
| Dau | 当日活跃用户量     | 
| lv1-get  |   7日卡购买人数   |
| lv1-have |   7日卡购买数   |
|	lv1-rate |   如果当日活跃用户量大于0，则计算7日卡购买数 除以 当日活跃用户量 乘以100并保留两位小数，否则返回0 |
| lv2-get  |   14日卡购买人数  |
| lv2-have  |   14日卡购买数 |
| lv2-rate |   如果当日活跃用户量大于0，则计算14日卡购买数 除以 当日活跃用户量 乘以100并保留两位小数，否则返回0  |
| lv3-get   | 30日卡购买人数  |
| lv3-have  |  30日卡购买数   |
| lv3-rate |   如果当日活跃用户量大于0，则计算30日卡购买数 除以 当日活跃用户量 乘以100并保留两位小数，否则返回0  |


<br><br><br><br>
# 用户管理
## 用户信息管理
- 后台分类名称：用户信息管理
- 服务端接口路径：console -> application -> index -> controller -> CardStatistics.php -> getUserList()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| 标题栏的records  | 代表查询到了几条记录     |
| Reg Begin | 筛选的开始时间     | 
| Reg End  |   筛选的结束时间   |
| UID |   用户id   |
|	Mail |   用户邮箱 |
| cell  |   用户电话号码  |
| status  |   用户状态 |
| Get channel按钮 |  获取渠道  |
| Tag Search: |  按标签查询  |
| &nbsp;   |   &nbsp;  |
| Date   | 用户注册时间 和 用户上次登录时间  |
| Info  |  用户详细信息   |
| UID |   用户id  |
| V Lv |   用户等级  |
| V Exp |  用户经验  |
| Wal |  用户钱包详情  |
| introd |   邀请用户（应该是邀请用户的数量）  |
| status |   状态（应该是正常和冻结  一个绿色一个红色）  |
| status -> Wpoint |   用户净胜分  |
| status -> PR |   盈利率  |
| buff |  标签buff   |
| Operate |   操作 是否冻结  |



<br><br><br><br>
# 用户管理
## 用户信息管理
- 后台分类名称：用户信息管理
- 服务端接口路径：console -> application -> index -> controller -> CardStatistics.php -> getUserList()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| 标题栏的records  | 代表查询到了几条记录     |
| Reg Begin | 筛选的开始时间     | 
| Reg End  |   筛选的结束时间   |
| UID |   用户id   |
|	Mail |   用户邮箱 |
| cell  |   用户电话号码  |
| status  |   用户状态 |
| Get channel按钮 |  获取渠道  |
| Tag Search: |  按标签查询  |
| &nbsp;   |   &nbsp;  |
| Date   | 用户注册时间 和 用户上次登录时间  |
| Info  |  用户详细信息   |
| UID |   用户id  |
| V Lv |   用户等级  |
| V Exp |  用户经验  |
| Wal |  用户钱包详情  |
| introd |   邀请用户（应该是邀请用户的数量）  |
| status |   状态（应该是正常和冻结  一个绿色一个红色）  |
| status -> Wpoint |   用户净胜分  |
| status -> PR |   盈利率  |
| buff |  标签buff   |
| Operate |   操作 是否冻结  |


<br><br><br><br>
## 用户头像修改审核
- 后台分类名称：用户头像修改审核
- 服务端接口路径：console -> application -> index -> controller -> Apply.php -> desUserInfoApplyList()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| ID  | 排序id     |
| UID | 用户id     | 
| Name |   用户名   |
| Avatar |   头像图片   |
|	Time |   申请时间 |
| Status  |   0:等待审核   1：审核通过  2：拒绝  |
| Operate  |   操作 |


<br><br><br><br>
## KYC VERTIFICATION AUDIT (用户实名认证申请列表)
- 后台分类名称：KYC VERTIFICATION AUDIT
- 服务端接口路径：console -> application -> index -> controller -> Apply.php -> desUserKycApplyList()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| Verification State：  | 认证状态（ Pending:等待审核 Approved：审核通过  Rejected：拒绝） |



<br><br><br><br>
## 验证码查询 
- 后台分类名称：验证码查询 
- 服务端接口路径：console -> application -> index -> controller -> user.php -> smsquery()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| call  | 电话 |
| otp  | 验证码 |
| type  | （ 1:实名认证 2：登录  3：更改身份证  4：更改手机号） |



<br><br><br><br>
# 财务管理
## 提现审核
- 后台分类名称：提现审核 
- 服务端接口路径：console -> application -> index -> controller -> WithdrawalOrder.php -> desOrderList()
  
| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| By month  | 按月份筛选 |
| By day  | 按日子筛选 |
| 注意：以上两个条件服务器的逻辑只能走一个 如果两个同时都选择了 会按By Day走  | |
| Time dimension  | 订单时间类型选择 |
| Time dimension -> created Time | 订单生成时间 |
| Time dimension -> callback Time | 订单支付时间 |
| UID  | 用户id |
| Order ID  | 订单号 |
| UID  | 用户id |
| Card  | 身份证号 |
| UID  | 用户id |
| status  | 提现订单状态  |
| status -> all  | 提现订单状态  |
| status ->  Pending | 等待审核  |
| status ->  Rejected | 审核拒绝  |
| status ->  Approved,Wating for payment | 审核通过 等待支付  |
| status ->  Payment failed | 支付失败  |
| status ->  Payment successful | 支付成功  |
| status ->  confirming| 状态不明确 等待重新查询  |
| Review Type  | 审查类型  |
| Review Type ->  auto  | 自动  |
| Review Type -> Manual | 人工  |
| &nbsp;   |   &nbsp;  |
| W-succ Amt:  | 支付金额  |
| sel All | 订单id  |
| O id | 订单id，第三方返回订单id，提现渠道  |
| Info | 用户id，银行卡号，真实姓名，电话，ifsc码  |
| Amt | 支付金额，提现渠道：手续费  |
| Wal | 币种：钱  |
| Date | Review Type: 审查类型，Create Time:订单创建时间，Review Time:审核时间，CallBack Time:订单支付时间，Interval:订单创建时间和订单支付时间的时间差 |
| Tag | 标签  |
| Status | 订单状态 ，REJ times:拒绝提现的次数，Last Review：上次审查日期 |
| Operate | 操作  |




<br><br><br><br>
## 充值流水
- 后台分类名称：充值流水 
- 服务端接口路径：console -> application -> index -> controller -> order.php -> desOrderList()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| By month  | 按月份筛选 |
| By day  | 按日子筛选 |
| 注意：以上两个条件服务器的逻辑只能走一个 如果两个同时都选择了 会按By Day走  | |
| Time dimension  | 订单时间类型选择 |
| Time dimension -> created Time | 订单生成时间 |
| Time dimension -> callback Time | 订单支付时间 |
| Sort by Amt  | 按支付金额排序 |
| Sort by Amt -> Large-Little  | 支付金额降序 |
| Sort by Amt -> Little-Large  | 支付金额升序 |
| UID  | 用户id |
| Order Id  | 订单id |
| Tube  | 支付方式 |
| Status  | 充值订单状态 |
| Status -> Pending Payment  | 审核通过 等待支付  |
| Status ->  Payment success,not arrive | 支付成功  |
| Status ->  Arrived | 支付成功 已经到账  |
| Status ->  Marked by third-party| 标记中  |
| Status ->  PENDING(status is not clear) | 等待中  |
| Status ->  Payment Failed | 支付失败  |
| Status ->  Payment cancelld| 支付已取消  |
| Act  | 活动类型（至于这三个活动具体是啥活动 目前不太清楚 代码里并没有体现） |
| Act 如果为1  |  RE Act  |
| Act 如果为2  |  Coupon  |
| Act 如果为3  |  Broken  |
| &nbsp;   |   &nbsp;  |
| Credited Amt   |   支付金额总和：  |
| Date   |   日期  |
| Date ->  Create Time: |  创建订单时间  |
| Date ->  Succ Time:  |   成功支付时间  |
| Order   |   订单号  |
| Order ->3rd ID   |   第三方订单号  |
| Order -> 第二个字段  |   支付方式  |
| Order -> 第三个字段 |   支付选用的sdk  |
| Amt   |   金额  |
| Amt -> RE:   |   支付金额  |
| Amt -> Credited:   |   到账金额  |
| Uid   |   用户id  |
| Uid -> 第二个字段  |   下单支付邮箱  |
| Act Type   |  活动类型（至于这三个活动具体是啥活动 目前不太清楚 代码里并没有体现）  |
| Act Type 如果为1  |  RE Act  |
| Act Type 如果为2  |  Coupon  |
| Act Type 如果为3  |  Broken  |
| Remark   |   备注  |
| Status   |   支付状态  |




<br><br><br><br>
## 返利流水
- 后台分类名称：返利流水 
- 服务端接口路径：console -> application -> index -> controller -> ProfitOrder.php -> desOrderList()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| By month  | 按月份筛选 |
| By day  | 按日子筛选 |
| 注意：以上两个条件服务器的逻辑只能走一个 如果两个同时都选择了 会按By Day走  | |
| UID  | 用户id |
| Status | 支付状态 |
| &nbsp;   |   &nbsp;  |
| Order Time | 创建订单的时间 |
| Uni ID  | 关联id |
| Order ID  | 返利订单号 |
| UID  | 用户id |
| Return Amt  | 返利金额 |
| Before Return | 返利前金额 |
| After Return  | 返利后金额 |
| Log ID  | 战绩id |
| Wal ID | 钱包编号 |
| Status  | 支付状态 |



<br><br><br><br>
## 体现银行卡
- 后台分类名称：体现银行卡 
- 服务端接口路径：console -> application -> index -> controller -> WithdrawalOrder.php -> withdrawalBankCard()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| Name  | 按名字筛选 |
| UID  | 按用户id筛选 |
| status  | 状态：  Active：启用  Inactive：禁用 |
| &nbsp;   |   &nbsp;  |
| Name  | 银行卡姓名 |
| card | 银行卡号 |
| IFSC | ifsc |
| UID  | 用户id |
| Add Time  | 添加时间 |
| Status  | 状态1：启用  0：禁用 |



<br><br><br><br>
## 提现记录
- 后台分类名称：提现记录 
- 服务端接口路径：console -> application -> index -> controller -> WithdrawalOrder.php -> withdrawalLog()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| O id  | 按订单号筛选 |
| Card  | 按银行卡号筛选 |
| Status  | 提现状态 |
| &nbsp;   |   &nbsp;  |
| Manual W  |   手动添加提现记录（请小心（没有第二次确认） |
| Refresh Status |   刷新状态（相当于重新请求了一下） |
| &nbsp;   |   &nbsp;  |
| W succ Amt   | 成功提现的总额 |
| O id  | 订单id / 第三方返回订单id /  |
| Info | 银行卡号 / 真实姓名 / 电话号码 / ifsc |
| Amt | 支付金额 / 手续费 |
| Date  | 创建订单时间 / 成功支付时间 / 创建订单时间与成功支付时间的时间差 |
| Status | 提现状态 / 备注 |
| Remark  | 状态1：启用  0：禁用 |
| Operate | 操作 |




<br><br><br><br>
## 提现小黑屋
- 后台分类名称：提现小黑屋 
- 服务端接口路径：console -> application -> index -> controller -> WithdrawalBlackOrder.php -> blackOrderList()

| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| By month  | 按月份筛选 |
| By day  | 按日子筛选 |
| 注意：以上两个条件服务器的逻辑只能走一个 如果两个同时都选择了 会按By Day走  | |
| Time dimension  | 按时间限度筛选（下单时间 / 支付时间） |
| UID   |  用户id  |
| Order ID  |  订单号） |
| status  | 提现订单状态  |
| status -> all  | 提现订单状态  |
| status ->  Pending | 等待审核  |
| status ->  Rejected | 审核拒绝  |
| status ->  Approved,Wating for payment | 审核通过 等待支付  |
| status ->  Payment failed | 支付失败  |
| status ->  Payment successful | 支付成功  |
| status ->  confirming| 状态不明确 等待重新查询  |
| Review Type  | 审查类型  |
| Review Type ->  auto  | 自动  |
| Review Type -> Manual | 人工  |
| &nbsp;   |   &nbsp;  |
| Sel All   | 不太确定是什么含义 |
| O id  | 订单id / 第三方返回订单id / 提现渠道  |
| Info | 详细信息 |
| Info -> uid   |  用户id  |
| Info -> card    |  银行卡号   |
| Info -> name    |  真实姓名  |
| Info -> Cell    |  电话号码  |
| Info -> IFSC    |  ifsc  |
| Amt | 金额 |
| Amt -> Amt   |  支付金额  |
| Amt -> Fee   |  手续费  |
| Wal  | 用户钱包（类型：金额） |
| date | 审核类型（auto：自动，Manual：人工） / Create Time：创建时间 / Review Time:审核时间 / CallBack Time:支付时间 / Interval:创建时间与支付时间的时间差  |
| Tag  | 用户标签 |
| Status | 备注 /  REJ times：被拒绝的次数 / Last Review:上次被拒绝的日期 / 脏钱和灰钱的金额|
| Operate | 操作 （通过/拒绝/返回）|





<br><br><br><br>
# general management(综合管理)
## Withdral Audit(提现审计)
- 后台分类名称：Withdral Audit(提现审计) 
- 服务端接口路径：console -> application -> index -> controller -> WithdrawalOrder.php -> desOrderListIndia()
  
| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| Month：  | 按月份筛选 |
| Date： | 按日子筛选 |
| 注意：以上两个条件服务器的逻辑只能走一个 如果两个同时都选择了 会按Date走  | |
| User ID:   |  用户id  |
| Transation ID：  |  订单号） |
| Status：  | 提现订单状态  |
| &nbsp;   |   &nbsp;  |
| Transation ID   |   订单号  |
| User Info   |   用户详情  |
| User Info -> User ID  |  用户id  |
| User Info ->  Bank Account |  银行卡号  |
| User Info -> Name  |  姓名  |
| User Info ->  Tel No |  手机号  |
| User Info ->  IFSC Code  |  ifsc码 |
| Amount   |   金额  |
| Amount -> Amount  |  支付金额  |
| Amount -> Commission Charge  |  手续费  |
| Balance   |  余额  |
| Balance ->  |  币种类型（1:充值现金 2:筹码 3:提现  4:返利(未使用) 5:充值总额 6:提现总额 7:返利总额  8:冻结金额 9:佣金 10:金币） ： 金额  |
| Time  | 时间  |
| Time-> Audit mode | 审查类型  |
| Time-> Order Time | 订单创建时间  |
| Time-> Audit Time | 审查时间  |
| Time-> Delivery Time: | 支付时间  |
| Time-> Time difference | 创建时间与支付时间的时间差  |
| Transation Status   |   支付状态 和 备注  |



<br><br><br><br>
## Recharge Flow
- 后台分类名称：Recharge Flow
- 服务端接口路径：console -> application -> index -> controller -> order.php -> desOrderListIndia()


| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| Month：  | 按月份筛选 |
| Date： | 按日子筛选 |
| 注意：以上两个条件服务器的逻辑只能走一个 如果两个同时都选择了 会按Date走  | |
| User ID:   |  用户id  |
| Transation ID：  |  订单号 |
| Status：  | 提现订单状态  |
| &nbsp;   |   &nbsp;  |
| Date： | 日期 |
| Date -> Creat  |  创建订单时间  |
| Date -> Success  |  订单成功支付时间  |
| Transation ID：  |  订单号 |
| Amount   |   金额  |
| Amount -> Recharge  |  支付金额  |
| Amount -> Arrived  |  到账金额  |
| User   |   用户详情：用户id / 下单的手机号 / 下单支付的邮箱  |
| Amount   |   金额  |
| Amount -> Amount  |  支付金额  |
| Amount -> Commission Charge  |  手续费  |
| Remark   |  备注  |
| Status |  订单状态  |
| Time  | 时间  |



<br><br><br><br>
## KYC VERTIFICATION AUDIT（展示用户实名认证申请列表）
- 后台分类名称：KYC VERTIFICATION AUDIT（展示用户实名认证申请列表）
- 服务端接口路径：console -> application -> index -> controller -> Apply.php -> desUserKycApplyListIn()


| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| User ID：  | 按月份筛选 |
| Verification State：  | 认证状态（ Pending:等待审核 Approved：审核通过  Rejected：拒绝） | 


<br><br><br><br>
## Money flow log(用户钱包流水日志)
- 后台分类名称：Money flow log (用户钱包流水日志)
- 服务端接口路径：console -> application -> index -> controller -> UserLog.php -> getUserMoneyLogListIndia()


| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| Date：  | 按月份筛选 |
| User ID: | 按日子筛选 |
| Uniquely identifies：   |  用户id  |
| Order ID：  |  订单号 |
| Flow type：  | 提现订单状态  |
| &nbsp;   |   &nbsp;  |
| Date | 创建时间 |
| Order  |  订单号 |
| Order -> User ID  |  用户id  |
| Order -> Order ID  |  订单id  |
| Order -> Uniquely identifies  |  关联id  |
| Amount   |   金额  |
| Amount -> Operation amount：  |  到账金额(抽取服务费后的金额)  |
| Amount -> Commission  |   服务费  |
| Amount -> Rate  |   服务费比例（百分比）  |
| Amount -> Amount before account change  |   账户变更前金额  |
| Amount -> Amount after account change  |   账户变更后金额  |
| Operating   |  币种类型 1:充值现金 2:筹码 3:充值 4:提现  5:充值总额 6:提现总额 7:返利  8:冻结金额 9佣金 10:金币 |



<br><br><br><br>
## Rummy Record(Rummy战绩（已经打完的房间log）)
- 后台分类名称：Rummy Record(Rummy战绩)
- 服务端接口路径：console -> application -> index -> controller -> RoomLog.php -> getRoomLogListIndia()


| 字段          | 含义                   | 
| ------------- | ---------------------- | 
| Date：  | 时间 |
| Room ID: | 房间号 |
| Uniquely identifies   |  关联id  |
| User ID：  |  用户id |
| 输分最小  |   |
| 输分最大   |     |
| Antes | 底注 |
| The number of participants  |  当前在线人数 |
| Game state  |  用户结果  |
| &nbsp;   |   &nbsp;  |
| Date  |  日期  |
| Date -> Room ID：|  房间id  |
| Date -> identifies：|  关联id  |
| Date -> Creation time：|  创建时间  |
| Date -> Starting time：|  房间开始时间  |
| Date -> End Time：|  结束时间  |
| User  |  用户  |
| User -> |  用户id  |
| User -> |  房间状态 / 用户结果 |
| User -> |  用户输分  |
| Room   |   金额  |
| Room -> |  玩法  |
| Room -> Participants/total number|  当前在线人数/房间最大人数  |
| Room -> Antes|  底注*0.001  |
| Room -> charges|  抽佣比例 |
| Pick card  |  到账金额(抽取服务费后的金额)  |
| Pick card -> Number of settlements for this room:|  回合数+1 |
| Pick card -> Number of draws in this game:|  轮数+1 |
| Pick card -> Duration:|  结束时间 减 房间开始时间 |
| The bureau turnover  |   房间流水*0.001  |
| Settlement  |   用户id / 房间状态 / 用户结果 / 用户输分 |


