## concole-->P-gain --> Rev 的计算公式？

- 我在代码里全局搜了一下 P-gain 找到了这个地方的静态页
- 往下走会看到 Rev 这条数据 是从 <{$dataView.aiWin}>这个变量里拿到的
- 接下来我去 main.php-->index() 看了一下这个变量的赋值
- 查询到 aiWin 是从 redis 里的《ai_room_data》里面拿出来的数据 计算的是 当日 ai 机器人营收的总和

<br><br>

## 综合统计-每日数据统计，DAU-Pure 的统计逻辑？

- 我在代码里全局搜了一下 DAU-Pure 找到了这个地方的静态页
- 通过静态页得知 这个字段的计算公式为 <{$vo.active_user_num-$vo.reg_user_num}>
- 接下来我去 UserBehaviorStatistics.php --> total() 看了一下 active_user_num 和 reg_user_num 这两个变量分别是什么意思
- 得知 active_user_num 为活跃人数，reg_user_num 为注册人数
- 结论：当日活跃人数 - 当日注册人数，求的是差值，这个字段应该是可以用来衡量用户流失率的

<br><br>

## 综合统计-新用户充值追踪，D1 的 RE Num、RE 与每日数据统计中数值有差异，检查逻辑是否有问题？

- 看表面得知 《新用户充值追踪》 比 《每日数据统计》的数值要少
  <br><br>

### 新用户充值追踪

- 先去 html 页查一下这几个字段接的是什么值

  RE 取值来自 total_recharge_amt<br>
  RE NUM 取值来自 total_recharge_num<br>
  W 取值来自 total_with_fee<br>
  Fee 取值来自 total_charges_fee

- 接下来去业务逻辑层查一下这几个字段来源于哪个表 具体含义是什么 UserRechargeTrack.php.php --> indexView()
  通过代码得知 这几个字段来源于 mysql 表的 gm_stat_user_recharge<br>

  这个表里的数据是通过跑定时任务得到的 每天在凌晨 2 点 40 分执行<br>
  'user_recharge_track' => 'app\common\command\UserRechargeTrackQuery',//用户充值追踪

  ```json
              $regDateStatArr = $sourceDb
                  ->table('t_user u')
                  ->field('
                  DATE_FORMAT(
                      FROM_UNIXTIME(reg_time),
                      "%Y-%m-%d"
                  ) AS reg_times,
                  count(distinct u.id) AS new_user_num,
                  SUM(IF(currency = 5, fee, 0)) AS recharge_amt,
                  count((currency = 5 AND fee > 0) OR NULL) AS recharge_num,
                  SUM(IF(currency = 9, fee, 0)) AS charges_fee,
                  SUM(IF(currency = 6, fee, 0)) AS with_fee,
                  SUM(IF(currency = 7, fee, 0)) AS give_fee
                  ')
                  ->join('t_user_wallet uw', 'uw.uid=u.id', 'left')
                  ->where($where)
                  ->group('reg_times')
                  ->select();
  ```

### 每日数据统计

- 先去 html 页查一下这几个字段接的是什么值

  RE 取值来自 pay_cash_amount<br>
  RE NUM 取值来自 pay_cash_num<br>
  W 取值来自 withdraw_amount<br>
  Fee 取值来自 platform_charge

- 接下来去业务逻辑层查一下这几个字段来源于哪个表 具体含义是什么 来到 UserBehaviorStatistics.php --> total()

  通过代码得知 这几个字段来源于 mysql 表的 gm_stat_summary
  'summary' => 'app\common\command\Summary' 每天在凌晨 2 点 37 分执行,

### 结论

这里我举个例子 比如我们想查询的是 5.20 号的统计数据 <br>
新用户充值追踪的每日数据 ：统计的是 注册时间在 5.20 号的人 充值金额是多少<br>
每日数据：统计的是 5.20 号当天 所有人的充值金额是多少<br>
所以每日数据的统计金额 一定会比 新用户充值追踪 数值大
他们两个完全是统计的是两码事 互相没有什么关联

<br><br>

## 综合统计-新用户充值追踪，ROI 的计算逻辑？感觉数值不对

- 先去 html 页查一下 ROI 的计算公式

  ```json
         <?php
              $rois = 0;
              if($advertise_total > 0 ){
                $rois = round(($vo['total_recharge_amt']*(1-$totalAmt['with']/$totalAmt['recharge']))/($advertise_total*82.80)*100,2);
              }
         ?>
  ```

- 然后去逻辑层追一下这几个字段分别是什么意思 UserRechargeTrack.php--->indexView()

  list.data['total_recharge_amt'] : 从 gm_stat_user_recharge 表中取出来的 当日充值总和<br>
  totalAmt['with'] : 从 gm_stat_user_amount 表中取出来的 提现金额总和<br>
  totalAmt['recharge']: 从 gm_stat_user_amount 表中取出来的 充值金额总和<br>
  advertise_total ：'advertise' + 'advertise_fb' 广告投放总和 <br>

- 最后得到计算公式为
  (当日充值总和 _ (1 - 提现金额总和 / 充值金额总和)) / (广告投放总和 _ 82.80) \* 100 得出的值 四舍五入且保留两位小数

<br><br>

## 综合统计-货币统计，充值手续费和提现手续费计算逻辑是否正确，是否根据支付渠道费用回传？

- 先去 html 页查一下 RE fee 和 W fee 这两个字段取的是谁的值

  RE fee : recharge_fee<br>
  W fee : withdraw_fee<br>
  <br>

- 然后看一下业务逻辑层 找一下这两个字段 是从哪个表取出来的

  通过 UserBehaviorStatistics.php ---> statisticsView 得知 数据来自 gm_stat_user_amount 表

  全局搜索表名得知 gm_stat_user_amount 表里的数据是这个定时任务里面处理的<br>'user_statistics_query' => 'app\common\command\UserStatisticsQuery',//充值提现返利金额一天之和

  ```json
         $list = Db::connect(\lconfig\MysqlConfigV2::InstanceV2()->get_think_common_admin_mysql())
           ->table('gm_stat_user_amount as tus')
           ->field('
               sum(recharge_amount) as recharge_amount,
               sum(recharge_amount_d) as recharge_amount_d,
               sum(ROUND(recharge_amount * 0.03,2)) as recharge_fee,
               sum(withdraw_amount) as withdraw_amount,
               sum(withdraw_fee) as withdraw_fee,
               sum(rebate_amount)as rebate_amount,
               sum(commission_fee)as commission_fee,
               sum(give_amt)as give_amt,
               sum(deposit_siltation) as deposit_siltation,
               sum(deposit_siltation_d) as deposit_siltation_d,
               sum(withdraw_siltation) as withdraw_siltation,
               sum(withdraw_siltation_d) as withdraw_siltation_d,
               sum(bonus_siltation) as bonus_siltation,
               sum(bonus_siltation_d) as bonus_siltation_d,
               sum(win_amt)as win_amt,
               sum(lost_amt)as lost_amt,
               sum(root_num)as root_num,
               create_date
           ')
           ->where($where)
           ->order('create_date', 'desc')
           ->group('create_date')
           ->paginate(30, false, $pageParam);
  ```

  通过这个 sql 我们可以得知 充值手续费（recharge_fee）是在 sql 语句里面写死的 计算方式为：用充值金额 \* 0.03
  <br><br>

- 接下来 我们回到定时任务的代码里 去找一下提现手续费（withdraw_fee）是在哪处理的

  ```json
            //提现金额
            $withdrawName = CommonModel::getWithdrawalOrderTable($start_time, 0, $db);

            $withAmt = Db::connect($db)
                ->field('
            sum(pay_money_qf) as pay_amt,
            sum(if(pay_money_qf<300000,10,pay_money_qf*0.03* 0.001)) as fee
            ')
                ->table($withdrawName . ' m')
                ->join('t_user_source s', 'm.player_index = s.uid', 'left')
                ->where($where)
                ->where('pay_states', CommonCode::WITHDRAWAL_ORDER_PAY_SUCCESS)
                ->where('s.source', $key)
                ->find();
  ```

  通过这个 sql 我们可以得知 提现手续费（withdraw*fee）也是在 sql 语句里面写死的 计算方式为：判断提现金额是否小于 300000，如果是则手续费为 10，否则手续费为提现金额 * 0.03 \_ 0.001

<br><br>

- 总结：充值手续费和提现手续费的计算 并不是根据支付渠道费用回传走的 是我们服务器的代码里写死的
- 充值手续费（recharge_fee）计算方式为：用充值金额 \* 0.03
- 提现手续费（withdraw*fee）计算方式为：判断提现金额是否小于 300000（这个单位应该不是人民币），如果是则手续费为 10，否则手续费为提现金额 * 0.03 \_ 0.001

<br><br>

## 综合统计->房间统计 选定日期内，查询不到房间结果。matches P\*times 的计算公式？ Participate rate 的计算公式？

### 选定日期内 查询不到房间结果

- 我看了一下业务逻辑层 UserBehaviorStatistics.php ---> roomStatisticsView()
  得知数据出自 gm_stat_user_behavior 这张表

- 然后我看了一下正式服的这张表 发现数据停留在了 5.17 号 没有最新的数据 猜测这可能是个定时任务推送的数据 并且定时任务可能出现了问题 导致数据不更新了
- 全局搜索了一下 gm_stat_user_behavior 这张表哪里有插入数据的地方
  得知出自下面的这个定时任务
  0 3 \* \* \* root /usr/bin/php /opt/yylbe/php/console/think room_statistics_query >> /data/logs/room_statistics_query.log 2>&1

- 看了代码 最近并没有改动 应该不会出现问题啊 所以我去正式服看了一下 log 发现也没有任何报错信息 怀疑是不是正式服把这个定时任务去掉了 看了一下 现在正式服没有这个定时任务了 所以这是导致这个问题的原因

### matches P\*times 的计算公式？ Participate rate 的计算公式？

- 先去 html 页查一下 matches P*times 和 Participate rate 这两个字段取的是谁的值
  matches P*times：<{$vo.p_nums}> 房间在线人数
Participate rate：<{$vo.p_nums/$vo.room_num|round='2'}> 房间在线人数/房间数量 最后的结果四舍五入且保留两位小数

<br><br>

## 财务管理---提现审核：Back 操作作用，红字“智能审核未通过 ==TP 拉米占比:0==流冲比:0==可提额度:4000==拉新赠送:0==脏钱:0==灰钱:0 下单失败，异常！”含义？Auto Result 按钮含义？

- Back 操作作用？
  答：Back 这个按钮是给玩家退钱用的 举个例子：如果后台订单的状态显示提现成功了 但玩家找我们反馈说他并没有收到钱 我们服务器查了一下问题 发现是三方支付渠道他们的问题 这种情况下就可以点击这个 back 按钮 把玩家的提现扣的钱给他退回去 不过要谨慎一些 防止他白嫖

- 红字“智能审核未通过 ==TP 拉米占比:0==流冲比:0==可提额度:4000==拉新赠送:0==脏钱:0==灰钱:0 下单失败，异常！”含义？
  答：看代码的含义是 如果玩家提现总额 大于 玩家提现额度 就会触发这个错误 导致自动审核失败
  提现额度的计算公式： （玩家提现总额 \* 提现率 / 100）

- Auto Result 按钮含义？
  答：这个按钮在我们的代码里叫做《人工操作-系统审核》 getAutoResult() 看了一下逻辑 当你点击这个按钮后 会把你当前选择的这个订单订单 再走一遍自动审核的逻辑

<br><br>

## 财务管理-->提现审核 拒绝按钮的 Change to D9 and B1、Yes、Deduct All 含义？

- 这个地方调用的是 WithdrawalOrder.php --> editApplyStates<br>
- Change to D9 and B1 的含义
  proportion 传的是 0，这个不会执行其他操作 只是单纯的拒绝提现<br><br>
- Yes 的含义
  proportion 传的是 1 会进行下面的操作

  ```json
            $fee = Db::connect(mysqlGameConnectBySource($source))
                ->table('t_user_wallet m')
                ->join('t_user_source s', 'm.uid = s.uid', 'left')
                ->where(['currency' => CommonCode::CURRENCY_TYPE_WITHDRAWAL, 'uid' => $uid])
                ->where('s.source', $this->source)
                ->sum('fee');
            \think\Log::write('proportion-' . $uid . 'w->' . $fee);
            //扣除W
            $res = \LogicHttpApi::reduceMoneyByType($uid, $fee, CommonCode::CURRENCY_TYPE_WITHDRAWAL, CommonCode::USER_PAYMENT_ORDER_TYPE_SYSTEM);
            \think\Log::write('proportion-' . $uid . '-w->' . $fee);
            //增加D
            $res = \LogicHttpApi::addMoneyByType($uid, round($fee * 0.9), CommonCode::CURRENCY_TYPE_DEPOSIT, CommonCode::USER_PAYMENT_ORDER_TYPE_SYSTEM);
            \think\Log::write('proportion-' . $uid . '+d->' . round($fee * 0.9));
            //增加B
            $res = \LogicHttpApi::addMoneyByType($uid, round($fee * 0.1), CommonCode::CURRENCY_TYPE_BONUS, CommonCode::USER_PAYMENT_ORDER_TYPE_SYSTEM);
            \think\Log::write('proportion-' . $uid . '+b->' . round($fee * 0.1));
  ```

  会把用户提现金额总和 全都扣除，然后返还 提现金额总和的 90% 的 deposit，返还 提现金额总和 10% 的 bonus<br><br>

- Deduct All 的含义
  proportion 传的是 2 会进行下面的操作
  ```json
            $tableName = CommonModel::getWithdrawalOrderTable('', $apply_id, $this->agent);
            $fee = Db::connect($this->agent)
                ->table($tableName)
                ->where(['order_id' => $apply_id])
                ->sum('pay_money_qf');
            $res = \LogicHttpApi::reduceMoneyByType($uid, $fee, CommonCode::CURRENCY_TYPE_WITHDRAWAL, CommonCode::USER_PAYMENT_ORDER_TYPE_SYSTEM);
  ```
  会执行的操作：全额扣除订单（把该订单的提现金额 从玩家身上减掉）

<br><br>

## 房间&log---用户钱包流水日志不连续，查看此处逻辑？

- 通过代码得知 这个功能的数据是从 UserLog.php ---> getUserMoneyLogList()里取出来的
- 接着来到 getUserMoneyLogList() 得知 数据来自 mysql 的 user*wallet_log*表 这是按天生成的日表
-

<br><br>

## 拉新排行，抬叫统计，充值统计，AI 结算查询，庄家战绩 页面异常

### 拉新排行

- 拉新排行应该是缺失 controller 的方法
- 通过 gm_rule.sql 找到了这个功能的控制器和方法名 RankList_index
- 全局搜索 Ranklist 找到了 UserInviteRank.php 这个页处理和它相关的定时任务

```json
但是被注掉了
#=====================每每天00点更新前一天的拉新排行榜
#30 2 * * * root /usr/local/services/php7/bin/php /data/www/houtai/console_web_cd/think invite_rank >> /data/logs/invite_rank.log 2>&1
```

### 抬叫统计

- 通过 gm_rule.sql 找到了这个功能的控制器和方法名 Stat_cryUpStat 代码里没有任何地方用到

### 充值统计

- 通过 gm_rule.sql 找到了这个功能的控制器和方法名 Stat_rechargeStat 代码里没有任何地方用到

### AI 结算查询

- 通过 gm_rule.sql 找到了这个功能的控制器和方法名 RoomLog_getroomlogstates 有其他地方调用 但是代码没有这个方法

### 庄家战绩

- 通过 gm_rule.sql 找到了这个功能的控制器和方法名 RoomLog_bookmakerlist 这个方法我们代码里是有的 去测试服带 bug 的环境 看了一下是否有报错信息 果然

```json
SQLSTATE[42S02]: Base table or view not found: 1146 Table 'yyldb.bookmaker_data_' doesn't exist
```

- 我们是有这块的代码的 但是没有这功能的数据表

<br><br>

## 财务管理---破产保护日志 页面缺失，需处理

- 没看到财务管理下有这个功能

<br><br>

## 游戏配置----充值提现配置，充值区间档位、充值引导金额，此 2 项含义及作用

- 通过查询 redis 配置得知 这两项配置存在与 redis 的 teentest_standar_config 下 （teentest 为拼接的渠道名）
- 充值引导金额：recharge_guide_amount_qf

  getRechargeList()这个接口里面可以拿到 但是这个接口在我们代码里被注掉了

- 充值区间档位：recharge_interval

  （1）这个接口有用到 不过现在已经被注掉了 Route::alias('rechargeRegion', 'api/Gift_Api/rechargeRegion');

  （2）getRechargeList 这个接口里面可以拿到 但是这个接口在我们代码里也被注掉了

- 这俩字段好像都是给客户端用的 不太清楚他们拿来干嘛

## console --> RH/H 今日统计（绿色的线）为什么会有 0 的情况 是不是数据错误了？

- 通过查询代码 今日统计这个功能来自 Main.php --->hourRechargeytoday()
- 由于我本地没什么数据 正式服数据比较多 我又不太方便在正式服打 log 所以我把下面的这段 sql 语句转换成了原生的 sql 语句 去线上服的 mysql 跑一下看看返回值是什么样的

```json
        $where['pay_time'] = ['>=', date('Y-m-d') . ' 00:00:00'];
        $where['status'] = 2;
        $data = Db::table('v_payin_order')->field("
                            sum(amount) as amt,
                            from_unixtime(unix_timestamp(pay_time), '%H') as hour
            ")
            ->where($where)
            ->group('hour')
            ->select();

       转换后的sql语句
       SELECT
          SUM(`amount`) AS `amt`,
          from_unixtime(unix_timestamp(`pay_time`), '%H') AS `hour`
       FROM
          `t_pay_in_order`
       WHERE
          `pay_time` >= CONCAT(CURDATE(), ' 00:00:00')
          AND `source` = 'rummyblitz'
          AND `status` = 2
       GROUP BY
          `hour`


得到结果为
amt   hour
400	    14
6341	00
100	    01
300	    02
200	    06
1600	07
200	    08
900	    09
2500	10
1221	11
1400	12
3521	13
4400	15
15708	16
529	    17

```

- 由此可见 是有几个小时没有数据的 然后我又去查了一下没有数据的时间段 确实是没有订单充值成功的
- 所以我们再回去看代码 发现他代码的处理方式 就是 如果没有该小时的数据 就赋值 0

```json
           foreach ($data as $key => $value) {
                for ($i = 0; $i < 24; $i++) {
                    if ($value['hour'] == $i) {
                        $amt += $value['amt'];
                        $list[$i] = $amt;
                    } elseif (empty($list[$i])) {
                        $list[$i] = 0;
                    }
                }
            }
```

- 总结：所以这不是一个 bug 只是代码的处理方式是这样的
