## 主要为了调查两个事

- 什么情况下走自动审核逻辑
- 什么情况下会走到人工

## 梳理一下 都有什么情况 会无法走自动审核 从而需要人工审核

- 诈骗用户无法自动审核
- 从来没充值过的玩家 并且 没有获得过拉新奖励的玩家（拉新目前没上 这个逻辑忽略）
- 身上带有 '停止自动提现'、'诈骗用户'、'疑似诈骗' 、'羊毛党' 任意一个标签 都无法自动审核
- 提现连续失败次数 >= 四次 无法自动审核
- 提现金额大于规定的提现金额 无法自动审核
- 校验提现金额是否满足自动审核线，大于该值则需要人工审核，小于等于该值则是自动审核
- 校验账号日提现数大于上限
- 校验自动审核开关是否开启
- 充值总额 不在下面的配置范围里 无法自动审核

  ```
  private $config = [
        '0' => ['lower' => 0, 'min' => 0, 'max' => 100, 'rate' => 300, 'l_rate' => '0.5', 't_rate' => 2],
        '1' => ['lower' => 0, 'min' => 101, 'max' => 500, 'rate' => 200, 'l_rate' => '0.5', 't_rate' => 3],
        '2' => ['lower' => 0, 'min' => 501, 'max' => 1000, 'rate' => 150, 'l_rate' => '0.5', 't_rate' => 4],
        '3' => ['lower' => 0, 'min' => 1001, 'max' => 3000, 'rate' => 150, 'l_rate' => '0.5', 't_rate' => 4],
        '4' => ['lower' => 0, 'min' => 3001, 'max' => 5000, 'rate' => 150, 'l_rate' => '0.5', 't_rate' => 4],
        '5' => ['lower' => 0, 'min' => 5001, 'max' => 10000, 'rate' => 150, 'l_rate' => '0.5', 't_rate' => 4],
        '6' => ['lower' => 0, 'min' => 10001, 'max' => 30000, 'rate' => 150, 'l_rate' => '0.5', 't_rate' => 4],
        '7' => ['lower' => 0, 'min' => 30001, 'max' => 50000, 'rate' => 150, 'l_rate' => '0.5', 't_rate' => 4],
        '8' => ['lower' => 0, 'min' => 50001, 'max' => 100000, 'rate' => 150, 'l_rate' => '0.5', 't_rate' => 4],
        '9' => ['lower' => 0, 'min' => 100001, 'max' => 500000, 'rate' => 150, 'l_rate' => '0.5', 't_rate' => 4],
        '10' => ['lower' => 0, 'min' => 500001, 'max' => 99999999, 'rate' => 150, 'l_rate' => '0.5', 't_rate' => 4],
    ];
  ```

- 下面的渠道是需要手动审核的 如果提现人的渠道在下面的配置里 无法自动审核
  <br />

  ```
  ['RummyPalace']
  ```

- 提现总额 必须 > 提现剩余额度 提现总额包含'等待支付'和'等待审核'和'等待二审'的单子
