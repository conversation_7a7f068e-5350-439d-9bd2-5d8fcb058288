破产保护
根据前端给的接口名：bankruptcy
查询到这是一个console对外的http接口

//破产保护获取订单展示信息
Route::alias('bankruptcy', 'api/Bankruptcy_Api/createBankruptcyOrder');

//破产保护订单最大个数
private $bankruptcyOrderMaxCnt = 3;


（1）根据用户的总充值金额 和 破产保护推荐金额 去给他设置充值挡位

        破产保护推荐金额 -> 计算公式：min(max(120000, (用户总充值金额 * (1 - 用户的提现率) * (0.5 + 用户的提现率 * 2 * 0.14))), ********);
        $maxRechargeAmount = ********;   //最大充值金额
        $list = [
            ['min' => 50000, 'max' => 1000000, 'x' => 120000],
            ['min' => 1000000, 'max' => 2000000, 'x' => 200000],
            ['min' => 2000000, 'max' => 5000000, 'x' => 500000],
            ['min' => 5000000, 'max' => ********, 'x' => 1000000],
            ['min' => ********, 'max' => ********, 'x' => 3000000],
        ];

        第一个逻辑 ： 如果用户充值总金额 >=  规定好的最大充值金额  那这个活动金额就按规定好的最大充值金额走
        如果第一个逻辑不满足 来到第二个逻辑：
            如果用户充值总金额 >= min  并且  < max
            得到最后充值档位为   将《破产保护金额》除以《配置里的x的数量》，然后四舍五入取整，再乘以《配置里的x的数量》



（2）破产活动购买后赠送金额的计算方式是这样的：
        具体参考 BankuptcyApi.php -->   public function setOrderSuccess()
        破产活动充值档位金额  -  破产活动充值档位金额 * 破产保护活动实际支付金额比例（服务器配置的 $payAmountRate 目前配的0.9）  
    120 - 120 * 

    破产活动购买后赠送bonus的计算方式是这样的：
        破产活动充值档位金额 *  破产保护活动赠送bouns比例（服务器配置的 $bounsRate 目前配的0.15） 


（3）破产活动的开启条件：
    （1）如果提现率>= 0.7 不可以开启  优先级最高
    （2）如果用户上一次充值金额为0 也就是说没充过钱  不可以开启
    （3）如果用户身上的 （deposit + 提现金额 + 未领取的bouns ） <= 10000的话     是可以开启的
    （4）如果用户身上（deposit + 提现金额 + 未领取的bouns ）< 上一次充值金额 *0.05    也是可以开启的





2023-09-08 jingzhao：发现线上有人充值破产礼包订单金额不对 于是重新回过来调查一下这里究竟是什么问题

（1）客户端会主动请求我们 createBankruptcyOrder 这个接口  我们会根据用户身上的一些东西判断它能不能触发破产活动
     如果满足触发条件 会生成一条破产礼包的订单 存到 t_bankruptcy_order 里
     最后给客户端返回这三个东西        
        $rtn['data']['recommend_gear_amount'] = ($recommendGearAmount == 0 ? 100000 : $recommendGearAmount) + 1000;
        $rtn['data']['bankruptcy_info'] = $orderShowInfo;
        $rtn['data']['monthly_card_info'] = $monthlyCardInfo;

    一一解释一下 recommend_gear_amount ：推荐档位充值金额
                 bankruptcy_info ：可以支付的订单信息（里面有支付金额 到账金额 礼包剩余时间等等参数）
                 monthly_card_info：获取没领取的月卡金额

    这是我打印的日志（Bankruptcy-BankruptcyOrder-info） 可以参考一下
    {"rtn":{"code":1,"data":{"recommend_gear_amount":121000,"bankruptcy_info":{"pay_amount_qf":108000,"deposit_amount_qf":120000,"bouns_amount_qf":18000,"expire_time":85048},"monthly_card_info":{"deposit":200000,"bonus":0,"cnt":4,"idList":[9257]}}},"uid":"5375521"}

（2）然后我和前端对了一下这三个东西分别有什么用
     前端给我的答案是 他们那边的逻辑 
        1、首先，会先判断看是否有monthly_card_info，有的话，展示破产-月卡弹窗（我猜测估计是让玩家领取没领取完的月卡金额，这样就不会破产 就可以继续玩了）。
        2、其次，如果月卡奖励没有的话，破产活动又没有过期，那展示的就是破产活动弹窗 
        3、最后，如果破产活动过期了，那展示的就是破产-普通奖励弹窗，使用的字段是recommend_gear_amount
    
    看日志得知 玩家支付错误的金额 就是对应的 recommend_gear_amount 字段的金额
    所以有问题的玩家都走到了第三个条件里

（3）然后我看了一下t_m_recharge_order_202309 和 t_pay_in_order 两张表对有问题的订单 是怎么记录的
    发现t_m_recharge_order_202309表记录的是t_bankruptcy_order表的支付金额
    t_pay_in_order记录的是recommend_gear_amount的支付金额

    所以才会导致回调的时候 我们的订单金额和三方实际的支付金额不一样 导致报错

（4）看了一下支付payV2逻辑 活动的订单会走到createRechargeActiveOrder这个方法里 
     如果是激励活动 会走到getNotExpireOrder这个方法
     走完这个方法会把$payInfo里面的参数给强制改变  所以导致t_m_recharge_order_202309里面记录的金额和实际支付金额对不上

（5）所以这个bug导致的原因是
    玩家他正常的生成了一个破产礼包  订单信息存储到了t_bankruptcy_order表里
    然后他触发了购买的请求 但是不知道为什么走到了 前端判断的第三个条件里 用 recommend_gear_amount 当了支付金额
    这个时候他去支付 支付金额是recommend_gear_amount  但是存进t_m_recharge_order_202309里面的记录是t_bankruptcy_order里面的破产礼包金额
