Lucky Discount 是个活动，调查相关业务逻辑

（1）前端说接口是 console 的 rechargeActiveOpt opt=1 active_type=5

（2）通过查询代码 得知这是个对外的http接口
    //注册对外接口 充值活动操作
    Route::alias('rechargeActiveOpt', 'api/Gift_Api/rechargeActiveOpt');

（3）活动开关在：console后台 -->  渠道自定义参数  -->  选择渠道 比如：teentest 编辑  -->    最下面的功能开关    -->   激励活动开关
                redis --> channel_service_info --> 选择渠道 比如：teentest -->  feature_switch --> not_recharge_active_switch

（4）进到rechargeActiveOpt这个接口以后 往下走 会走到一个switch语句判断opt  --> case CommonCode::RECHARGE_ACTIVE_OPT_SHOW === 1    -->  $obj->getShowInfo();
     进到getShowInfo这个接口以后往下走 会来到活动的前置判断 $this->activeStatus 和 $this->canJoinActive

     $this->activeStatus 的两个硬性条件有点奇怪 不知道是什么业务逻辑
        条件1：必须没有充值到账金额 也就是说你这个玩家不能有充值成功的情况
        条件2：距离第一次点击充值 4个小时以后触发 我看了一下代码 第一次点击充值的这个时间 是存在redis里的（first_click_recharge_time） 三方sdk下单成功之后 会存储这个时间
        这两个条件都过了的话  $this->activeStatus会返回一个true 反之为false

     接着来到$this->canJoinActive 首先就会判断$this->activeStatus有没有通过 没有通过是不能往下走的
     接下来会判断活动开关是否开启 如果开启的话 就会给玩家开始任务了 会把任务相关的数据插到redis里 -->  user_tag_list  --> recharge_active_join_status_5
     这里走完 活动就已经开启了 就可以在客户端看到了

（5）此活动最大触发次数配置的是0（setMaxTriggerCnt），0次代表一直可以触发  触发过一次的话 下次触发时间是88个小时之后 
        这块的代码在给玩家添加活动时间的方法里setTriggerInfoToRedis  -->  'nextTriggerTime' => $this->nowTime + $this->countDownCd + $this->triggerCd,





看完这里顺便看了一下Super Deals

总结：
Super Deals 参与活动条件：所有用户都可以参加
Lucky Discount 参与活动的条件：        
	条件1：必须没有充值到账金额 也就是说你这个玩家不能有充值成功的情况
    条件2：距离第一次点击充值 必须大于4个小时 才能触发 
		   ‘第一次点击充值的’的含义就是去第三方支付渠道成功下单  但是并没有成功支付

Super Deals 下次可以参与的时间是 该活动到期后的10个小时后
Lucky Discount 下次可以参与的时间是 该活动到期后的80个小时后
这两个活动 都有一个参数 用来控制玩家总共可以参与多少次 是写死在我们代码里的  目前配置的是0   0代表可以无限参与 没有限制


                  
              
              
                
