$$ 研究百人场里的机器人， 以龙虎房为例 $$

- 目录
- <b>一、百人场房间里的 ai 用户数据 什么时候生成到 user_data 里的？</b>
- <b>二、百人场房间里的 ai 用户是怎么实现下注的？绘制流程图 并解析</b>

## 百人场房间里的 ai 用户数据 什么时候生成到 user_data 里的？

- 新房间 **init_room** 的时候，第一次创建 ai 机器人的数据。
- 房间一个流程走完 **reset_process** 的时候，补 ai 机器人。
- 统一调用的是

```php
$this->robot_supplement($room_id, $room_data);
```

## 百人场房间里的 ai 用户是怎么实现下注的？绘制流程图 并解析

**此时 user_data 里已经有了 ai 机器人的用户数据，但是还没有 ai 下注相关的数据，什么时候生成的呢？ 往下看流程图**

<b>紫色块</b>：方法名
<br />
<b>灰色快</b>：调用链以及注解
<br />

```mermaid
graph TD

房间定时器正常运转 --> deal

deal -- $this->init_robot_bet_option_data <br />初始化ai下注数据 --> init_robot_bet_option_data

init_robot_bet_option_data -- $this->robot_logic->init_robot_bet_data <br / > 过滤掉不符合条件的用户 并且 做用户分类后 <br / > 分两次请求api服务 初始化AI下注数据--> initAiTableInfo

initAiTableInfo -- 每次到新的一轮都会走到这里初始化一下<br / >计算每个ai机器人的下注区域、本轮要下注的总金额<br / > 最后得到新一轮的AI下注列表  --> 返回到init_robot_bet_option_data

返回到init_robot_bet_option_data -- 更新room_data里对应的字段 --> 至此,初始化Ai下注数据结束

```

<br /><br />

---

**此时 user_data 里已经初始化好了 每个 ai 机器人的下注区域和本轮下注的总金额，接下来就可以进行批量下注了，往下看流程图**

```mermaid
graph TD

房间定时器正常运转 --> start_bet

start_bet -- $this->robot_batch_act_bet <br /> AI批量下注事件添加--> robot_batch_act_bet

robot_batch_act_bet -- $robot_bet_data = $this->robot_logic->api_batch_bet <br /> 中转 请求api服务 --> api_batch_bet

api_batch_bet -- http请求到api服务<br />通过api的route.php找到相对应的调用 --> betAmount

betAmount -- 逻辑在这里面处理 <br /> 最后返回一个列表 内容是ai机器人每秒的下注情况 <br />包含下注的区域、下注的筹码、下注的筹码数量 <br /> 同一个机器人可以在一轮下注多次--> 返回robot_batch_act_bet

返回robot_batch_act_bet -- 把返回的列表更新到room_data下的ai_bet_batch里<br />遍历下注时间 把每秒的下注情况添加定时任务和定时器 --> update_robot_batch_act_timer

update_robot_batch_act_timer -- 执行定时器 --> execute_room_timer

execute_room_timer  -- 判断定时器执行类型 是否为机器人下注 如果是 -->  excute_robot_batch_act_logic

excute_robot_batch_act_logic --> operate_times_up_robot_act_batch

operate_times_up_robot_act_batch --> robot_batch_bet


```

<br /><br />
**至此 ai 机器人下注逻辑 结束**
