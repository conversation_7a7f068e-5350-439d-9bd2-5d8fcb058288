## PHP Notice: Undefined offset: 5428922 in /opt/yylbe/php/api/common/AiTeenPatty/AiTeenPattyPrSee.php on line 233

- <b>2023/10/12</b>
- 已修复

## PHP Notice: Undefined index: seat in /opt/yylbe/php/server/wm/games/AB/RoomLogic.php on line 2029

- <b>2023/10/13</b>
- 看错误日志 是 slots 玩法 运行到 wm\games\AB\RoomLogic->get_client_room_user_data_one 导致报错
- 解决办法：向 slots 的 gameConfig 的配置文件 给这几个报错的字段一个默认值

## PHP Notice: Undefined index: set in /opt/yylbe/php/server/wm/games/AB/CardLogic.php on line 54

- <b>2023/10/13</b>
- 看错误日志，是 slots 玩法通过定时器，代码运行到了 AB 基类的 wm\games\AB\RoomLogic->deal 方法，然后往下运行导致了报错，这是不正确的，slots 不会走到 deal 方法里面，怀疑是之前加的定时器补偿导致的线上些许房间有了问题，还在一直运转定时器补偿。
- 解决办法：向 slots 的 RoomLogic.php 子类里 增加 deal 方法，在该方法里直接 return，避免报错。

## PHP Notice: Undefined index: id_begin_3 in /opt/yylbe/php/server/wm/games/Base/RoomLogic.php on line 3888

- <b>2023/10/13</b>
- 看错误日志，是 slots 玩法 解散房间的时候，代码运行到了 add_room_type_free_incr_id 方法里，会去相关玩法的 game_config 里读取生成房间 id 的段。
- 但是 slots 生成房间号的方式比较特殊，不会在 game_config 里规定 id_begin，具体生成方式可以参考 Common::get_room_id();
- 所以就会导致去 config 里找不到 id*begin*\*\*\*，导致 notice 报错：Undefined index: id_begin_3。
