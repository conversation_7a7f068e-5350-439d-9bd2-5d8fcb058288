## 定时器任务与函数的对应关系

```mermaid
graph LR

ROOM_BASE_TIME_ACT_SEND_CARD -- ROOM_RULE_DATA_SENDCARDTIME --> deal
ROOM_BASE_TIME_ACT_START_BET -- ROOM_RULE_DATA_PROTECTTIME --> start_bet
ROOM_BASE_TIME_ACT_STOP_BET -- ROOM_RULE_DATA_BASETIME --> stop_bet
ROOM_BASE_TIME_ACT_DECLEAR -- get_declear_time --> show
ROOM_BASE_TIME_ACT_DECLEAR_ALL -- get_reset_time --> reset_process


```

## ABClassic 目前的状态变化流程 20230926

倒梯形表示状态，只能有一个出口

矩形表示动作，可以有多个出口

嵌套矩形表示子类

```mermaid
graph TD

deal --> ROOM_BASE_STATE_UNION_DEAL_AFTER --> start_bet

start_bet --> ROOM_BASE_STATE_UNION_START --> stop_bet

stop_bet --> ROOM_BASE_STATE_UNION_DECLEAR --> show

show --> ROOM_BASE_STATE_UNION_DEAL --> start_bet

show --> ROOM_BASE_STATE_UNION_DECLEAR --> reset_process

reset_process -->ROOM_BASE_STATE_UNION_DEAL --> deal

ROOM_BASE_STATE_UNION_DEAL_AFTER[\ROOM_BASE_STATE_UNION_DEAL_AFTER/]
ROOM_BASE_STATE_UNION_START[\ROOM_BASE_STATE_UNION_START/]
ROOM_BASE_STATE_UNION_DECLEAR[\ROOM_BASE_STATE_UNION_DECLEAR/]
ROOM_BASE_STATE_UNION_DEAL[\ROOM_BASE_STATE_UNION_DEAL/]

show[[show]]
reset_process[[reset_process]]

```

## ABClassic 修改后的状态变化流程 20230926

倒梯形表示状态，只能有一个出口

矩形表示动作，可以有多个出口

嵌套矩形表示子类

动作函数包括动作调用的辅助函数需要兼容虚线

```mermaid
graph TD

deal --> ROOM_BASE_STATE_UNION_DEAL_AFTER -- deal --> start_bet

start_bet --> ROOM_BASE_STATE_UNION_START --> stop_bet

stop_bet --> ROOM_BASE_STATE_UNION_DECLEAR --> show

show ==> ROOM_BASE_STATE_UNION_DEAL_AFTER -- show --> start_bet
ROOM_BASE_STATE_UNION_DEAL -.-> start_bet

show ===> ROOM_BASE_STATE_UNION_END --> reset_process
ROOM_BASE_STATE_UNION_DECLEAR -.-> reset_process

reset_process -->ROOM_BASE_STATE_UNION_DEAL --> deal

ROOM_BASE_STATE_UNION_DEAL_AFTER[\ROOM_BASE_STATE_UNION_DEAL_AFTER/]
ROOM_BASE_STATE_UNION_START[\ROOM_BASE_STATE_UNION_START/]
ROOM_BASE_STATE_UNION_DECLEAR[\ROOM_BASE_STATE_UNION_DECLEAR/]
ROOM_BASE_STATE_UNION_DEAL[\ROOM_BASE_STATE_UNION_DEAL/]
ROOM_BASE_STATE_UNION_END[\ROOM_BASE_STATE_UNION_END/]

show[[show]]
reset_process[[reset_process]]

```
