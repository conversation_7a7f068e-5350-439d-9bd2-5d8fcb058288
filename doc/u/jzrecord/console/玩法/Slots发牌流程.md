# 发牌前的代码逻辑与其他百人场无异(是否命中杀、是否需要扶植)。这个文档主要研究 slots 的发牌逻辑。

```
调用到发牌接口后

    //执行一次发牌
    protected function dealCardOnce(): array
    {
        $rtn['dealCard'] = $this->getDealCardsList();
        $rtn['scattrInfo'] = $this->calculationScattrInfo();
        $rtn['windRaiseList'] = array_values($this->getWinRaiseIdList($rtn['dealCard']));
        $rtn['winAmount'] = $this->getWinAmount($rtn);

        return $rtn;
    }

最后会 return 四个变量，下面我们一个一个来分析👇
```

## $rtn['dealCard'] : 获取最后发牌结果

- 获取结果有三套配表，根据不同情况走不通的配置表，最后根据权重随机出结果
- 还有个特殊逻辑，在系统杀和复仇杀的情况下，不会随到 3 个 SCATTR
  <br>

```
先记录一下 配置表上的牌号 对应客户端上的 icon
  1 ---> 10
  2 ---> J
  3 ---> Q
  4 ---> K
  5 ---> A
  6 ---> 蛇
  7 ---> 孔雀
  8 ---> 猴
  9 ---> 老虎
  10 ---> SCATTER
  11 ---> WILD
  12 ---> WILD
```

- 身上有 SCATTER 次数 免费发牌走的权重

```
const SOTS_FREE_DEAL_CARDS_WEIGHT = [
    1 => 130,
    2 => 130,
    3 => 130,
    4 => 130,
    5 => 105,
    6 => 105,
    7 => 91,
    8 => 80,
    9 => 80,
    11 => 33,
    12 => 25,
];
```

- 复仇杀状态下走的权重

```
const SOTS_WIN_POINT_KILLER_DEAL_CARDS_WEIGHT = [
    1 => 126,
    2 => 125,
    3 => 124,
    4 => 123,
    5 => 120,
    6 => 88,
    7 => 83,
    8 => 83,
    9 => 81,
    10 => 62,
    11 => 5,
];
```

- 普通情况走的权重

```
const SLOTS_DEAL_CARDS_WEIGHT = [
    1 => 126,
    2 => 125,
    3 => 124,
    4 => 123,
    5 => 120,
    6 => 88,
    7 => 83,
    8 => 83,
    9 => 81,
    10 => 62,
    11 => 38,
];
```

## $rtn['scattrInfo'] 计算你本局随到了几个 SCATTR

- 没啥特殊逻辑，应该是给客户端展示用的，产品忽略即可。

## $rtn['windRaiseList'] 计算中奖路线、获胜倍率、获胜金额

- 根据 dealCard 拿到的结果，计算中奖路线、获胜倍率、获胜金额。
  <br><br>
  （1）计算中奖路线<br>
  中奖路线的计算方式是<b>遍历路线配置表的每一条路线</b>，首先确定好这条中奖线的<b>排头</b>是什么(也就是中奖的牌是哪张)，然后取该路线<b>每一个中奖点位的坐标</b>，最后根据发牌结果去判断有没有<b>连续 3 个以上的坐标</b>命中

```
//获胜路线配置表 可以结合客户端的路线图看 客户端的更方便观看
const SLOTS_WIN_WAY = [
    1 => [1, 1, 1, 1, 1],
    2 => [2, 2, 2, 2, 2],
    3 => [0, 0, 0, 0, 0],
    4 => [2, 1, 0, 1, 2],
    5 => [0, 1, 2, 1, 0],
    6 => [1, 2, 2, 2, 1],
    7 => [1, 0, 0, 0, 1],
    8 => [2, 2, 1, 0, 0],
    9 => [0, 0, 1, 2, 2],
    10 => [1, 0, 1, 2, 1],
    11 => [1, 2, 1, 0, 1],
    12 => [0, 1, 1, 1, 0],
    13 => [2, 1, 1, 1, 2],
    14 => [2, 1, 2, 1, 2],
    15 => [0, 1, 0, 1, 0],
    16 => [1, 1, 2, 1, 1],
    17 => [1, 1, 0, 1, 1],
    18 => [0, 0, 2, 0, 0],
    19 => [2, 2, 0, 2, 2],
    20 => [2, 0, 0, 0, 2],
    21 => [0, 2, 2, 2, 0],
    22 => [2, 0, 2, 0, 2],
    23 => [0, 2, 0, 2, 0],
    24 => [1, 2, 0, 2, 1],
    25 => [1, 0, 2, 0, 1],
];

```

<br>
（2）获胜倍率（也叫赔率）直接从配置表里取出 <br >
有个特殊逻辑，如果你的中奖路线里面有 <b>WILD（万能牌）</b>，
倍率会扩大，计算方式为<b>当前倍率 \* WILD 出现次数对应的倍率</b>（参考配置表里的 11、12）

```
//获胜路线连续个数 以及赔率
const SLOTS_X_OPTION = [
    1 => [3 => 5, 4 => 30, 5 => 80],
    2 => [3 => 5, 4 => 35, 5 => 85],
    3 => [3 => 5, 4 => 40, 5 => 95],
    4 => [3 => 5, 4 => 45, 5 => 105],
    5 => [3 => 10, 4 => 80, 5 => 155],
    6 => [3 => 10, 4 => 95, 5 => 180],
    7 => [3 => 15, 4 => 125, 5 => 210],
    8 => [3 => 20, 4 => 140, 5 => 270],
    9 => [3 => 50, 4 => 250, 5 => 420],
    10 => [3 => 14, 4 => 21, 5 => 28],
    11 => [1 => 1, 2 => 1, 3 => 1, 4 => 1, 5 => 1],
    12 => [1 => 2, 2 => 4, 3 => 8, 4 => 16, 5 => 32],
];
```

<br>
（3）计算获胜金额<br>
单条线的获胜金额的计算方式为：<b>当前中奖线的获胜倍率</b> \* <b>当前中奖线的下注金额</b><br>
当前中奖线的下注金额是这样计算的 👇<br>

```
$this->baseLineBetAmount = $betAmount > 0 ? bcdiv($betAmount, count(SLOTS_WIN_WAY), 3) : 0;
```

比如本次 slots BET 金额为 2.5 卢比<br>
那 $this->baseLineBetAmount = 2500(2.5 卢比) 除以 25(25 条中奖线) 得到 100，并保留 3 位小数，结果是 100.000。

## $rtn['winAmount'] 计算获胜金额

- 这块代码没用 应该是弃用了 获胜金额在上面已经算好了
