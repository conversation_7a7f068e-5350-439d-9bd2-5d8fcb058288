# 新用户充值追踪

## rate 82.80 Re(SEL)7500 W(SEL)200 DAO(EL)$316.91 Roi(SEL)30.08% Roi(channel)24.72% W Rate(channel) 3.23%

```html
<div class="alert alert-success" role="alert">
  汇率 1 美元等于多少卢比
  <span>rate 82.80</span>&nbsp;&nbsp;&nbsp;&nbsp; Re(SEL) 列表总充值额 <span>Re(SEL)<{$roi.recharge}></span>&nbsp;&nbsp;&nbsp;&nbsp; W(SEL) 列表总提现额
  <span>W(SEL)<{$roi.with}></span>&nbsp;&nbsp;&nbsp;&nbsp; DAO(EL) 列表总投放美元 <span>DAO(EL)$<{$roi.advertise}></span>&nbsp;&nbsp;&nbsp;&nbsp; Roi(SEL)
  列表总投资回报率：(列表总充值额 - 列表总提现额) / (列表总投放美元 * 82.80) * 100
  <span
    >Roi(SEL)<?php if($roi['advertise'] >
    0){ echo round(($roi['recharge']-$roi['with'])/($roi['advertise']*82.80)*100,2);}?>%</span
  >&nbsp;&nbsp;&nbsp;&nbsp; Roi(channel) 渠道总投资回报率：(渠道历史总充值额 - 渠道历史总提现额) / (渠道总投放美元 * 82.80) * 100
  <span
    >Roi(channel)<?php if($totalAdvertise >
    0){ echo round(($totalAmt['recharge']-$totalAmt['with'])/($totalAdvertise*82.80)*100,2);}?>%</span
  >&nbsp;&nbsp;&nbsp;&nbsp; W Rate(channel) 渠道提现占充值百分比：渠道历史总提现额 / 渠道历史总充值额 * 100
  <span
    >W Rate(channel)
    <?php if($totalAmt['recharge'] == 0){ echo 0;}else{
        echo round( $totalAmt['with']/$totalAmt['recharge']*100,2);
    } ?>%</span
  >&nbsp;&nbsp;&nbsp;&nbsp;
</div>
```

## CPP Reg Until-Y(delta(Re)/u RE W ROI) D1-ROI D2-ROI D3-ROI ...

```html
CPP: 当日一个付费用户的美元成本
<td class="borderstyle">
  <?php if($vo['day1_recharge_num'] >
  0){ echo round($advertise_total/$vo['day1_recharge_num'],2); }else{ echo 0;} ?>
</td>

Reg: 当日新增用户数
<td class="borderstyle"><{$vo.new_user_num}></td>

delta(Re)/u: (至今充值额-至今提现额)/当日新增用户数
<td>
  <?php echo round(($vo['total_recharge_amt']-$vo['total_with_fee'])/$vo['new_user_num'],2); ?>
</td>

RE: 至今充值额
<td><{$vo.total_recharge_amt}></td>

W: 至今提现额
<td><{$vo.total_with_fee}></td>

ROI: 预测的当日投资的投资回报率：当日充值额 * (1 - 渠道历史总提现额/渠道历史总充值额) / (当日投放美元 * 82.80) * 100
<?php  
    $rois = 0;
    if($advertise_total >
0 ){ $rois = round(($vo['total_recharge_amt']*(1-$totalAmt['with']/$totalAmt['recharge']))/($advertise_total*82.80)*100,2); } ?>
<td class="borderstyle" style="<?php if($rois > 100){echo 'background-color: #cef5ea';}?>">
  <?php echo $rois ? $rois.'%' : '' ;?>
</td>

D1-ROI: 第一天的投资回报率：第一天充值额 * (1 - 渠道历史总提现额/渠道历史总充值额) / (当日投放美元 * 82.80) * 100 <{volist name = "dayArray" id = "v"}>
<?php  
        $roisday = 0;
        if($advertise_total >
0){ $roisday = round(($vo['day'.$v.'_recharge_amt']*(1-$totalAmt['with']/$totalAmt['recharge']))/($advertise_total*82.80)*100,2); } ?>
<td class="borderstyle" style="<?php if($roisday > 100){echo 'background-color: #cef5ea';}?>">
  <?php echo $roisday ? $roisday.'%' : '' ;?>
</td>
<{/volist}>
```
