## 货币操作类型

```php
    // =============================钱包 订单类型=======================
    const WALLET_TYPE_GAME_WIN = 1; // 游戏赢
    const WALLET_TYPE_GAME_LOSE = 2; // 游戏输
    const WALLET_TYPE_GAME_PAY = 3; // 充值
    const WALLET_TYPE_GAME_WITHDREW = 4; // 提现
    const WALLET_TYPE_GAME_SYSTEM = 5; // 系统
    const WALLET_TYPE_GAME_REGISTER = 6; // 新用户注册
    const WALLET_TYPE_GAME_PROFIT_BONUS = 7; //输钱返利 老服 => 输钱返利   新服 => 输钱抵扣
    const WALLET_TYPE_GAME_PAY_GIVE = 8; //充值赠送
    const WALLET_TYPE_GAME_SINGED = 9; //签到
    const WALLET_TYPE_GAME_INVITE = 10; //拉新
    const WALLET_TYPE_MONTH_CARD = 11; // 月卡
    const WALLET_TYPE_VIP_GIFT = 12; // VIP礼包

    const WALLET_TYPE_VIP_GIFT_LESS = 13; // VIP礼包兑换扣除货币
    const WALLET_TYPE_VIP_RECHARGE = 14; // VIP充值赠送bonus
    const WALLET_TYPE_WITHDRAWAL_FEE_ADD = 15; // VIP提现手续费返还
    const WALLET_TYPE_WITHDRAWAL_FEE_LESS = 16; // VIP提现手续费扣除
    const WALLET_TYPE_MONTHLY_CARD = 17; // 月卡
    const WALLET_TYPE_AI_LOSE = 18; // AI lose
    const WALLET_TYPE_AI_WIN = 19; // AI win
    const WALLET_TYPE_BANKRUPTCY = 20; // 破产保护

    const WALLET_TYPE_TASK_REWARD = 31; // 任务奖励
    const WALLET_TYPE_TASK_ACTIVITY = 32; // 活动奖励
    const WALLET_TYPE_LOTO_WIN = 33; // 赛事赢
    const WALLET_TYPE_LOTO_LOSE = 34; // 赛事输

    const WALLET_TYPE_GAME_APP_UPDATE = 90; //用户热更奖励
    const WALLET_TYPE_GAME_ADVERT_REWARD = 91; //观看广告奖励
    const WALLET_TYPE_GAME_KYC_APPLY = 92; // KYC申请
    const WALLET_TYPE_GAME_APPROVED = 93; // KYC审核通过
    const WALLET_TYPE_BIND_OPEN_PLATFORM = 94; // 绑定第三方账号
    const WALLET_TYPE_GAME_NOT_RECHARGE = 95; // 0 - 20 局未充值保护

    const WALLET_TYPE_GAME_REFUND = 97; // 玩法赢加退还门票
    const WALLET_TYPE_GAME_TICKET = 98; // 玩法进场扣除的门票
    const WALLET_TYPE_GAME_DAILY = 99; // 每日奖励
```

## 货币类型

```php
    // ==========================钱包 现金相关===========================
    const WALLET_CURRENCY_CASH = 1; // 充值现金
    const WALLET_CURRENCY_PRACTICE = 2; // 筹码
    const WALLET_CURRENCY_WITHDRAW = 3; // 提现现金
    const WALLET_CURRENCY_BONUS = 4; // 充值返利
    // ============================其他 ==============================
    const WALLET_CURRENCY_TOTAL_CASH = 5; // 充值总金额
    const WALLET_CURRENCY_TOTAL_WITHDRAW = 6; // 提现总金额
    const WALLET_CURRENCY_PROFIT_BONUS = 7; // 返利总额
    const WALLET_CURRENCY_FREEZE = 8; // 冻结金额
    const WALLET_CURRENCY_TOTAL_CHARGES = 9; // 总手续费
    const WALLET_CURRENCY_GOLD = 10; //金币
    const WALLET_CURRENCY_DIRTY = 11;
    const WALLET_CURRENCY_GREY = 12;
    //  11 脏钱 12 灰钱
    const WALLET_CURRENCY_TOTAL_BONUS = 13; //总bonus
    const WALLET_CURRENCY_TOTAL_SYSTEM_GIFT = 14; //总赠送Deposit
    const CURRENCY_TYPE_DEALER_DEPOSIT = 15; //货币类型   庄家输赢
    const CURRENCY_TYPE_BONUS_UNACCALIMED = 16; //bonus未领取

    const WALLET_CURRENCY_LIST = [
        self::WALLET_CURRENCY_CASH => 'cash',
        self::WALLET_CURRENCY_WITHDRAW => 'withdraw',
        self::WALLET_CURRENCY_BONUS => 'bonus',
        self::WALLET_CURRENCY_TOTAL_CASH => 'total_cash',
        self::WALLET_CURRENCY_PROFIT_BONUS => 'profit_bonus',
        self::WALLET_CURRENCY_TOTAL_WITHDRAW => 'total_withdraw',
        self::WALLET_CURRENCY_FREEZE => 'freeze',
        self::WALLET_CURRENCY_PRACTICE => 'practice',
        self::WALLET_CURRENCY_TOTAL_CHARGES => 'total_charges',
        self::WALLET_CURRENCY_GOLD => 'gold',
        self::WALLET_CURRENCY_TOTAL_BONUS => 'total_bonus',
    ];
```
