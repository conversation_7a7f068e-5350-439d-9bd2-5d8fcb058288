

"Super Deals"是英文短语，意思是"超级交易"或"超值优惠"。
这个短语通常用于描述商品或服务提供的特别优惠或折扣。
"Super Deals"可能是商家、零售商或在线平台使用的营销术语，旨在吸引消费者以获取超过平常的优惠价格或特殊交易条件。
这可以包括打折、促销活动、买一送一等形式的优惠。
总而言之，"Super Deals"是指非常具有吸引力和价值的交易或优惠。


分三天领取，第一次当时冲完就可以领取，后续领取间隔 24 小时
花 100 买，三次分别得 100、15、25，还有 bonus


Super Deals 前端查的接口是 rechargeActiveOpt
{ opt: 1, player_index: this.uid, token: this.token, active_type: 4 }

读取调用链
GiftApi.rechargeActiveOpt -- active_type=RECHARGE_ACTIVITY_REGISTER --> RegisterRechargeActive.getShowInfo
--> RechargeActiveBase.getShowInfo --> $this->activeInfo

谁设置的 $this->activeInfo
RechargeActiveBase.__construct --> RegisterRechargeActive.setActiveInfo

活动配置是代码写死的
活动开关在：渠道自定义参数 --> feature_switch --> register_active_switch
4  CommonCode::RECHARGE_ACTIVITY_REGISTER => 'register_active_switch', // 小额充值活动  Super Deals
5  CommonCode::RECHARGE_ACTIVITY_NOT_RECHARGE => 'not_recharge_active_switch', // 激励活动

把这个活动关掉后，还是继续给我弹窗
这个活动的开启状态会绑定在玩家身上

user_tag_list_1443904 HASH(recharge_active_join_status_4 => {
    "endShowTime": 1684315626,
    "receiveStep": 0,
    "receiveAllStep": 0,
    "nextTriggerTime": 1684351626,
    "overBuyCnt": 0,
    "overTriggerCnt": 1,
    "minAmount": false
})

    const USER_TAG_RECHARGE_ACTIVE_JOIN_STATUS = 'recharge_active_join_status_'; //玩家参与充值活动状态  注册充值活动  未充值激励活动

结论：Super Deals
后台可以控制开启和关闭：渠道自定义参数 --> 功能开关 --> 小额充值活动开关
一旦在玩家身上开启，开启状态就绑定在玩家身上了
配置是代码写死的
    protected function setActiveInfo()
    {
        //活动信息
        $activeInfo = [
            // 注册小额充值活动
            "active_type" => CommonCode::RECHARGE_ACTIVITY_REGISTER,
            // 注册小额充值活动
            "payment_type" => CommonCode::USER_PAYMENT_ORDER_TYPE_ACTIVITY_REGISTER,
            "active_status" => $this->activeStatus,
            // 展示结束时间
            "end_time" => $this->nowTime + $this->countDownCd,
            // 支付价格
            "pay_qf" => 100000,
            // 原始价格
            "price_qf" => 210000,
            // 奖励阶段
            "reward" => [
                [
                    "currey_qf" => [
                            // 货币类型   真金
                            // 单位是点数，1000 点 = 1 卢布
                        CommonCode::CURRENCY_TYPE_DEPOSIT => 100000,
                            // 货币类型   剩余bonus
                        CommonCode::CURRENCY_TYPE_BONUS => 20000,
                    ],
                    // 未领取
                    "receive_stat" => CommonCode::MONTHLY_CARD_NOT_COLLECTION,
                    "receive_cd" => 0,
                    // 秒 距离支付时间
                    "receive_time" => 0,
                    // 目前只支持 领取间隔为0 的 自动到账
                    "receive_type" => self::RECHARGE_ACTIVE_RECEIVE_TYPE_AUTO,
                ],
                [
                    "currey_qf" => [
                        CommonCode::CURRENCY_TYPE_DEPOSIT => 10000,
                        CommonCode::CURRENCY_TYPE_BONUS => 20000,
                    ],
                    // 未领取
                    "receive_stat" => CommonCode::MONTHLY_CARD_NOT_COLLECTION,
                    "receive_cd" => 24 * 3600 * 1,
                    //秒
                    "receive_time" => -1,
                    // 手动到账
                    "receive_type" => self::RECHARGE_ACTIVE_RECEIVE_TYPE_MANUAL,
                ],
                [
                    "currey_qf" => [
                        CommonCode::CURRENCY_TYPE_DEPOSIT => 20000,
                        CommonCode::CURRENCY_TYPE_BONUS => 40000,
                    ],
                    // 未领取
                    "receive_stat" => CommonCode::MONTHLY_CARD_NOT_COLLECTION,
                    "receive_cd" => 24 * 3600 * 1,
                    "receive_time" => -1,
                    // 手动到账
                    "receive_type" => self::RECHARGE_ACTIVE_RECEIVE_TYPE_MANUAL,
                ]
            ]
        ];
        $this->activeInfo = $activeInfo;
    }

把玩家身上的状态清掉后，又给我弹了个新的弹窗
DAILY WELFARE
Deposit	Bonus	Price
2080	300		2000
5300	850		5000
10800	2000	10000

日充活动
gift 接口返回的 data 数组中 type 为 5 的项
title: Daily Recharge
subtitle: Bonus Up to 4000

