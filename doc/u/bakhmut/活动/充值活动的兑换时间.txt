充值活动的兑换时间“购买时起 30 天有效”是什么意思？

一种猜测：Join 后充值得到的 Bonus 是有有效期的
一种猜测：Join 后如果 30 天内不充值就不能获得 Bonus

购买时起 <{$vo['end_time_day']}> 天 有效

    const GIFT_END_TIME_TYPE_OF_DATE = 1; //活动兑换结束时间类型  统一截止日期
    const GIFT_END_TIME_TYPE_OF_DAY = 2; //活动兑换结束时间类型  购买时间点  累加 天数

    //充值活动返利账户表 规则按照每个活动一张表  此表在创建活动的时候生成
    const GIFT_BONUS_TABLE_PREFIX = 't_m_recharge_gift_bonus_';

//注册对外接口  客户端查询用户返利账户列表
Route::alias('bonusList', 'api/Bonus_Api/getUserBonusList');

搜索 getRechargeGiftBonus 这个获取表名的函数能找到很多相关代码

    //货币类型定义
    //-------------------------------------------------------------
    const CURRENCY_TYPE_DEPOSIT = 1; //货币类型   真金 及时到账
    const CURRENCY_TYPE_COUNTER = 2; //货币类型   筹码
    const CURRENCY_TYPE_WITHDRAWAL = 3; //货币类型   提现
    const CURRENCY_TYPE_BONUS = 4; //货币类型   剩余bonus
    const CURRENCY_TYPE_ALL_DEPOSIT = 5; //货币类型   充值总额账户
    const CURRENCY_TYPE_ALL_WITHDRAWAL = 6; //货币类型   提现总额
    const CURRENCY_TYPE_PROFIT_BONUS = 7; //货币类型   已返利 总额
    const CURRENCY_TYPE_FREE_WITHDRAWAL = 8; //货币类型   提现冻结资金
    const CURRENCY_TYPE_COMMISSION = 9; //货币类型   佣金
    const CURRENCY_TYPE_GOLD = 10; //货币类型  金币
    const CURRENCY_TYPE_DIRTY = 11; //货币类型   脏钱
    const CURRENCY_TYPE_GREY = 12; //货币类型  灰钱
    const CURRENCY_TYPE_ALL_BONUS = 13; //货币类型   总的bonus
    const CURRENCY_TYPE_FREE_DEPOSIT = 14; //货币类型   累积所有免费获得的货币
    const CURRENCY_TYPE_DEALER_DEPOSIT = 15; //货币类型   庄家输赢
    const CURRENCY_TYPE_BONUS_UNACCALIMED = 16; //bonus未领取
    const CURRENCY_TYPE_WITHDRAWAL_PULL_NEW_PENDING = 17; //拉新充值返利上级 待领取金额

我用老版本客户端，充值活动赠送的 bonus 直接加到我的账上了，这个没有有效期的
    const CURRENCY_TYPE_BONUS = 4; //货币类型   剩余bonus

那个钱罐图标不知道怎么弄出来的？
哦去游戏里面输钱，那个钱罐图标就会加钱，bonus 账户就会相应的扣钱
点击钱罐图标 给 console POST http://127.0.0.1:8850/bonusreceive
{"uid":1443904,"token":"4b3834db55ad63bcfc9f1f4c982d8290"}
webSocket onmessage text msg: {"m":"Room","f":"send_available_balance","d":{"uid":"1443904","rid":0,"total_fee":9908190,"currency":1,"user_wallet":{"3":4590,"1":9903600,"4":4990000,"16":0}},"DvVarBrT8AZEjSDvCIonWiVILuuBlr7JeNpk50Dq81684150275":"dygnuvWAyke4SuIFn1"}
webSocket onmessage text msg: {"m":"Room","f":"send_available_balance","d":{"uid":"1443904","rid":0,"total_fee":9918190,"currency":1,"user_wallet":{"3":4590,"1":9913600,"4":4990000,"16":0}},"6OH579FCPaR4UKzX1u5VrCSbwdfVTaljKKLhzs1684150275":"CXEi59ZgxlKU9iIujWax5s9o7s"}
前端给 console POST http://127.0.0.1:8850/getbonusunaccalimed
{"uid":1443904,"token":"4b3834db55ad63bcfc9f1f4c982d8290"}
WS was sended: {"m":"Wallet","f":"get_user_wallet","d":{"u":1443904,"t":"4b3834db55ad63bcfc9f1f4c982d8290"}}
webSocket onmessage text msg: {"m":"Wallet","f":"get_user_wallet","d":{"1":9913.6,"3":4.59,"4":4990,"2":0,"10":0,"5":10000,"16":0},"1UXGcMmXMf1P5YtvVE5rL1684150275":"I1mKQICxkd5IzS4GK5HWPKZPSvoSKYyY2QHAc4WCIdb3ZuR5I"}
getbonusunaccalimed: {code: 1, data: 0}

//领取bonus
Route::alias('bonusreceive', 'api/User_Api/bonusReceive');

    const CURRENCY_TYPE_BONUS_UNACCALIMED = 16; //bonus未领取
看来未领取 bonus 也是一个账户

目前的结论
剩余 bonus 是一个账户，没有有效期，里面的钱在输的时候会转移一部分到未领取 bonus 账户
未领取 bonus 也是一个账户，没有有效期，里面的钱在处理前端的 bonusreceive 指令时转移到 DEPOSITE 账户
钱在这些在账户里面，没有有效期一说，比如某个部分过期了自动给扣掉，没有这种机制
所以这种说法错误：Join 后充值得到的 Bonus 是有有效期的
“购买时起 30 天有效”是什么意思还需要再查
