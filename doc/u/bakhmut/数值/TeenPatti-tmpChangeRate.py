# %%
import matplotlib.pyplot as plt

plt.rcParams["font.sans-serif"] = ["SimHei"]  # 设置字体
plt.rcParams["axes.unicode_minus"] = False  # 该语句解决图像中的“-”负号的乱码问题
import numpy as np
import math

"""
            //Tp基础杀率=min(配置的杀率上限,原有杀率+房间控系数*当日实时缺口额度/max(当日实时盈利目标,5000,房间底注*1000倍))
            // tmpChangeRate = min(sys_killer_limit_rate, deal_rate + max(0, 缺口率) * intervene_rate)
            // sys_killer_limit_rate 房间控上限
            // deal_rate 房间控初始
            // intervene_rate 房间控系数
            // 最后取所有真人的 tmpChangeRate 的最小值作为大盘控的概率
"""

deal_rate = 0.02  # 房间控初始
sys_killer_limit_rate = 0.33  # 房间控上限

# 列举几种房间控系数
for intervene_rate in [0.6, 0.8, 1, 2, 4, 8, 16]:
    # 缺口率
    gapRate = np.linspace(0, 1, 101)
    # 大盘控概率
    tmpChangeRate = np.minimum(sys_killer_limit_rate, deal_rate + np.maximum(0, gapRate) * intervene_rate)

    plt.plot(gapRate, tmpChangeRate, label=f"{intervene_rate}")
    # 曲线末尾加标注
    plt.text(gapRate[-1], tmpChangeRate[-1], f"{intervene_rate}")

# 图例顺序倒过来
# Get current handles and labels
handles, labels = plt.gca().get_legend_handles_labels()
# Reverse handles and labels
handles.reverse()
labels.reverse()

# 图例放在图的外面右侧
plt.legend(handles, labels, bbox_to_anchor=(1.05, 1))
plt.xlabel("缺口率")
plt.ylabel("大盘控概率")
plt.show()

# %% [markdown]
# 如果多人玩，取大盘控概率最小的那个人的大盘控概率
