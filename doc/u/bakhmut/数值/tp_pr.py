# %%
print("Hello!")

# %% [markdown]
# $sqrt{2}$

# %%
import matplotlib.pyplot as plt
import numpy as np

x = np.linspace(0, 10, 101)
plt.plot(x, np.sin(x))
plt.show()


# %%
import matplotlib.pyplot as plt
import numpy as np

n = np.linspace(1, 300, 300)
winAmount = np.linspace(1000, 300 * 1000, 300)
lossAmount = 10 * 1000
profit = winAmount - lossAmount
deposit = 20 * 1000

lossAmount = winAmount * 0.1
profit = winAmount - lossAmount
deposit = [x < 200 and 100 * 1000 or 300 * 1000 for x in n]

user_wallet = deposit

lossAmountAll = lossAmount  # 只玩 TP

z1 = np.maximum(10000, lossAmount)
lossRatioToAll = lossAmount / np.maximum(10000, lossAmountAll)
lossRatioToAllRevised = np.round(lossRatioToAll * 50 + 0.5) / 50  # 相当于加了 0.02 这是何意？
z2 = np.maximum(10000, user_wallet * lossRatioToAllRevised)
prParam = np.minimum(z1, z2)

pr = 1 + (profit / prParam)

plt.plot(winAmount, pr)
plt.show()


# %% [markdown]
# - 初期净输钱就抬
# - 中期输钱快才抬，不看输钱多少，而是看输钱快慢
# - 后期同样输钱快慢时，抬不充钱的

# %%

import matplotlib.pyplot as plt
import numpy as np

n = np.linspace(1, 300, 300)
winAmount = np.linspace(1000, 300 * 1000, 300)
lossAmount = 10 * 1000
profit = winAmount - lossAmount
deposit = 20 * 1000

lossAmount = winAmount * 1.2
profit = winAmount - lossAmount
deposit = [x < 200 and 100 * 1000 or 300 * 1000 for x in n]

user_wallet = deposit

lossAmountAll = lossAmount  # 只玩 TP

z1 = np.maximum(10000, lossAmount)
lossRatioToAll = lossAmount / np.maximum(10000, lossAmountAll)
lossRatioToAllRevised = np.round(lossRatioToAll * 50 + 0.5) / 50  # 相当于加了 0.02 这是何意？
z2 = np.maximum(10000, user_wallet * lossRatioToAllRevised)
prParam = np.minimum(z1, z2)

pr = 1 + (profit / prParam)

plt.plot(winAmount, pr)
plt.show()

# %%
import matplotlib.pyplot as plt
import numpy as np

k = 300
np.random.seed(1311)
n = np.linspace(1, k, k)
randAmount = np.random.randint(-5 * 1000, 5 * 1000, k)
# 根据 randAmount 计算总赢钱
winAmount = np.cumsum(np.maximum(randAmount, 0))
lossAmount = -np.cumsum(np.minimum(randAmount, 0))
profit = winAmount - lossAmount
deposit = 100 * 10000 * 1000

deposit = [x < 200 and 100 * 1000 or 300 * 1000 for x in n]

user_wallet = deposit

lossAmountAll = lossAmount  # 只玩 TP

z1 = np.maximum(10000, lossAmount)
lossRatioToAll = lossAmount / np.maximum(10000, lossAmountAll)
lossRatioToAllRevised = np.round(lossRatioToAll * 50 + 0.5) / 50  # 相当于加了 0.02 这是何意？
z2 = np.maximum(10000, user_wallet * lossRatioToAllRevised)
prParam = np.minimum(z1, z2)

pr = 1 + (profit / prParam)

plt.plot(n, profit)
plt.show()
plt.plot(n, pr)
plt.show()
