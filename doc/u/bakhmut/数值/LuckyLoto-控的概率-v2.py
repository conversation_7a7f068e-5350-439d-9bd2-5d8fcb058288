# %%
import matplotlib.pyplot as plt

plt.rcParams["font.sans-serif"] = ["SimHei"]  # 设置字体
plt.rcParams["axes.unicode_minus"] = False  # 该语句解决图像中的“-”负号的乱码问题
import numpy as np
import math


# 各个开奖区域的权重配置
LUCKY_LOTO_WIN_OPTION_WEIGHT = [25, 1890, 3150, 3780, 4730, 6310]

# 列举几种房间控系数
for intervene_rate in [0.31, 0.5, 0.8, 1, 2, 4, 6, 8, 15]:
    # 缺口率
    gapRate = np.linspace(0, 1, 101)
    # 权重放大倍数
    boostFactor = 1 / np.minimum(1, np.maximum(0.05, 1 - intervene_rate * gapRate))
    # 如果开奖区域 1 有利于玩家，那么其他开奖区域的概率和
    zone1Prob = 1890 / (
        25 * boostFactor + 1890 + 3150 * boostFactor + 3780 * boostFactor + 4730 * boostFactor + 6310 * boostFactor
    )
    otherProb = 1 - zone1Prob

    plt.plot(gapRate, otherProb, label=f"{intervene_rate}")
    # 曲线末尾加标注
    plt.text(gapRate[-1], otherProb[-1], f"{intervene_rate}")

# 图例顺序倒过来
# Get current handles and labels
handles, labels = plt.gca().get_legend_handles_labels()
# Reverse handles and labels
handles.reverse()
labels.reverse()

# 图例放在图的外面右侧
plt.legend(handles, labels, bbox_to_anchor=(1.05, 1))
plt.xlabel("缺口率")
plt.ylabel("非开奖区域 1 的概率")
plt.show()

# %% [markdown]
# 如果多人玩，取缺口率最小的那个人的缺口率
