## 是否执行大盘控

- 真人的有效充值次数 --> 确定真人杀率类型
- 玩法+底注+真人杀率类型 --> 唯一确定一个奖池
- 奖池配置+奖池状态+时间参数 --> 确定某个真人的大盘控概率
- 取最小值作为本次发牌的大盘控概率

| 字段                     | 含义                                 | 单位 |
| ------------------------ | ------------------------------------ | ---- |
| capitalType              | 真人杀率类型                         |      |
| todayGapAmount           | 奖池今日实时利润距离动态目标的缺口额 | 点   |
| waveRate                 |                                      |      |
| base                     | 底注                                 | 点   |
| todayDynamicTargetAmount | 奖池今日实时动态目标额               | 点   |
| todaySystemAmount        | 奖池今日实时利润                     | 点   |
| todayOldTargetAmount     | 奖池今日实时原始目标额               | 点   |
| deal_rate                | 房间控初始                           |      |
| sys_killer_limit_rate    | 房间控上限                           |      |
| intervene_rate           | 房间控系数                           |      |
| changeRate               | 大盘控概率                           |      |
| tmpChangeRate            | 某个真人的大盘控概率                 |      |
| todayMinutes             | 今日实时分钟数                       |

$$
奖池今日实时原始目标额 =  奖池今日实时真人输赢流水总额 * 真人杀率类型关联的盈利率
$$

$$
timeRate = \frac{今日实时分钟数 * 0.96}{今日实时分钟数 + 300} + 0.2
$$

if 奖池今日实时利润 > 奖池今日实时原始目标额

$$
奖池今日实时动态目标额 = 奖池今日实时原始目标额 *  timeRate
$$

else

$$
奖池今日实时动态目标额 = 奖池今日实时原始目标额
$$

$$
奖池今日实时利润距离动态目标的缺口额 = \max(0, 奖池今日实时动态目标额 - 奖池今日实时利润)
$$

$$
waveRate = 房间控系数 * \frac{奖池今日实时利润距离动态目标的缺口额}{\max(奖池今日实时动态目标额,1000000, 底注*1000)}
$$

$$
某个真人的大盘控概率 = \min(房间控上限, 房间控初始 + waveRate)
$$

$$
大盘控概率 = \min(各个真人的大盘控概率)
$$

## 大盘控的发牌顺序

```mermaid
graph TD
A{{如果没有真人则随机顺序}} --> 返回
A --> B["第二大牌给 pr 最高的真人，抑制！"]
--> C["第一大牌给谁？扶植或回收！
<br>(1) pr 低于 0.3 且 pr 最低的真人
<br>(2) 某个机器人
<br>(3) pr 最低的真人
"]
--> D["确定两个关键角色后，剩下的随机打乱"]
--> 返回
```

## 非大盘控的发牌顺序

```mermaid
graph TD
A{{如果需要抑制真人则走抑制型}} --> 返回
A --> B["如果需要扶植真人则走扶植型"] --> 返回
B --> C["随机型"]
--> 返回
```

## 抑制型的发牌顺序

```mermaid
graph TD
B["第二大牌给 pr 最高的真人，抑制！"]
--> C["第一大牌给谁？扶植或回收！
<br>(1) 需要被扶植且 pr 最低的真人
<br>(2) 某个机器人
<br>(3) pr 最低的真人
"]
--> D["确定两个关键角色后，剩下的随机打乱"]
--> 返回
```

## 扶植型的发牌顺序

```mermaid
graph TD
B["第一大牌给需要被扶植且 pr 最低的真人，扶植！"]
--> D["确定这个关键角色后，剩下的随机打乱"]
--> 返回
```

## AiPlayerPr 术语

1 卢比 = 1000 点

| 字段          | 含义                   | 单位 |
| ------------- | ---------------------- | ---- |
| clsWinAmount  | 在该玩法的赢钱流水     | 点   |
| clsLossAmount | 在该玩法的输钱流水     | 点   |
| allLossAmount | 在所有玩法的输钱流水   | 点   |
| deposite      | 充值流水               | 点   |
| clsProfit     | 在该玩法的净利润       | 点   |
| allProfit     | 在所有玩法的净利润     | 点   |
| p             | 用于查 prConfig 表     | 卢比 |
| dLimit        | 扶植下限               | pr   |
| dBegin        | 扶植起点               | pr   |
| uBegin        | 抑制起点               | pr   |
| uLimit        | 抑制上限               | pr   |
| ed            | 扶植的插值计算用的指数 |      |
| eu            | 抑制的插值计算用的指数 |      |
| isRecharge    | 充过值                 |      |

## getPlayerPrConfig

if not isRecharge

$$
prConfig = 第一档
$$

if p 太小

$$
prConfig = 第一档
$$

if p 太大

$$
prConfig = 最后一档
$$

else

$$
prConfig = 两档插值
$$

$$
dLimit = dLimit2 - (\frac{p - p1}{p2 - p1})^{ed} * (dLimit2 - dLimit1)
$$

$$
dBegin = dBegin2 - (\frac{p - p1}{p2 - p1})^{ed} * (dBegin2 - dBegin1)
$$

$$
uBegin = uBegin1 + (\frac{p - p1}{p2 - p1})^{eu} * (uBegin2 - uBegin1)
$$

$$
uLimit = uLimit1 + (\frac{p - p1}{p2 - p1})^{eu} * (uLimit2 - uLimit1)
$$

prConfig 表的某种配置是：

| 函数段 | 预设节点 | 扶植下限 | 扶植起点 | 抑制起点 | 抑制上限 | 扶植插值指数 | 抑制插值指数 |
| ------ | -------- | -------- | -------- | -------- | -------- | ------------ | ------------ |
|        | p        | dLimit   | dBegin   | uBegin   | uLimit   | ed           | eu           |
| 1      | 100      | 0.3      | 0.7      | 2        | 4        | 1            | 1            |
| 2      | 1000     | 0.4      | 0.7      | 1.8      | 3        | 1            | 1            |
| 3      | 5000     | 0.45     | 0.7      | 1.5      | 2.5      | 1            | 1            |
| 4      | 20000    | 0.5      | 0.7      | 1.35     | 2        | 1            | 1            |
| 5      | 50000    | 0.5      | 0.7      | 1.2      | 1.4      | 1            | 1            |
| 6      | 100000   | 0.5      | 0.7      | 1.11     | 1.2      | 1            | 1            |
| 7      | 1000000  | 0.5      | 0.7      | 1.05     | 1.1      | 1            | 1            |
| 8      | 5000000  | 0.5      | 0.7      | 1.04     | 1.08     | 1            | 1            |

扶植是反向插值，造成的效果估计是：同样的 pr 值，随着局数增加，扶植概率档内降低，跨档突增。

抑制是正向插值，效果估计是：同样的 pr 值，随着局数增加，抑制概率增加，跨档平滑。

## getPlayerPrByCls

$$
revisedLossAmount = \max(10'000, clsLossAmount)
$$

$$
clsLossRatioToAll = \frac{clsLossAmount}{\max(10'000, allLossAmount)} + 0.02
$$

$$
revisedDepositeAmount = \max(10'000, deposite * clsLossRatioToAll)
$$

$$
prParam = \min(revisedLossAmount, revisedDepositeAmount)
$$

$$
clsProfit = clsWinAmount - clsLossAmount
$$

$$
pr = 1 + \frac{clsProfit}{prParam}
$$

$$
p = \frac{\min(clsLossAmount, prParam)}{1000}
$$

### 新手只玩该玩法 clsLossAmount in [0, 2'000) and allLossAmount = clsLossAmount and deposite = 50'000 忽略 0.02

$$
revisedLossAmount = 10'000
$$

$$
clsLossRatioToAll = [0, 0.2)
$$

$$
revisedDepositeAmount = 10'000
$$

$$
prParam = 10'000
$$

$$
clsProfit = clsWinAmount - clsLossAmount
$$

$$
pr = 1 + \frac{clsProfit}{10'000}
$$

$$
p = [0, 2)
$$

### 新手只玩该玩法 clsLossAmount \in [2'000, 10'000) and allLossAmount = clsLossAmount and deposite = 50'000 忽略 0.02

$$
revisedLossAmount = 10'000
$$

$$
clsLossRatioToAll \in [0.2, 1)
$$

$$
revisedDepositeAmount \in [10'000, 50'000)
$$

$$
prParam = 10'000
$$

$$
clsProfit = clsWinAmount - clsLossAmount
$$

$$
pr = 1 + \frac{clsProfit}{10'000}
$$

$$
p \in [2, 10)
$$

### 新手只玩该玩法 clsLossAmount \in [10'000, 50'000) and allLossAmount = clsLossAmount and deposite = 50'000 忽略 0.02

$$
revisedLossAmount = clsLossAmount
$$

$$
clsLossRatioToAll = 1
$$

$$
revisedDepositeAmount =  50'000
$$

$$
prParam = clsLossAmount
$$

$$
clsProfit = clsWinAmount - clsLossAmount
$$

$$
pr = 1 + \frac{clsProfit}{clsLossAmount}
$$

$$
p \in [10, 50)
$$

### 新手只玩该玩法 clsLossAmount \in [50'000, 100'000) and allLossAmount = clsLossAmount and deposite = 50'000 忽略 0.02

$$
revisedLossAmount = clsLossAmount
$$

$$
clsLossRatioToAll = 1
$$

$$
revisedDepositeAmount =  50'000
$$

$$
prParam = revisedDepositeAmount
$$

$$
clsProfit = clsWinAmount - clsLossAmount
$$

$$
pr = 1 + \frac{clsProfit}{50'000}
$$

$$
p \in [50, 100)
$$

## getPlayerAllPr

### 没充过值

minAmount 为第一档的 $p * 1'000$，默认 15'000

$$
prParam = \min(minAmount, \max(10'000, allLossAmount))
$$

$$
allProfit = allWinAmount - allLossAmount
$$

$$
pr = 1 + \frac{allProfit}{prParam}
$$

### 充过值

$$
revisedLossAmount = \max(10'000, allLossAmount)
$$

$$
allLossRatioToAll = \frac{allLossAmount}{\max(10'000, allLossAmount)} + 0.02
$$

$$
revisedDepositeAmount = \max(10'000, deposite * allLossRatioToAll)
$$

$$
prParam = \min(revisedLossAmount, revisedDepositeAmount)
$$

$$
allProfit = allWinAmount - allLossAmount
$$

$$
pr = 1 + \frac{allProfit}{prParam}
$$

$$
p = \frac{\min(allLossAmount, prParam)}{1000}
$$
