## AiPlayerPr 术语

1 卢比 = 1000 点

| 字段          | 含义                   | 单位 |
| ------------- | ---------------------- | ---- |
| clsWinAmount  | 在该玩法的赢钱流水     | 点   |
| clsLossAmount | 在该玩法的输钱流水     | 点   |
| allLossAmount | 在所有玩法的输钱流水   | 点   |
| deposite      | 充值流水               | 点   |
| clsProfit     | 在该玩法的净利润       | 点   |
| allProfit     | 在所有玩法的净利润     | 点   |
| p             | 用于查 prConfig 表     | 卢比 |
| dLimit        | 扶植下限               | pr   |
| dBegin        | 扶植起点               | pr   |
| uBegin        | 抑制起点               | pr   |
| uLimit        | 抑制上限               | pr   |
| ed            | 扶植的插值计算用的指数 |      |
| eu            | 抑制的插值计算用的指数 |      |
| isRecharge    | 充过值                 |      |

## getPlayerPrConfig

$$
prConfig =
\begin{cases}
  第一档 &\text{if not isRecharge} \\
  第一档 &\text{if } p 太小 \\
  最后一档 &\text{if } p 太大 \\
  两档插值 &\text{otherwise}
\end{cases} \\

dLimit = dLimit2 - (\frac{p - p1}{p2 - p1})^{ed} * (dLimit2 - dLimit1) \\
dBegin = dBegin2 - (\frac{p - p1}{p2 - p1})^{ed} * (dBegin2 - dBegin1) \\
uBegin = uBegin1 + (\frac{p - p1}{p2 - p1})^{eu} * (uBegin2 - uBegin1) \\
uLimit = uLimit1 + (\frac{p - p1}{p2 - p1})^{eu} * (uLimit2 - uLimit1) \\
$$

prConfig 表的某种配置是：

| 函数段 | 预设节点 | 扶植下限 | 扶植起点 | 抑制起点 | 抑制上限 | 扶植插值指数 | 抑制插值指数 |
| ------ | -------- | -------- | -------- | -------- | -------- | ------------ | ------------ |
|        | p        | dLimit   | dBegin   | uBegin   | uLimit   | ed           | eu           |
| 1      | 100      | 0.3      | 0.7      | 2        | 4        | 1            | 1            |
| 2      | 1000     | 0.4      | 0.7      | 1.8      | 3        | 1            | 1            |
| 3      | 5000     | 0.45     | 0.7      | 1.5      | 2.5      | 1            | 1            |
| 4      | 20000    | 0.5      | 0.7      | 1.35     | 2        | 1            | 1            |
| 5      | 50000    | 0.5      | 0.7      | 1.2      | 1.4      | 1            | 1            |
| 6      | 100000   | 0.5      | 0.7      | 1.11     | 1.2      | 1            | 1            |
| 7      | 1000000  | 0.5      | 0.7      | 1.05     | 1.1      | 1            | 1            |
| 8      | 5000000  | 0.5      | 0.7      | 1.04     | 1.08     | 1            | 1            |

扶植是反向插值，造成的效果估计是：同样的 pr 值，随着局数增加，扶植概率档内降低，跨档突增。

抑制是正向插值，效果估计是：同样的 pr 值，随着局数增加，抑制概率增加，跨档平滑。

## getPlayerPrByCls

$$
revisedLossAmount = \max(10'000, clsLossAmount) \\
clsLossRatioToAll = \frac{clsLossAmount}{\max(10'000, allLossAmount)} + 0.02 \\
revisedDepositeAmount = \max(10'000, deposite * clsLossRatioToAll) \\
prParam = \min(revisedLossAmount, revisedDepositeAmount) \\
clsProfit = clsWinAmount - clsLossAmount \\
pr = 1 + \frac{clsProfit}{prParam} \\
p = \frac{\min(clsLossAmount, prParam)}{1000} \\
$$

### 新手只玩该玩法 $clsLossAmount in [0, 2'000) \And allLossAmount = clsLossAmount \And deposite = 50'000$ 忽略 0.02

$$
revisedLossAmount = 10'000 \\
clsLossRatioToAll = [0, 0.2) \\
revisedDepositeAmount = 10'000 \\
prParam = 10'000 \\
clsProfit = clsWinAmount - clsLossAmount \\
pr = 1 + \frac{clsProfit}{10'000} \\
p = [0, 2) \\
$$

### 新手只玩该玩法 $clsLossAmount \in [2'000, 10'000) \And allLossAmount = clsLossAmount \And deposite = 50'000$ 忽略 0.02

$$
revisedLossAmount = 10'000 \\
clsLossRatioToAll \in [0.2, 1) \\
revisedDepositeAmount \in [10'000, 50'000) \\
prParam = 10'000 \\
clsProfit = clsWinAmount - clsLossAmount \\
pr = 1 + \frac{clsProfit}{10'000} \\
p \in [2, 10) \\
$$

### 新手只玩该玩法 $clsLossAmount \in [10'000, 50'000) \And allLossAmount = clsLossAmount \And deposite = 50'000$ 忽略 0.02

$$
revisedLossAmount = clsLossAmount \\
clsLossRatioToAll = 1 \\
revisedDepositeAmount =  50'000 \\
prParam = clsLossAmount \\
clsProfit = clsWinAmount - clsLossAmount \\
pr = 1 + \frac{clsProfit}{clsLossAmount} \\
p \in [10, 50) \\
$$

### 新手只玩该玩法 $clsLossAmount \in [50'000, 100'000) \And allLossAmount = clsLossAmount \And deposite = 50'000$ 忽略 0.02

$$
revisedLossAmount = clsLossAmount \\
clsLossRatioToAll = 1 \\
revisedDepositeAmount =  50'000 \\
prParam = revisedDepositeAmount \\
clsProfit = clsWinAmount - clsLossAmount \\
pr = 1 + \frac{clsProfit}{50'000} \\
p \in [50, 100) \\
$$

## getPlayerAllPr

### 没充过值

minAmount 为第一档的 $p * 1'000$，默认 15'000

$$
prParam = \min(minAmount, \max(10'000, allLossAmount)) \\
allProfit = allWinAmount - allLossAmount \\
pr = 1 + \frac{allProfit}{prParam} \\
$$

### 充过值

$$
revisedLossAmount = \max(10'000, allLossAmount) \\
allLossRatioToAll = \frac{allLossAmount}{\max(10'000, allLossAmount)} + 0.02 \\
revisedDepositeAmount = \max(10'000, deposite * allLossRatioToAll) \\
prParam = \min(revisedLossAmount, revisedDepositeAmount) \\
allProfit = allWinAmount - allLossAmount \\
pr = 1 + \frac{allProfit}{prParam} \\
p = \frac{\min(allLossAmount, prParam)}{1000} \\
$$
