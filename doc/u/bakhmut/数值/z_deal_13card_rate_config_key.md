```mermaid
graph TD


AiRummyDealCardProfitToPlayer.ratePwConfigList --> getPrCardsGroupConfigByMaxCardNum --> z_deal_13card_rate_config_key

```

```mermaid
graph TD

api.deal_cards_v2_bat --> ApiRummyAct.dealCardsByProfitBat --> AiRummyDealCardBat.dealCardsBatForNormal --> AiRummyDealCardProfitToPlayer
api.deal_cards_v2 --> ApiRummyAct.dealCardsByProfit --> AiRummyDealCardProfitToPlayer

RummyPoints.RoomLogic.hand_card_process --> RummyPoints.CardLogic.init_hand_card --> RummyPoints.CardLogic.init_user_hand_card_all --> Base.RobotLogic.api_deal_card_v2_bat --> api.deal_cards_v2_bat
RummyPoints.CardLogic.init_user_hand_card_all --> RummyPoints.CardLogic.init_user_hand_card --> RummyPoints.CardLogic.init_hand_card_api --> Base.RobotLogic.api_deal_card_v2 --> api.deal_cards_v2


```

```mermaid
graph LR

RummyPoints.TimerLogic.excute_room_logic.ROOM_BASE_TIME_ACT_COMPARE --> RummyPoints.RoomLogic.hand_card_process
RummyPoints.RoomLogic.order_confirm_process --> RummyPoints.RoomLogic.hand_card_process
RummyPoints.RoomLogic.is_refresh_order --> RummyPoints.RoomLogic.hand_card_process
RummyPoints.RoomLogic.hand_card --> RummyPoints.RoomLogic.hand_card_process

```

```mermaid
graph LR
Room.order_confirm --> RummyPoints.RoomLogic.order_confirm --> RummyPoints.RoomLogic.order_confirm_process

RummyPoints.RoomLogic.user_order_process --> RummyPoints.RoomLogic.is_refresh_order

nobody --> RummyPoints.RoomLogic.hand_card
```

```mermaid
graph LR

RummyPoints.TimerLogic.excute_room_logic.ROOM_BASE_TIME_ACT_START --> RummyPoints.RoomLogic.user_order_process
RummyPoints.RoomLogic.user_order --> RummyPoints.RoomLogic.user_order_process
RummyPoints.RoomLogic.leave_process --> RummyPoints.RoomLogic.user_order_process
RummyPoints.RoomLogic.ready_process --> RummyPoints.RoomLogic.user_order_process


```
