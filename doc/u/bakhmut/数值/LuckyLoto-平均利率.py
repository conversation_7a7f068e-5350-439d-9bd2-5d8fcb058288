# %%
import matplotlib.pyplot as plt

plt.rcParams["font.sans-serif"] = ["SimHei"]  # 设置字体
plt.rcParams["axes.unicode_minus"] = False  # 该语句解决图像中的“-”负号的乱码问题
import numpy as np
import math


# 各个开奖区域的权重配置
LUCKY_LOTO_WIN_OPTION_WEIGHT = [25, 1890, 3150, 3780, 4730, 6310]
# 各个开奖区域的赔率
ODDS_ARRAY = [0.2, 10.6, 6.4, 5.3, 4.2, 3.2]

TOTAL_WEIGHT = sum(LUCKY_LOTO_WIN_OPTION_WEIGHT)

real_odds_array = [
    round(odds * weight / TOTAL_WEIGHT, 3) for odds, weight in zip(ODDS_ARRAY, LUCKY_LOTO_WIN_OPTION_WEIGHT)
]

print(real_odds_array)


def calc_zones_interest_rate_with_boost_factor(zones, boostFactor):
    totalWeight = 0
    for i in range(len(LUCKY_LOTO_WIN_OPTION_WEIGHT)):
        if i not in zones:
            totalWeight += LUCKY_LOTO_WIN_OPTION_WEIGHT[i] * boostFactor
        else:
            totalWeight += LUCKY_LOTO_WIN_OPTION_WEIGHT[i]
    totalOdds = sum([ODDS_ARRAY[zone] * LUCKY_LOTO_WIN_OPTION_WEIGHT[zone] / totalWeight for zone in zones])
    interest_rate = (totalOdds - len(zones)) / len(zones)
    return round(interest_rate, 3)


print(calc_zones_interest_rate_with_boost_factor([3, 4, 5], 1))
print([calc_zones_interest_rate_with_boost_factor([zone], 1) for zone in range(6)])
print([calc_zones_interest_rate_with_boost_factor([zone], 20) for zone in range(6)])

# 列举几种房间控系数
for intervene_rate in [0.31, 0.5, 0.8, 1, 2, 4, 6, 8, 15]:
    # 缺口率
    gapRate = np.linspace(0, 1, 101)
    # 权重放大倍数
    boostFactor = 1 / np.minimum(1, np.maximum(0.05, 1 - intervene_rate * gapRate))
    # 不控的时候，开奖区域5最有利于玩家，所以这里关注开奖区域5的利率变化
    f = np.vectorize(lambda x: calc_zones_interest_rate_with_boost_factor([5], x))
    zone5_interest_rate = f(boostFactor)

    plt.plot(gapRate, zone5_interest_rate, label=f"{intervene_rate}")
    # 曲线末尾加标注
    plt.text(gapRate[-1], zone5_interest_rate[-1], f"{intervene_rate}")


# 图例放在图的外面右侧
plt.legend(bbox_to_anchor=(1.05, 1))
plt.xlabel("缺口率")
plt.ylabel("开奖区域 5 的利率")
plt.show()

# %% [markdown]
# 如果多人玩，取缺口率最小的那个人的缺口率
