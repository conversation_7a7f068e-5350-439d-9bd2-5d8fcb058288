# 调查：这个配置有没有被用到：Rummy-真人 --> 13card 发牌配置 --> 新充权重

## 结论

### 结论摘要

- 有用到
- 本局给玩家发什么牌是依据牌型权重表随机的
- 影响牌型权重表的因素：玩家的 pr 值，pr 配置表，用户分类，13card 发牌配置表
- 粗略理解就是 pr 值低的发好牌，pr 值高的发差牌，有新充 buff 的发好牌，新手发好牌

### 13card 发牌配置

| 牌型       | 基础权重 | 扶植系数 | 抑制系数 | 新手权重 | 新充权重 |
| ---------- | -------- | -------- | -------- | -------- | -------- |
| 牌型       | 基础权重 | 扶植系数 | 抑制系数 | 新手权重 | 新充权重 |
| 3 绿及以上 | 45       | 0.8      | -1       | 1        | 0.4      |
| 2 绿       | 183      | 0.5      | -0.6     | 0.4      | 0.3      |
| 1 绿       | 140      | -1       | 0.8      | 0        | -0.5     |
| 0 绿有癞   | 451      | 0        | -0.45    | 0        | 0        |
| 0 绿无癞   | 182      | -1       | 1.1      | -0.75    | -0.5     |

## z_deal_13card_rate_config_key

```json
{
  "group_name_3lv": {
    "normal": "45",
    "up": "0.8",
    "down": "-1",
    "novice": "1",
    "recharge": "0.4"
  },
  "group_name_2lv": {
    "normal": "183",
    "up": "0.5",
    "down": "-0.6",
    "novice": "0.4",
    "recharge": "0.3"
  },
  "group_name_1lv": {
    "normal": "140",
    "up": "-1",
    "down": "0.8",
    "novice": "0",
    "recharge": "-0.5"
  },
  "group_name_nolv_yeslai": {
    "normal": "451",
    "up": "0",
    "down": "-0.45",
    "novice": "0",
    "recharge": "0"
  },
  "group_name_nolv_nolai": {
    "normal": "182",
    "up": "-1",
    "down": "1.1",
    "novice": "-0.75",
    "recharge": "-0.5"
  }
}
```

## 调用链

```mermaid
graph TD


AiRummyDealCardProfitToPlayer.ratePwConfigList --> getPrCardsGroupConfigByMaxCardNum --> z_deal_13card_rate_config_key

```

```mermaid
graph TD

api.deal_cards_v2_bat --> ApiRummyAct.dealCardsByProfitBat --> AiRummyDealCardBat.dealCardsBatForNormal --> AiRummyDealCardProfitToPlayer
api.deal_cards_v2 --> ApiRummyAct.dealCardsByProfit --> AiRummyDealCardProfitToPlayer

RummyPoints.RoomLogic.hand_card_process --> RummyPoints.CardLogic.init_hand_card --> RummyPoints.CardLogic.init_user_hand_card_all --> Base.RobotLogic.api_deal_card_v2_bat --> api.deal_cards_v2_bat
RummyPoints.CardLogic.init_user_hand_card_all --> RummyPoints.CardLogic.init_user_hand_card --> RummyPoints.CardLogic.init_hand_card_api --> Base.RobotLogic.api_deal_card_v2 --> api.deal_cards_v2


```

```mermaid
graph LR

RummyPoints.TimerLogic.excute_room_logic.ROOM_BASE_TIME_ACT_COMPARE --> RummyPoints.RoomLogic.hand_card_process
RummyPoints.RoomLogic.order_confirm_process --> RummyPoints.RoomLogic.hand_card_process
RummyPoints.RoomLogic.is_refresh_order --> RummyPoints.RoomLogic.hand_card_process
RummyPoints.RoomLogic.hand_card --> RummyPoints.RoomLogic.hand_card_process

```

## 这些名词是什么意思：3 绿及以上、2 绿、1 绿、0 绿有癞、0 绿无癞

## luckyPw

依据用户的 pr 值和用户的 pr 配置计算比率：

$$
y = max(1, min(0, \frac{dBegin - pr}{dBegin - dLimit}))
$$

比率的取值范围是 [0, 1]

在扶植配置中，dBegin 比 dLimit 大，用户的 pr 值未降低到 dBegin 之前，比率保持 0 值，用户的 pr 值降低到 dBegin 时开始扶植，降低到 dLimit 时达到比率上限 1

有了比率，基础权重和扶植系数就能变换出实际权重了

$$
实际权重 = 基础权重 * (1 + 扶植系数 * 比率)
$$

扶植系数配置为正数，比如 0.8，则实际权重最高能在基础权重的基础上增加 80%

扶植系数配置为负数，比如 -0.8，则实际权重最低能在基础权重的基础上减少 80%

## unluckyPw

原理同 luckyPw

依据用户的 pr 值和用户的 pr 配置计算比率：

$$
y = max(1, min(0, \frac{pr - uBegin}{uLimit - uBegin}))
$$

在抑制配置中，uLimit 是大于 uBegin 的，当用户的 pr 增长到 uBegin 时才开始抑制，增加到 uLimit 时达到抑制上限

$$
实际权重 = 基础权重 * (1 + 抑制系数 * 比率)
$$

## upPw

$$
实际权重 = 基础权重 * (1 + 系数)
$$

这个比较简单，比如系数为 0.8，则实际权重最高能在基础权重的基础上增加 80%

## getDealRate

### 针对 up 型用户

用 luckyPw 和扶植系数表对基础权重表做变换得到实际权重表

### 针对 down 型用户

用 unluckyPw 和抑制系数表对基础权重表做变换得到实际权重表

### 针对 novice 型用户

用 luckyPw 和新手权重表对基础权重表做变换得到实际权重表

### 针对 recharge 型用户

这个基于 up 型用户的实际权重表和自己的兜底权重表，好牌型用自己的兜底权重表兜底，坏牌型用自己的兜底权重表限高

自己的兜底权重表是用 upPw 和新充权重表对基础权重表做变换得到的

### 针对 normal 型用户

基础权重表就是实际权重表

### getUseRatePwType 用户分类的计算规则

```php
// AiRummyDealCardProfitToPlayer
$this->ratePwType = $this->getUseRatePwType();
```

| 用户分类 | 说明 |
| -------- | ---- |
| novice   | 新手 |
| recharge | 新充 |
| normal   | 普通 |
| down     | 抑制 |
| up       | 扶植 |

TODO

### getIsUpBuff 的复杂规则

TODO
