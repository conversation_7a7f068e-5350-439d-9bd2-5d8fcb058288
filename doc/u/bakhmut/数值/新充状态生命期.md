# 新充状态生命期

## 调用链

is_user_charge_buff --> get_user_tag_value --> USER_TAG_CHARGE_BUFF

refresh_user_charge_buff --> set_user_tag_value --> USER_TAG_CHARGE_BUFF

reset_charge_buff_data --> set_user_tag_value --> USER_TAG_CHARGE_BUFF

refresh_user_charge_buff --> reset_charge_buff_data

declear_user_stat_data --> update_user_charge_buff --> reset_charge_buff_data

## 分析 update_user_charge_buff

若 is_user_forbid_buff 则清掉新充状态

根据这局输赢更新新充状态的小计输钱流水和小计赢钱流水

$$
有效充值次数 = 充值次数 - 有效续充次数
$$

根据新充额度、有效充值次数、小计输钱流水和小计赢钱流水来清掉新充状态的机制，是被注掉的，即新充额度没有用了

更新新充局数

符合下面这个条件则清掉新充状态

$$
新充局数 > 220 * 0.9^{有效充值次数}
$$

即有效充值次数越多，新充状态维持的局数越少
