# 新手状态生命期

## 结论

赢钱多了或者玩的多了新手状态会失效

## 用户注册时

user_reg --> set_user_rookie --> set_user_tag_value --> USER_TAG_ROOKIE

## 用户注册时获取新手状态有限制条件

set_user_rookie --> is_user_forbid_buff

user_reg --> is_related_user

## 更新、删除新手状态的调用链

update_user_rookie --> del_user_rookie

refresh_user_charge_buff --> del_user_rookie

update_user_rookie --> set_user_rookie

Base.update_rookie_data --> update_user_rookie

AB.update_rookie_data --> update_user_rookie

## 分析 update_user_rookie

get_user_rookie_round 清零时会清除新手状态

$winAmount = allWinAmount - allBetAmount$

$winRate = winAmount / max(rookieParamWinRateMax, allBetAmount)$

若 winAmount 大于等于 rookieParamWinAmount2，新手状态失效

若 winAmount 大于等于 rookieParamWinAmount1，且 winRate 大于 rookieParamWinRate，新手状态失效

若 $allWinAmount + allBetAmount$ 大于等于 rookieParamAllAmount，新手状态失效

## rookie_param

```php
$rookie_param = self::get_common_config(Struct::GAME_COMMON_CONFIG_PROJ_PARAM, 'rookie');


    Struct::GAME_COMMON_CONFIG_PROJ_PARAM => [
        'default_name' => 'USER_',
        'country_code' => '91',
        'rookie' => [
            'win_amount_1' => 25000,
            'win_amount_2' => 50000,
            'win_rate' => 2,
            'all_amount' => 400000,
            'win_rate_max' => 10000,
        ],
```

这里的单位是点数，1 卢比 = 1000 点

## 分析 get_user_rookie_round

get_user_rookie_round --> get_user_rookie_round_key

update_user_rookie_round --> get_user_rookie_round_key

```php
    public static function get_user_rookie_round_key($uid)
    {
        return Struct::USER_ROOKIE_ROUND_KEY . ($uid % Mrds::get_div());
    }

    const USER_ROOKIE_ROUND_KEY = 'user_rookie_round_'; //新手开局次数
```

GameDataLogic.declear_user_stat_data --> update_user_rookie_round

rookie_round 初始值 --> GAME_COMMON_CONFIG_ROOKIE --> rookie_buff --> 100

意思就是初始 100 局，打一局减一，到零了就清掉新手状态
