## 是否执行大盘控

- 真人的有效充值次数 --> 确定真人杀率类型
- 玩法+底注+真人杀率类型 --> 唯一确定一个奖池
- 奖池配置+奖池状态+时间参数 --> 确定某个真人的大盘控概率
- 取最小值作为本次发牌的大盘控概率

| 字段                     | 含义                                 | 单位 |
| ------------------------ | ------------------------------------ | ---- |
| capitalType              | 真人杀率类型                         |      |
| todayWinAmount           | 奖池今日实时利润距离动态目标的缺口额 | 点   |
| waveRate                 |                                      |      |
| base                     | 底注                                 | 点   |
| todayDynamicTargetAmount | 奖池今日实时动态目标额               | 点   |
| todaySystemAmount        | 奖池今日实时利润                     | 点   |
| todayOldTargetAmount     | 奖池今日实时原始目标额               | 点   |
| winRate                  | 真人杀率类型关联的盈利率             |      |
| deal_rate                | 房间控初始                           |      |
| sys_killer_limit_rate    | 房间控上限                           |      |
| intervene_rate           | 房间控系数                           |      |
| changeRate               | 大盘控概率                           |      |
| tmpChangeRate            | 某个真人的大盘控概率                 |      |
| todayMinutes             | 今日实时分钟数                       |

目标是大概赚玩家输赢流水的 10% 左右

$$
奖池今日实时原始目标额 =  奖池今日实时真人输赢流水总额 * 真人杀率类型关联的盈利率
$$

今天赚的多了，就把动态目标额降一降

$$
timeRate = \frac{今日实时分钟数 * 0.96}{今日实时分钟数 + 300} + 0.2 \\
$$

$$
奖池今日实时动态目标额 = \\
\begin{cases}
奖池今日实时原始目标额 *  timeRate &\text{(if 奖池今日实时利润 > 奖池今日实时原始目标额)} \\
奖池今日实时原始目标额 &\text{(else)}\\
\end{cases} \\
$$

比如缺口 50%，房间控系数 2，那么 waveRate 就是 1，那么大盘控概率就是上限 33%
比如缺口 10%，房间控系数 2，那么 waveRate 就是 20%，那么大盘控概率就是上限 20%

$$
waveRate = 房间控系数 * \frac{奖池今日实时利润距离动态目标的缺口额}{\max(奖池今日实时动态目标额,1000000, base*1000)} \\
$$

$$
某个真人的大盘控概率 = \min(房间控上限, 房间控初始 + waveRate) \\
$$

$$
大盘控概率 = \min(各个真人的大盘控概率) \\
$$

## 百人场的

增益系数 buff_coefficient

$$
waveRate = 增益系数 * 房间控系数 * \frac{奖池今日实时利润距离动态目标的缺口额}{\max(奖池今日实时动态目标额,1000000, base*1000)} \\
$$

$$
某个真人的大盘控概率 = \min(0.333, waveRate) \\
$$

$$
大盘控概率 = \min(各个真人的大盘控概率) \\
$$

```php

    //获取系统杀率
    public function getSysKillerWaveRate($killConfigType): float
    {
        $todayWinAmount = $this->todayDynamicTargetAmount - $this->todaySystemAmount;
        //如果是负数 则说明挣钱的状态 不执行波动概率
        if ($todayWinAmount <= 0) {
            $rate = 0;
        } else {
            //$rate = $this->roomConfig['buff_coefficient'][$killConfigType] * $this->roomConfig['intervene_rate'] * ($todayWinAmount / max($this->todayDynamicTargetAmount, 5000000));
            $rate = $this->roomConfig['buff_coefficient'][$killConfigType] * $this->roomConfig['intervene_rate'] * ($todayWinAmount / max($this->todayDynamicTargetAmount, 50000));
        }
        return intval($rate * 1000) * 0.001;
    }

    //获取tp的杀率
    // 缺口率 = (动态目标 - 奖池余额) / max(动态目标, ₹1000, 底注的 1000 倍)
    // waveRate = max(0, 缺口率) * intervene_rate
    public function getTpSysKillerWaveRate($dymaicInfo): float
    {
        $todayWinAmount = $this->todayDynamicTargetAmount - $this->todaySystemAmount;
        //Tp基础杀率=max(配置的杀率上限,原有杀率+房间杀率系数*当日实时缺口额度/max(当日实时盈利目标,5000))
        if ($todayWinAmount <= 0) {
            $rate = 0;
        } else {
            //$rate = $dymaicInfo['intervene_rate'] * ($todayWinAmount / max($this->todayDynamicTargetAmount, 1000000, ($this->roomInfo['base'] * 1000)));
            $rate = $dymaicInfo['intervene_rate'] * ($todayWinAmount / max($this->todayDynamicTargetAmount, 10000, ($this->roomInfo['base'] * 10)));
        }
        return intval($rate * 1000) * 0.001;
    }

```

## winRate

```mermaid
graph TD
winRate --> getWinRate --> getPrWinRateOneV2 --> ai_new_win_rate_config_key_1
```

````php
// 奖池名，货币类型，用户杀率类型
$this->winRate = RedisOpt::getPrWinRateOneV2($this->jackPortName, $this->currency, $this->capitalType);
```

```json
{
  "100000_tp_jack_pool": {
    "1": "-0.5",
    "2": "0.01",
    "4": "0.13",
    "5": "0.05",
    "6": "0.075",
    "7": "0.075",
    "8": "0.045",
    "9": "0.075",
    "10": "0.075",
    "11": "0.135",
    "12": "0.09",
    "13": "0.11",
    "14": "0.11"
  },
  "100_jack_pool": {
    "1": "0",
    "2": "0.01",
    "4": "0.1",
    "5": "0.055",
    "6": "0.07",
    "7": "0.07",
    "8": "0.045",
    "9": "0.07",
    "10": "0.07",
    "11": "0.135",
    "12": "0.9",
    "13": "0.135",
    "14": "0.135"
  },
````

## 奖池今日实时利润距离动态目标的缺口额

```mermaid
graph TD
todayWinAmount --> getTodayWinAmount
```

$$
max(0, todayDynamicTargetAmount - todaySystemAmount)
$$

## 大盘控的发牌顺序

```mermaid
graph TD
A{{如果没有真人则随机顺序}} --> 返回
A --> B["第二大牌给 pr 最高的真人，抑制！"]
--> C["第一大牌给谁？扶植或回收！
<br>(1) pr 低于 0.3 且 pr 最低的真人
<br>(2) 某个机器人
<br>(3) pr 最低的真人
"]
--> D["确定两个关键角色后，剩下的随机打乱"]
--> 返回
```

## 非大盘控的发牌顺序

```mermaid
graph TD
A{{如果需要抑制真人则走抑制型}} --> 返回
A --> B["如果需要扶植真人则走扶植型"] --> 返回
B --> C["随机型"]
--> 返回
```

## 抑制型的发牌顺序

```mermaid
graph TD
B["第二大牌给 pr 最高的真人，抑制！"]
--> C["第一大牌给谁？扶植或回收！
<br>(1) 需要被扶植且 pr 最低的真人
<br>(2) 某个机器人
<br>(3) pr 最低的真人
"]
--> D["确定两个关键角色后，剩下的随机打乱"]
--> 返回
```

## 扶植型的发牌顺序

```mermaid
graph TD
B["第一大牌给需要被扶植且 pr 最低的真人，扶植！"]
--> D["确定这个关键角色后，剩下的随机打乱"]
--> 返回
```

## AiTeenPattyPrControlDealCardV2 代码流程

```mermaid
graph TD
subgraph 主流程
initAvgSequence
--> getPrConfig
--> getDispositionConfig
--> setPlayerControlInfo
--> getChangeCardRes
--> sortPlayerList
--> getDealCardList
--> dealCardRun
end

setPlayerControlInfo["setPlayerControlInfo<small>
estimatedList
sortAiList
prInfoList
normalPlayerPrList
downPlayerPrList
upPlayerPrList
slowerPlayerPrList
allPlayerPrList
</small>"]

subgraph sortPlayerList
isChangeCard -- true --> sortPlayerListBySysKiller
isChangeCard -- false --> sortPlayerListByControlType
end

subgraph getDealCardList
getUserDealCardsList --> sortDealCardsListByScore
end

subgraph dealCardRun
assignCardsToUser --> cardBaseWaveRate
end

subgraph assignCardsToUser
maxCardKiller
end

```
