# 庄---AI 净盈利、比---AI 净盈利占比总盘净流水比率、注---净流水之和，去掉服务费和机器人数据，确认此三项释义是否准确

<br>

## 庄、比、注 定义

- 庄 ： 系统总盈亏，数额大小体现系统的盈利能力强弱
- 注 ： 玩家赢输流水总和(去掉机器人数据、佣金) ； 公式 = （总赢 - 佣金）+ 总输
- 比 ： 庄 / 注 \* 100

<br>

### 举个栗子

假设佣金 10%

庄 = 1000

注 = 10000

比 = 10%

#### 🌰

玩家本金 10 赢 10

机器人本金 10 输 10

<br>

玩家 + 10 - (10 \* 0.1) = +9

系统 - 10 + (10 \* 0.1) = -9

庄 = 991 ；注 = 10009 ； 比 ：991 / 10009 \* 100 = 9.9%

#### 🌰🌰

<p>玩家1本金 10   赢20
<p>玩家2本金 10   输10
<p>机器人本金 10   输10

<br>

<p> 玩家 1   + 20 - (20 * 0.1)  = +18
<p> 玩家 2   - 10
<p> 系统     - 10 + (20 * 0.1)  = -8
<p> 庄 = 992 ；注 = 10028 ； 比 ：992 / 10028 * 100 = 9.89%

<br><br><br><br>

# 新充 1，新充 2，新充 3...为新充时间控制，比如新充 1 隔 14 小时再充值算新充 2，具体逻辑需要服务端确认

| 新充类型  |
| --------- |
| 新手      |
| 非新未充  |
| 新充 1    |
| 新充 2... |

## 有效充值次数划分用户类型

$$
有效充值次数 = 充值次数 - 有效续充次数
$$

比如 新充 N ： 有效充值 N 次

## 新手

### 生效条件

- 用户注册

### 失效条件

- 玩家游戏回合数达到 100

$$
完胜率 = (总赢额 - 总下注额) / max （总下注额 ， 10000）
$$

- 总赢额 - 总下注额 > 50000
- 总赢额 - 总下注额 > 25000 && 总赢额 - 总下注额 < 50000 && 完胜率 > 2
- 总赢额 + 总下注额 > 400000
- 设置新充 buff 和续充时

注：总赢额--玩家赢钱流水
<br>

## 新充 buff

### 生效条件

- 充值时如果玩家没有新充 buff （设置新充 buff， 重置新充 buff 局数，删除新手 buff）
-

### 失效条件

- 打了 forbid_buff 禁止标签的玩家不会增加有效续充次数，不会再获得新充 buff，已有的新充 buff 会被删除
- 用户总充值金额 > 100 万卢比 ，不增加有效续充次数，不再获得新充 buff
- 有效充值次数达到 20 后，不再增加有效续充次数，不再获得新充 buff
- 第一次充值且数额为 50 卢比，且账户余额超过 80 卢比，不获得新充 buff
- 符合此条件，新充 buff 清除
  $$
  新充局数 > 220 * 0.9^{有效充值次数}
  $$

## 充值时算作续充的限制条件

- 有新充 buff 且距离上次充值时间在 14 小时以内，递增有效续充次数
- 有新充 buff 且距离上次充值时间在 14 小时以外。<font color="yellow">（把新充额度，新充输赢流水清零（不生效，使用这三个字段的代码被注释））</font>

<br><br><br>

# Rummy 真人 ---确认现在代码是否有用到这个逻辑

- 根据权重给用户随机发牌
- 影响牌型权重表的因素：玩家的 pr 值，pr 配置表，用户分类，13card 发牌配置表
- 粗略理解就是 pr 值低的发好牌，pr 值高的发差牌，有新充 buff 的发好牌，新手发好牌

## 13card 发牌配置

| 牌型       | 基础权重 | 扶植系数 | 抑制系数 | 新手权重 | 新充权重 |
| ---------- | -------- | -------- | -------- | -------- | -------- |
| 牌型       | 基础权重 | 扶植系数 | 抑制系数 | 新手权重 | 新充权重 |
| 3 绿及以上 | 45       | 0.8      | -1       | 1        | 0.4      |
| 2 绿       | 183      | 0.5      | -0.6     | 0.4      | 0.3      |
| 1 绿       | 140      | -1       | 0.8      | 0        | -0.5     |
| 0 绿有癞   | 451      | 0        | -0.45    | 0        | 0        |
| 0 绿无癞   | 182      | -1       | 1.1      | -0.75    | -0.5     |

<br>

## Rummy 真人发牌用户加权分类的规则

| 用户分类 | 说明 |
| -------- | ---- |
| normal   | 普通 |
| up       | 扶植 |
| down     | 抑制 |
| novice   | 新手 |
| recharge | 新充 |

<br>

分类规则

- normal
  - 普通用户 (默认)
- novice
  - 新手
- down
  - Pr >= 限制起点
- recharge
  - 新充用户 && 是扶植类型用户
  - Pr <= 扶植起点 && 新充用户 && 是扶植类型用户
- up
  - Pr <= 扶植起点 && 不是新充用户 && 不是扶植类型用户

### 扶植类型用户

充值次数 = 获取用户充值次数 redis recharge*all_cnt key userId
续充次数 = 获取用户续充次数 redis user_tag_list_userID key charge_buff_renew
。。。
用户的 buff 新充额度 = redis user_tag_list_userID key charge_buff_count
用户的净赢流水 = redis user_tag_list_userID key charge_buff_profit
用户的净输流水 = redis user_tag_list_userID key charge_buff_loss
用户的总的净赢流水 = redis user_tag_list_userID key profit*（key）
用户的总的净输流水 = redis user*tag_list_userID key loss*（key）

<br><br><br>

## 各个用户分类计算牌型实际权重表的公式和相关配置内容（通过定义函数来简化表述）

<br>

#### 针对 up 型用户

用 luckyPw 和扶植系数表对基础权重表做变换得到实际权重表

#### 针对 down 型用户

用 unluckyPw 和抑制系数表对基础权重表做变换得到实际权重表

#### 针对 novice 型用户

用 upPw 和新手权重表对基础权重表做变换得到实际权重表

#### 针对 recharge 型用户

基于 up 型用户，但比 up 型用户要好，好牌型的权重有兜底，坏牌型的权重有限高

兜底和限高的权重表是用 upPw 和新充权重表对基础权重表做变换得到的

#### 针对 normal 型用户

基础权重表就是实际权重表

<br><br><br>

## 计算规则实现

### luckyPw

依据用户的 pr 值和用户的 pr 配置计算比率：

$$
y = min(1, max(0, \frac{dBegin - pr}{dBegin - dLimit}))
$$

比率的取值范围是 [0, 1]

在扶植配置中，dBegin 比 dLimit 大，用户的 pr 值未降低到 dBegin 之前，比率保持 0 值，用户的 pr 值降低到 dBegin 时开始扶植，降低到 dLimit 时达到比率上限 1

有了比率，基础权重和扶植系数就能变换出实际权重了

$$
实际权重 = 基础权重 * (1 + 扶植系数 * 比率)
$$

扶植系数配置为正数，比如 0.8，则实际权重最高能在基础权重的基础上增加 80%

扶植系数配置为负数，比如 -0.8，则实际权重最低能在基础权重的基础上减少 80%

### unluckyPw

原理同 luckyPw

依据用户的 pr 值和用户的 pr 配置计算比率：

$$
y = min(1, max(0, \frac{pr - uBegin}{uLimit - uBegin}))
$$

在抑制配置中，uLimit 是大于 uBegin 的，当用户的 pr 增长到 uBegin 时才开始抑制，增加到 uLimit 时达到抑制上限

$$
实际权重 = 基础权重 * (1 + 抑制系数 * 比率)
$$

### upPw

$$
实际权重 = 基础权重 * (1 + 系数)
$$

这个比较简单，比如系数为 0.8，则实际权重最高能在基础权重的基础上增加 80%

<br><br><br><br>

# Rummy AI ---房间控系数的逻辑确认

- 房间控系数用于计算杀率
- 根据杀率计算 ai 发牌概率，随机（小白、普通、高手）

intervene_rate 是房间控系数

```json
{
  "10000_jack_pool": {
    "a_control_rate": "",
    "a_control_coefficient": "",
    "z_control_coefficient": "",
    "finish_rate": "0",
    "intervene_rate": "2"
  },
  "1000_jack_pool": {
    "a_control_rate": "",
    "a_control_coefficient": "",
    "z_control_coefficient": "",
    "finish_rate": "0.1",
    "intervene_rate": "2"
  },
```

| 字段                      | 含义                                 | 单位 |
| ------------------------- | ------------------------------------ | ---- |
| todayLogAmount            | 奖池今日实时真人输赢流水总额         | 点   |
| capitalType               | 真人杀率类型                         |      |
| winRate                   | 真人杀率类型关联的盈利率             |      |
| todayOldTargetAmount      | 奖池今日实时原始目标额               | 点   |
| todaySystemAmount         | 奖池今日实时利润                     | 点   |
| todayMinutes              | 今日实时分钟数                       |      |
| todayDynamicTargetAmount  | 奖池今日实时动态目标额               | 点   |
| todayWinAmount            | 奖池今日实时利润距离动态目标的缺口额 | 点   |
| intervene_rate            | 房间控系数                           |      |
| getRummySysKillerWaveRate | 小白和普通的百分比乘以这个比率       |      |

目标是大概赚玩家输赢流水的 10% 左右

$$
奖池今日实时原始目标额 =  奖池今日实时真人输赢流水总额 * 真人杀率类型关联的盈利率
$$

今天赚的多了，就把动态目标额降一降

$$
timeRate = \frac{今日实时分钟数 * 0.96}{今日实时分钟数 + 300} + 0.2
$$

if 奖池今日实时利润 > 奖池今日实时原始目标额

$$
奖池今日实时动态目标额 = 奖池今日实时原始目标额 *  timeRate
$$

else

$$
奖池今日实时动态目标额 = 奖池今日实时原始目标额
$$

比如缺口 50%，房间控系数 2，那么 waveRate 就是 1，那么大盘控概率就是上限 33%
比如缺口 10%，房间控系数 2，那么 waveRate 就是 20%，那么大盘控概率就是上限 20%

$$
waveRate = 1 - 房间控系数 * \frac{奖池今日实时利润距离动态目标的缺口额}{\max(奖池今日实时动态目标额,1000000, base*1000)} \\

waveRate = min(1, max(0, waveRate))
$$

waveRate 越小机器人越厉害，负值相当于零

Rummy 各个奖池的小白、普通、高手的百分比配置

profit_ai_room_plan_1_1

```json
{
  "10000_jack_pool": {
    "1": "20",
    "2": "30",
    "3": "50"
  },
  "1000_jack_pool": {
    "1": "30",
    "2": "30",
    "3": "40"
  },
  "100_jack_pool": {
    "1": "30",
    "2": "35",
    "3": "35"
  },
```

这个百分比配置会被 waveRate 调整

```php
        //小白
        $newRoomPlanInfo[1] = $roomPlanInfo[1] * 0.01 * $this->waveRate;
        //普通
        $newRoomPlanInfo[2] = $roomPlanInfo[2] * 0.01 * $this->waveRate;
        //高手
        $newRoomPlanInfo[3] = 1 - $newRoomPlanInfo[1] - $newRoomPlanInfo[2];
```

小白、普通、高手的牌型权重表配置

ai_level --> ai_level_13

```json
{
  "group_name_3lv": {
    "1": "45",
    "2": "100",
    "3": "200"
  },
  "group_name_2lv": {
    "1": "200",
    "2": "330",
    "3": "700"
  },
  "group_name_1lv": {
    "1": "150",
    "2": "95",
    "3": "0"
  },
  "group_name_nolv_yeslai": {
    "1": "470",
    "2": "420",
    "3": "100"
  },
  "group_name_nolv_nolai": {
    "1": "135",
    "2": "0",
    "3": "0"
  }
}
```

## 计算 AI 发牌概率

```json
  "50_jack_pool": "{"1":"20","2":"35","3":"35"}",
```

- 小白 = 20 \* 0.01 \* 杀率
- 普通 = 35 \* 0.01 \* 杀率
- 高手 = 1 - 小白 - 普通

<br>
<p>系统赚钱情况下，rate = 1 ，代表AI是高手的机率低，对玩家相对友好
<p>系统亏钱情况下，rate < 1 , 代表AI时高手的机率高，对玩家不友好

<br><br><br><br>

# TP AI 参数房间控系数（小数）、房间控上限、房间控系数，作用是调节有缺口后大盘杀的上升幅度，此 3 处具体控制逻辑看代码

- 房间控系数用于计算杀率
- 房间控系数（小数）、房间控上限、房间控系数 影响 AI 换牌概率

<br><br>

## TP AI 房间控初始、上限、系数的相关公式和相关配置内容

| 玩法                  | 房间控初始（小数） | 房间控上限 | 房间控系数 |
| --------------------- | ------------------ | ---------- | ---------- |
| Teen Party 底注：0.01 | 0.02               | 0.33       | 0.5        |
| Teen Party 底注：0.05 | 0.02               | 0.33       | 0.5        |

<br>

| 字段                   | 含义             |
| ---------------------- | ---------------- |
| intervene_rate         | 房间控系数       |
| getTpSysKillerWaveRate | 获取 AI 换牌概率 |

<br><br>

## Tp 杀率计算

- 根据今日系统利润缺口金额计算
  <br><br><br>

if 今日缺口金额 <= 0

$$
rate = 0
$$

if 今日缺口金额 > 0

$$
rate = 房间控系数 *  (\frac{今日缺口数据}{max（今日营收目标 ， 1000000 ，底注 * 1000）})
$$

## AI 换牌概率

$$
min ( 房间控上限 ,（ 房间控初始 + rate ） )
$$

<br>
<p>杀率控制在 房间控初始--房间控上限 之间 , 随着rate越大 ，换牌机率越大
<p>系统赚钱情况下，rate = 0 ，代表AI的换牌机率低，对玩家相对友好
<p>系统亏钱情况下，rate > 0 , 代表AI时换牌机率高，对玩家不友好

<br><br><br>

# 百人场 AI 普通房/高手/大户、Buff 系数、强杀下注额、初始概率、容忍系数、浮动概率 确认影响逻辑

| 字段       | 渲染                   |
| ---------- | ---------------------- |
| 普通房     | room_type_killer[0]    |
| Buff 系数  | buff_coefficient[0]    |
| 高手 Buff  | room_type_killer[1]    |
| Buff 系数  | buff_coefficient[1]    |
| 大户 Buff  | room_type_killer[2]    |
| Buff 系数  | buff_coefficient[2]    |
| 强杀下注额 | win_score_killer_base  |
| 房间控系数 | intervene_rate         |
| 初始概率   | initial_rate【未使用】 |
| 容忍系数   | tolerance_factor       |
| 浮动概率   | float_rate             |

<br>

```json
{
	"1000_tp_war_jack_pool": "{"
	room_type_killer ":["
	0.02 ","
	0.1 ","
	0.15 "],"
	buff_coefficient ":["
	20 ","
	40 ","
	60 "],"
	win_score_killer_base ":"
	3000 ","
	intervene_rate ":"
	0.22 ","
	initial_rate ":"
	0.0 ","
	tolerance_factor ":"
	0.5 ","
	float_rate ":"
	0.33 "}"
}
```

<br>

## 批量发牌

```mermaid
graph TD
AiHundredDealCard.__construct
```

```

if ($this->cls == Common::GAME_PLAY_TYPE_LUCKY_LOTO) { //jackpot loto
            $sysKillerWaveRateInfo = Common::getHundredLuckyLotoSysKillerWaveRate($this->betUidList, $this->roomType, $this->getRoomKillConfigType());
        } else {
            $sysKillerWaveRateInfo = Common::getHundredSysKillerWaveRate($this->betUidList, $this->roomType, $this->getRoomKillConfigType());
        }

今日波动的系统强杀概率  ysKillerWaveRate = $sysKillerWaveRateInfo['waveRate'];
系统近日缺口额 sysWinAmount = $sysKillerWaveRateInfo['sysWinAmount'];
系统此时的盈利目标 sysTargetAmount = $sysKillerWaveRateInfo['sysTargetAmount'];

```

### 百人根据房间下注人的 buff 属性计算出最低的波动杀率

```mermaid
graph TD
AiHundredDealCard.__construct --> Common.getHundredLuckyLotoSysKillerWaveRate
```

```php
获取大盘的缺口金额

if 波动率 >= 1
  取波动最小的
else
  取波动最大的
```

#### 获取 lucky loto 扶植或者强杀的概率

```mermaid
graph TD
Common.getHundredLuckyLotoSysKillerWaveRate --> AiOverallWinRateV2.getLuckyLotoSysKillerWaveRate --> AiOverallWinRateV2.getRoomConfig
```

今日超额利润 todayWinAmount = 今日系统盈利 todaySystemAmount - 今日系统动态目标额 todayDynamicTargetAmount

$$
max （ 0.05 ， ( 1 + 房间控系数 * (\frac{今日超额利润}{max（今日系统动态目标额 ， 5000000 )}) ))
$$

<font color="red">
- 房间控系数 。系数越大，杀率越高
</font>

### 百人根据房间下注人的 buff 属性计算出最低的波动杀率

```mermaid
graph TD

AiHundredDealCard.__construct --> common.getHundredSysKillerWaveRate
```

```
获取非高手和大户房 读取普通房的杀率
```

```php
获取系统杀率 tmpWaveRate
默认 波动率 waveRate = 0
if 波动率 = 0 || 波动率 <= 系统杀率
  波动率 waveRate = 系统杀率 tmpWaveRate
  今日缺口金额 sysWinAmount = 今日波动目标额 todayDynamicTargetAmount - 今日利润 todaySystemAmount;
  营收目标金额 sysTargetAmount
```

#### 获取 buff 类型

```mermaid
graph TD

AiHundredDealCard --> getRoomKillConfigType --> Common::getSysKillerTypeByBuffer --> RedisOpt::getPlayerNowUseBuff

```

```php
    //百人场房间强杀类型枚举
    const ROOM_UNION_TYPE_NORMAL = 0; //普通
    const ROOM_UNION_TYPE_WIN = 1; //高手
    const ROOM_UNION_TYPE_RICH = 2; //大户
    const ROOM_UNION_TYPE_BUFF = 3; //buff屋

 //################用户buff
    const USER_TAG_CHARGE_BUFF = 'charge_buff'; //充值buff
    const USER_TAG_ROOKIE = 'rookie'; //是否是新手
    const USER_TAG_WIN_BUFF = 'win_buff'; //高手buff
    const USER_TAG_RICH_BUFF = 'rich_buff'; //小黑屋buff
    const USER_TAG_NORMAL_BUFF = 'normal_buff'; //正常状态 无任何buff
    const USER_TAG_TP_DISCLOSE_KILLER = 'tp_disclose_killer'; //tp透杀


    //根据buff类型获取强杀类型
    public static function getSysKillerTypeByBuffer($buff)
    {
        $list = [
                //新充 新手  正常 读取正常房间的杀率
            Common::USER_TAG_CHARGE_BUFF => Common::ROOM_UNION_TYPE_BUFF,
            Common::USER_TAG_ROOKIE => Common::ROOM_UNION_TYPE_BUFF,
                //普通房
            Common::USER_TAG_NORMAL_BUFF => Common::ROOM_UNION_TYPE_NORMAL,

                //高手房 大户房  读取原有的 小黑屋 小灰屋的杀率
            Common::USER_TAG_WIN_BUFF => Common::ROOM_UNION_TYPE_WIN,
            Common::USER_TAG_RICH_BUFF => Common::ROOM_UNION_TYPE_RICH,
        ];
        if (isset($list[$buff])) {
            return $list[$buff];
        } else {
            return Common::ROOM_UNION_TYPE_NORMAL;
        }
    }


```

<br><br>

| buff 类型   | buff 状态 | buff 类型名称 | 代码常量定义         |
| ----------- | --------- | ------------- | -------------------- |
| charge_buff | 3         | buff 屋       | USER_TAG_CHARGE_BUFF |
| rookie      | 3         | buff 屋       | USER_TAG_ROOKIE      |
| win_buff    | 1         | 高手          | USER_TAG_WIN_BUFF    |
| rich_buff   | 2         | 大户          | USER_TAG_RICH_BUFF   |
| normal_buff | 0         | 普通          | USER_TAG_NORMAL_BUFF |

<br>

```php

根据玩家的buff类型 获取当前使用的buff，buff关系优先级 大户>高手>普通

```

- 大户 buff ； user*tag_list*玩家 ID --> Common::USER*TAG_IS_RICH_BUFF . '*' . $cls
- 高手 buff ； user*tag_list*玩家 ID --> Common::USER*TAG_IS_WIN_BUFF . '*' . $cls
- 新充 buff ； user*tag_list*玩家 ID --> Common::USER_TAG_CHARGE_BUFF
- 新手 buff ； user*tag_list*玩家 ID --> Common::USER_TAG_ROOKIE
- 默认普通 buff ；Common::USER_TAG_NORMAL_BUFF

#### 大户 buff / 高手 buff 计算

| 字短          | 描述                       |
| ------------- | -------------------------- |
| userStatBet   | 玩家历史总下注             |
| userStatWin   | 玩家历史总赢钱（包含本金） |
| userStatRound | 玩家历史总局数             |

```php
配置参数
'rich_fee' => 5000000,
'rich' => [
  ['min' => 0, 'max' => 50, 'rate_min' => 5.0, 'rate_max' => -1],
  ['min' => 50, 'max' => 300, 'rate_min' => 2.0, 'rate_max' => -1],
  ['min' => 300, 'max' => 500, 'rate_min' => 0.5, 'rate_max' => -1],
  ['min' => 500, 'max' => -1, 'rate_min' => 0.1, 'rate_max' => -1],
],
'win' => [
  ['min' => 0, 'max' => 50, 'rate_min' => 1.5, 'rate_max' => 5.0],
  ['min' => 50, 'max' => 300, 'rate_min' => 0.5, 'rate_max' => 2.0],
  ['min' => 300, 'max' => 500, 'rate_min' => 0.1, 'rate_max' => -1],
  ['min' => 500, 'max' => -1, 'rate_min' => 0.0001, 'rate_max' => -1],
],
```

```
1、  userStatWin - userStatBet >= rich_fee
	buff = rich_buff（大户）


winRate =（ userStatWin - userStatBet ） / userStatBet
2、userStatRound 在配置参数rich的min max之间 && winRate 在rich的rate_min rate_max之间
	buff = rich_buff（大户）


3、userStatRound 在配置参数win的min max之间 && winRate在win的rate_min rate_max之间
	buff = win_buff（高手）


4、是新手 || 是新充
	buff = normal（普通）


注：&& = 并且 ； || = 或者
注：该玩法下玩家历史总赢额 （包含本金）
```

### 获取系统杀率

```mermaid
graph TD

common.getHundredSysKillerWaveRate --> AiOverallWinRateV2.getSysKillerWaveRate
--> roomConfig
```

$$
今日缺口金额 = 今日营收目标 - 今日利润
$$

if 今日缺口额 < 0

$$
rate = 0
$$

else

$$
buff系数 * 房间控系数 * (\frac{今日缺口数据}{max（今日营收目标 ， 5000000 )})
$$

<br>

<font color="red">
涉及面边配置字段

- buff 系数 根据形参带入【0，1，2】(非 1，2 为 0)
- 房间控系数

</font>

<br>

## 获取系统强杀结果

```mermaid
graph TD
AiHundredDealCard --> getSysKillerRes
```

```
根据房间强杀类型 killerType  获取原始强杀概率 room_type_killer

本局波动的系统强杀概率 = （标题2计算得出）
```

$$
系统强杀概率 = min （ 0.333 ，（系统原始强杀率 + 本局波动的系统强杀概率 ））
$$

$$
强杀结果 = 随机（系统强杀概率 * 1000 , 1000 - 系统强杀概率 * 1000 ）
$$

<br>
<font color="red">
涉及面边配置字段

- room_type_killer [普通房 ,高手 buff，大户 buff]

</font>

## 根据下注玩家贫富程度强杀(劫富)

```mermaid
graph TD
AiHundredDealCard --> getWinPointKillerRes
```

<br>

- 玩家在该玩法内历史总赢钱 || 玩家在该玩法内历史总下注 = 0 。 不执行

- 如果有效下注 <= 复仇杀配置（强杀下注额 win_score_killer_base） \* 1000 。 不执行

<br><br><br>

$$
  玩家有效下注 = max（本局下注, 玩家在该玩法内历史总赢钱-玩家在该玩法内历史总下注+本局下注）
$$

if 玩家在该玩法内历史总下注 > 玩家在该玩法内历史总赢钱

$$
新复仇杀概率 scorePoint = 系统强杀概率 sysKillerRate
$$

else

$$
新复仇杀概率  = 系统强杀概率 sysKillerRate +  min（1，(\frac{\frac{本局下注}{玩家在该玩法内历史总赢钱 - 玩家在该玩法内历史总下注}}{复仇杀容忍系数(tolerance_factor)}) * 复仇杀浮动概率(float_rate))
$$

<br><br><br>

$$
新复仇强杀结果 = 随机（新复仇杀概率 * 1000 , 1000 - 新复仇杀概率 * 1000 ）
$$

<br>

<font color="red">

涉及面边配置字段

- tolerance_factor 容忍系数
- float_rate 浮动概率
- win_score_killer_base 强杀下注额

</font>

<br><br>

## 正常发牌

```mermaid
graph TD

AiHundredDealCard.dealCardByNorma --> AiHundredDealCard.sysKillerRes

```

```

如果 系统强杀 = false && 复仇杀 = false ，
通过  getHunderGameIsSysKillerByLotteryResult 计算 系统是否盈利 .
如果亏钱 ， 强制执行强杀

如果 系统强杀 或者 复仇杀
判断 系统赔的钱 小于 系统设置最大输钱额 或者 系统赔的钱 = 0
正常发牌


```

## 根据开奖结果计算是否需要执行大盘杀

```mermaid
graph TD

AiHundredDealCard.dealCardByNorma --> Common.getHunderGameIsSysKillerByLotteryResult
```

系统赚钱情况下，不强制大盘杀；系统亏欠情况强制大盘杀。

- 如果系统赔的钱 = 0 或者 用户这局总下注的钱 = 0 ； 不重置系统强杀结果

if 系统要赔的钱 sysPayAmount = 0
赔钱后的目标缺口金额 = 系统赚的钱 sysWinAmount - 用户这局总下注的钱 playerBetAmount
else
赔钱后的目标缺口金额 = 系统赚的钱 sysWinAmount + 系统要赔的钱 sysPayAmount

<br><br>
if 赔钱后的目标缺口金额 sysPayOutWinAmount <= 0 或者 系统的盈利目标 <= 0 ; 不重置系统强杀结果

else

$$
rate = min(0.333, buff系数 * 0.15 * (\frac{赔钱后的目标缺口金额}{max(系统的盈利目标, 10000000))}) * max(1, 系统要赔的钱 / 这局总下注的钱 - 1)
$$

- rate <= 0 ; 不重置系统强杀结果
- rate > 1 ; 重置系统强杀结果
- 否则 随机 （ rate _ 1000 ， 1000 - rate _ 1000 ）
