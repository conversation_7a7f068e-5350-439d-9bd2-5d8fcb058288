
流程
- 用户根据房间类型匹配房间   LobbyLogic/function join
- 进入房间  RoomLogic/function enter_room
- 发牌  RoomLogic/function deal
- 开始下注 RoomLogic/function start_bet
- 给用户发数据 GroupLogic/function bet_batch   （获取所有用户数据，判断是否是机器人/已离开房间的用户 ， 如果是 跳过 不是 发送数据）
- 同上 GroupLogic/function bet
- 同上 停止 GroupLogic/function stop_bet
- 发送信息给用户（计算金额）GroupLogic/function show
- 发送信息给用户 GroupLogic/function reset_room
- 单独通知离开房间的用户 GroupLogic/function leave_single
-  LobbyLogic/function home




