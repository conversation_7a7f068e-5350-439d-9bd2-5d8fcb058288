wm\games\AB.RoomLogic.join_process --> wm\games\AB.RoomLogic.rand_room

1、判断是否已经在该玩法里
wm\games\Base.RoomLogic.in_same_cls_room

2、是否在 buff 房列表
get_room_user_buff_list

update_room_group.room_group_update_room

<br>

RoomGameEvents.onWorkerStart --> LogicMgr::init_rooms() --〉wm\games\Base\RoomLogic.gen_room_type_id

# 生成对应房间类型的房间 ID

检测是否有空闲 ID

- room_type_free_incr_id\*

```json
["1", "2"]
```

获取(房间类型增量 ID) room_type_incr_id key cls_rutype

```json
{
  "10_0": "2",
  "14_0": "2",
  "13_0": "4",
  "16_0": "1"
}
```

```
$common = 'room' => [
      'min_fee' => 100,
      "max_bet_fee" => 20000000,
      'id_begin_' . Struct::ROOM_UNION_TYPE_NORMAL => 1800000,
      'id_begin_' . Struct::ROOM_UNION_TYPE_WIN => 1801000,
      'id_begin_' . Struct::ROOM_UNION_TYPE_RICH => 1802000,
      'id_begin_' . Struct::ROOM_UNION_TYPE_BUFF => 1803000,
      'count_max_' . Struct::ROOM_UNION_TYPE_NORMAL => 1,
      'count_max_' . Struct::ROOM_UNION_TYPE_WIN => 0,
      'count_max_' . Struct::ROOM_UNION_TYPE_RICH => 0,
      'count_max_' . Struct::ROOM_UNION_TYPE_BUFF => 100,
      'count_' . Struct::ROOM_UNION_TYPE_NORMAL => 1,
      'count_' . Struct::ROOM_UNION_TYPE_WIN => 0,
      'count_' . Struct::ROOM_UNION_TYPE_RICH => 0,
      'count_' . Struct::ROOM_UNION_TYPE_BUFF => 0,
      'rich_fee' => 5000000
  ]
```

cls_rutype >= $common 不创建

# room_group:{$room_type}

room_group_select_room_ids_by_user_max_at_game_base
room_group_select_room_ids_by_user_max_at_game_ab
room_group_update_room
room_group_del_room

update_room_group --> room_group_update_room

# room_type_incr_id 核销房间

ROOM_TYPE_INCR_ID_KEY
get_room_type_incr_id_key

无调用

# opt_config

# room_type_config 房间类型配置

```json
{
	"16_1_1_1000": "{"
	name ":"
	Spacex ","
	max ":1,"
	currency ":1,"
	rate ":1,"
	base ":1000,"
	set ":1,"
	max_card ":1,"
	wait_time ":0,"
	match_time ":0,"
	base_time ":10,"
	protect_time ":1,"
	compare_time ":3,"
	send_card_time ":2,"
	declear_time ":1,"
	reset_time ":2,"
	ai_state ":1,"
	charges ":10,"
	cls ":16,"
	ai_min ":1,"
	ai_max ":3,"
	is_hot ":0}"
}
```

# join_process 加入房间

## 计算用户 type 普通 buff

# "room_data:{$room_id}"

get_room_data --> key_room_data
set_room_data --> key_room_data
del_room_data --> key_room_data
lock_room_data --> key_room_data

update_room --> set_room_data

join_room_process --> update_room
init_room --> update_room
deal --> update_room
show --> update_room
reset_process --> update_room
leave_process --> update_room
bet_process --> update_room
start_bet -> update_room
stop_bet --> update_room
robot_batch_bet --> update_room
deal_cards_err --> update_room

del_room --> del_room_data

dismiss_room --> del_room

# 解散房间 dismiss_room

--> del_room_user

- 删除 room_data:{roomId} --> user_data
  - 从财富排行榜删除玩家 del_room_rich_rank (重置 room_data:{roomId} -- rich_rank )
  - 从上座列表删除玩家 del_room_rand (重置 room_data:{roomId} -- rand_uid )
  - 从当局活跃玩家中删除 del_room_active_uid (重置 room_data:{roomId} -- active_uid )
  - 非 AI 删除用户房间 ID room_user\*
  - 更新玩法人数 reduce_room_type_playing -- room_type_palying{cls}
  - 删除 room_data：{roomId} user_data[uid]

--> del_room

- 匹配列表中删除 room*group*{room_type}
- 删除房间对应类型数据 del_room_group_type rds:room_group_type-->room_type
- 删除定时任务
- 删除房间公共数据 del*room_common_data rds：room_common_data*{roomId}
- 删除房间数据 del_room_data rds:room_data:{roomId}

--> add_room_type_free_incr_id (释放 buff 房间 id)

- room*type_free_incr_id*{clas_type(1 普通/3buff)} roomId - count_max_type(1 普通/3buff) add

--> del_room_user_buff_list (释放玩家 buff 房间 id)

- 删除 rds：room*user_buff_list*{room*type}*{user*type}*

# 删除房间

wm\games\Base\RoomLogic.del_room

room_group_type 房间 ID

```json
{
  "1500001": "13_1_1_1000",
  "1800001": "16_1_1_1000",
  "2200001": "20_1_1_1000",
  "1300001": "11_1_1_1000",
  "1200001": "10_1_1_1000",
  "1900001": "17_1_1_1000",
  "1600001": "14_1_1_1000"
}
```

- 如果 room_group_type 房间 ID 不存在

  - 删除 room_group:{room_type} 中的 roomID
  - 删除 room_group_type 对应 roomID 的类型数据

- 如果 room_data:{roomID} 不为空

  - 删除定时器

- 删除 room*common_data*{roomId}
- 删除房间数据 room_data:{roomId}

# 删除定时器 room*data*{roomId} -- base_data

- del_room_timeline
- 删除定时任务 del_room_timer_event
  - 获取 room_data:{roomID} 玩法名称
  - 获取定时器堆的名字 get_room_base_data_type （room_timer_heap / room_hrtimer_heap）
