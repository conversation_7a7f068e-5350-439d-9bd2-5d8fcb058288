



<p>真人发牌 dealCardsRun 
<p>发牌概率 getDealRate 根据判断ratePwType值计算执行概率
<p>发牌牌组名称计算 get_rand
<p>根据牌组进行发牌 getDealCardsByGroup (逻辑待跟进)
<p>获取最终发牌名称结果 getDealCardGroupName
<p>获取剩余牌池 getPool




<br><br><br><br><br><br>



```mermaid
graph LR

AiRummyDealCardProfitToPlayer --> dealCardsRun 

AiRummyDealCardProfitToPlayer --> getDealRate

get_rand

AiRummyDealCardProfit --> getDealCardsByGroup

AiRummyDealCardProfit --> getDealCardGroupName

AiRummyDealCardProfit --> getPool

```



<br><br><br><br><br><br><br><br>




ratePwType 

# ratePwType值定义


  ## normal 
  - <b>定义规则</b> ：普通用户 默认
  - <b>计算执行概率</b> ：
    -   默认基础权重 redis  key  z_deal_13card_rate_config_key
  
  ```
    group_name_3lv ：normal
    group_name_2lv ：normal
    group_name_1lv ：normal
    group_name_nolv_yeslai ：normal
    group_name_nolv_nolai ：normal
  ```





<br><br>

  ## novice 
   -  <b>定义规则</b> ： 新手保护 rookieBuff=true（rookieBuff=true规则待确认）
   -  <b>计算执行概率</b> ：
      -  原有加权计算

```
    group_name_3lv ：upPw（normal，novice）
    group_name_2lv ：upPw（normal，novice）
    group_name_1lv ：upPw（normal，novice）
    group_name_nolv_yeslai ：upPw（normal，novice）
    group_name_nolv_nolai ：upPw（normal，novice）
    
```

$$
upPw = 基础权重normal * （1 + 对应系数）
$$





<br><br><br>

  ## recharge 
  - <b>定义规则</b> ： 新充buff用户 && isset(扶植) && 扶植==true  
              <p> || 
              <p> Pr <= 扶植起点 && (新充buff用户 && isset(扶植) && 扶植==true)）


  - <b>计算执行概率</b> ： 
    - 新充buff加权概率
    
  ```
    group_name_3lv:max（luckyPw（normal，up），upPw（normal，recharge））
    group_name_2lv:max（luckyPw（normal，up），upPw（normal，recharge））
    group_name_1lv:min（luckyPw（normal，up），upPw（normal，recharge））
    group_name_nolv_yeslai:max（luckyPw（normal，up），upPw（normal，recharge））
    group_name_nolv_nolai: min（luckyPw（normal，up），upPw（normal，recharge））
  ```


$$
luckyPw = 1 + (\frac{扶植起点-pr}{扶植起点-扶植极值}) * 对应系数 * 倍数(基础权重normal)
$$

$$
upPw = 基础权重normal * （1 + 对应系数）
$$





<br><br><br>

  ## down 
  - <b>定义规则</b> ： Pr >= 限制起点 （盈利状态 走抑制）
  - <b>计算执行概率</b>
    - 抑制概率加权计算

```
    group_name_3lv ：unluckyPw（normal，down）
    group_name_2lv ：unluckyPw（normal，down）
    group_name_1lv ：unluckyPw（normal，down）
    group_name_nolv_yeslai ：unluckyPw（normal，down）
    group_name_nolv_nolai ：unluckyPw（normal，down）
```


$$
unluckyPw = 1 - (\frac{pr-限制起点}{限制极值-限制起点}) * 对应系数 * 倍数(基础权重normal)
$$


<br><br><br>



  ## up 
  - <b>定义规则</b> ： Pr <= 扶植起点 
                    <p> && 
                    <p> ！(新充buff用户 && isset(扶植) && 扶植==true ）
  - <b>计算执行概率</b>
    - 扶植概率加权计算
    
    
```
    group_name_3lv ：luckyPw（normal，up）
    group_name_2lv ：luckyPw（normal，up）
    group_name_1lv ：luckyPw（normal，up）
    group_name_nolv_yeslai ：luckyPw（normal，up）
    group_name_nolv_nolai ：luckyPw（normal，up）
```

$$
luckyPw = 1 + (\frac{扶植起点-pr}{扶植起点-扶植极值}) * 对应系数 * 倍数(基础权重normal)
$$

<br><br><br>
