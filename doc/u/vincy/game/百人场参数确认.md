```mermaid
graph TD

AiOverallWinRateV2.getRoomConfig --> getPrRoomConfigOne --> ai_room_config_key --> pr_hp_ai_config_list



getPrRoomConfigRate --> ai_room_config_key
getPrRoomConfigOne --> ai_room_config_key

AiHundredDealCard.__construct --> getPrRoomConfigOne
AiOverallWinRateV2 --> setRoomConfig --> getPrRoomConfigOne



AiHundredDealCard.__construct --> common.getHundredSysKillerWaveRate --> AiOverallWinRateV2.getSysKillerWaveRate --> AiOverallWinRateV2.getRoomConfig

AiHundredDealCard.__construct --> Common.getHundredLuckyLotoSysKillerWaveRate --> AiOverallWinRateV2.getLuckyLotoSysKillerWaveRate --> AiOverallWinRateV2.getRoomConfig


AiHundredDealCard --> getSysKillerRes

AiHundredDealCard --> getWinPointKillerRes



AiHundredDealCard.getIsUpBuff --> AiHundredDealCard.sysKillerRes
AiHundredDealCard.dealCardByUpAndDown --> AiHundredDealCard.sysKillerRes
AiHundredDealCard.dealCardByNorma --> AiHundredDealCard.sysKillerRes


AiHundredDealCard.getIsUpBuff --> AiHundredDealCard.winPointKillerRes
AiHundredDealCard.dealCardByNorma --> AiHundredDealCard.winPointKillerRes


```

<br>

| 字段       | 渲染                   |
| ---------- | ---------------------- |
| 普通房     | room_type_killer[0]    |
| Buff 系数  | buff_coefficient[0]    |
| 高手 Buff  | room_type_killer[1]    |
| Buff 系数  | buff_coefficient[1]    |
| 大户 Buff  | room_type_killer[2]    |
| Buff 系数  | buff_coefficient[2]    |
| 强杀下注额 | win_score_killer_base  |
| 房间控系数 | intervene_rate         |
| 初始概率   | initial_rate【未使用】 |
| 容忍系数   | tolerance_factor       |
| 浮动概率   | float_rate             |

<br>

```json
{
	"1000_tp_war_jack_pool": "{"
	room_type_killer ":["
	0.02 ","
	0.1 ","
	0.15 "],"
	buff_coefficient ":["
	20 ","
	40 ","
	60 "],"
	win_score_killer_base ":"
	3000 ","
	intervene_rate ":"
	0.22 ","
	initial_rate ":"
	0.0 ","
	tolerance_factor ":"
	0.5 ","
	float_rate ":"
	0.33 "}"
}
```

<br>

## AiHundredDealCard 类 是一个基类

```
继承此类的类

Ai7up7downDealCard
AiABClassicDealCard
AiABDealCard
AiBenzBmwDealCard
AiLongHuDealCard
AiLuckyDiceDealCard
AiLuckyLotoDealCard
AiRedBlackDealCard
AiRedBlackFastDealCard
AiSlotsDealCard
AiSpacexDealCard
AiTpWarDealCard

```

## 批量发牌

```mermaid
graph TD
AiHundredDealCard.__construct
```

```

if ($this->cls == Common::GAME_PLAY_TYPE_LUCKY_LOTO) { //jackpot loto
            $sysKillerWaveRateInfo = Common::getHundredLuckyLotoSysKillerWaveRate($this->betUidList, $this->roomType, $this->getRoomKillConfigType());
        } else {
            $sysKillerWaveRateInfo = Common::getHundredSysKillerWaveRate($this->betUidList, $this->roomType, $this->getRoomKillConfigType());
        }

今日波动的系统强杀概率  ysKillerWaveRate = $sysKillerWaveRateInfo['waveRate'];
系统近日缺口额 sysWinAmount = $sysKillerWaveRateInfo['sysWinAmount'];
系统此时的盈利目标 sysTargetAmount = $sysKillerWaveRateInfo['sysTargetAmount'];

```

### 百人根据房间下注人的 buff 属性计算出最低的波动杀率

```mermaid
graph TD
AiHundredDealCard.__construct --> Common.getHundredLuckyLotoSysKillerWaveRate
```

```php
获取大盘的缺口金额

 //如果对应的小盘 有亏损时（无论是否有没有盈利） 取亏损最小的数值 （比如小盘亏5000和小盘亏1000时，大盘按亏1000算）
  if ($waveRateLeft1) {
      //取波动最大的
      $waveRateList = array_column($waveRateLeft1, 'waveRate');
      array_multisort($waveRateList, SORT_DESC, $waveRateLeft1);
      Log::console_log(__FUNCTION__, '$waveRateLeft1：' . json_encode($waveRateLeft1), 'common');
      $rtn = array_shift($waveRateLeft1);
  } else {
      //如果对应的小盘 只有盈利时 取盈利最小的数值 （比如小盘盈利5000和小盘盈利1000时，大盘按盈利1000算）
      //取波动最小的
      $waveRateList = array_column($waveRateRight1, 'waveRate');
      array_multisort($waveRateList, SORT_ASC, $waveRateRight1);
      Log::console_log(__FUNCTION__, '$waveRateRight1：' . json_encode($waveRateRight1), 'common');
      $rtn = array_shift($waveRateRight1);
  }


if 波动率 >= 1
  取波动最小的
else
  取波动最大的
```

#### 获取 lucky loto 扶植或者强杀的概率

```mermaid
graph TD
Common.getHundredLuckyLotoSysKillerWaveRate --> AiOverallWinRateV2.getLuckyLotoSysKillerWaveRate --> AiOverallWinRateV2.getRoomConfig
```

今日超额利润 todayWinAmount = 今日系统盈利 todaySystemAmount - 今日系统动态目标额 todayDynamicTargetAmount

$$
max （ 0.05 ， ( 1 + 房间控系数 * (\frac{今日超额利润}{max（今日系统动态目标额 ， 5000000 )}) ))
$$

<font color="red">
- 房间控系数 。系数越大，杀率越高
</font>

### 百人根据房间下注人的 buff 属性计算出最低的波动杀率

```mermaid
graph TD

AiHundredDealCard.__construct --> common.getHundredSysKillerWaveRate
```

```
获取非高手和大户房 读取普通房的杀率
```

```php
获取系统杀率 tmpWaveRate
默认 波动率 waveRate = 0
if 波动率 = 0 || 波动率 <= 系统杀率
  波动率 waveRate = 系统杀率 tmpWaveRate
  今日缺口金额 sysWinAmount = 今日波动目标额 todayDynamicTargetAmount - 今日利润 todaySystemAmount;
  营收目标金额 sysTargetAmount
```

#### 获取 buff 类型

```mermaid
graph TD

AiHundredDealCard --> getRoomKillConfigType --> Common::getSysKillerTypeByBuffer --> RedisOpt::getPlayerNowUseBuff

```

```php
    //百人场房间强杀类型枚举
    const ROOM_UNION_TYPE_NORMAL = 0; //普通
    const ROOM_UNION_TYPE_WIN = 1; //高手
    const ROOM_UNION_TYPE_RICH = 2; //大户
    const ROOM_UNION_TYPE_BUFF = 3; //buff屋

 //################用户buff
    const USER_TAG_CHARGE_BUFF = 'charge_buff'; //充值buff
    const USER_TAG_ROOKIE = 'rookie'; //是否是新手
    const USER_TAG_WIN_BUFF = 'win_buff'; //高手buff
    const USER_TAG_RICH_BUFF = 'rich_buff'; //小黑屋buff
    const USER_TAG_NORMAL_BUFF = 'normal_buff'; //正常状态 无任何buff
    const USER_TAG_TP_DISCLOSE_KILLER = 'tp_disclose_killer'; //tp透杀


    //根据buff类型获取强杀类型
    public static function getSysKillerTypeByBuffer($buff)
    {
        $list = [
                //新充 新手  正常 读取正常房间的杀率
            Common::USER_TAG_CHARGE_BUFF => Common::ROOM_UNION_TYPE_BUFF,
            Common::USER_TAG_ROOKIE => Common::ROOM_UNION_TYPE_BUFF,
                //普通房
            Common::USER_TAG_NORMAL_BUFF => Common::ROOM_UNION_TYPE_NORMAL,

                //高手房 大户房  读取原有的 小黑屋 小灰屋的杀率
            Common::USER_TAG_WIN_BUFF => Common::ROOM_UNION_TYPE_WIN,
            Common::USER_TAG_RICH_BUFF => Common::ROOM_UNION_TYPE_RICH,
        ];
        if (isset($list[$buff])) {
            return $list[$buff];
        } else {
            return Common::ROOM_UNION_TYPE_NORMAL;
        }
    }


```

<br><br>

| buff 类型   | buff 状态 | buff 类型名称 | 代码常量定义         |
| ----------- | --------- | ------------- | -------------------- |
| charge_buff | 3         | buff 屋       | USER_TAG_CHARGE_BUFF |
| rookie      | 3         | buff 屋       | USER_TAG_ROOKIE      |
| win_buff    | 1         | 高手          | USER_TAG_WIN_BUFF    |
| rich_buff   | 2         | 大户          | USER_TAG_RICH_BUFF   |
| normal_buff | 0         | 普通          | USER_TAG_NORMAL_BUFF |

<br>

```php

根据玩家的buff类型 获取当前使用的buff，buff关系优先级 新手>高手/小黑屋>新充

```

- 大户 buff ； user*tag_list*玩家 ID --> Common::USER*TAG_IS_RICH_BUFF . '*' . $cls
- 高手 buff ； user*tag_list*玩家 ID --> Common::USER*TAG_IS_WIN_BUFF . '*' . $cls
- 新充 buff ； user*tag_list*玩家 ID --> Common::USER_TAG_CHARGE_BUFF
- 新手 buff ； user*tag_list*玩家 ID --> Common::USER_TAG_ROOKIE
- 默认普通 buff ；Common::USER_TAG_NORMAL_BUFF

#### 设置 buff 类型

```

```

### 获取系统杀率

```mermaid
graph TD

common.getHundredSysKillerWaveRate --> AiOverallWinRateV2.getSysKillerWaveRate
--> roomConfig
```

$$
今日缺口金额 = 今日营收目标 - 今日利润
$$

if 今日缺口额 < 0

$$
rate = 0
$$

else

$$
buff系数 * 房间控系数 * (\frac{今日缺口数据}{max（今日营收目标 ， 5000000 )})
$$

<br>

<font color="red">

- buff 系数 根据形参带入【0，1，2】(非 1，2 为 0)
- 房间控系数

</font>

<br>

## 获取系统强杀结果

```mermaid
graph TD
AiHundredDealCard --> getSysKillerRes
```

```
根据房间强杀类型 killerType  获取原始强杀概率 room_type_killer

本局波动的系统强杀概率 = （标题2计算得出）
```

$$
系统强杀概率 = min （ 0.333 ，（系统原始强杀率 + 本局波动的系统强杀概率 ））
$$

$$
强杀结果 = 随机（系统强杀概率 * 1000 , 1000 - 系统强杀概率 * 1000 ）
$$

<font color="red">

- room_type_killer [普通房 ,高手 buff，大户 buff]

</font>

## 根据下注玩家贫富程度强杀(劫富)

```mermaid
graph TD
AiHundredDealCard --> getWinPointKillerRes
```

<br>

- 玩家在该玩法内历史总赢钱 || 玩家在该玩法内历史总下注 = 0 。 不执行

- 如果有效下注 <= 复仇杀配置（强杀下注额 win_score_killer_base） \* 1000 。 不执行

<br><br><br>

$$
  玩家有效下注 = max（本局下注, 玩家在该玩法内历史总赢钱-玩家在该玩法内历史总下注+本局下注）
$$

if 玩家在该玩法内历史总下注 > 玩家在该玩法内历史总赢钱

$$
新复仇杀概率 scorePoint = 系统强杀概率 sysKillerRate
$$

else

$$
新复仇杀概率  = 系统强杀概率 sysKillerRate +  min（1，(\frac{\frac{本局下注}{玩家在该玩法内历史总赢钱 - 玩家在该玩法内历史总下注}}{复仇杀容忍系数(tolerance_factor)} , 3) * 复仇杀浮动概率(float_rate)
$$

<br><br><br>

$$
新复仇强杀结果 = 随机（新复仇杀概率 * 1000 , 1000 - 新复仇杀概率 * 1000 ）
$$

<font color="red">

- tolerance_factor 容忍系数
- float_rate 浮动概率
- win_score_killer_base 强杀下注额

</font>

<br><br>

## 正常发牌

```mermaid
graph TD

AiHundredDealCard.dealCardByNorma --> AiHundredDealCard.sysKillerRes

```

```

如果 系统强杀 = false && 复仇杀 = false ，
通过  getHunderGameIsSysKillerByLotteryResult 计算 系统是否盈利 .
如果亏钱 ， 强制执行大盘杀

如果 系统强杀 或者 复仇杀
判断 系统赔的钱 小于 系统设置最大输钱额 或者 系统赔的钱 = 0



```

- 去除牌池里的牌组

  - 发牌循环设置的最大次数 10
  - 在不执行系统强制杀和复仇杀的情况下重新计算大盘杀且不强制执行（系统赚钱）
  - 执行系统强制杀 或者 复仇杀

- 不去除除牌池里的牌组
  - 系统强制杀
  - 复仇杀

## 根据开奖结果计算是否需要执行大盘杀

```mermaid
graph TD

AiHundredDealCard.dealCardByNorma --> Common.getHunderGameIsSysKillerByLotteryResult
```

系统赚钱情况下，不强制大盘杀；系统亏欠情况强制大盘杀。

- 如果系统赔的钱 = 0 或者 用户这局总下注的钱 = 0 ； 不重置系统强杀结果

if 系统要赔的钱 sysPayAmount = 0
赔钱后的目标缺口金额 = 系统赚的钱 sysWinAmount - 用户这局总下注的钱 playerBetAmount
else
赔钱后的目标缺口金额 = 系统赚的钱 sysWinAmount + 系统要赔的钱 sysPayAmount

- 如果 赔钱后的目标缺口金额 sysPayOutWinAmount <= 0 或者 系统的盈利目标 <= 0 ; 不重置系统强杀结果

如果 赔钱后的目标缺口金额 sysPayOutWinAmount >= 0 或者 系统的盈利目标 >= 0

$$
rate = min(0.333, buff调控系数 * 0.15 * (\frac{赔钱后的目标缺口金额}{max(系统的盈利目标, 10000000))}) * max(1, 系统要赔的钱 / 这局总下注的钱 - 1)
$$

- rate <= 0 ; 不重置系统强杀结果
- rate > 1 ; 重置系统强杀结果
- 否则 随机 （ rate _ 1000 ， 1000 - rate _ 1000 ）

## 新充 buff 发牌

AiHundredDealCard.dealCardByUpAndDown
