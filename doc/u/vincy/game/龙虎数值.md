# 龙虎数值

## 奖池

### 庄注比

大盘记录中的一个小框就是一个奖池。奖池状态每天零点重置。

- 庄 ： 奖池总盈亏，奖池利润，佣金也是奖池利润的一部分
- 注 ： 真人输赢流水总额，不含有佣金
- 比 ： 庄 / 注 \* 100

<br>举例说明

```js
[新充1]庄 	比 	      注
5679.68 	+17.73 % 	32020.88
我下注 1000，赢了，给我加回 1972，庄 -972，注 +972
4707.68 	+14.26 % 	32992.88
我下注 1000，输了，庄 +1000，注 +1000
5707.68 	+16.79 % 	33992.88
```

### 缺口率

$$
原始目标额 = 真人输赢流水总额 * 奖池预设盈利率
$$

<br>

$$
缺口额 = 动态目标额 - 利润
$$

$$
超额 = 利润 - 动态目标额
$$

<br>

$$
缺口率 = \frac{缺口额}{\max(动态目标额,1000000, 底注*1000)}
$$

### 动态目标额

- 有缺口时，动态目标额等于原始目标额，系统较紧，使缺口缩小
- 无缺口时，动态目标额低于原始目标额，系统较松，使缺口产生
- 由此产生一定的松紧变化，使利润围绕原始目标额上下波动

$$
timeRate = \frac{今日实时分钟数 * 0.96}{今日实时分钟数 + 300} + 0.2
$$

<br>

if 利润 > 原始目标额

$$
动态目标额 = 原始目标额 *  timeRate
$$

else

$$
动态目标额 = 原始目标额
$$

## 发牌逻辑

### dealCardByNorma 普通发牌

- 有强杀或复仇杀标记，极大概率玩家亏钱，因为会重试 10 次
- lottery 命中，则执行强杀逻辑
- lottery 没命中，则玩家盈亏结果完全随机

### dealCardByRechargeBuff 开启扶植

判定标准 1：真人赢钱不低于真人下注的 N 倍
判定标准 2：符合判定标准 1 且 系统赔钱少的

- 利用 getSetDealCardInfo 获得一次开奖结果
- 如果符合判断标准 1 且 lottery 命中 ，则随机 5 次尝试找一个符合判定标准 2 的
- 无论如何，如果玩家赢钱（包括本金）超过 5000 卢比，或者超过奖池利润，则执行强杀逻辑

### dealCardByUpAndDown 既要被扶植也要被保底杀

- 利用 getSetDealCardInfo 获得一次开奖结果，如果系统赢钱则使用，否则执行 lottery 测试
- lottery 命中，则执行强杀逻辑
- lottery 没命中，则玩家赢钱

## getIsUpBuff 决定发牌分支

| 字短               | 描述               |
| ------------------ | ------------------ |
| allProfit          | 总的净赢流水       |
| allLoss            | 总的净输流水       |
| rechargeBuffLoss   | 新充 BUFF 输钱流水 |
| rechargeBuffProfit | 新充 BUFF 赢钱流水 |
| rechargeBuffCount  | 新充 BUFF 额度     |
| buffRenewgeCnt     | 有效续充次数       |
| rechargeCnt        | 充值次数           |
| lossAmount         | 净输额度           |
| lossRate           | 净输率             |

<br>

新充 BUFF 期间所有玩法的净亏损：

$$
buffNetLoss = rechargeBuffLoss - rechargeBuffProfit
$$

该玩家所有玩法总的净亏损：

$$
allNetLoss = allLoss - allProfit
$$

净输额度：

$$
lossAmount = buffNetLoss - max(buffNetLoss - allNetLoss, 0)
$$

<br>
净输率：

if
rechargeBuffCount == 0 || lossAmount == 0

$$
lossRate = 0;
$$

else

$$
lossRate = (\frac{lossAmount}{rechargeBuffCount})
$$

<br>

净输率阈值 1：

$$
limitRate1 = min(0.8，(0.55 + 0.05 * (rechargeCnt - buffRenewgeCnt)));
$$

净输率阈值 2：

$$
limitRate2 = min(0.2，(-0.1 + 0.05 * (rechargeCnt - buffRenewgeCnt)));
$$

<br>

- 如果复仇杀开启，不开启扶植
- 有新充 BUFF 且在 BUFF 房间
  - lossRate 大于 limitRate1，则关闭强杀开启扶植
  - lossRate 在 limitRate2 和 limitRate1 之间
    - 没有强杀标记，则既要被扶植也要被保底杀
    - 有强杀标记，则不开启扶植
- 其他情况，不开启扶植

## 净输额度

这个公式不太好理解，需要转换一下：

$$
lossAmount = buffNetLoss - max(buffNetLoss - allNetLoss, 0)
$$

总的净亏损减去 BUFF 期间的净亏损，应该是充值之前的总的净亏损：

$$
oldNetLoss = allNetLoss - buffNetLoss
$$

充值之前的总的净亏损的负值就是充值之前的总的净利润：

$$
oldNetProfit = -oldNetLoss
$$

所以净输额度的公式可以转换为：

$$
lossAmount = buffNetLoss - max(oldNetProfit, 0)
$$

如果玩家在充值之前是净亏钱的，那么：

$$
lossAmount = buffNetLoss
$$

如果玩家在充值之前是净赢钱的，那么：

$$
lossAmount = buffNetLoss - oldNetProfit \\

lossAmount = buffNetLoss + oldNetLoss \\

lossAmount = allNetLoss
$$

## 扶植时发牌重试次数

### 新充次数

次数为 2 的概率为：

$$
dealCardRandRate = 0.8 * min(0.5, lossRate);
$$

其他情况概率为 1

### 新手次数

- 20% 概率次数为 2
- 其他情况次数为 1

## getSetDealCardInfo

判定标准 1：真人赢钱不低于真人下注的 N 倍（N = 1 ， spaceX N = 2）
判定标准 2：符合判定标准 1 且 真人赢钱小于真人下注的 4 倍

随机开奖一次 ， 如果符合判定标准 1 ， 则用本次开奖结果
否则，如果还有随机次数，则尝试找符合判定标准儿 2 的开奖结果

## 多人情况

### 系统波动强杀概率

一个真人关联一个奖池，这些奖池可能有不同的系统波动强杀概率，多人情况取最小值

$$
系统强杀概率 = buff系数 * 房间空系数 * max(0，奖池缺口率)
$$

### 真人赢钱总额

设：龙、和局、虎的真人下注总额 为 A B C

龙赢：

$$
A * 2.08
$$

和局：

$$
B * 10 + (A+C) * 0.5
$$

虎赢：

$$
C * 2.08
$$

### 用户分类：普通、高手、大户

#### NORMAL 房

用户分类就是普通

#### BUFF 房

从已下注的真人中找最先进入房间的那个，使用他的用户标签计算用户分类
