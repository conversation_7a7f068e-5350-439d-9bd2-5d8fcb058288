# 手续费规则

## CalcPayServiceFee / CalcPayoutServiceFee

- DoiPay ✅

```php
public function CalcPayServiceFee(int $amount): int
    {
        //代收费率：[0W,500W) , 7.5%;   [500W,+∞) , 7%
        //这里就先用百7.5
        return round($amount * 0.075, 2);

    }

    // 根据提现数额计算三方收取的手续费，单位卢比
    public function CalcPayoutServiceFee(int $amount): int
    {

        //代付费率：3%
        return round($amount * 0.03, 2);
    }
```

- 代收 <=500 7.5% ; >500 7% 目前使用 7.5%（没法儿计算累计额度）
- 调整 代付 不支持

<br>

- FirstPayer ❌

```php
// 根据充值数额计算三方收取的手续费，单位卢比
    public function CalcPayServiceFee(int $amount): int
    {
        // FirstPayer 代收7.5%最低8卢比，代付小于等于200时6%+6rs，代付大于200时3%+6rs
        return round(min(8, $amount * 0.075), 2);
    }

    // 根据提现数额计算三方收取的手续费，单位卢比
    public function CalcPayoutServiceFee(int $amount): int
    {
        // FirstPayer 代收7.5%最低8卢比，代付小于等于200时6%+6rs，代付大于200时3%+6rs
        if ($amount <= 200) {
            return round($amount * 0.06 + 6, 2);
        } else {
            return round($amount * 0.03 + 6, 2);
        }
    }
```

- 调整 代收 <=100 8 ； >100 7.5%
- 调整 代付 <=200 6+6 ; >200 3% + 6

<br>

- LamePay ✅

```php
// 根据充值数额计算三方收取的手续费，单位卢比
    public function CalcPayServiceFee(int $amount): int
    {
        // 代收费率：7.5%
        return round($amount * 0.075, 2);
    }

    // 根据提现数额计算三方收取的手续费，单位卢比
    public function CalcPayoutServiceFee(int $amount): int
    {
        // 代付费率：3%+6.00₹
        return round($amount * 0.03, 2) + 6;
    }

```

- 代收 7.5%
- 代付 3% + 6

<br>

- MetagoPay ✅

```php
// 根据充值数额计算三方收取的手续费，单位卢比
    public function CalcPayServiceFee(int $amount): int
    {
        // MetagoPay 代收8%，代付3%
        return round($amount * 0.08, 2);
    }

    // 根据提现数额计算三方收取的手续费，单位卢比
    public function CalcPayoutServiceFee(int $amount): int
    {
        // MetagoPay 代收8%，代付3%
        return round($amount * 0.03, 2);
    }
```

- 代收 8%
- 代付 3%

<br>

- YoyoPay ✅

```php
 // 根据充值数额计算三方收取的手续费，单位卢比
    public function CalcPayServiceFee(int $amount): int
    {
        // 代收费率：7%
        return round($amount * 0.07, 2);
    }

    // 根据提现数额计算三方收取的手续费，单位卢比
    public function CalcPayoutServiceFee(int $amount): int
    {
        // 代付费率：2%+6.00₹
        return round($amount * 0.02, 2) + 6;
    }

```

- 代收 7%
- 代付 2% + 6
