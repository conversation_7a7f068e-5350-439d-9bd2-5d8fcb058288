﻿### (2024.03.21) (TODO) 3Patti Huge Win（第二次使用）
pthugewin
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-pthugewin
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.133.48(公)
172.16.1.30(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
pthugewin1.xyz

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.pthugewin1.xyz/center
http://www.pthugewin1.xyz/privacy.html
http://console.pthugewin1.xyz/

9、给前端的地址
center地址：  https://www.pthugewin1.xyz/center
隐私协议地址：  https://www.pthugewin1.xyz/privacy.html






### (2024.03.21) (TODO) Teen Patti Go
tpgomar
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-tpgomar
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.128.218(公)
172.16.1.29(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
tpgomar1.xyz

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.tpgomar1.xyz/center
http://www.tpgomar1.xyz/privacy.html
http://console.tpgomar1.xyz/

9、给前端的地址
center地址：  https://www.tpgomar1.xyz/center
隐私协议地址：  https://www.tpgomar1.xyz/privacy.html








### (2024.03.21) (TODO) 3Patti Master
ptmaster
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-ptmaster
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.132.149(公)
172.16.1.28(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
ptmaster1.xyz

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.ptmaster1.xyz/center
http://www.ptmaster1.xyz/privacy.html
http://console.ptmaster1.xyz/

9、给前端的地址
center地址：  https://www.ptmaster1.xyz/center
隐私协议地址：  https://www.ptmaster1.xyz/privacy.html







### (2024.03.21) (TODO) TeenPatti Super Win
tpwinmar
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-tpwinmar
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.136.134(公)
172.16.1.27(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
tpwinmar1.xyz

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.tpwinmar1.xyz/center
http://www.tpwinmar1.xyz/privacy.html
http://console.tpwinmar1.xyz/

9、给前端的地址
center地址：  https://www.tpwinmar1.xyz/center
隐私协议地址：  https://www.tpwinmar1.xyz/privacy.html






### (2024.03.20) (TODO) Teen Patti Lucky
tplucky
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-tplucky
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.131.222(公)
172.16.1.26(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
tplucky1.xyz

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.tplucky1.xyz/center
http://www.tplucky1.xyz/privacy.html
http://console.tplucky1.xyz/

9、给前端的地址
center地址：  https://www.tplucky1.xyz/center
隐私协议地址：  https://www.tplucky1.xyz/privacy.html







### (2024.03.20) (TODO) TeenPatti Super Win（第二次使用）
tpsuperwin
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-tpsuperwin
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.146.141(公)
172.16.1.25(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
tpsuperwin1.xyz

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.tpsuperwin1.xyz/center
http://www.tpsuperwin1.xyz/privacy.html
http://console.tpsuperwin1.xyz/

9、给前端的地址
center地址：  https://www.tpsuperwin1.xyz/center
隐私协议地址：  https://www.tpsuperwin1.xyz/privacy.html






### (2024.03.15) (TODO) 3Patti Huge Win
pattihugewin
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-pattihugewin
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.146.14(公)
172.16.1.24(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
pattihugewin1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.pattihugewin1.fun/center
http://www.pattihugewin1.fun/privacy.html
http://console.pattihugewin1.fun/

9、给前端的地址
center地址：  https://www.pattihugewin1.fun/center
隐私协议地址：  https://www.pattihugewin1.fun/privacy.html







### (2024.03.13) (TODO) TeenPatti Win（第二次使用）
teenpattiwin
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattiwin
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.132.202(公)
172.16.1.23(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
teenpattiwin1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.teenpattiwin1.fun/center
http://www.teenpattiwin1.fun/privacy.html
http://console.teenpattiwin1.fun/

9、给前端的地址
center地址：  https://www.teenpattiwin1.fun/center
隐私协议地址：  https://www.teenpattiwin1.fun/privacy.html







### (2024.03.13) (TODO) TeenPatti Win（第二次使用）
teenpattiwin
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattiwin
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.132.202(公)
172.16.1.23(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
teenpattiwin1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.teenpattiwin1.fun/center
http://www.teenpattiwin1.fun/privacy.html
http://console.teenpattiwin1.fun/

9、给前端的地址
center地址：  https://www.teenpattiwin1.fun/center
隐私协议地址：  https://www.teenpattiwin1.fun/privacy.html






### (2024.03.13) (TODO) 3Patti Rich
pattirich
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-pattirich
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.176.213(公)
172.16.1.22(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
pattirich1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.pattirich1.fun/center
http://www.pattirich1.fun/privacy.html
http://console.pattirich1.fun/

9、给前端的地址
center地址：  https://www.pattirich1.fun/center
隐私协议地址：  https://www.pattirich1.fun/privacy.html








### (2024.03.12) (TODO) ड्रैगन टाइगर जीत
tpdragontiger
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-tpdragontiger
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.145.125(公)
172.16.1.21(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
tpdragontiger1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.tpdragontiger1.fun/center
http://www.tpdragontiger1.fun/privacy.html
http://console.tpdragontiger1.fun/

9、给前端的地址
center地址：  https://www.tpdragontiger1.fun/center
隐私协议地址：  https://www.tpdragontiger1.fun/privacy.html






### (2024.03.07) (TODO) TeenPatti Bodhi
tpoffical
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-tpoffical
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.130.252(公)
172.16.1.20(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
tpoffical1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.tpoffical1.fun/center
http://www.tpoffical1.fun/privacy.html
http://console.tpoffical1.fun/

9、给前端的地址
center地址：  https://www.tpoffical1.fun/center
隐私协议地址：  https://www.tpoffical1.fun/privacy.html





### (2024.03.06) (TODO) TeenPatti Bodhi
pwaroibest
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-pwaroibest
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.179.135(公)
172.16.1.19(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
pwaroibest1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.pwaroibest1.fun/center
http://www.pwaroibest1.fun/privacy.html
http://console.pwaroibest1.fun/

9、给前端的地址
center地址：  https://www.pwaroibest1.fun/center
隐私协议地址：  https://www.pwaroibest1.fun/privacy.html






### (2024.03.06) (TODO) Teen Patti Mega Win
tpmegawin
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-tpmegawin
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.147.205(公)
172.16.1.18(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
tpmegawin1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.tpmegawin1.fun/center
http://www.tpmegawin1.fun/privacy.html
http://console.tpmegawin1.fun/

9、给前端的地址
center地址：  https://www.tpmegawin1.fun/center
隐私协议地址：  https://www.tpmegawin1.fun/privacy.html






### (2024.02.29) (TODO) 3Patti Blitz
pattiblitz
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-pattiblitz
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.139.160(公)
172.16.1.17(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
pattiblitz1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.pattiblitz1.fun/center
http://www.pattiblitz1.fun/privacy.html
http://console.pattiblitz1.fun/

9、给前端的地址
center地址：  https://www.pattiblitz1.fun/center
隐私协议地址：  https://www.pattiblitz1.fun/privacy.html







### (2024.02.29) (TODO) TeenPatti Bodhi
tpmoloco
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-tpmoloco
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.179.113(公)
172.16.1.16(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
tpmoloco1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.tpmoloco1.fun/center
http://www.tpmoloco1.fun/privacy.html
http://console.tpmoloco1.fun/

9、给前端的地址
center地址：  https://www.tpmoloco1.fun/center
隐私协议地址：  https://www.tpmoloco1.fun/privacy.html










### (2024.02.28) (TODO) Teen Patti Dhani
tpdhanifeb
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-tpdhanifeb
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.180.106(公)
172.16.1.15(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
tpdhanifeb1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.tpdhanifeb1.fun/center
http://www.tpdhanifeb1.fun/privacy.html
http://console.tpdhanifeb1.fun/

9、给前端的地址
center地址：  https://www.tpdhanifeb1.fun/center
隐私协议地址：  https://www.tpdhanifeb1.fun/privacy.html






### (2024.02.28) (TODO) Teen Patti Rich
tprichfeb
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-tprichfeb
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.178.28(公)
172.16.1.14(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
tprichfeb1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.tprichfeb1.fun/center
http://www.tprichfeb1.fun/privacy.html
http://console.tprichfeb1.fun/

9、给前端的地址
center地址：  https://www.tprichfeb1.fun/center
隐私协议地址：  https://www.tprichfeb1.fun/privacy.html






### (2024.02.26) (TODO) Teen Patti bodhi(web2apk)
tpwebtoapk
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-tpwebtoapk
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.137.232(公)
172.16.1.13(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
tpwebtoapk1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.tpwebtoapk1.fun/center
http://www.tpwebtoapk1.fun/privacy.html
http://console.tpwebtoapk1.fun/

9、给前端的地址
center地址：  https://www.tpwebtoapk1.fun/center
隐私协议地址：  https://www.tpwebtoapk1.fun/privacy.html








### (2024.02.20) (TODO) Lucky 3Patti
luckypatti
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-luckypatti
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.136.173(公)
172.16.1.12(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
luckypatti1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.luckypatti1.fun/center
http://www.luckypatti1.fun/privacy.html
http://console.luckypatti1.fun/

9、给前端的地址
center地址：  https://www.luckypatti1.fun/center
隐私协议地址：  https://www.luckypatti1.fun/privacy.html







### (2024.02.20) (TODO) 3Patti Super Win（第二次使用）
pattisuperwin
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-pattisuperwin
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.132.182(公)
172.16.1.11(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
pattisuperwin1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.pattisuperwin1.fun/center
http://www.pattisuperwin1.fun/privacy.html
http://console.pattisuperwin1.fun/

9、给前端的地址
center地址：  https://www.pattisuperwin1.fun/center
隐私协议地址：  https://www.pattisuperwin1.fun/privacy.html








### (2024.02.18) (TODO) TeenPatti Bodhi(第五次使用）
tpbodhifeb
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-tpbodhifeb
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.128.136(公)
172.16.1.10(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
tpbodhifeb1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.tpbodhifeb1.top/center
http://www.tpbodhifeb1.top/privacy.html
http://console.tpbodhifeb1.top/

9、给前端的地址
center地址：  https://www.tpbodhifeb1.top/center
隐私协议地址：  https://www.tpbodhifeb1.top/privacy.html










### (2024.02.18) (TODO) TeenPatti Lucky
teenpattilucky
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattilucky
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.138.216(公)
172.16.1.9(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
teenpattilucky1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.teenpattilucky1.top/center
http://www.teenpattilucky1.top/privacy.html
http://console.teenpattilucky1.top/

9、给前端的地址
center地址：  https://www.teenpattilucky1.top/center
隐私协议地址：  https://www.teenpattilucky1.top/privacy.html








### (2024.02.18) (TODO) Royal Teen Patti
royalteenpatti
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-royalteenpatti
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.145.198(公)
172.16.1.8(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
royalteenpatti1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.royalteenpatti1.top/center
http://www.royalteenpatti1.top/privacy.html
http://console.royalteenpatti1.top/

9、给前端的地址
center地址：  https://www.royalteenpatti1.top/center
隐私协议地址：  https://www.royalteenpatti1.top/privacy.html









### (2024.01.29) (TODO) TeenPatti Bodhi(第四次使用，旧UI免费金币场)
tpbodhicoinjan
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-tpbodhicoinjan
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.137.116(公)
172.16.1.7(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
tpbodhicoinjan1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.tpbodhicoinjan1.fun/center
http://www.tpbodhicoinjan1.fun/privacy.html
http://console.tpbodhicoinjan1.fun/

9、给前端的地址
center地址：  https://www.tpbodhicoinjan1.fun/center
隐私协议地址：  https://www.tpbodhicoinjan1.fun/privacy.html







### (2024.01.29) (TODO) TeenPatti Bodhi（第4次使用）
tpbodhijan
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-tpbodhijan
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.134.31(公)
172.16.1.6(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
tpbodhijan1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.tpbodhijan1.fun/center
http://www.tpbodhijan1.fun/privacy.html
http://console.tpbodhijan1.fun/

9、给前端的地址
center地址：  https://www.tpbodhijan1.fun/center
隐私协议地址：  https://www.tpbodhijan1.fun/privacy.html






### (2024.01.19) (TODO) TeenPatti Khazana
tpkhazanacoin
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-tpkhazanacoin
对比原配置：弹性网卡、主机名、实例名称、自动续费
	

147.139.64.56(公)
172.16.1.5(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
tpkhazanacoin1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.tpkhazanacoin1.fun/center
http://www.tpkhazanacoin1.fun/privacy.html
http://console.tpkhazanacoin1.fun/

9、给前端的地址
center地址：  https://www.tpkhazanacoin1.fun/center
隐私协议地址：  https://www.tpkhazanacoin1.fun/privacy.html









### (2024.01.05) (TODO) Jhandi Munda Journey
jhandimundajourney
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-jhandimundajourney
对比原配置：弹性网卡、主机名、实例名称、自动续费
	

147.139.28.62(公)
172.16.2.121(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
jhandimundajourney1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.jhandimundajourney1.fun/center
http://www.jhandimundajourney1.fun/privacy.html
http://console.jhandimundajourney1.fun/

9、给前端的地址
center地址：  https://www.jhandimundajourney1.fun/center
隐私协议地址：  https://www.jhandimundajourney1.fun/privacy.html








### (2024.01.05) (TODO) Rocket Flight Challenge
rocketflightchallenge
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rocketflightchallenge
对比原配置：弹性网卡、主机名、实例名称、自动续费
	

147.139.72.253(公)
172.16.2.120(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
rocketflightchallenge1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.rocketflightchallenge1.fun/center
http://www.rocketflightchallenge1.fun/privacy.html
http://console.rocketflightchallenge1.fun/

9、给前端的地址
center地址：  https://www.rocketflightchallenge1.fun/center
隐私协议地址：  https://www.rocketflightchallenge1.fun/privacy.html









### (2024.01.05) (TODO) Poker Duel
pokerduel
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-pokerduel
对比原配置：弹性网卡、主机名、实例名称、自动续费
	

149.129.173.62(公)
172.16.2.119(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
pokerduel1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.pokerduel1.fun/center
http://www.pokerduel1.fun/privacy.html
http://console.pokerduel1.fun/

9、给前端的地址
center地址：  https://www.pokerduel1.fun/center
隐私协议地址：  https://www.pokerduel1.fun/privacy.html







### (2024.01.05) (TODO) Super Fortune Game
superfortunegame
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-superfortunegame
对比原配置：弹性网卡、主机名、实例名称、自动续费
	

147.139.73.211(公)
172.16.2.117(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
superfortunegame1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.superfortunegame1.fun/center
http://www.superfortunegame1.fun/privacy.html
http://console.superfortunegame1.fun/

9、给前端的地址
center地址：  https://www.superfortunegame1.fun/center
隐私协议地址：  https://www.superfortunegame1.fun/privacy.html









### (2024.01.05) (TODO) 3Patti Showdown
threepattishowdown
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-threepattishowdown
对比原配置：弹性网卡、主机名、实例名称、自动续费
	

147.139.29.179(公)
172.16.2.116(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
threepattishowdown1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.threepattishowdown1.fun/center
http://www.threepattishowdown1.fun/privacy.html
http://console.threepattishowdown1.fun/

9、给前端的地址
center地址：  https://www.threepattishowdown1.fun/center
隐私协议地址：  https://www.threepattishowdown1.fun/privacy.html







### (2024.01.05) (TODO) Rummy Royale Advisor
rummyroyaleadvisor
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummyroyaleadvisor
对比原配置：弹性网卡、主机名、实例名称、自动续费
	

147.139.45.151(公)
172.16.2.115(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
rummyroyaleadvisor1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.rummyroyaleadvisor1.fun/center
http://www.rummyroyaleadvisor1.fun/privacy.html
http://console.rummyroyaleadvisor1.fun/

9、给前端的地址
center地址：  https://www.rummyroyaleadvisor1.fun/center
隐私协议地址：  https://www.rummyroyaleadvisor1.fun/privacy.html









### (2024.01.05) (TODO) TeenPatti ProGuide
teenpattiproguide
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattiproguide
对比原配置：弹性网卡、主机名、实例名称、自动续费
	

*************(公)
************(私有)




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
teenpattiproguide1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.teenpattiproguide1.fun/center
http://www.teenpattiproguide1.fun/privacy.html
http://console.teenpattiproguide1.fun/

9、给前端的地址
center地址：  https://www.teenpattiproguide1.fun/center
隐私协议地址：  https://www.teenpattiproguide1.fun/privacy.html









### (2023.12.24) (TODO) Teen Patti Go
tpgodec
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-tpgodec
对比原配置：弹性网卡、主机名、实例名称、自动续费
	

**************（公）
************（私有）






3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
tpgodec1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.tpgodec1.fun/center
http://www.tpgodec1.fun/privacy.html
http://console.tpgodec1.fun/

9、给前端的地址
center地址：  https://www.tpgodec1.fun/center
隐私协议地址：  https://www.tpgodec1.fun/privacy.html






### (2023.12.24) (TODO) Lucky Car Survival
luckycarsurvival
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-luckycarsurvival
对比原配置：弹性网卡、主机名、实例名称、自动续费
	

147.139.72.125（公）
172.16.2.112（私有）






3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
luckycarsurvival1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.luckycarsurvival1.fun/center
http://www.luckycarsurvival1.fun/privacy.html
http://console.luckycarsurvival1.fun/

9、给前端的地址
center地址：  https://www.luckycarsurvival1.fun/center
隐私协议地址：  https://www.luckycarsurvival1.fun/privacy.html







### (2023.12.24) (TODO) Rummy World
rummyworld
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummyworld
对比原配置：弹性网卡、主机名、实例名称、自动续费
	

147.139.43.3（公）
172.16.2.111（私有）






3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
rummyworld1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.rummyworld1.fun/center
http://www.rummyworld1.fun/privacy.html
http://console.rummyworld1.fun/

9、给前端的地址
center地址：  https://www.rummyworld1.fun/center
隐私协议地址：  https://www.rummyworld1.fun/privacy.html





### (2023.12.24) (TODO) Poker Choice
pokerchoice
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-pokerchoice
对比原配置：弹性网卡、主机名、实例名称、自动续费
	

147.139.43.161（公）
172.16.2.110（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
pokerchoice1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.pokerchoice1.fun/center
http://www.pokerchoice1.fun/privacy.html
http://console.pokerchoice1.fun/

9、给前端的地址
center地址：  https://www.pokerchoice1.fun/center
隐私协议地址：  https://www.pokerchoice1.fun/privacy.html






### (2023.12.24) (TODO) Lucky Spin（第三次使用）
luckyspindec
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-luckyspindec
对比原配置：弹性网卡、主机名、实例名称、自动续费
	

147.139.31.143（公）
172.16.2.109（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
luckyspindec1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.luckyspindec1.fun/center
http://www.luckyspindec1.fun/privacy.html
http://console.luckyspindec1.fun/

9、给前端的地址
center地址：  https://www.luckyspindec1.fun/center
隐私协议地址：  https://www.luckyspindec1.fun/privacy.html





### (2023.12.24) (TODO) Royal Rocket Race
royalrocketrace
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-royalrocketrace
对比原配置：弹性网卡、主机名、实例名称、自动续费
	

147.139.46.120（公）
172.16.2.108（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
royalrocketrace1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.royalrocketrace1.fun/center
http://www.royalrocketrace1.fun/privacy.html
http://console.royalrocketrace1.fun/

9、给前端的地址
center地址：  https://www.royalrocketrace1.fun/center
隐私协议地址：  https://www.royalrocketrace1.fun/privacy.html






### (2023.12.24) (TODO) Royal Poker Win
royalpokerwin
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-royalpokerwin
对比原配置：弹性网卡、主机名、实例名称、自动续费
	

147.139.47.87（公）
172.16.2.107（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
royalpokerwin1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.royalpokerwin1.fun/center
http://www.royalpokerwin1.fun/privacy.html
http://console.royalpokerwin1.fun/

9、给前端的地址
center地址：  https://www.royalpokerwin1.fun/center
隐私协议地址：  https://www.royalpokerwin1.fun/privacy.html






### (2023.12.24) (TODO) Cards Clash
cardsclash
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-cardsclash
对比原配置：弹性网卡、主机名、实例名称、自动续费
	

	
147.139.75.190（公）
172.16.2.106（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
cardsclash1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.cardsclash1.fun/center
http://www.cardsclash1.fun/privacy.html
http://console.cardsclash1.fun/

9、给前端的地址
center地址：  https://www.cardsclash1.fun/center
隐私协议地址：  https://www.cardsclash1.fun/privacy.html






### (2023.12.16) (TODO) Fast Cars Showdown
fastcarsshowdown
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-fastcarsshowdown
对比原配置：弹性网卡、主机名、实例名称、自动续费
	

	
147.139.44.48（公）
172.16.2.105（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
fastcarsshowdown1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.fastcarsshowdown1.fun/center
http://www.fastcarsshowdown1.fun/privacy.html
http://console.fastcarsshowdown1.fun/

9、给前端的地址
center地址：  https://www.fastcarsshowdown1.fun/center
隐私协议地址：  https://www.fastcarsshowdown1.fun/privacy.html






### (2023.12.16) (TODO) Card Combo
cardcombo
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-cardcombo
对比原配置：弹性网卡、主机名、实例名称、自动续费
	

	
147.139.40.247（公）
172.16.2.104（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
cardcombo1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.cardcombo1.fun/center
http://www.cardcombo1.fun/privacy.html
http://console.cardcombo1.fun/

9、给前端的地址
center地址：  https://www.cardcombo1.fun/center
隐私协议地址：  https://www.cardcombo1.fun/privacy.html







### (2023.12.16) (TODO) TeenPatti Bodhi
tpbodhicoin
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-tpbodhicoin
对比原配置：弹性网卡、主机名、实例名称、自动续费
	

	
147.139.35.70（公）
172.16.2.103（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
tpbodhicoin1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.tpbodhicoin1.fun/center
http://www.tpbodhicoin1.fun/privacy.html
http://console.tpbodhicoin1.fun/

9、给前端的地址
center地址：  https://www.tpbodhicoin1.fun/center
隐私协议地址：  https://www.tpbodhicoin1.fun/privacy.html









### (2023.12.08) (TODO) AB Coin King
abcoinking
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-abcoinking
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.10.85（公）
172.16.2.102（私有）





3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
abcoinking1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.abcoinking1.fun/center
http://www.abcoinking1.fun/privacy.html
http://console.abcoinking1.fun/

9、给前端的地址
center地址：  https://www.abcoinking1.fun/center
隐私协议地址：  https://www.abcoinking1.fun/privacy.html






### (2023.12.08) (TODO) Super Ball Guess
superballguess
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-superballguess
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.41.235（公）
172.16.2.101（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
superballguess1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.superballguess1.fun/center
http://www.superballguess1.fun/privacy.html
http://console.superballguess1.fun/

9、给前端的地址
center地址：  https://www.superballguess1.fun/center
隐私协议地址：  https://www.superballguess1.fun/privacy.html







### (2023.12.08) (TODO) Lightning Dragon Tiger
lightningdt
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-lightningdt
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.41.4（公）
172.16.2.100（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
lightningdt1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.lightningdt1.fun/center
http://www.lightningdt1.fun/privacy.html
http://console.lightningdt1.fun/

9、给前端的地址
center地址：  https://www.lightningdt1.fun/center
隐私协议地址：  https://www.lightningdt1.fun/privacy.html






### (2023.12.08) (TODO) Poker Master
pokermaster
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-pokermaster
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.11.223（公）
172.16.2.99（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
pokermaster1.fun

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.pokermaster1.fun/center
http://www.pokermaster1.fun/privacy.html
http://console.pokermaster1.fun/

9、给前端的地址
center地址：  https://www.pokermaster1.fun/center
隐私协议地址：  https://www.pokermaster1.fun/privacy.html







### (2023.12.02) (TODO) Andar Bahar Spin
andarbaharspin
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-andarbaharspin
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.33.108(公)
172.16.2.98(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
andarbaharspin1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.andarbaharspin1.top/center
http://www.andarbaharspin1.top/privacy.html
http://console.andarbaharspin1.top/

9、给前端的地址
center地址：  https://www.andarbaharspin1.top/center
隐私协议地址：  https://www.andarbaharspin1.top/privacy.html







### (2023.12.02) (TODO) 3Patti Ace
threepattiace
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-threepattiace
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.167.171(公)
172.16.2.97(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
threepattiace1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.threepattiace1.top/center
http://www.threepattiace1.top/privacy.html
http://console.threepattiace1.top/

9、给前端的地址
center地址：  https://www.threepattiace1.top/center
隐私协议地址：  https://www.threepattiace1.top/privacy.html






### (2023.12.02) (TODO) Dice Superstar
dicesuperstar
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-dicesuperstar
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.43.34(公)
172.16.2.96(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
dicesuperstar1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.dicesuperstar1.top/center
http://www.dicesuperstar1.top/privacy.html
http://console.dicesuperstar1.top/

9、给前端的地址
center地址：  https://www.dicesuperstar1.top/center
隐私协议地址：  https://www.dicesuperstar1.top/privacy.html




### (2023.12.02) (TODO) Lucky AB Online
luckyabonline
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-luckyabonline
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.43.13(公)
172.16.2.95(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
luckyabonline1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.luckyabonline1.top/center
http://www.luckyabonline1.top/privacy.html
http://console.luckyabonline1.top/

9、给前端的地址
center地址：  https://www.luckyabonline1.top/center
隐私协议地址：  https://www.luckyabonline1.top/privacy.html







### (2023.12.02) (TODO) Jhandi Munda War
jhandimundawar
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-jhandimundawar
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.11.175(公)
172.16.2.94(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
jhandimundawar1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.jhandimundawar1.top/center
http://www.jhandimundawar1.top/privacy.html
http://console.jhandimundawar1.top/

9、给前端的地址
center地址：  https://www.jhandimundawar1.top/center
隐私协议地址：  https://www.jhandimundawar1.top/privacy.html





### (2023.12.02) (TODO) Queen War
queenwar
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-queenwar
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.4.123(公)
172.16.2.93(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
queenwar1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.queenwar1.top/center
http://www.queenwar1.top/privacy.html
http://console.queenwar1.top/

9、给前端的地址
center地址：  https://www.queenwar1.top/center
隐私协议地址：  https://www.queenwar1.top/privacy.html





### (2023.12.02) (TODO) Spin To Win
spintowin
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-spintowin
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.30.19(公)
172.16.2.92(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
spintowin1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.spintowin1.top/center
http://www.spintowin1.top/privacy.html
http://console.spintowin1.top/

9、给前端的地址
center地址：  https://www.spintowin1.top/center
隐私协议地址：  https://www.spintowin1.top/privacy.html






### (2023.11.24) (TODO) Red Black Get Online
redblackgetonline
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-redblackgetonline
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.28.255(公)
172.16.2.91(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
redblackgetonline1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.redblackgetonline1.top/center
http://www.redblackgetonline1.top/privacy.html
http://console.redblackgetonline1.top/

9、给前端的地址
center地址：  https://www.redblackgetonline1.top/center
隐私协议地址：  https://www.redblackgetonline1.top/privacy.html





### (2023.11.24) (TODO) Double Lucky Wheel
doubleluckywheel
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-doubleluckywheel
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.30.91(公)
172.16.2.90(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
doubleluckywheel1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.doubleluckywheel1.top/center
http://www.doubleluckywheel1.top/privacy.html
http://console.doubleluckywheel1.top/

9、给前端的地址
center地址：  https://www.doubleluckywheel1.top/center
隐私协议地址：  https://www.doubleluckywheel1.top/privacy.html







### (2023.11.24) (TODO) Poker Era
pokerera
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-pokerera
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.5.102(公)
172.16.2.89(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
pokerera1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.pokerera1.top/center
http://www.pokerera1.top/privacy.html
http://console.pokerera1.top/

9、给前端的地址
center地址：  https://www.pokerera1.top/center
隐私协议地址：  https://www.pokerera1.top/privacy.html






### (2023.11.24) (TODO) Lucky Spin
luckyspin
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-luckyspin
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.1.202(公)
172.16.2.88(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
luckyspin2.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.luckyspin2.top/center
http://www.luckyspin2.top/privacy.html
http://console.luckyspin2.top/

9、给前端的地址
center地址：  https://www.luckyspin2.top/center
隐私协议地址：  https://www.luckyspin2.top/privacy.html







### (2023.11.24) (TODO) Dragon Tiger Blitz
dragontigerblitz
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-dragontigerblitz
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.34.246(公)
172.16.2.87(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
dragontigerblitz1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.dragontigerblitz1.top/center
http://www.dragontigerblitz1.top/privacy.html
http://console.dragontigerblitz1.top/

9、给前端的地址
center地址：  https://www.dragontigerblitz1.top/center
隐私协议地址：  https://www.dragontigerblitz1.top/privacy.html






### (2023.11.24) (TODO) TeenPatti War
teenpattiwar
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattiwar
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.35.158(公)
172.16.2.59(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
teenpattiwar1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.teenpattiwar1.top/center
http://www.teenpattiwar1.top/privacy.html
http://console.teenpattiwar1.top/

9、给前端的地址
center地址：  https://www.teenpattiwar1.top/center
隐私协议地址：  https://www.teenpattiwar1.top/privacy.html





### (2023.11.18) (TODO) AB Challenge
abchallenge
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-abchallenge
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.28.122(公)
172.16.2.86(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
abchallenge1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.abchallenge1.top/center
http://www.abchallenge1.top/privacy.html
http://console.abchallenge1.top/

9、给前端的地址
center地址：  https://www.abchallenge1.top/center
隐私协议地址：  https://www.abchallenge1.top/privacy.html







### (2023.11.18) (TODO) Quick Win Rummy
quickwinrummy
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-quickwinrummy
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.1.225(公)
172.16.2.85(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
quickwinrummy1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.quickwinrummy1.top/center
http://www.quickwinrummy1.top/privacy.html
http://console.quickwinrummy1.top/

9、给前端的地址
center地址：  https://www.quickwinrummy1.top/center
隐私协议地址：  https://www.quickwinrummy1.top/privacy.html






### (2023.11.18) (TODO) Lucky 3Patti
luckythreepatti
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-luckythreepatti
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.12.31(公)
172.16.2.84(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
luckythreepatti1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.luckythreepatti1.top/center
http://www.luckythreepatti1.top/privacy.html
http://console.luckythreepatti1.top/

9、给前端的地址
center地址：  https://www.luckythreepatti1.top/center
隐私协议地址：  https://www.luckythreepatti1.top/privacy.html






### (2023.11.18) (TODO) TeenPatti Play
teenpattiplay
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattiplay
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
**************(公)
***********(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
teenpattiplay1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.teenpattiplay1.top/center
http://www.teenpattiplay1.top/privacy.html
http://console.teenpattiplay1.top/

9、给前端的地址
center地址：  https://www.teenpattiplay1.top/center
隐私协议地址：  https://www.teenpattiplay1.top/privacy.html







### (2023.11.18) (TODO) Dragon Tiger War
dragontigerwar
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-dragontigerwar
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
**************(公)
***********(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
dragontigerwar1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.dragontigerwar1.top/center
http://www.dragontigerwar1.top/privacy.html
http://console.dragontigerwar1.top/

9、给前端的地址
center地址：  https://www.dragontigerwar1.top/center
隐私协议地址：  https://www.dragontigerwar1.top/privacy.html







### (2023.11.13) (TODO) Car Racing Guess
carracingguess
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-carracingguess
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.33.91(公)
172.16.2.81(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
carracingguess1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.carracingguess1.top/center
http://www.carracingguess1.top/privacy.html
http://console.carracingguess1.top/

9、给前端的地址
center地址：  https://www.carracingguess1.top/center
隐私协议地址：  https://www.carracingguess1.top/privacy.html





### (2023.11.13) (TODO) Super Andar Bahar
superandarbahar
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-superandarbahar
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.167.63(公)
172.16.2.80(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
superandarbahar1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.superandarbahar1.top/center
http://www.superandarbahar1.top/privacy.html
http://console.superandarbahar1.top/

9、给前端的地址
center地址：  https://www.superandarbahar1.top/center
隐私协议地址：  https://www.superandarbahar1.top/privacy.html





### (2023.11.13) (TODO) Rocket Star
rocketstar
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rocketstar
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.167.19(公)
172.16.2.79(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
rocketstar1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.rocketstar1.top/center
http://www.rocketstar1.top/privacy.html
http://console.rocketstar1.top/

9、给前端的地址
center地址：  https://www.rocketstar1.top/center
隐私协议地址：  https://www.rocketstar1.top/privacy.html








### (2023.11.13) (TODO) AB Classic Game
abclassicgame
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-abclassicgame
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.34.187(公)
172.16.2.78(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
abclassicgame1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.abclassicgame1.top/center
http://www.abclassicgame1.top/privacy.html
http://console.abclassicgame1.top/

9、给前端的地址
center地址：  https://www.abclassicgame1.top/center
隐私协议地址：  https://www.abclassicgame1.top/privacy.html






### (2023.11.09) (TODO) Crazy Coin
crazycoin
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-crazycoin
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.11.202(公)
172.16.2.77(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
crazycoin1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.crazycoin1.top/center
http://www.crazycoin1.top/privacy.html
http://console.crazycoin1.top/

9、给前端的地址
center地址：  https://www.crazycoin1.top/center
隐私协议地址：  https://www.crazycoin1.top/privacy.html





### (2023.11.09) (TODO) Poker Star
pokerstar
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-pokerstar
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.11.202(公)
172.16.2.77(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
pokerstar1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.pokerstar1.top/center
http://www.pokerstar1.top/privacy.html
http://console.pokerstar1.top/

9、给前端的地址
center地址：  https://www.pokerstar1.top/center
隐私协议地址：  https://www.pokerstar1.top/privacy.html





### (2023.11.09) (TODO) 3Patti Win
threepattiwin
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-threepattiwin
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.28.185(公)
172.16.2.76(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
threepattiwin1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.threepattiwin1.top/center
http://www.threepattiwin1.top/privacy.html
http://console.threepattiwin1.top/

9、给前端的地址
center地址：  https://www.threepattiwin1.top/center
隐私协议地址：  https://www.threepattiwin1.top/privacy.html





### (2023.11.09) (TODO) Lucky Ball
luckyball
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-luckyball
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.174.186(公)
172.16.2.74(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
luckyball1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.luckyball1.top/center
http://www.luckyball1.top/privacy.html
http://console.luckyball1.top/

9、给前端的地址
center地址：  https://www.luckyball1.top/center
隐私协议地址：  https://www.luckyball1.top/privacy.html






### (2023.11.09) (TODO) Dragon Tiger Run
dragontigerrun
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-dragontigerrun
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.12.156(公)
172.16.2.73(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
dragontigerrun1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.dragontigerrun1.top/center
http://www.dragontigerrun1.top/privacy.html
http://console.dragontigerrun1.top/

9、给前端的地址
center地址：  https://www.dragontigerrun1.top/center
隐私协议地址：  https://www.dragontigerrun1.top/privacy.html





### (2023.11.09) (TODO) Roll Lucky 7
rolllucky
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rolllucky
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.34.230(公)
172.16.2.72(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
rolllucky1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.rolllucky1.top/center
http://www.rolllucky1.top/privacy.html
http://console.rolllucky1.top/

9、给前端的地址
center地址：  https://www.rolllucky1.top/center
隐私协议地址：  https://www.rolllucky1.top/privacy.html




### (2023.11.09) (TODO) Dragon Tiger Coin
dragontigercoin
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-dragontigercoin
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.3.226(公)
172.16.2.70(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
dragontigercoin1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.dragontigercoin1.top/center
http://www.dragontigercoin1.top/privacy.html
http://console.dragontigercoin1.top/

9、给前端的地址
center地址：  https://www.dragontigercoin1.top/center
隐私协议地址：  https://www.dragontigercoin1.top/privacy.html






### (2023.11.04) (TODO) Super Spin
superspin
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-superspin
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.8.34(公)
172.16.2.68(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
superspin1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.superspin1.top/center
http://www.superspin1.top/privacy.html
http://console.superspin1.top/

9、给前端的地址
center地址：  https://www.superspin1.top/center
隐私协议地址：  https://www.superspin1.top/privacy.html







### (2023.11.04) (TODO) Mines Master
minesmaster
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-minesmaster
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.10.99(公)
172.16.2.67(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
minesmaster1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.minesmaster1.top/center
http://www.minesmaster1.top/privacy.html
http://console.minesmaster1.top/

9、给前端的地址
center地址：  https://www.minesmaster1.top/center
隐私协议地址：  https://www.minesmaster1.top/privacy.html






### (2023.11.04) (TODO) Queen Card
queencard
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-queencard
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.34.135(公)
172.16.2.66(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
queencard1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.queencard1.top/center
http://www.queencard1.top/privacy.html
http://console.queencard1.top/

9、给前端的地址
center地址：  https://www.queencard1.top/center
隐私协议地址：  https://www.queencard1.top/privacy.html







### (2023.11.04) (TODO) Wheel of Wins
wheelofwins
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-wheelofwins
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.33.227(公)
172.16.2.64(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
wheelofwins1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.wheelofwins1.top/center
http://www.wheelofwins1.top/privacy.html
http://console.wheelofwins1.top/

9、给前端的地址
center地址：  https://www.wheelofwins1.top/center
隐私协议地址：  https://www.wheelofwins1.top/privacy.html






### (2023.11.04) (TODO) Spin Winner
spinwinner
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-spinwinner
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.11.43(公)
172.16.2.62(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
spinwinner1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.spinwinner1.top/center
http://www.spinwinner1.top/privacy.html
http://console.spinwinner1.top/

9、给前端的地址
center地址：  https://www.spinwinner1.top/center
隐私协议地址：  https://www.spinwinner1.top/privacy.html





### (2023.11.04) (TODO) Poker Get Online
pokergetonline
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-pokergetonline
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.9.234(公)
172.16.2.61(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
pokergetonline1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.pokergetonline1.top/center
http://www.pokergetonline1.top/privacy.html
http://console.pokergetonline1.top/

9、给前端的地址
center地址：  https://www.pokergetonline1.top/center
隐私协议地址：  https://www.pokergetonline1.top/privacy.html







### (2023.11.04) (TODO) Slots Myth
slotsmyth
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-slotsmyth
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.35.77(公)
172.16.2.60(私有)


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
slotsmyth1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.slotsmyth1.top/center
http://www.slotsmyth1.top/privacy.html
http://console.slotsmyth1.top/

9、给前端的地址
center地址：  https://www.slotsmyth1.top/center
隐私协议地址：  https://www.slotsmyth1.top/privacy.html





### (2023.11.04) (TODO) Vegas Party Slots
vegaspartyslots
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-vegaspartyslots
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.34.167(公)
172.16.2.58(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
vegaspartyslots1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.vegaspartyslots1.top/center
http://www.vegaspartyslots1.top/privacy.html
http://console.vegaspartyslots1.top/

9、给前端的地址
center地址：  https://www.vegaspartyslots1.top/center
隐私协议地址：  https://www.vegaspartyslots1.top/privacy.html





### (2023.11.04) (TODO) teenpattinova
teenpattinova
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattinova
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.7.18(公)
172.16.2.56(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
teenpattinova1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.teenpattinova1.com/center
http://www.teenpattinova1.com/privacy.html
http://console.teenpattinova1.com/

9、给前端的地址
center地址：  https://www.teenpattinova1.com/center
隐私协议地址：  https://www.teenpattinova1.com/privacy.html





### (2023.11.01) (TODO) Slots Victory
slotsvictory
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-slotsvictory
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.10.125(公)
172.16.2.55(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
slotsvictory1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.slotsvictory1.top/center
http://www.slotsvictory1.top/privacy.html
http://console.slotsvictory1.top/

9、给前端的地址
center地址：  https://www.slotsvictory1.top/center
隐私协议地址：  https://www.slotsvictory1.top/privacy.html





### (2023.11.01) (TODO) Poker Snap
pokersnap
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-pokersnap
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.32.132(公)
172.16.2.36(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
pokersnap1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.pokersnap1.top/center
http://www.pokersnap1.top/privacy.html
http://console.pokersnap1.top/

9、给前端的地址
center地址：  https://www.pokersnap1.top/center
隐私协议地址：  https://www.pokersnap1.top/privacy.html






### (2023.10.10) (TODO) Rocket Winner
rocketwinner
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rocketwinner
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.10.81(公)
172.16.2.54(私有)




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
rocketwinner1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.rocketwinner1.top/center
http://www.rocketwinner1.top/privacy.html
http://console.rocketwinner1.top/

9、给前端的地址
center地址：  https://www.rocketwinner1.top/center
隐私协议地址：  https://www.rocketwinner1.top/privacy.html




### (2023.10.10) (TODO) Andar Bahar Go
andarbahargo
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-andarbahargo
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.1.83(公)
172.16.2.48(私有)




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
andarbahargo1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.andarbahargo1.top/center
http://www.andarbahargo1.top/privacy.html
http://console.andarbahargo1.top/

9、给前端的地址
center地址：  https://www.andarbahargo1.top/center
隐私协议地址：  https://www.andarbahargo1.top/privacy.html




### (2023.10.7) (TODO) Dragon Tiger Wild
dragontigerrace
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-dragontigerrace
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.173.10（公）
172.16.2.38（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
dragontigerrace.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.dragontigerrace.top/center
http://www.dragontigerrace.top/privacy.html
http://console.dragontigerrace.top/

9、给前端的地址
center地址：  https://www.dragontigerrace.top/center
隐私协议地址：  https://www.dragontigerrace.top/privacy.html





### (2023.10.7) (TODO) 3Patti|Fist Challenge
firstchange3patti
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-firstchange3patti
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.8.165（公）
172.16.2.31（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
firstchange3patti1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.firstchange3patti1.top/center
http://www.firstchange3patti1.top/privacy.html
http://console.firstchange3patti1.top/

9、给前端的地址
center地址：  https://www.firstchange3patti1.top/center
隐私协议地址：  https://www.firstchange3patti1.top/privacy.html




### (2023.9.22) (TODO) Dragon Tiger Go
dragontigergo
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-dragontigergo
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
149.129.167.176（公）
172.16.2.30（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
dragontigergo1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.dragontigergo1.top/center
http://www.dragontigergo1.top/privacy.html
http://console.dragontigergo1.top/

9、给前端的地址
center地址：  https://www.dragontigergo1.top/center
隐私协议地址：  https://www.dragontigergo1.top/privacy.html





### (2023.9.22) (TODO) TeenPatti Get Online
tpgetonlinea
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-tpgetonlinea
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
149.129.172.149（公）
**********9（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
tpgetonlinea1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.tpgetonlinea1.top/center
http://www.tpgetonlinea1.top/privacy.html
http://console.tpgetonlinea1.top/

9、给前端的地址
center地址：  https://www.tpgetonlinea1.top/center
隐私协议地址：  https://www.tpgetonlinea1.top/privacy.html




### (2023.9.22) (TODO) Rummy VIP
rummyvip
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummyvip
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
*************（公）
**********7（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
rummyvip1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.rummyvip1.top/center
http://www.rummyvip1.top/privacy.html
http://console.rummyvip1.top/

9、给前端的地址
center地址：  https://www.rummyvip1.top/center
隐私协议地址：  https://www.rummyvip1.top/privacy.html




### (2023.9.22) (TODO) Lucky 3Patti
lucky3patti
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-lucky3patti
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
************（公）
**********6（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service
yylinstall

4、申请域名
如果被占用则末尾加数字 1
lucky3patti1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.lucky3patti1.top/center
http://www.lucky3patti1.top/privacy.html
http://console.lucky3patti1.top/

9、给前端的地址
center地址：  https://www.lucky3patti1.top/center
隐私协议地址：  https://www.lucky3patti1.top/privacy.html






### (2023.9.19) (TODO) Epic Wheel: Dragon Tiger
epicwheeldt
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-epicwheeldt
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.12.43（公）
***********（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
epicwheeldt1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.epicwheeldt1.top/center
http://www.epicwheeldt1.top/privacy.html
http://console.epicwheeldt1.top/

9、给前端的地址
center地址：  https://www.epicwheeldt1.top/center
隐私协议地址：  https://www.epicwheeldt1.top/privacy.html




### (2023.9.15) (TODO) TeenPatti Go
teenpattigoa
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattigoa
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
149.129.173.88（公）
172.16.2.18（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattigoa1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.teenpattigoa1.top/center
http://www.teenpattigoa1.top/privacy.html
http://console.teenpattigoa1.top/

9、给前端的地址
center地址：  https://www.teenpattigoa1.top/center
隐私协议地址：  https://www.teenpattigoa1.top/privacy.html





### (2023.9.15) (TODO) TeenPatti Master
teenpattimaster
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattimaster
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.11.212（公）
172.16.2.17（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattimaster1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.teenpattimaster1.top/center
http://www.teenpattimaster1.top/privacy.html
http://console.teenpattimaster1.top/

9、给前端的地址
center地址：  https://www.teenpattimaster1.top/center
隐私协议地址：  https://www.teenpattimaster1.top/privacy.html







### (2023.9.15) (TODO) Rummy Blitz
rummyblitzb
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummyblitzb
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.11.163（公）
172.16.2.16（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummyblitzb1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.rummyblitzb1.top/center
http://www.rummyblitzb1.top/privacy.html
http://console.rummyblitzb1.top/

9、给前端的地址
center地址：  https://www.rummyblitzb1.top/center
隐私协议地址：  https://www.rummyblitzb1.top/privacy.html






### (2023.9.15) (TODO) Gold Slots
goldslots
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-goldslots
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.6.251（公）
172.16.2.15（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
goldslots1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.goldslots1.top/center
http://www.goldslots1.top/privacy.html
http://console.goldslots1.top/

9、给前端的地址
center地址：  https://www.goldslots1.top/center
隐私协议地址：  https://www.goldslots1.top/privacy.html






### (2023.9.15) (TODO) TeenPatti Era
teenpattiera
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattiera
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
149.129.174.126（公）
172.16.2.12（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattiera1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.teenpattiera1.top/center
http://www.teenpattiera1.top/privacy.html
http://console.teenpattiera1.top/

9、给前端的地址
center地址：  https://www.teenpattiera1.top/center
隐私协议地址：  https://www.teenpattiera1.top/privacy.html




### (2023.9.15) (TODO) Quick Hit Slots
quickhitslots
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-quickhitslots
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.11.170（公）
172.16.2.11（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
quickhitslots1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.quickhitslots1.top/center
http://www.quickhitslots1.top/privacy.html
http://console.quickhitslots1.top/

9、给前端的地址
center地址：  https://www.quickhitslots1.top/center
隐私协议地址：  https://www.quickhitslots1.top/privacy.html






### (2023.9.15) (TODO) Rummy Adventure
rummyadventure
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummyadventure
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.9.152（公）
172.16.2.8（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummyadventure1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.rummyadventure1.top/center
http://www.rummyadventure1.top/privacy.html
http://console.rummyadventure1.top/

9、给前端的地址
center地址：  https://www.rummyadventure1.top/center
隐私协议地址：  https://www.rummyadventure1.top/privacy.html






### (2023.9.15) (TODO) Dragon Tiger King
dragontigerking
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-dragontigerking
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.4.87（公）
172.16.2.6（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
dragontigerking1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.dragontigerking1.top/center
http://www.dragontigerking1.top/privacy.html
http://console.dragontigerking1.top/

9、给前端的地址
center地址：  https://www.dragontigerking1.top/center
隐私协议地址：  https://www.dragontigerking1.top/privacy.html




### (2023.9.15) (TODO) TeenPatti Gold
teenpattigolda
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattigolda
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.1.83（公）
172.16.2.5（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattigolda1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.teenpattigolda1.top/center
http://www.teenpattigolda1.top/privacy.html
http://console.teenpattigolda1.top/

9、给前端的地址
center地址：  https://www.teenpattigolda1.top/center
隐私协议地址：  https://www.teenpattigolda1.top/privacy.html





### (2023.9.15) (TODO) TeenPatti Aura
teenpattiaura
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattiaura
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.10.81（公）
**********52（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattiaura1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.teenpattiaura1.top/center
http://www.teenpattiaura1.top/privacy.html
http://console.teenpattiaura1.top/

9、给前端的地址
center地址：  https://www.teenpattiaura1.top/center
隐私协议地址：  https://www.teenpattiaura1.top/privacy.html





### (2023.9.06) (TODO) Rummy Apna
rummyapna
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummyapna
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.9.151（公）
172.16.2.71（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummyapna1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.rummyapna1.top/center
http://www.rummyapna1.top/privacy.html
http://console.rummyapna1.top/

9、给前端的地址
center地址：  https://www.rummyapna1.top/center
隐私协议地址：  https://www.rummyapna1.top/privacy.html




### (2023.9.06) (TODO) TeenPatti Rang
teenpattirang
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattirang
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.11.232（公）
172.16.2.70（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattirang1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.teenpattirang1.top/center
http://www.teenpattirang1.top/privacy.html
http://console.teenpattirang1.top/

9、给前端的地址
center地址：  https://www.teenpattirang1.top/center
隐私协议地址：  https://www.teenpattirang1.top/privacy.html




### (2023.9.06) (TODO) Dragon Tiger Rush
dragontigerrush
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-dragontigerrush
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.173.143（公）
172.16.2.69（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
dragontigerrush1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.dragontigerrush1.top/center
http://www.dragontigerrush1.top/privacy.html
http://console.dragontigerrush1.top/

9、给前端的地址
center地址：  https://www.dragontigerrush1.top/center
隐私协议地址：  https://www.dragontigerrush1.top/privacy.html





### (2023.9.06) (TODO) Super 3Patti
super3patti
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-super3patti
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.6.194（公）
172.16.2.68（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
super3patti1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.super3patti1.top/center
http://www.super3patti1.top/privacy.html
http://console.super3patti1.top/

9、给前端的地址
center地址：  https://www.super3patti1.top/center
隐私协议地址：  https://www.super3patti1.top/privacy.html




### (2023.9.06) (TODO) Rummy Lotus
rummylotus
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummylotus
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.4.128（公）
172.16.2.67（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummylotus1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.rummylotus1.top/center
http://www.rummylotus1.top/privacy.html
http://console.rummylotus1.top/

9、给前端的地址
center地址：  https://www.rummylotus1.top/center
隐私协议地址：  https://www.rummylotus1.top/privacy.html



### (2023.9.06) (TODO) TeenPatti Get Online
tpgetonline
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-tpgetonline
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.3.244（公）
172.16.2.66（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
tpgetonline1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.tpgetonline1.top/center
http://www.tpgetonline1.top/privacy.html
http://console.tpgetonline1.top/

9、给前端的地址
center地址：  https://www.tpgetonline1.top/center
隐私协议地址：  https://www.tpgetonline1.top/privacy.html



### (2023.9.06) (TODO) Crazy Slots
crazyslots
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-crazyslots
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.4.240（公）
172.16.2.65（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
crazyslots1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.crazyslots1.top/center
http://www.crazyslots1.top/privacy.html
http://console.crazyslots1.top/

9、给前端的地址
center地址：  https://www.crazyslots1.top/center
隐私协议地址：  https://www.crazyslots1.top/privacy.html



### (2023.9.06) (TODO) TeenPatti BlitzX
teenpattiblitzx
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattiblitzx
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.9.104（公）
172.16.2.64（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattiblitzx1.top

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.teenpattiblitzx1.top/center
http://www.teenpattiblitzx1.top/privacy.html
http://console.teenpattiblitzx1.top/

9、给前端的地址
center地址：  https://www.teenpattiblitzx1.top/center
隐私协议地址：  https://www.teenpattiblitzx1.top/privacy.html





### (2023.8.30) (TODO) Tiger Power
tigerpower
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-tigerpower
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.9.190（公）
172.16.2.7（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
tigerpower1.com

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.tigerpower1.com/center
http://www.tigerpower1.com/privacy.html
http://console.tigerpower1.com/

9、给前端的地址
center地址：  https://www.tigerpower1.com/center
隐私协议地址：  https://www.tigerpower1.com/privacy.html



### (2023.8.30) (TODO) Wild Dragon
wilddragon
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-wilddragon
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.1.152（公）
172.16.2.3（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
wilddragon1.com

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.wilddragon1.com/center
http://www.wilddragon1.com/privacy.html
http://console.wilddragon1.com/

9、给前端的地址
center地址：  https://www.wilddragon1.com/center
隐私协议地址：  https://www.wilddragon1.com/privacy.html




### (2023.8.30) (TODO) Rummy King
rummyking
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummyking
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.4.90（公）
172.16.2.1（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummyking1.com

5、设置子域名解析
console
kefu
www

6、管理后台添加渠道

7、Postman 增加渠道配置

8、测试网络连通性
http://www.rummyking1.com/center
http://www.rummyking1.com/privacy.html
http://console.rummyking1.com/

9、给前端的地址
center地址：  https://www.rummyking1.com/center
隐私协议地址：  https://www.rummyking1.com/privacy.html



### (2023.8.12) (TODO) 777 Fortunes Slots
fortunesslots
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-fortunesslots
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.3.251（公）
172.16.2.63（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
fortunesslots1.com

5、设置子域名解析
console
kefu
www

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.fortunesslots1.com/center
http://www.fortunesslots1.com/privacy.html
http://console.fortunesslots1.com/

10、给前端的地址
center地址：  https://www.fortunesslots1.com/center
隐私协议地址：  https://www.fortunesslots1.com/privacy.html





### (2023.8.12) (TODO) Diamond Slots
diamondslots
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-diamondslots
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.10.54（公）
172.16.2.62（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
diamondslots1.com

5、设置子域名解析
console
kefu
www

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.diamondslots1.com/center
http://www.diamondslots1.com/privacy.html
http://console.diamondslots1.com/

10、给前端的地址
center地址：  https://www.diamondslots1.com/center
隐私协议地址：  https://www.diamondslots1.com/privacy.html




### (2023.8.12) (TODO) Rummy Zone
rummyzonea
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummyzonea
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.7.78（公）
172.16.2.61（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummyzonea1.com

5、设置子域名解析
console
kefu
www

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummyzonea1.com/center
http://www.rummyzonea1.com/privacy.html
http://console.rummyzonea1.com/

10、给前端的地址
center地址：  https://www.rummyzonea1.com/center
隐私协议地址：  https://www.rummyzonea1.com/privacy.html





### (2023.8.12) (TODO) Rummy Royale
rummyroyale
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummyroyale
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.11.13（公）
172.16.2.60（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummyroyale1.com

5、设置子域名解析
console
kefu
www

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummyroyale1.com/center
http://www.rummyroyale1.com/privacy.html
http://console.rummyroyale1.com/

10、给前端的地址
center地址：  https://www.rummyroyale1.com/center
隐私协议地址：  https://www.rummyroyale1.com/privacy.html





### (2023.8.12) (TODO) TeenPatti Boost
teenpattiboosta
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattiboosta
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.6.2（公）
172.16.2.59（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattiboosta1.com

5、设置子域名解析
console
kefu
www

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattiboosta1.com/center
http://www.teenpattiboosta1.com/privacy.html
http://console.teenpattiboosta1.com/

10、给前端的地址
center地址：  https://www.teenpattiboosta1.com/center
隐私协议地址：  https://www.teenpattiboosta1.com/privacy.html





### (2023.8.12) (TODO) TeenPatti Battle
teenpattibattle
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattibattle
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	

147.139.11.75（公）
172.16.2.58（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattibattle1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattibattle1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattibattle1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattibattle1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattibattle1.com/center
http://www.teenpattibattle1.com/privacy.html
http://console.teenpattibattle1.com/

10、给前端的地址
center地址：  https://www.teenpattibattle1.com/center
隐私协议地址：  https://www.teenpattibattle1.com/privacy.html
CDN 地址：  http://hot.teenpattibattle1.com

CDN 测试连通性
http://hot.teenpattibattle1.com/hot_update/teentest/curVersion.txt




### (2023.8.12) (TODO) Dragon Tiger Diamond
dragontigerdiamond
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-dragontigerdiamond
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.173.75（公）
172.16.2.57（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
dragontigerdiamond1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.dragontigerdiamond1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.dragontigerdiamond1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.dragontigerdiamond1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.dragontigerdiamond1.com/center
http://www.dragontigerdiamond1.com/privacy.html
http://console.dragontigerdiamond1.com/

10、给前端的地址
center地址：  https://www.dragontigerdiamond1.com/center
隐私协议地址：  https://www.dragontigerdiamond1.com/privacy.html
CDN 地址：  http://hot.dragontigerdiamond1.com

CDN 测试连通性
http://hot.dragontigerdiamond1.com/hot_update/teentest/curVersion.txt



### (2023.8.12) (TODO) Dragon Tiger Thunder
dragontigerthunder
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-dragontigerthunder
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.9.86（公）
172.16.2.56（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
dragontigerthunder1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.dragontigerthunder1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.dragontigerthunder1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.dragontigerthunder1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.dragontigerthunder1.com/center
http://www.dragontigerthunder1.com/privacy.html
http://console.dragontigerthunder1.com/

10、给前端的地址
center地址：  https://www.dragontigerthunder1.com/center
隐私协议地址：  https://www.dragontigerthunder1.com/privacy.html
CDN 地址：  http://hot.dragontigerthunder1.com

CDN 测试连通性
http://hot.dragontigerthunder1.com/hot_update/teentest/curVersion.txt




### (2023.8.12) (TODO) Dragon Tiger Battle
dragontigerbattle
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-dragontigerbattle
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.9.110（公）
172.16.2.55（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
dragontigerbattle1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.dragontigerbattle1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.dragontigerbattle1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.dragontigerbattle1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.dragontigerbattle1.com/center
http://www.dragontigerbattle1.com/privacy.html
http://console.dragontigerbattle1.com/

10、给前端的地址
center地址：  https://www.dragontigerbattle1.com/center
隐私协议地址：  https://www.dragontigerbattle1.com/privacy.html
CDN 地址：  http://hot.dragontigerbattle1.com

CDN 测试连通性
http://hot.dragontigerbattle1.com/hot_update/teentest/curVersion.txt





### (2023.8.4) (TODO) Rummy Pro
rummypro
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummypro
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.10.25（公）
172.16.2.54（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummypro1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummypro1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummypro1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.rummypro1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummypro1.com/center
http://www.rummypro1.com/privacy.html
http://console.rummypro1.com/

10、给前端的地址
center地址：  https://www.rummypro1.com/center
隐私协议地址：  https://www.rummypro1.com/privacy.html
CDN 地址：  http://hot.rummypro1.com

CDN 测试连通性
http://hot.rummypro1.com/hot_update/teentest/curVersion.txt



### (2023.8.4) (TODO) TeenPatti Bolt
teenpattibolt
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattibolt
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.10.155（公）
172.16.2.53（私有）





3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattibolt1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattibolt1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattibolt1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattibolt1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattibolt1.com/center
http://www.teenpattibolt1.com/privacy.html
http://console.teenpattibolt1.com/

10、给前端的地址
center地址：  https://www.teenpattibolt1.com/center
隐私协议地址：  https://www.teenpattibolt1.com/privacy.html
CDN 地址：  http://hot.teenpattibolt1.com

CDN 测试连通性
http://hot.teenpattibolt1.com/hot_update/teentest/curVersion.txt



### (2023.8.4) (TODO) TeenPatti Live
teenpattilive
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattilive
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.8.136（公）
172.16.2.52（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattilive1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattilive1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattilive1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattilive1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattilive1.com/center
http://www.teenpattilive1.com/privacy.html
http://console.teenpattilive1.com/

10、给前端的地址
center地址：  https://www.teenpattilive1.com/center
隐私协议地址：  https://www.teenpattilive1.com/privacy.html
CDN 地址：  http://hot.teenpattilive1.com

CDN 测试连通性
http://hot.teenpattilive1.com/hot_update/teentest/curVersion.txt




### (2023.8.4) (TODO) TeenPatti Boom
teenpattiboom
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattiboom
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
149.129.175.80（公）
172.16.2.51（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattiboom1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattiboom1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattiboom1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattiboom1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattiboom1.com/center
http://www.teenpattiboom1.com/privacy.html
http://console.teenpattiboom1.com/

10、给前端的地址
center地址：  https://www.teenpattiboom1.com/center
隐私协议地址：  https://www.teenpattiboom1.com/privacy.html
CDN 地址：  http://hot.teenpattiboom1.com

CDN 测试连通性
http://hot.teenpattiboom1.com/hot_update/teentest/curVersion.txt





### (2023.8.4) (TODO) Dragon Tiger Nova
dragontigernova
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-dragontigernova
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.6.236（公）
172.16.2.50（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
dragontigernova1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.dragontigernova1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.dragontigernova1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.dragontigernova1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.dragontigernova1.com/center
http://www.dragontigernova1.com/privacy.html
http://console.dragontigernova1.com/

10、给前端的地址
center地址：  https://www.dragontigernova1.com/center
隐私协议地址：  https://www.dragontigernova1.com/privacy.html
CDN 地址：  http://hot.dragontigernova1.com

CDN 测试连通性
http://hot.dragontigernova1.com/hot_update/teentest/curVersion.txt




### (2023.8.4) (TODO) Dragon Tiger Peak
dragontigerpeak
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-dragontigerpeak
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
149.129.167.172（公）
172.16.2.49（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
dragontigerpeak1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.dragontigerpeak1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.dragontigerpeak1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.dragontigerpeak1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.dragontigerpeak1.com/center
http://www.dragontigerpeak1.com/privacy.html
http://console.dragontigerpeak1.com/

10、给前端的地址
center地址：  https://www.dragontigerpeak1.com/center
隐私协议地址：  https://www.dragontigerpeak1.com/privacy.html
CDN 地址：  http://hot.dragontigerpeak1.com

CDN 测试连通性
http://hot.dragontigerpeak1.com/hot_update/teentest/curVersion.txt






### (2023.8.4) (TODO) Slots Match
slotsmatch
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-slotsmatch
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.1.198（公）
172.16.2.48（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
slotsmatch1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.slotsmatch1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.slotsmatch1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.slotsmatch1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.slotsmatch1.com/center
http://www.slotsmatch1.com/privacy.html
http://console.slotsmatch1.com/

10、给前端的地址
center地址：  https://www.slotsmatch1.com/center
隐私协议地址：  https://www.slotsmatch1.com/privacy.html
CDN 地址：  http://hot.slotsmatch1.com

CDN 测试连通性
http://hot.slotsmatch1.com/hot_update/teentest/curVersion.txt





### (2023.8.3) (TODO) Rummy Rise
rummyrise
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummyrise
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.6.156（公）
172.16.2.47（私有）





3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummyrise1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummyrise1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummyrise1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.rummyrise1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummyrise1.com/center
http://www.rummyrise1.com/privacy.html
http://console.rummyrise1.com/

10、给前端的地址
center地址：  https://www.rummyrise1.com/center
隐私协议地址：  https://www.rummyrise1.com/privacy.html
CDN 地址：  http://hot.rummyrise1.com

CDN 测试连通性
http://hot.rummyrise1.com/hot_update/teentest/curVersion.txt



### (2023.7.27) (TODO) Rummy Zenith
rummyzenith
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummyzenith
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.9.142（公）
172.16.2.46（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummyzenith1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummyzenith1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummyzenith1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.rummyzenith1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummyzenith1.com/center
http://www.rummyzenith1.com/privacy.html
http://console.rummyzenith1.com/

10、给前端的地址
center地址：  https://www.rummyzenith1.com/center
隐私协议地址：  https://www.rummyzenith1.com/privacy.html
CDN 地址：  http://hot.rummyzenith1.com

CDN 测试连通性
http://hot.rummyzenith1.com/hot_update/teentest/curVersion.txt






### (2023.7.27) (TODO) TeenPatti Triumph
teenpattitriumph
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattitriumph
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.8.55（公）
172.16.2.45（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattitriumph1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattitriumph1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattitriumph1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattitriumph1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattitriumph1.com/center
http://www.teenpattitriumph1.com/privacy.html
http://console.teenpattitriumph1.com/

10、给前端的地址
center地址：  https://www.teenpattitriumph1.com/center
隐私协议地址：  https://www.teenpattitriumph1.com/privacy.html
CDN 地址：  http://hot.teenpattitriumph1.com

CDN 测试连通性
http://hot.teenpattitriumph1.com/hot_update/teentest/curVersion.txt




### (2023.7.27) (TODO) TeenPatti Challenge
teenpattichallenge
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattichallenge
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.3.234（公）
172.16.2.44（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattichallenge1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattichallenge1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattichallenge1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattichallenge1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattichallenge1.com/center
http://www.teenpattichallenge1.com/privacy.html
http://console.teenpattichallenge1.com/

10、给前端的地址
center地址：  https://www.teenpattichallenge1.com/center
隐私协议地址：  https://www.teenpattichallenge1.com/privacy.html
CDN 地址：  http://hot.teenpattichallenge1.com

CDN 测试连通性
http://hot.teenpattichallenge1.com/hot_update/teentest/curVersion.txt




### (2023.7.27) (TODO) TeenPatti Storm
teenpattistorma
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattistorma
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
149.129.173.199（公）
172.16.2.43（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattistorma1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattistorma1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattistorma1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattistorma1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattistorma1.com/center
http://www.teenpattistorma1.com/privacy.html
http://console.teenpattistorma1.com/

10、给前端的地址
center地址：  https://www.teenpattistorma1.com/center
隐私协议地址：  https://www.teenpattistorma1.com/privacy.html
CDN 地址：  http://hot.teenpattistorma1.com

CDN 测试连通性
http://hot.teenpattistorma1.com/hot_update/teentest/curVersion.txt




### (2023.7.27) (TODO) Rummy Bodhi
rummybodhi
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummybodhi
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.9.134（公）
172.16.2.42（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummybodhi1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummybodhi1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummybodhi1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.rummybodhi1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummybodhi1.com/center
http://www.rummybodhi1.com/privacy.html
http://console.rummybodhi1.com/

10、给前端的地址
center地址：  https://www.rummybodhi1.com/center
隐私协议地址：  https://www.rummybodhi1.com/privacy.html
CDN 地址：  http://hot.rummybodhi1.com

CDN 测试连通性
http://hot.rummybodhi1.com/hot_update/teentest/curVersion.txt




### (2023.7.27) (TODO) TeenPatti Dynasty
teenpattidynasty
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattidynasty
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.3.221（公）
172.16.2.7（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattidynasty1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattidynasty1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattidynasty1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattidynasty1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattidynasty1.com/center
http://www.teenpattidynasty1.com/privacy.html
http://console.teenpattidynasty1.com/

10、给前端的地址
center地址：  https://www.teenpattidynasty1.com/center
隐私协议地址：  https://www.teenpattidynasty1.com/privacy.html
CDN 地址：  http://hot.teenpattidynasty1.com

CDN 测试连通性
http://hot.teenpattidynasty1.com/hot_update/teentest/curVersion.txt




### (2023.7.27) (TODO) TeenPatti Feral
teenpattiferal
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattiferal
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.8.179（公）
172.16.2.3（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattiferal1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattiferal1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattiferal1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattiferal1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattiferal1.com/center
http://www.teenpattiferal1.com/privacy.html
http://console.teenpattiferal1.com/

10、给前端的地址
center地址：  https://www.teenpattiferal1.com/center
隐私协议地址：  https://www.teenpattiferal1.com/privacy.html
CDN 地址：  http://hot.teenpattiferal1.com

CDN 测试连通性
http://hot.teenpattiferal1.com/hot_update/teentest/curVersion.txt




### (2023.7.27) (TODO) Rummy Flash
rummyflash
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummyflash
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.6.239（公）
172.16.2.1（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummyflash1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummyflash1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummyflash1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.rummyflash1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummyflash1.com/center
http://www.rummyflash1.com/privacy.html
http://console.rummyflash1.com/

10、给前端的地址
center地址：  https://www.rummyflash1.com/center
隐私协议地址：  https://www.rummyflash1.com/privacy.html
CDN 地址：  http://hot.rummyflash1.com

CDN 测试连通性
http://hot.rummyflash1.com/hot_update/teentest/curVersion.txt




### (2023.7.21) (TODO) Crazy Rummy
crazyrummy
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-crazyrummy
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.3.220(公)
172.16.2.41(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
crazyrummy1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.crazyrummy1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.crazyrummy1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.crazyrummy1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.crazyrummy1.com/center
http://www.crazyrummy1.com/privacy.html
http://console.crazyrummy1.com/

10、给前端的地址
center地址：  https://www.crazyrummy1.com/center
隐私协议地址：  https://www.crazyrummy1.com/privacy.html
CDN 地址：  http://hot.crazyrummy1.com

CDN 测试连通性
http://hot.crazyrummy1.com/hot_update/teentest/curVersion.txt




### (2023.7.21) (TODO) TeenPatti Swift
teenpattiswift
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattiswift
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.8.114(公)
172.16.2.40(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattiswift1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattiswift1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattiswift1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattiswift1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattiswift1.com/center
http://www.teenpattiswift1.com/privacy.html
http://console.teenpattiswift1.com/

10、给前端的地址
center地址：  https://www.teenpattiswift1.com/center
隐私协议地址：  https://www.teenpattiswift1.com/privacy.html
CDN 地址：  http://hot.teenpattiswift1.com

CDN 测试连通性
http://hot.teenpattiswift1.com/hot_update/teentest/curVersion.txt




### (2023.7.21) (TODO) TeenPatti Star
teenpattistar
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattistar
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.167.28(公)
172.16.2.39(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattistar1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattistar1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattistar1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattistar1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattistar1.com/center
http://www.teenpattistar1.com/privacy.html
http://console.teenpattistar1.com/

10、给前端的地址
center地址：  https://www.teenpattistar1.com/center
隐私协议地址：  https://www.teenpattistar1.com/privacy.html
CDN 地址：  http://hot.teenpattistar1.com

CDN 测试连通性
http://hot.teenpattistar1.com/hot_update/teentest/curVersion.txt




### (2023.7.21) (TODO) Rummy Blaze
rummyblaze
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummyblaze
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.7.15(公)
172.16.2.38(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummyblaze1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummyblaze1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummyblaze1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.rummyblaze1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummyblaze1.com/center
http://www.rummyblaze1.com/privacy.html
http://console.rummyblaze1.com/

10、给前端的地址
center地址：  https://www.rummyblaze1.com/center
隐私协议地址：  https://www.rummyblaze1.com/privacy.html
CDN 地址：  http://hot.rummyblaze1.com

CDN 测试连通性
http://hot.rummyblaze1.com/hot_update/teentest/curVersion.txt



### (2023.7.21) (TODO) Rummy Ace
rummyace
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummyace
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.3.18(公)
172.16.2.37(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummyace1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummyace1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummyace1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.rummyace1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummyace1.com/center
http://www.rummyace1.com/privacy.html
http://console.rummyace1.com/

10、给前端的地址
center地址：  https://www.rummyace1.com/center
隐私协议地址：  https://www.rummyace1.com/privacy.html
CDN 地址：  http://hot.rummyace1.com

CDN 测试连通性
http://hot.rummyace1.com/hot_update/teentest/curVersion.txt




### (2023.7.21) (TODO) Rummy Diamond
rummydiamond
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummydiamond
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.7.173(公)
172.16.2.36(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummydiamond1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummydiamond1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummydiamond1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.rummydiamond1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummydiamond1.com/center
http://www.rummydiamond1.com/privacy.html
http://console.rummydiamond1.com/

10、给前端的地址
center地址：  https://www.rummydiamond1.com/center
隐私协议地址：  https://www.rummydiamond1.com/privacy.html
CDN 地址：  http://hot.rummydiamond1.com

CDN 测试连通性
http://hot.rummydiamond1.com/hot_update/teentest/curVersion.txt





### (2023.7.21) (TODO) TeenPatti ArenaX
teenpattiarenax
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattiarenax
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.6.182(公)
172.16.2.35(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattiarenax1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattiarenax1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattiarenax1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattiarenax1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattiarenax1.com/center
http://www.teenpattiarenax1.com/privacy.html
http://console.teenpattiarenax1.com/

10、给前端的地址
center地址：  https://www.teenpattiarenax1.com/center
隐私协议地址：  https://www.teenpattiarenax1.com/privacy.html
CDN 地址：  http://hot.teenpattiarenax1.com

CDN 测试连通性
http://hot.teenpattiarenax1.com/hot_update/teentest/curVersion.txt







### (2023.7.21) (TODO) TeenPatti Bodhi
teenpattibodhi
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattibodhi
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.8.199(公)
172.16.2.34(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattibodhi1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattibodhi1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattibodhi1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattibodhi1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattibodhi1.com/center
http://www.teenpattibodhi1.com/privacy.html
http://console.teenpattibodhi1.com/

10、给前端的地址
center地址：  https://www.teenpattibodhi1.com/center
隐私协议地址：  https://www.teenpattibodhi1.com/privacy.html
CDN 地址：  http://hot.teenpattibodhi1.com

CDN 测试连通性
http://hot.teenpattibodhi1.com/hot_update/teentest/curVersion.txt





### (2023.7.14) (TODO) TeenPatti Gladiator
teenpattigladiator
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattigladiator
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.3.27(公)
172.16.2.33(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattigladiator1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattigladiator1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattigladiator1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattigladiator1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattigladiator1.com/center
http://www.teenpattigladiator1.com/privacy.html
http://console.teenpattigladiator1.com/

10、给前端的地址
center地址：  https://www.teenpattigladiator1.com/center
隐私协议地址：  https://www.teenpattigladiator1.com/privacy.html
CDN 地址：  http://hot.teenpattigladiator1.com

CDN 测试连通性
http://hot.teenpattigladiator1.com/hot_update/teentest/curVersion.txt




### (2023.7.14) (TODO) TeenPatti Go
teenpattigo
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattigo
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.3.163(公)
172.16.2.32(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattigo1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattigo1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattigo1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattigo1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattigo1.com/center
http://www.teenpattigo1.com/privacy.html
http://console.teenpattigo1.com/

10、给前端的地址
center地址：  https://www.teenpattigo1.com/center
隐私协议地址：  https://www.teenpattigo1.com/privacy.html
CDN 地址：  http://hot.teenpattigo1.com

CDN 测试连通性
http://hot.teenpattigo1.com/hot_update/teentest/curVersion.txt





### (2023.7.14) (TODO) TeenPatti Flare
teenpattiflare
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattiflare
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.4.170(公)
172.16.2.31(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattiflare1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattiflare1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattiflare1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattiflare1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattiflare1.com/center
http://www.teenpattiflare1.com/privacy.html
http://console.teenpattiflare1.com/

10、给前端的地址
center地址：  https://www.teenpattiflare1.com/center
隐私协议地址：  https://www.teenpattiflare1.com/privacy.html
CDN 地址：  http://hot.teenpattiflare1.com

CDN 测试连通性
http://hot.teenpattiflare1.com/hot_update/teentest/curVersion.txt




### (2023.7.14) (TODO) TeenPatti Nitro
teenpattinitro
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattinitro
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.8.14(公)
172.16.2.30(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattinitro1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattinitro1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattinitro1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattinitro1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattinitro1.com/center
http://www.teenpattinitro1.com/privacy.html
http://console.teenpattinitro1.com/

10、给前端的地址
center地址：  https://www.teenpattinitro1.com/center
隐私协议地址：  https://www.teenpattinitro1.com/privacy.html
CDN 地址：  http://hot.teenpattinitro1.com

CDN 测试连通性
http://hot.teenpattinitro1.com/hot_update/teentest/curVersion.txt




### (2023.7.14) (TODO) TeenPatti Madness
teenpattimadness
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattimadness
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.3.156(公)
**********9(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattimadness1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattimadness1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattimadness1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattimadness1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattimadness1.com/center
http://www.teenpattimadness1.com/privacy.html
http://console.teenpattimadness1.com/

10、给前端的地址
center地址：  https://www.teenpattimadness1.com/center
隐私协议地址：  https://www.teenpattimadness1.com/privacy.html
CDN 地址：  http://hot.teenpattimadness1.com

CDN 测试连通性
http://hot.teenpattimadness1.com/hot_update/teentest/curVersion.txt




### (2023.7.14) (TODO) TeenPatti Momentum
teenpattimomentum
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattimomentum
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
149.129.167.117(公)
**********8(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattimomentum1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattimomentum1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattimomentum1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattimomentum1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattimomentum1.com/center
http://www.teenpattimomentum1.com/privacy.html
http://console.teenpattimomentum1.com/

10、给前端的地址
center地址：  https://www.teenpattimomentum1.com/center
隐私协议地址：  https://www.teenpattimomentum1.com/privacy.html
CDN 地址：  http://hot.teenpattimomentum1.com

CDN 测试连通性
http://hot.teenpattimomentum1.com/hot_update/teentest/curVersion.txt




### (2023.7.14) (TODO) TeenPatti Nexus
teenpattinexus
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattinexus
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.81.83(公)
**********7(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattinexus1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattinexus1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattinexus1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattinexus1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattinexus1.com/center
http://www.teenpattinexus1.com/privacy.html
http://console.teenpattinexus1.com/

10、给前端的地址
center地址：  https://www.teenpattinexus1.com/center
隐私协议地址：  https://www.teenpattinexus1.com/privacy.html
CDN 地址：  http://hot.teenpattinexus1.com

CDN 测试连通性
http://hot.teenpattinexus1.com/hot_update/teentest/curVersion.txt




### (2023.7.14) (TODO) Rummy Revolution
rummyrevolution
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummyrevolution
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.44.158(公)
**********6(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummyrevolution1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummyrevolution1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummyrevolution1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.rummyrevolution1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummyrevolution1.com/center
http://www.rummyrevolution1.com/privacy.html
http://console.rummyrevolution1.com/

10、给前端的地址
center地址：  https://www.rummyrevolution1.com/center
隐私协议地址：  https://www.rummyrevolution1.com/privacy.html
CDN 地址：  http://hot.rummyrevolution1.com

CDN 测试连通性
http://hot.rummyrevolution1.com/hot_update/teentest/curVersion.txt



### (2023.7.14) (TODO) TeenPatti JumpX
teenpattijumpx
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattijumpx
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.41.187(公)
172.16.2.4(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattijumpx1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattijumpx1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattijumpx1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattijumpx1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattijumpx1.com/center
http://www.teenpattijumpx1.com/privacy.html
http://console.teenpattijumpx1.com/

10、给前端的地址
center地址：  https://www.teenpattijumpx1.com/center
隐私协议地址：  https://www.teenpattijumpx1.com/privacy.html
CDN 地址：  http://hot.teenpattijumpx1.com

CDN 测试连通性
http://hot.teenpattijumpx1.com/hot_update/teentest/curVersion.txt



### (2023.7.14) (TODO) TeenPatti Lightning
teenpattilightning
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattilightning
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.107.249(公)
**********52(私有)



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattilightning1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattilightning1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattilightning1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattilightning1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattilightning1.com/center
http://www.teenpattilightning1.com/privacy.html
http://console.teenpattilightning1.com/

10、给前端的地址
center地址：  https://www.teenpattilightning1.com/center
隐私协议地址：  https://www.teenpattilightning1.com/privacy.html
CDN 地址：  http://hot.teenpattilightning1.com

CDN 测试连通性
http://hot.teenpattilightning1.com/hot_update/teentest/curVersion.txt




### (2023.7.07) (TODO) teenpattilotusa
teenpattilotusa
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattilotusa
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.4.24（公）
**********5（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattilotusa1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattilotusa1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattilotusa1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattilotusa1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattilotusa1.com/center
http://www.teenpattilotusa1.com/privacy.html
http://console.teenpattilotusa1.com/

10、给前端的地址
center地址：  https://www.teenpattilotusa1.com/center
隐私协议地址：  https://www.teenpattilotusa1.com/privacy.html
CDN 地址：  http://hot.teenpattilotusa1.com

CDN 测试连通性
http://hot.teenpattilotusa1.com/hot_update/teentest/curVersion.txt




### (2023.7.13 THU) (TODO) rummymaster
rummymaster
com.rummy.master.ji

1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummymaster
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.1.115(公)
**********51(私有)




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummymaster2.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummymaster2.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummymaster2.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.rummymaster2.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummymaster2.com/center
http://www.rummymaster2.com/privacy.html
http://console.rummymaster2.com/

10、给前端的地址
center地址：  https://www.rummymaster2.com/center
隐私协议地址：  https://www.rummymaster2.com/privacy.html
CDN 地址：  http://hot.rummymaster2.com

CDN 测试连通性
http://hot.rummymaster2.com/hot_update/teentest/curVersion.txt



### (2023.7.07) (TODO) teenpattilotus
teenpattilotus
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattilotus
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
149.129.167.169（公）
**********4（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattilotus1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattilotus1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattilotus1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattilotus1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattilotus1.com/center
http://www.teenpattilotus1.com/privacy.html
http://console.teenpattilotus1.com/

10、给前端的地址
center地址：  https://www.teenpattilotus1.com/center
隐私协议地址：  https://www.teenpattilotus1.com/privacy.html
CDN 地址：  http://hot.teenpattilotus1.com

CDN 测试连通性
http://hot.teenpattilotus1.com/hot_update/teentest/curVersion.txt



### (2023.7.07) (TODO) rummypower
rummypower
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummypower
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.8.49（公）
**********3（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummypower1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummypower1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummypower1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.rummypower1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummypower1.com/center
http://www.rummypower1.com/privacy.html
http://console.rummypower1.com/

10、给前端的地址
center地址：  https://www.rummypower1.com/center
隐私协议地址：  https://www.rummypower1.com/privacy.html
CDN 地址：  http://hot.rummypower1.com

CDN 测试连通性
http://hot.rummypower1.com/hot_update/teentest/curVersion.txt



### (2023.7.07) (TODO) rummyprecision
rummyprecision
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummyprecision
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
149.129.167.20（公）
**********2（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummyprecision1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummyprecision1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummyprecision1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.rummyprecision1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummyprecision1.com/center
http://www.rummyprecision1.com/privacy.html
http://console.rummyprecision1.com/

10、给前端的地址
center地址：  https://www.rummyprecision1.com/center
隐私协议地址：  https://www.rummyprecision1.com/privacy.html
CDN 地址：  http://hot.rummyprecision1.com

CDN 测试连通性
http://hot.rummyprecision1.com/hot_update/teentest/curVersion.txt



### (2023.7.07) (TODO) teenpattiplatinum
teenpattiplatinum
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattiplatinum
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
*************（公）
**********1（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattiplatinum1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattiplatinum1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattiplatinum1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattiplatinum1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattiplatinum1.com/center
http://www.teenpattiplatinum1.com/privacy.html
http://console.teenpattiplatinum1.com/

10、给前端的地址
center地址：  https://www.teenpattiplatinum1.com/center
隐私协议地址：  https://www.teenpattiplatinum1.com/privacy.html
CDN 地址：  http://hot.teenpattiplatinum1.com

CDN 测试连通性
http://hot.teenpattiplatinum1.com/hot_update/teentest/curVersion.txt



### (2023.7.07) (TODO) teenpattipursuit
teenpattipursuit
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattipursuit
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
**************（公）
**********0（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattipursuit1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattipursuit1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattipursuit1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattipursuit1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattipursuit1.com/center
http://www.teenpattipursuit1.com/privacy.html
http://console.teenpattipursuit1.com/

10、给前端的地址
center地址：  https://www.teenpattipursuit1.com/center
隐私协议地址：  https://www.teenpattipursuit1.com/privacy.html
CDN 地址：  http://hot.teenpattipursuit1.com

CDN 测试连通性
http://hot.teenpattipursuit1.com/hot_update/teentest/curVersion.txt



### (2023.7.07) (TODO) teenpattisilver
teenpattisilver
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattisilver
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
************（公）
***********（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattisilver1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattisilver1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattisilver1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattisilver1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattisilver1.com/center
http://www.teenpattisilver1.com/privacy.html
http://console.teenpattisilver1.com/

10、给前端的地址
center地址：  https://www.teenpattisilver1.com/center
隐私协议地址：  https://www.teenpattisilver1.com/privacy.html
CDN 地址：  http://hot.teenpattisilver1.com

CDN 测试连通性
http://hot.teenpattisilver1.com/hot_update/teentest/curVersion.txt



### (2023.7.07) (TODO) teenpattiinfinity
teenpattiinfinity
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattiinfinity
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.8.79（公）
172.16.2.18（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattiinfinity1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattiinfinity1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattiinfinity1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattiinfinity1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattiinfinity1.com/center
http://www.teenpattiinfinity1.com/privacy.html
http://console.teenpattiinfinity1.com/

10、给前端的地址
center地址：  https://www.teenpattiinfinity1.com/center
隐私协议地址：  https://www.teenpattiinfinity1.com/privacy.html
CDN 地址：  http://hot.teenpattiinfinity1.com

CDN 测试连通性
http://hot.teenpattiinfinity1.com/hot_update/teentest/curVersion.txt



### (2023.7.07) (TODO) teenpattiinsanity
teenpattiinsanity
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattiinsanity
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.175.19（公）
172.16.2.17（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattiinsanity1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattiinsanity1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattiinsanity1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattiinsanity1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattiinsanity1.com/center
http://www.teenpattiinsanity1.com/privacy.html
http://console.teenpattiinsanity1.com/

10、给前端的地址
center地址：  https://www.teenpattiinsanity1.com/center
隐私协议地址：  https://www.teenpattiinsanity1.com/privacy.html
CDN 地址：  http://hot.teenpattiinsanity1.com

CDN 测试连通性
http://hot.teenpattiinsanity1.com/hot_update/teentest/curVersion.txt



### (2023.7.07) (TODO) teenpattihorizon
teenpattihorizon
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattihorizon
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.1.92（公）
172.16.2.16（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattihorizon1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattihorizon1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattihorizon1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.teenpattihorizon1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattihorizon1.com/center
http://www.teenpattihorizon1.com/privacy.html
http://console.teenpattihorizon1.com/

10、给前端的地址
center地址：  https://www.teenpattihorizon1.com/center
隐私协议地址：  https://www.teenpattihorizon1.com/privacy.html
CDN 地址：  http://hot.teenpattihorizon1.com

CDN 测试连通性
http://hot.teenpattihorizon1.com/hot_update/teentest/curVersion.txt



### (2023.7.07) (TODO) rummypace
rummypace
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummypace
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.8.41（公）
172.16.2.15（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummypace1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummypace1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummypace1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.rummypace1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummypace1.com/center
http://www.rummypace1.com/privacy.html
http://console.rummypace1.com/

10、给前端的地址
center地址：  https://www.rummypace1.com/center
隐私协议地址：  https://www.rummypace1.com/privacy.html
CDN 地址：  http://hot.rummypace1.com

CDN 测试连通性
http://hot.rummypace1.com/hot_update/teentest/curVersion.txt




### (2023.7.06) (TODO) tpkhazana
tpkhazana
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-tpkhazana
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.3.15（公）
172.16.2.14（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
tpkhazana1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.tpkhazana1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.tpkhazana1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.tpkhazana1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.tpkhazana1.com/center
http://www.tpkhazana1.com/privacy.html
http://console.tpkhazana1.com/

10、给前端的地址
center地址：  https://www.tpkhazana1.com/center
隐私协议地址：  https://www.tpkhazana1.com/privacy.html
CDN 地址：  http://hot.tpkhazana1.com

CDN 测试连通性
http://hot.tpkhazana1.com/hot_update/teentest/curVersion.txt







### (2023.7.06) (TODO) rummyblitza
rummyblitza
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummyblitza
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.8.16（公）
172.16.2.13（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummyblitza1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummyblitza1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummyblitza1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名 --> hot.rummyblitza1.com
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummyblitza1.com/center
http://www.rummyblitza1.com/privacy.html
http://console.rummyblitza1.com/

10、给前端的地址
center地址：  https://www.rummyblitza1.com/center
隐私协议地址：  https://www.rummyblitza1.com/privacy.html
CDN 地址：  http://hot.rummyblitza1.com

CDN 测试连通性
http://hot.rummyblitza1.com/hot_update/teentest/curVersion.txt




### (2023.7.03) (TODO) TeenPatti Orbit
teenpattiorbit
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattiorbit
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.44.238（公）
172.16.2.12（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattiorbit1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattiorbit1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattiorbit1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattiorbit1.com/center
http://www.teenpattiorbit1.com/privacy.html
http://console.teenpattiorbit1.com/

10、给前端的地址
center地址：  https://www.teenpattiorbit1.com/center
隐私协议地址：  https://www.teenpattiorbit1.com/privacy.html
CDN 地址：  http://hot.teenpattiorbit1.com

CDN 测试连通性
http://hot.teenpattiorbit1.com/hot_update/teentest/curVersion.txt





### (2023.7.03) (TODO) TeenPatti Gold
teenpattigold
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattigold
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.4.252（公）
172.16.2.11（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattigold1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattigold1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattigold1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattigold1.com/center
http://www.teenpattigold1.com/privacy.html
http://console.teenpattigold1.com/

10、给前端的地址
center地址：  https://www.teenpattigold1.com/center
隐私协议地址：  https://www.teenpattigold1.com/privacy.html
CDN 地址：  http://hot.teenpattigold1.com

CDN 测试连通性
http://hot.teenpattigold1.com/hot_update/teentest/curVersion.txt



### (2023.6.30) (TODO) TeenPatti Guide
teenpattiguide
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattiguide
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.32.72（公）
172.16.2.10（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattiguide1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattiguide1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattiguide1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattiguide1.com/center
http://www.teenpattiguide1.com/privacy.html
http://console.teenpattiguide1.com/

10、给前端的地址
center地址：  https://www.teenpattiguide1.com/center
隐私协议地址：  https://www.teenpattiguide1.com/privacy.html
CDN 地址：  http://hot.teenpattiguide1.com

CDN 测试连通性
http://hot.teenpattiguide1.com/hot_update/teentest/curVersion.txt






### (2023.6.30) (TODO) Rummy Playbook
rummyplaybook
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummyplaybook
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.127.152（公）
172.16.2.9（私有）




3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummyplaybook1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummyplaybook1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummyplaybook1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummyplaybook1.com/center
http://www.rummyplaybook1.com/privacy.html
http://console.rummyplaybook1.com/

10、给前端的地址
center地址：  https://www.rummyplaybook1.com/center
隐私协议地址：  https://www.rummyplaybook1.com/privacy.html
CDN 地址：  http://hot.rummyplaybook1.com

CDN 测试连通性
http://hot.rummyplaybook1.com/hot_update/teentest/curVersion.txt






### (2023.6.26) (TODO) Rummy Bolt
rummybolt
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummybolt
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.33.186（公）
172.16.2.8（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummybolt1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummybolt1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummybolt1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummybolt1.com/center
http://www.rummybolt1.com/privacy.html
http://console.rummybolt1.com/

10、给前端的地址
center地址：  https://www.rummybolt1.com/center
隐私协议地址：  https://www.rummybolt1.com/privacy.html
CDN 地址：  http://hot.rummybolt1.com

CDN 测试连通性
http://hot.rummybolt1.com/hot_update/teentest/curVersion.txt




### (2023.6.26) (TODO) TeenPatti Zenith
teenpattizenith
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattizenith
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
	
147.139.41.187（公）
172.16.2.7（私有）



3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattizenith1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattizenith1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattizenith1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattizenith1.com/center
http://www.teenpattizenith1.com/privacy.html
http://console.teenpattizenith1.com/

10、给前端的地址
center地址：  https://www.teenpattizenith1.com/center
隐私协议地址：  https://www.teenpattizenith1.com/privacy.html
CDN 地址：  http://hot.teenpattizenith1.com

CDN 测试连通性
http://hot.teenpattizenith1.com/hot_update/teentest/curVersion.txt




### (2023.6.26) (TODO) Rummy Craft
rummycraft
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummycraft
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.28.84（公）
172.16.2.6（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummycraft1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummycraft1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummycraft1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummycraft1.com/center
http://www.rummycraft1.com/privacy.html
http://console.rummycraft1.com/

10、给前端的地址
center地址：  https://www.rummycraft1.com/center
隐私协议地址：  https://www.rummycraft1.com/privacy.html
CDN 地址：  http://hot.rummycraft1.com

CDN 测试连通性
http://hot.rummycraft1.com/hot_update/teentest/curVersion.txt





### (2023.6.26) (TODO) TeenPatti Wild
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattiwild
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
147.139.29.160（公）
172.16.2.5（私有）


3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattiwild1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattiwild1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattiwild1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattiwild1.com/center
http://www.teenpattiwild1.com/privacy.html
http://console.teenpattiwild1.com/

10、给前端的地址
center地址：  https://www.teenpattiwild1.com/center
隐私协议地址：  https://www.teenpattiwild1.com/privacy.html
CDN 地址：  http://hot.teenpattiwild1.com

CDN 测试连通性
http://hot.teenpattiwild1.com/hot_update/teentest/curVersion.txt




## (2023.6.11) (TODO) rummyfiesta  
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummyfiesta
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
147.139.72.69(公)
172.16.2.4(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummyfiesta1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummyfiesta1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummyfiesta1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummyfiesta1.com/center
http://www.rummyfiesta1.com/privacy.html
http://console.rummyfiesta1.com/

10、给前端的地址
center地址：  https://www.rummyfiesta1.com/center
隐私协议地址：  https://www.rummyfiesta1.com/privacy.html
CDN 地址：  http://hot.rummyfiesta1.com

CDN 测试连通性
http://hot.rummyfiesta1.com/hot_update/teentest/curVersion.txt




### (2023.6.11) (TODO) rummytrek  
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummytrek
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
147.139.1.115(公)
172.16.2.3(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummytrek1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummytrek1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummytrek1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummytrek1.com/center
http://www.rummytrek1.com/privacy.html
http://console.rummytrek1.com/

10、给前端的地址
center地址：  https://www.rummytrek1.com/center
隐私协议地址：  https://www.rummytrek1.com/privacy.html
CDN 地址：  http://hot.rummytrek1.com

CDN 测试连通性
http://hot.rummytrek1.com/hot_update/teentest/curVersion.txt






### (2023.6.11) (TODO) teenpattiduel  
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattiduel
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
147.139.41.123(公)
**********(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattiduel.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattiduel.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattiduel.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattiduel.com/center
http://www.teenpattiduel.com/privacy.html
http://console.teenpattiduel.com/

10、给前端的地址
center地址：  https://www.teenpattiduel.com/center
隐私协议地址：  https://www.teenpattiduel.com/privacy.html
CDN 地址：  http://hot.teenpattiduel.com

CDN 测试连通性
http://hot.teenpattiduel.com/hot_update/teentest/curVersion.txt




### (2023.6.11) (TODO) rummysurf   
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummysurf 
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
149.129.167.117(公)
172.16.2.1(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummysurf1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummysurf1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummysurf1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummysurf1.com/center
http://www.rummysurf1.com/privacy.html
http://console.rummysurf1.com/

10、给前端的地址
center地址：  https://www.rummysurf1.com/center
隐私协议地址：  https://www.rummysurf1.com/privacy.html
CDN 地址：  http://hot.rummysurf1.com

CDN 测试连通性
http://hot.rummysurf1.com/hot_update/teentest/curVersion.txt



### (2023.6.11) (TODO) rummyvibe   
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummyvibe 
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
147.139.127.152(公)
**********52(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummyvibe1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummyvibe1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummyvibe1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummyvibe1.com/center
http://www.rummyvibe1.com/privacy.html
http://console.rummyvibe1.com/

10、给前端的地址
center地址：  https://www.rummyvibe1.com/center
隐私协议地址：  https://www.rummyvibe1.com/privacy.html
CDN 地址：  http://hot.rummyvibe1.com

CDN 测试连通性
http://hot.rummyvibe1.com/hot_update/teentest/curVersion.txt




### (2023.6.10) (TODO) rummyvolt   
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummyvolt 
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
147.139.5.92(公)
**********51(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummyvolt1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummyvolt1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummyvolt1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummyvolt1.com/center
http://www.rummyvolt1.com/privacy.html
http://console.rummyvolt1.com/

10、给前端的地址
center地址：  https://www.rummyvolt1.com/center
隐私协议地址：  https://www.rummyvolt1.com/privacy.html
CDN 地址：  http://hot.rummyvolt1.com

CDN 测试连通性
http://hot.rummyvolt1.com/hot_update/teentest/curVersion.txt





### (2023.6.10) (TODO) teenpattisteel   
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattisteel 
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.33.126(公)
**********50(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattisteel1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattisteel1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattisteel1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattisteel1.com/center
http://www.teenpattisteel1.com/privacy.html
http://console.teenpattisteel1.com/

10、给前端的地址
center地址：  https://www.teenpattisteel1.com/center
隐私协议地址：  https://www.teenpattisteel1.com/privacy.html
CDN 地址：  http://hot.teenpattisteel1.com

CDN 测试连通性
http://hot.teenpattisteel1.com/hot_update/teentest/curVersion.txt





### (2023.6.10) (TODO) teenpattidiamond   
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattidiamond 
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.172.176(公)
**********49(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattidiamond2.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattidiamond2.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattidiamond2.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattidiamond2.com/center
http://www.teenpattidiamond2.com/privacy.html
http://console.teenpattidiamond2.com/

10、给前端的地址
center地址：  https://www.teenpattidiamond2.com/center
隐私协议地址：  https://www.teenpattidiamond2.com/privacy.html
CDN 地址：  http://hot.teenpattidiamond2.com

CDN 测试连通性
http://hot.teenpattidiamond2.com/hot_update/teentest/curVersion.txt




### (2023.6.10) (TODO) teenpatticobalt   
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpatticobalt 
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.41.8(公)
**********48(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpatticobalt1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpatticobalt1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpatticobalt1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpatticobalt1.com/center
http://www.teenpatticobalt1.com/privacy.html
http://console.teenpatticobalt1.com/

10、给前端的地址
center地址：  https://www.teenpatticobalt1.com/center
隐私协议地址：  https://www.teenpatticobalt1.com/privacy.html
CDN 地址：  http://hot.teenpatticobalt1.com

CDN 测试连通性
http://hot.teenpatticobalt1.com/hot_update/teentest/curVersion.txt




### (2023.6.10) (TODO) teenpattirush   
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattiush 
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.32.72(公)
**********47(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattiush1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattiush1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattiush1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattiush1.com/center
http://www.teenpattiush1.com/privacy.html
http://console.teenpattiush1.com/

10、给前端的地址
center地址：  https://www.teenpattiush1.com/center
隐私协议地址：  https://www.teenpattiush1.com/privacy.html
CDN 地址：  http://hot.teenpattiush1.com

CDN 测试连通性
http://hot.teenpattiush1.com/hot_update/teentest/curVersion.txt



### (2023.6.8) (TODO) rummyjump   
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummyjump 
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.44.27(公)
**********46(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummyjump1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummyjump1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummyjump1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummyjump1.com/center
http://www.rummyjump1.com/privacy.html
http://console.rummyjump1.com/

10、给前端的地址
center地址：  https://www.rummyjump1.com/center
隐私协议地址：  https://www.rummyjump1.com/privacy.html
CDN 地址：  http://hot.rummyjump1.com

CDN 测试连通性
http://hot.rummyjump1.com/hot_update/teentest/curVersion.txt

### (2023.6.8) (TODO) teenpattibronze   
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattibronze 
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.46.132(公)
**********44(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattibronze1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattibronze1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattibronze1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattibronze1.com/center
http://www.teenpattibronze1.com/privacy.html
http://console.teenpattibronze1.com/

10、给前端的地址
center地址：  https://www.teenpattibronze1.com/center
隐私协议地址：  https://www.teenpattibronze1.com/privacy.html
CDN 地址：  http://hot.teenpattibronze1.com

CDN 测试连通性
http://hot.teenpattibronze1.com/hot_update/teentest/curVersion.txt



### (2023.6.8) (TODO) rummyswift   
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummyswift 
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.44.158(公)
**********43(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummyswift1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummyswift1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummyswift1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummyswift1.com/center
http://www.rummyswift1.com/privacy.html
http://console.rummyswift1.com/

10、给前端的地址
center地址：  https://www.rummyswift1.com/center
隐私协议地址：  https://www.rummyswift1.com/privacy.html
CDN 地址：  http://hot.rummyswift1.com

CDN 测试连通性
http://hot.rummyswift1.com/hot_update/teentest/curVersion.txt



### (2023.6.8) (TODO) teenpattimix   
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattimix 
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.46.6(公)
**********42(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattimix1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattimix1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattimix1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattimix1.com/center
http://www.teenpattimix1.com/privacy.html
http://console.teenpattimix1.com/

10、给前端的地址
center地址：  https://www.teenpattimix1.com/center
隐私协议地址：  https://www.teenpattimix1.com/privacy.html
CDN 地址：  http://hot.teenpattimix1.com

CDN 测试连通性
http://hot.teenpattimix1.com/hot_update/teentest/curVersion.txt



### (2023.6.8) (TODO) teenpattistorm   
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattistorm 
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
149.129.173.40（公）
**********41（私有）

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattistorm1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattistorm1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattistorm1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattistorm1.com/center
http://www.teenpattistorm1.com/privacy.html
http://console.teenpattistorm1.com/

10、给前端的地址
center地址：  https://www.teenpattistorm1.com/center
隐私协议地址：  https://www.teenpattistorm1.com/privacy.html
CDN 地址：  http://hot.teenpattistorm1.com

CDN 测试连通性
http://hot.teenpattistorm1.com/hot_update/teentest/curVersion.txt




### (2023.6.7) (TODO) teenpattijolt   
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattijolt 
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.73.211（公）
**********40（私有）

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattijolt1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattijolt1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattijolt1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattijolt1.com/center
http://www.teenpattijolt1.com/privacy.html
http://console.teenpattijolt1.com/

10、给前端的地址
center地址：  https://www.teenpattijolt1.com/center
隐私协议地址：  https://www.teenpattijolt1.com/privacy.html
CDN 地址：  http://hot.teenpattijolt1.com

CDN 测试连通性
http://hot.teenpattijolt1.com/hot_update/teentest/curVersion.txt




### (2023.6.7) (TODO) teenpattirumble   
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattirumble 
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.41.170（公）
**********39（私有）

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattirumble1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattirumble1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattirumble1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattirumble1.com/center
http://www.teenpattirumble1.com/privacy.html
http://console.teenpattirumble1.com/

10、给前端的地址
center地址：  https://www.teenpattirumble1.com/center
隐私协议地址：  https://www.teenpattirumble1.com/privacy.html
CDN 地址：  http://hot.teenpattirumble1.com

CDN 测试连通性
http://hot.teenpattirumble1.com/hot_update/teentest/curVersion.txt









### (2023.6.6) (TODO) rummywave   
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummywave 
对比原配置：弹性网卡、主机名、实例名称、自动续费
	
	
147.139.30.185（公）
**********38（私有）

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummywave1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummywave1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummywave1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummywave1.com/center
http://www.rummywave1.com/privacy.html
http://console.rummywave1.com/

10、给前端的地址
center地址：  https://www.rummywave1.com/center
隐私协议地址：  https://www.rummywave1.com/privacy.html
CDN 地址：  http://hot.rummywave1.com

CDN 测试连通性
http://hot.rummywave1.com/hot_update/teentest/curVersion.txt




### (2023.5.30) (TODO) teenpattisprint   
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattisprint 
对比原配置：弹性网卡、主机名、实例名称、自动续费
147.139.112.152（公）
**********35（私有）

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattisprint1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattisprint1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattisprint1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattisprint1.com/center
http://www.teenpattisprint1.com/privacy.html
http://console.teenpattisprint1.com/

10、给前端的地址
center地址：  https://www.teenpattisprint1.com/center
隐私协议地址：  https://www.teenpattisprint1.com/privacy.html
CDN 地址：  http://hot.teenpattisprint1.com

CDN 测试连通性
http://hot.teenpattisprint1.com/hot_update/teentest/curVersion.txt




### (2023.5.29) (TODO) teenpattihustle  
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattihustle 
对比原配置：弹性网卡、主机名、实例名称、自动续费
147.139.107.249(公)
**********34(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattihustle1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattihustle1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattihustle1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattihustle1.com/center
http://www.teenpattihustle1.com/privacy.html
http://console.teenpattihustle1.com/

10、给前端的地址
center地址：  https://www.teenpattihustle1.com/center
隐私协议地址：  https://www.teenpattihustle1.com/privacy.html
CDN 地址：  http://hot.teenpattihustle1.com

CDN 测试连通性
http://hot.teenpattihustle1.com/hot_update/teentest/curVersion.txt


### (2023.5.25 THU) (TODO) flashslots
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-flashslots
对比原配置：弹性网卡、主机名、实例名称、自动续费
147.139.42.118(公)
**********33(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
flashslots2.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.flashslots2.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.flashslots2.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.flashslots2.com/center
http://www.flashslots2.com/privacy.html
http://console.flashslots2.com/

10、给前端的地址
https://www.flashslots2.com/center
https://www.flashslots2.com/privacy.html
CDN 地址
http://hot.flashslots2.com
CDN 测试连通性
http://hot.flashslots2.com/hot_update/teentest/curVersion.txt



### (2023.5.25 THU) (TODO) rummysnap
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-rummysnap
对比原配置：弹性网卡、主机名、实例名称、自动续费
147.139.118.68(公)
**********32(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
rummysnap1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.rummysnap1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.rummysnap1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.rummysnap1.com/center
http://www.rummysnap1.com/privacy.html
http://console.rummysnap1.com/

10、给前端的地址
https://www.rummysnap1.com/center
https://www.rummysnap1.com/privacy.html
CDN 地址
http://hot.rummysnap1.com
CDN 测试连通性
http://hot.rummysnap1.com/hot_update/teentest/curVersion.txt







### (2023.5.25 THU) (TODO) teenpattiglory
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
打开 突发性能实例 无性能约束模式
改实例名和主机名 pro1gate-teenpattiglory
对比原配置：弹性网卡、主机名、实例名称、自动续费
147.139.116.50(公)
**********31(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
teenpattiglory1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.teenpattiglory1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.teenpattiglory1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭

7.来到OSS控制台
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.teenpattiglory1.com/center
http://www.teenpattiglory1.com/privacy.html
http://console.teenpattiglory1.com/

10、给前端的地址
https://www.teenpattiglory1.com/center
https://www.teenpattiglory1.com/privacy.html
CDN 地址
http://hot.teenpattiglory1.com
CDN 测试连通性
http://hot.teenpattiglory1.com/hot_update/teentest/curVersion.txt













### (2023.5.25 THU) (TODO) flashslots  flashslots2.com



### (2023.5.25 THU) (TODO) rummysnap



### (2023.5.25 THU) (TODO) solitairefuse
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
改实例名和主机名 pro1gate-solitairefuse
对比原配置：弹性网卡、主机名、实例名称、自动续费
147.139.79.33(公)
**********30(私有)

3、Xshell 增加接入机登录配置
journalctl -fu yylcron1m.service

4、申请域名
如果被占用则末尾加数字 1
solitairefuse1.com

5、设置子域名解析
console
kefu
www
CNAME: hot --> hot.solitairefuse1.com.w.cdngslb.com
TXT: verification --> 复制 CDN 添加域名界面里的

6、添加 CDN
填写加速域名
hot.solitairefuse1.com
去 DNS 解析那里加 TXT 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭
OSS tphotupdate 桶 --> 域名管理 --> 绑定域名
OSS tphotupdate 桶 --> 域名管理 --> 打开这个域名的 CDN 缓存自动刷新，需要选 8 项

7、管理后台添加渠道

8、Postman 增加渠道配置

9、测试网络连通性
http://www.solitairefuse1.com/center
http://www.solitairefuse1.com/privacy.html
http://console.solitairefuse1.com/

10、给前端的地址
https://www.solitairefuse1.com/center
https://www.solitairefuse1.com/privacy.html
CDN 地址
http://hot.solitairefuse1.com
CDN 测试连通性
http://hot.solitairefuse1.com/hot_update/teentest/curVersion.txt



### (2023.5.23 TUE) (TODO) 渠道表
https://shimo.im/sheets/473QyV2rbMHvvg3w/pfInO



### (2023.5.23 TUE) (TODO) teenpattichase
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
改实例名和主机名 pro1gate-teenpattichase
对比原配置：弹性网卡、主机名、实例名称、自动续费
147.139.127.152(公)
**********27(私有)

3、申请域名
如果被占用则末尾加数字 1
teenpattichase1.com

4、设置子域名解析
center
console
kefu

TXT verification

5、添加 CDN
填写加速域名
hot.teenpattichase1.com
去 DNS 那里加 txt 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭
添加 CNAME 记录 hot --> hot.teenpattichase1.com.w.cdngslb.com
tphotupdate 域名管理 --> 绑定域名
tphotupdate 域名管理 --> 打开这个域名的 CDN 缓存自动刷新

6、管理后台添加渠道

7、代码中添加隐私协议 deploy/nginx/htmlprivacy/teentest.html

8、提交代码

9、Xshell 增加接入机登录配置

10、接入机拉取代码，启动服务
git checkout --track origin/release-gate

11、Postman 增加渠道配置

12、测试网络连通性
http://center.teenpattichase1.com/center
http://center.teenpattichase1.com/privacy.html
http://console.teenpattichase1.com/

13、给前端的地址
https://center.teenpattichase1.com/center
https://center.teenpattichase1.com/privacy.html
CDN 地址
http://hot.teenpattichase1.com
CDN 测试连通性
http://hot.teenpattichase1.com/hot_update/teentest/curVersion.txt



### (2023.5.23 TUE) (TODO) teenpattigalaxy
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
改实例名和主机名 pro1gate-teenpattigalaxy
对比原配置：弹性网卡、主机名、实例名称、自动续费
147.139.80.218(公)
**********26(私有)

3、申请域名
如果被占用则末尾加数字 1
teenpattigalaxy1.com

4、设置子域名解析
center
console
kefu

TXT verification

5、添加 CDN
填写加速域名
hot.teenpattigalaxy1.com
去 DNS 那里加 txt 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭
添加 CNAME 记录 hot --> hot.teenpattigalaxy1.com.w.cdngslb.com
tphotupdate 域名管理 --> 绑定域名
tphotupdate 域名管理 --> 打开这个域名的 CDN 缓存自动刷新

6、管理后台添加渠道

7、代码中添加隐私协议 deploy/nginx/htmlprivacy/teentest.html

8、提交代码

9、Xshell 增加接入机登录配置

10、接入机拉取代码，启动服务
git checkout --track origin/release-gate

11、Postman 增加渠道配置

12、测试网络连通性
http://center.teenpattigalaxy1.com/center
http://center.teenpattigalaxy1.com/privacy.html
http://console.teenpattigalaxy1.com/

13、给前端的地址
https://center.teenpattigalaxy1.com/center
https://center.teenpattigalaxy1.com/privacy.html
CDN 地址
http://hot.teenpattigalaxy1.com
CDN 测试连通性
http://hot.teenpattigalaxy1.com/hot_update/teentest/curVersion.txt



### (2023.5.23 TUE) (TODO) teenpattiblaze
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
改实例名和主机名 pro1gate-teenpattiblaze
对比原配置：弹性网卡、主机名、实例名称、自动续费
147.139.122.62(公)
**********29(私有)

3、申请域名
如果被占用则末尾加数字 1
teenpattiblaze1.com

4、设置子域名解析
center
console
kefu

TXT verification

5、添加 CDN
填写加速域名
hot.teenpattiblaze1.com
去 DNS 那里加 txt 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭
添加 CNAME 记录 hot --> hot.teenpattiblaze1.com.w.cdngslb.com
tphotupdate 域名管理 --> 绑定域名
tphotupdate 域名管理 --> 打开这个域名的 CDN 缓存自动刷新

6、管理后台添加渠道

7、代码中添加隐私协议 deploy/nginx/htmlprivacy/teentest.html

8、提交代码

9、Xshell 增加接入机登录配置

10、接入机拉取代码，启动服务
git checkout --track origin/release-gate

11、Postman 增加渠道配置

12、测试网络连通性
http://center.teenpattiblaze1.com/center
http://center.teenpattiblaze1.com/privacy.html
http://console.teenpattiblaze1.com/

13、给前端的地址
https://center.teenpattiblaze1.com/center
https://center.teenpattiblaze1.com/privacy.html
CDN 地址
http://hot.teenpattiblaze1.com
CDN 测试连通性
http://hot.teenpattiblaze1.com/hot_update/teentest/curVersion.txt



### (2023.5.23 TUE) (TODO) solitairedash
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
改实例名和主机名 pro1gate-solitairedash
对比原配置：弹性网卡、主机名、实例名称、自动续费
147.139.126.79(公)
**********28(私有)

3、申请域名
如果被占用则末尾加数字 1
solitairedash1.com

4、设置子域名解析
center
console
kefu

TXT verification

5、添加 CDN
填写加速域名
hot.solitairedash1.com
去 DNS 那里加 txt 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭
添加 CNAME 记录 hot --> hot.solitairedash1.com.w.cdngslb.com
tphotupdate 域名管理 --> 绑定域名
tphotupdate 域名管理 --> 打开这个域名的 CDN 缓存自动刷新

6、管理后台添加渠道

7、代码中添加隐私协议 deploy/nginx/htmlprivacy/teentest.html

8、提交代码

9、Xshell 增加接入机登录配置

10、接入机拉取代码，启动服务
git checkout --track origin/release-gate

11、Postman 增加渠道配置

12、测试网络连通性
http://center.solitairedash1.com/center
http://center.solitairedash1.com/privacy.html
http://console.solitairedash1.com/

13、给前端的地址
https://center.solitairedash1.com/center
https://center.solitairedash1.com/privacy.html
CDN 地址
http://hot.solitairedash1.com
CDN 测试连通性
http://hot.solitairedash1.com/hot_update/teentest/curVersion.txt



### (2023.5.23 TUE) (TODO) coveslots
2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
改实例名和主机名 pro1gate-coveslots
对比原配置：弹性网卡、主机名、实例名称、自动续费
147.139.107.249(公)
**********25(私有)

3、申请域名
如果被占用则末尾加数字 1
coveslots.com

4、设置子域名解析
center
console
kefu

5、添加 CDN
填写加速域名
hot.coveslots.com
去 DNS 那里加 txt 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭
添加 CNAME 记录 hot --> hot.coveslots.com.w.cdngslb.com
tphotupdate 域名管理 --> 绑定域名
tphotupdate 域名管理 --> 打开这个域名的 CDN 缓存自动刷新

6、管理后台添加渠道

7、代码中添加隐私协议 deploy/nginx/htmlprivacy/coveslots.html

8、提交代码

9、Xshell 增加接入机登录配置

10、接入机拉取代码，启动服务
git checkout --track origin/release-gate

11、Postman 增加渠道配置

12、测试网络连通性
http://center.coveslots.com/center
http://center.coveslots.com/privacy.html
http://console.coveslots.com/

13、给前端的地址
https://center.coveslots.com/center
https://center.coveslots.com/privacy.html
CDN 地址
http://hot.coveslots.com
CDN 测试连通性
http://hot.coveslots.com/hot_update/teentest/curVersion.txt



### (2023.5.18 THU) (TODO) 总表
teenpattijive
teenpattimatch
teenpattiboost teenpattiboost1
teenpattisurge
rummyzone rummyzone1
rummywave rummywave1



### (2023.5.12 FRI) (TODO) rummyzone
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
改实例名和主机名 pro1gate-rummyzone
对比原配置：弹性网卡、主机名、实例名称、自动续费
147.139.41.65(公)
**********22(私有)

3、申请域名
如果被占用则末尾加数字 1
rummyzone1.com

4、设置子域名解析
center
console
kefu

5、添加 CDN
填写加速域名
hot.rummyzone1.com
去 DNS 那里加 txt 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭
添加 CNAME 记录 hot --> hot.rummyzone1.com.w.cdngslb.com
tphotupdate 域名管理 --> 绑定域名
tphotupdate 域名管理 --> 打开这个域名的 CDN 缓存自动刷新

6、管理后台添加渠道

7、代码中添加隐私协议 deploy/nginx/htmlprivacy/teentest.html

8、提交代码

9、Xshell 增加接入机登录配置

10、接入机拉取代码，启动服务
git checkout --track origin/release-gate

11、Postman 增加渠道配置

12、测试网络连通性
http://center.rummyzone1.com/center
http://center.rummyzone1.com/privacy.html
http://console.rummyzone1.com/

13、给前端的地址
https://center.rummyzone1.com/center
https://center.rummyzone1.com/privacy.html
CDN 地址
http://hot.rummyzone1.com
CDN 测试连通性
http://hot.rummyzone1.com/hot_update/teentest/curVersion.txt



### (2023.5.12 FRI) (TODO) teenpattisurge
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
改实例名和主机名 pro1gate-teenpattisurge
对比原配置：弹性网卡、主机名、实例名称、自动续费
147.139.30.251(公)
**********21(私有)

3、申请域名
如果被占用则末尾加数字 1
teenpattisurge.com

4、设置子域名解析
center
console
kefu

5、添加 CDN
填写加速域名
hot.teenpattisurge.com
去 DNS 那里加 txt 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭
添加 CNAME 记录 hot --> hot.teenpattisurge.com.w.cdngslb.com
tphotupdate 域名管理 --> 绑定域名
tphotupdate 域名管理 --> 打开这个域名的 CDN 缓存自动刷新

6、管理后台添加渠道

7、代码中添加隐私协议 deploy/nginx/htmlprivacy/teentest.html

8、提交代码

9、Xshell 增加接入机登录配置

10、接入机拉取代码，启动服务
git checkout --track origin/release-gate

11、Postman 增加渠道配置

12、测试网络连通性
http://center.teenpattisurge.com/center
http://center.teenpattisurge.com/privacy.html
http://console.teenpattisurge.com/

13、给前端的地址
https://center.teenpattisurge.com/center
https://center.teenpattisurge.com/privacy.html
CDN 地址
http://hot.teenpattisurge.com
CDN 测试连通性
http://hot.teenpattisurge.com/hot_update/teentest/curVersion.txt



### (2023.5.12 FRI) (TODO) teenpattiboost
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
改实例名和主机名 pro1gate-teenpattiboost
对比原配置：弹性网卡、主机名、实例名称、自动续费
149.129.167.117(公)
**********20(私有)

3、申请域名
如果被占用则末尾加数字 1
teenpattiboost1.com

4、设置子域名解析
center
console
kefu

5、添加 CDN
填写加速域名
hot.teenpattiboost1.com
去 DNS 那里加 txt 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭
添加 CNAME 记录 hot --> hot.teenpattiboost1.com.w.cdngslb.com
tphotupdate 域名管理 --> 绑定域名
tphotupdate 域名管理 --> 打开这个域名的 CDN 缓存自动刷新

6、管理后台添加渠道

7、代码中添加隐私协议 deploy/nginx/htmlprivacy/teentest.html

8、提交代码

9、Xshell 增加接入机登录配置

10、接入机拉取代码，启动服务
git checkout --track origin/release-gate

11、Postman 增加渠道配置

12、测试网络连通性
http://center.teenpattiboost1.com/center
http://center.teenpattiboost1.com/privacy.html
http://console.teenpattiboost1.com/

13、给前端的地址
https://center.teenpattiboost1.com/center
https://center.teenpattiboost1.com/privacy.html
CDN 地址
http://hot.teenpattiboost1.com
CDN 测试连通性
http://hot.teenpattiboost1.com/hot_update/teentest/curVersion.txt



### (2023.5.12 FRI) (TODO) teenpattijive
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
改实例名和主机名 pro1gate-teenpattijive
对比原配置：弹性网卡、主机名、实例名称、自动续费
147.139.30.208(公)
**********19(私有)

3、申请域名
如果被占用则末尾加数字 1
teenpattijive.com

4、设置子域名解析
center
console
kefu

5、添加 CDN
填写加速域名
hot.teenpattijive.com
去 DNS 那里加 txt 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭
添加 CNAME 记录 hot --> hot.teenpattijive.com.w.cdngslb.com
tphotupdate 域名管理 --> 绑定域名
tphotupdate 域名管理 --> 打开这个域名的 CDN 缓存自动刷新

6、管理后台添加渠道

7、代码中添加隐私协议 deploy/nginx/htmlprivacy/teentest.html

8、提交代码

9、Xshell 增加接入机登录配置

10、接入机拉取代码，启动服务
git checkout --track origin/release-gate

11、Postman 增加渠道配置

12、测试网络连通性
http://center.teenpattijive.com/center
http://center.teenpattijive.com/privacy.html
http://console.teenpattijive.com/

13、给前端的地址
https://center.teenpattijive.com/center
https://center.teenpattijive.com/privacy.html
CDN 地址
http://hot.teenpattijive.com
CDN 测试连通性
http://hot.teenpattijive.com/hot_update/teentest/curVersion.txt



### (2023.5.17 WED) (TODO) teenpattimatch.com 白买了
申请 CDN 时这个根域名被占用了

        // 有些特殊的是因为创建 CDN 时，提示：该域名的根域已经被其他账号占用，若需要新增请提交工单处理。
        // 理解错了，不是 hot 部分被占用了，是 teenpattimatch.com 部分被占用了
        // 可能是别人申请了 teenpattimatch.com 建了 CDN，然后又释放了 teenpattimatch.com 域名



### (2023.5.12 FRI) (TODO) teenpattimatch
1、复制一份渠道添加步骤，替换渠道名

2、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
改实例名和主机名 pro1gate-teenpattimatch
对比原配置：弹性网卡、主机名、实例名称、自动续费
147.139.45.174(公)
**********18(私有)

3、申请域名
如果被占用则末尾加数字 1
teenpattimatch1.com

4、设置子域名解析
center
console
kefu

5、添加 CDN
填写加速域名
hot.teenpattimatch1.com
去 DNS 那里加 txt 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭
添加 CNAME 记录 hot --> hot.teenpattimatch1.com.w.cdngslb.com
tphotupdate 域名管理 --> 绑定域名
tphotupdate 域名管理 --> 打开这个域名的 CDN 缓存自动刷新

6、管理后台添加渠道

7、代码中添加隐私协议 deploy/nginx/htmlprivacy/teentest.html

8、提交代码

9、Xshell 增加接入机登录配置

10、接入机拉取代码，启动服务
git checkout --track origin/release-gate

11、Postman 增加渠道配置

12、测试网络连通性
http://center.teenpattimatch1.com/center
http://center.teenpattimatch1.com/privacy.html
http://console.teenpattimatch1.com/

13、给前端的地址
https://center.teenpattimatch1.com/center
https://center.teenpattimatch1.com/privacy.html
CDN 地址
http://hot.teenpattimatch1.com
CDN 测试连通性
http://hot.teenpattimatch1.com/hot_update/teentest/curVersion.txt



### (2023.5.12 FRI) (TODO) rummyblitz 添加 CDN
填写加速域名
hot.rummyblitz1.com
去 DNS 那里加 txt 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭
添加 CNAME 记录 hot --> hot.rummyblitz1.com.w.cdngslb.com
tphotupdate 域名管理 --> 绑定域名
tphotupdate 域名管理 --> 打开这个域名的 CDN 缓存自动刷新

CDN 地址
http://hot.rummyblitz1.com
CDN 测试连通性
http://hot.rummyblitz1.com/hot_update/teentest/curVersion.txt



### (2023.5.12 FRI) (TODO) 退订废弃的接入机
texaspokerrush
frenzyslots
solitairearena
teenpattihive



### (2023.5.12 FRI) (TODO) teenpattiquest
1、模板购买接入机
buy-pro1gate-40rmb-v3
密钥对 pro1gate
改实例名和主机名 pro1gate-teenpattiquest
147.139.112.152(公)
**********16(私有)

2、申请域名
如果被占用则末尾加数字 1
teenpattiquest1.com

3、设置子域名解析
center
console
kefu

3、添加 CDN
填写加速域名
hot.teenpattiquest1.com
去 DNS 那里加 txt 验证记录
加速区域：全球（不包含中国内地）
资源分组：rgpro1
源站信息：tphotupdate.oss-ap-south-1.aliyuncs.com
页面优化：全部关闭
添加 CNAME 记录 hot --> hot.teenpattiquest1.com.w.cdngslb.com
tphotupdate 域名管理 --> 绑定域名
tphotupdate 域名管理 --> 打开这个域名的 CDN 缓存自动刷新

5、管理后台添加渠道

6、代码中添加隐私协议 deploy/nginx/htmlprivacy/teentest.html

8、提交代码

10、Xshell 增加接入机登录配置

11、接入机拉取代码，启动服务

12、Postman 增加渠道配置

12、测试网络连通性
http://center.teenpattiquest1.com/center
http://center.teenpattiquest1.com/privacy.html

13、给前端的地址
https://center.teenpattiquest1.com/center
https://center.teenpattiquest1.com/privacy.html
CDN 地址
http://hot.teenpattiquest1.com
CDN 测试连通性
http://hot.teenpattiquest1.com/hot_update/teentest/curVersion.txt



### (2023.5.7 SUN) (DONE) Teen Test
teentest

1、模板购买接入机
buy-pro1gate-40rmb
密钥对 pro1gate
改实例名和主机名 pro1gate-teentest
147.139.30.80(公)
**********15(私有)

2、申请域名
如果被占用则末尾加数字 1
teentest.top

3、设置子域名解析
center
console
kefu

5、管理后台添加渠道

6、代码中添加隐私协议 deploy/nginx/htmlprivacy/teentest.html

7、代码中的渠道表添加渠道信息 php/lconfig/source/SourceTable.php

8、提交代码

9、业务机拉取代码，更新服务

10、Xshell 增加接入机登录配置

11、接入机拉取代码，启动服务

12、测试网络连通性
http://center.teentest.top/center
http://center.teentest.top/privacy.html
http://www.teentest.top/privacy.html

13、给前端的地址
https://center.teentest.top/center
https://center.teentest.top/privacy.html
https://www.teentest.top/privacy.html



### (2023.5.7 SUN) (DONE) 接入层优化：Texas Poker Rush 迁移到接入机模式 被拒
texaspokerrush
texaspokerrush.com

1、模板购买接入机带自定义镜像
buy-pro1gate-40rmb
密钥对 pro1gate
改实例名和主机名 pro1gate-texaspokerrush
启用自动续费
签署服务条款
147.139.43.99(公)
**********14(私有)

3、设置子域名解析
center
console
kefu

10、接入机拉取代码，启动服务

11、测试网络连通性
http://center.texaspokerrush.com/center
http://center.texaspokerrush.com/privacy.html

12、给前端的地址
https://center.texaspokerrush.com/center
https://center.texaspokerrush.com/privacy.html



### (2023.5.7 SUN) (DONE) 接入层优化：Rummy Blitz 迁移到接入机模式
rummyblitz
rummyblitz1.com

1、模板购买接入机带自定义镜像
buy-pro1gate-40rmb
密钥对 pro1gate
改实例名和主机名 pro1gate-rummyblitz
147.139.30.192(公)
**********13(私有)

3、设置子域名解析
center
console
kefu

10、接入机拉取代码，启动服务

11、测试网络连通性
http://center.rummyblitz1.com/center
http://center.rummyblitz1.com/privacy.html

12、给前端的地址
https://center.rummyblitz1.com/center
https://center.rummyblitz1.com/privacy.html



### (2023.5.7 SUN) (DONE) Frenzy Slots 被拒
frenzyslots

1、购买接入机（本次优化为模板购买）
buy-pro1gate-40rmb
密钥对 pro1gate
改实例名和主机名 pro1gate-frenzyslots
147.139.31.127
**********11

2、申请域名
如果被占用则末尾加数字 1
frenzyslots1.com

3、设置子域名解析
center
console
kefu

4、给接入机装系统（待下次购买时测试自定义镜像）
需要优化掉这步骤，买机器时用自定义镜像

5、管理后台添加渠道

6、代码中添加隐私协议 deploy/nginx/htmlprivacy/frenzyslots.html

7、代码中的渠道表添加渠道信息 php/lconfig/source/SourceTable.php

8、提交代码

9、业务机拉取代码，更新服务

10、Xshell 增加接入机登录配置

11、接入机拉取代码，启动服务

12、测试网络连通性
http://center.frenzyslots1.com/center
http://center.frenzyslots1.com/privacy.html

13、给前端的地址
https://center.frenzyslots1.com/center
https://center.frenzyslots1.com/privacy.html



### (2023.5.6 SAT) (DONE) Solitaire Arena
solitairearena

1、购买接入机
asfas4543shgJGLJKFD
pro1gate-solitairearena
147.139.33.220(公)
**********10(私有)

2、申请域名
solitairearena1.com

3、设置域名解析
center
console
kefu

4、给接入机装系统
需要优化掉这步骤，用镜像

4、接入层添加解析、证书
certbot 证书会自动续期

5、管理后台添加渠道
程序人员设置 --> 渠道自定义参数

7、给前端的地址
https://center.solitairearena1.com/center
https://center.solitairearena1.com/privacy.html



### (2023.5.6 SAT) (DONE) TeenPatti Hive
1、购买接入机
adfjas3204320AFDASLIFe
pro1gate-teenpattihive
*************(公)
************(私有)

2、申请域名
teenpattihive.com

3、设置域名解析
center
console
kefu

4、接入层添加解析、证书
certbot 证书会自动续期

5、管理后台添加渠道
程序人员设置 --> 渠道自定义参数

6、额外加两个子域名
pay.teenpattihive.com
aiadmin.teenpattihive.com
打到 pro1main2 ***************
pro1main2 的内网 IP ************

管理后台 https://console.teenpattihive.com/index/index/adminlogin.html

7、给前端的地址
https://center.teenpattihive.com/center
https://center.teenpattihive.com/privacy.html



### (2023.5.5 FRI) (TODO) Rummy Blitz
rummyblitz
rummyblitz1.com
弹性网卡 enirummyblitz **********
eiprummyblitz **************



### (2023.5.5 FRI) (TODO) Texas Poker Rush
texaspokerrush
texaspokerrush.com
wwwtexaspokerrush.oss-ap-south-1.aliyuncs.com
iptexaspokerrush ************* 这个 IP 绑到机器上了 pro1main1
www.texaspokerrush.com
https://center.texaspokerrush.com/center



### (2023.5.5 FRI) (TODO) 添加渠道的步骤简化版
1、注册域名
2、阿里云 OSS 加一个桶放官网
3、买一个弹性 IP 地址，绑定到某个接入机
4、设置域名解析
www
center
console
kefu
5、接入层添加解析、证书
certbot 证书会自动续期
6、管理后台添加渠道
程序人员设置 --> 渠道自定义参数



### (2023.5.5 FRI) (TODO) 添加渠道的步骤
1、注册域名
之前三个域名是在 namecheap 申请的，后边的都去阿里云申请
不会自动续期，要注意续期问题！！！

2、阿里云 OSS 加一个桶放官网
这个子账号只有阿里云 OSS 的权限
https://signin.aliyun.com/login.htm?username=b771%401181179360868999.onaliyun.com&defaultShowQrCode=false#/main
<EMAIL>
z!MxGesKWU(SJZemDpu$7kdC7}EZc1j4

3、给接入 NAT 申请一个弹性 IP 地址，配置 DNAT
**************
*************
但是发现一个 DNAT 不能让两个公网 IP 端口指向同一个内网端口
给接入机绑定弹性 IP 试试
IP 地址有成本：CN¥0.040 /小时/个
绑定弹性 IP 之后，之前 DNAT 的端口映射不通了
加个辅助弹性网卡，最多 10 个私网 IP，每个能对应一个公网 IP

3、设置域名解析
www
center
console
kefu

4、接入层添加解析、证书
certbot 证书会自动续期

4、OSS 添加证书

5、管理后台添加渠道
程序人员设置 --> 渠道自定义参数



### (2023.5.6 SAT) (TODO) 按一个渠道一台接入机的方式
adfjas3204320AFDASLIFe
pro1gate-teenpattihive
*************(公)
************(私有)
