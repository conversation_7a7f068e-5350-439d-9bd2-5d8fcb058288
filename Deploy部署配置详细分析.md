# Deploy部署配置详细分析

## 概述

`deploy/` 文件夹包含了整个项目的部署、运维、监控和配置管理的完整体系。这是一个高度自动化的运维系统，支持多环境部署和完整的监控告警机制。

---

## 📁 目录结构总览

```
deploy/
├── aws/              # AWS云服务配置
├── bootstrap/        # 系统初始化脚本
├── cppenv/          # 开发环境安装脚本
├── cron/            # 定时任务配置
├── dns/             # DNS管理脚本
├── envfiles/        # 环境配置文件
├── fixdb/           # 数据库修复脚本
├── fpm/             # PHP-FPM配置
├── nginx/           # Nginx配置
├── prometheus/      # 监控配置
├── systemd/         # 系统服务配置
├── vector/          # 日志收集配置
├── vice/            # 第三方服务安装
└── yylenv/          # 运维工具脚本
```

---

## 🔧 详细文件分析

### 📂 deploy/aws/ - AWS云服务配置

#### `iam-policy-bedev.json`
**作用**: 后端开发人员IAM权限策略
**权限范围**:
- 只读权限：EC2、VPC、RDS、ElastiCache、CloudWatch、CloudFront、Route53、S3、Athena等
- 写权限：S3存储、Athena查询、CodeCommit代码库
- 个人权限：修改自己的密码和SSH密钥

#### `iam-policy-fedev.json`
**作用**: 前端开发人员IAM权限策略
**权限范围**:
- S3特定目录的读写权限（apks、agame、h5_release等）
- CloudFront缓存刷新权限
- Athena查询权限
- CodeCommit代码库权限

#### `iam-policy-imgup.json`
**作用**: 图片上传专用账号权限策略

#### `update_iam.sh`
**作用**: AWS IAM用户和权限管理脚本
**功能**:
- 创建不同角色的IAM用户（fedev、bedev、imgup、bot）
- 分配相应的权限策略
- 生成访问密钥

### 📂 deploy/bootstrap/ - 系统初始化

#### `bootstrap_aws.sh`
**作用**: AWS EC2实例初始化脚本
**功能**:
- 安装基础工具（htop、git、gcc、mysql客户端、redis客户端等）
- 编译安装lrzsz文件传输工具
- 从Git仓库拉取项目代码
- 执行环境初始化

### 📂 deploy/cppenv/ - 开发环境安装

包含多个开发环境安装脚本：
- `cppenv_install4linux_go.sh` - Linux下Go环境安装
- `cppenv_install4win_php_*.sh` - Windows下PHP环境安装
- `cppenv_install*_pytools.sh` - Python工具安装
- `cppenv_install*_cpptools.sh` - C++工具安装

### 📂 deploy/cron/ - 定时任务配置

#### `cron1m.sh` - 每分钟执行
**功能**:
- 代码更新检测和自动拉取
- 服务注册（node_exporter）
- 支付订单检查
- 营收数据统计
- CloudFront同步检查

#### `cron1h.sh` - 每小时执行
**功能**:
- Adjust广告数据同步
- 代理系统用户池统计
- 代理每日数据统计
- 代理佣金明细统计

#### `cron0500.sh` - 每日5点执行
**功能**:
- 每日数据统计汇总
- 支付通道统计
- 充值用户玩法统计
- Super Deals活动统计
- 大R用户监测
- 分时充值注册统计
- 各种业务数据报表生成

#### 其他定时任务
- `cron15s.sh` - 每15秒执行
- `cron5m.sh` - 每5分钟执行
- `cron30m.sh` - 每30分钟执行
- `cron1h29.sh` - 每小时29分执行
- `cron0233.sh` - 每日2:33执行
- `cron1030.sh` - 每日10:30执行

### 📂 deploy/dns/ - DNS管理

#### `SetupDns.php`
**作用**: DNS记录管理脚本
**功能**: 自动化域名解析配置

### 📂 deploy/envfiles/ - 环境配置文件

#### `addr.pro3.sh` - 生产环境配置
**包含**:
- 数据库连接配置（Redis、MySQL、ClickHouse）
- 内部服务地址配置
- Nginx upstream配置
- AWS服务配置
- 第三方服务账号配置
- 对象存储配置
- 域名配置
- Telegram机器人配置
- 功能开关配置

#### `addr.dev2.sh` - 开发环境配置
**特点**: 使用本地服务地址，测试用的配置参数

#### 其他环境配置
- `addr.test3.sh` - 测试环境
- `addr.local1.*.sh` - 本地开发环境（不同机器）

### 📂 deploy/fixdb/ - 数据库修复脚本

包含多个数据修复和历史数据恢复脚本：
- `media.sh` - 媒体数据修复
- `promo_ranking.sh` - 代理排名修复
- `restoreHistoryData_*.sh` - 各种历史数据恢复脚本

### 📂 deploy/fpm/ - PHP-FPM配置

#### `php-fpm.conf` - 主配置文件
#### `php-fpm.d--www.conf` - 进程池配置
#### `fpmus.conf` - 自定义FPM配置

### 📂 deploy/nginx/ - Nginx配置

#### `confbiz/nginx.conf` - 主配置文件
**特点**:
- 高性能配置（worker_connections 65535）
- 安全配置（隐藏服务器版本）
- 日志配置
- 模块化配置加载

#### `confbiz/sites-available/` - 站点配置
- `console.conf` - 管理后台配置
- `api.conf` - API服务配置
- `kefu.conf` - 客服系统配置
- `pay.conf` - 支付系统配置

#### `confbiz/services/` - 服务代理配置
包含各种内部服务的反向代理配置：
- `alertbot.conf` - 告警机器人
- `console.conf` - 管理控制台
- `grafana.conf` - 监控面板
- `prometheus.conf` - 监控系统
- `clickhouse.conf` - 数据库代理

#### `baseconf/` - 基础配置
- `cors.conf` - 跨域配置
- `security.conf` - 安全配置
- `fastcgi.conf` - FastCGI配置

### 📂 deploy/prometheus/ - 监控配置

#### `prometheus.yml` - Prometheus主配置
**监控目标**:
- pushmetrics服务（业务指标）
- servermetrics服务（服务器指标）
- node_exporter（系统指标）

#### `alertmanager.yml` - 告警管理配置
**功能**:
- 告警分组和去重
- 告警路由规则
- Webhook通知配置

#### `rules.d/` - 告警规则
- `pay.alert.yml` - 支付相关告警
- `users.alert.yml` - 用户相关告警
- `node.alert.yml` - 系统资源告警
- `sms.alert.yml` - 短信服务告警

### 📂 deploy/systemd/ - 系统服务配置

包含所有服务的systemd配置文件：

#### 核心游戏服务
- `yylserverws.service` - WebSocket网关服务
- `yylserverbiz.service` - 业务逻辑服务
- `yylserverhttp.service` - HTTP API服务
- `yylserverreg.service` - 服务注册中心

#### Go微服务
- `yylalertbot.service` - 告警机器人
- `yylpushmetrics.service` - 指标收集服务
- `yylchbuf.service` - ClickHouse缓冲服务
- `yylmybuf.service` - MySQL缓冲服务
- `yylattributionserver.service` - 用户归因服务

#### 监控服务
- `yylprometheus.service` - 监控系统
- `yylgrafana.service` - 监控面板
- `yylalertmanager.service` - 告警管理

#### 其他服务
- `yylfpmus.service` - PHP-FPM服务
- `yylnginx.service` - Nginx服务
- `yylvector.service` - 日志收集服务

### 📂 deploy/vector/ - 日志收集配置

#### `dev2bj1.toml`
**作用**: Vector日志收集配置
**功能**: 日志聚合和转发

### 📂 deploy/vice/ - 第三方服务安装

包含各种第三方服务的安装脚本：
- `aws_install_alertmanager.sh` - 告警管理器安装
- `aws_install_clickhouse.sh` - ClickHouse数据库安装
- `aws_install_grafana.sh` - Grafana监控面板安装
- `aws_install_victoriametrics.sh` - VictoriaMetrics时序数据库安装
- `aws_install_metabase.sh` - Metabase数据分析工具安装

### 📂 deploy/yylenv/ - 运维工具脚本

#### `api.sh` - 运维API入口
**作用**: 加载所有运维工具函数

#### `api_config_*.sh` - 配置管理脚本
- `api_config_host.sh` - 主机配置
- `api_config_addr.sh` - 地址配置
- `api_config_service.sh` - 服务配置
- `api_config_yylinstall.sh` - 安装配置

#### `api_util_*.sh` - 工具函数
- `api_util_yyl.sh` - 项目工具函数
- `api_util_wm.sh` - Workerman工具函数
- `api_util_sc.sh` - 服务控制函数
- `api_util_version.sh` - 版本管理函数

#### `install_*.sh` - 安装脚本
- `install_php81_aws.sh` - PHP 8.1安装（AWS环境）
- `install_php81_aliyun.sh` - PHP 8.1安装（阿里云环境）
- `install_openresty.sh` - OpenResty安装
- `install_node_exporter.sh` - Node Exporter安装
- `install_docker.sh` - Docker安装
- `install_acmesh.sh` - ACME.sh SSL证书工具安装

#### `cron_*.sh` - 定时任务脚本
- `cron_check_git_commit_changed.sh` - Git提交变更检测
- `cron_check_reboot_serverws.sh` - WebSocket服务重启检查
- `cron_log_server_status.sh` - 服务器状态日志
- `cron_update_metrics_node_info.sh` - 节点信息指标更新

#### `on_git_commit_changed.sh`
**作用**: Git提交变更时的自动化处理

---

## 🎯 核心特性

### 1. **多环境支持**
- 生产环境（pro3）
- 开发环境（dev2）
- 测试环境（test3）
- 本地环境（local1）

### 2. **完整的监控体系**
- Prometheus指标收集
- Grafana可视化面板
- Alertmanager告警管理
- 自定义告警规则

### 3. **自动化部署**
- 一键环境初始化
- 自动服务安装配置
- Git自动更新机制
- 服务自动重启

### 4. **高可用架构**
- 多进程服务配置
- 服务自动重启
- 负载均衡配置
- 故障自动恢复

### 5. **安全配置**
- IAM权限精细控制
- Nginx安全配置
- 服务隔离
- 访问控制

### 6. **数据处理**
- 定时数据统计
- 历史数据恢复
- 数据库优化
- 缓存配置

---

## 🚀 部署流程

### 1. **初始化阶段**
```bash
# 1. 执行bootstrap脚本
bash deploy/bootstrap/bootstrap_aws.sh

# 2. 加载环境配置
source deploy/envfiles/addr.pro3.sh

# 3. 执行安装脚本
bash deploy/yylenv/api_config_yylinstall.sh
```

### 2. **服务配置阶段**
```bash
# 1. 安装Nginx配置
bash deploy/nginx/install.sh

# 2. 安装systemd服务
# 所有.service文件会被复制到/etc/systemd/system/

# 3. 启动服务
systemctl enable --now yyl*
```

### 3. **监控配置阶段**
```bash
# 1. 配置Prometheus
cp deploy/prometheus/prometheus.yml /etc/prometheus/

# 2. 配置告警规则
cp deploy/prometheus/rules.d/* /etc/prometheus/rules.d/

# 3. 启动监控服务
systemctl enable --now prometheus alertmanager grafana
```

---

## 📊 监控告警

### 支付系统告警
- 无充值回调近十分钟
- 充值到账波动
- 代收成功率低
- 支付通道异常

### 系统资源告警
- CPU使用率过高
- 内存使用率过高
- 磁盘空间不足
- 网络异常

### 业务指标告警
- 用户活跃度异常
- 游戏收入异常
- 服务响应时间过长

---

## 🔧 运维工具

### 自动化脚本
- 代码自动更新
- 服务自动重启
- 配置自动同步
- 数据自动备份

### 监控工具
- 实时指标监控
- 日志聚合分析
- 性能分析
- 告警通知

### 管理工具
- 服务状态管理
- 配置文件管理
- 用户权限管理
- 域名管理

---

## 📝 总结

这个deploy目录展现了一个**企业级的运维管理体系**：

### ✅ 优势
1. **高度自动化** - 从部署到监控全流程自动化
2. **多环境支持** - 开发、测试、生产环境完整分离
3. **完整监控** - 覆盖系统、应用、业务的全方位监控
4. **安全可靠** - 精细的权限控制和安全配置
5. **易于维护** - 模块化配置，便于管理和扩展

### 🎯 核心价值
- **降低运维成本** - 自动化减少人工操作
- **提高系统稳定性** - 完整的监控和自动恢复机制
- **支持快速扩展** - 标准化的部署和配置流程
- **保障业务连续性** - 多重保障机制确保服务可用性

这是一个**生产级别的运维系统**，体现了现代化运维的最佳实践。